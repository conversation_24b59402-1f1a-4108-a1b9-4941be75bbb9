<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>senox-realty-api</artifactId>
    <packaging>jar</packaging>

    <parent>
        <artifactId>senox-realty</artifactId>
        <groupId>com.senox.realty</groupId>
        <version>1.3.2-SNAPSHOT</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.senox</groupId>
            <artifactId>api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.senox.pm</groupId>
            <artifactId>senox-payment-api</artifactId>
        </dependency>
    </dependencies>

</project>
