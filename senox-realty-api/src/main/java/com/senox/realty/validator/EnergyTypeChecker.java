package com.senox.realty.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2020/12/24 11:11
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnergyTypeValidator.class)
public @interface EnergyTypeChecker {

    String message() default "Invalid realty readings type.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
