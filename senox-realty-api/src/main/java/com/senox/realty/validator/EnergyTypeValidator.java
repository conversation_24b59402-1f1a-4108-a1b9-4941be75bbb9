package com.senox.realty.validator;

import com.senox.common.constant.device.EnergyType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 物业读数类型校验
 * <AUTHOR>
 * @Date 2020/12/22 11:16
 */
public class EnergyTypeValidator implements ConstraintValidator<EnergyTypeChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        EnergyType readingsType = EnergyType.fromValue(value);
        return readingsType != null;
    }
}
