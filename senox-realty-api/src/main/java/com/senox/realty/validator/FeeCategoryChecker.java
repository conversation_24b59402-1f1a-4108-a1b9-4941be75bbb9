package com.senox.realty.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 费项类别状态
 * <AUTHOR>
 * @date 2021/2/19 15:01
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = FeeCategoryValidator.class)
public @interface FeeCategoryChecker {

    String message() default "Invalid fee category.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
