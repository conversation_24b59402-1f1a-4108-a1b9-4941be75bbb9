package com.senox.realty.validator;

import com.senox.realty.constant.ContractStatus;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 合同状态校验
 * <AUTHOR>
 * @date 2021/2/19 15:04
 */
public class ContractStatusValidator implements ConstraintValidator<ContractStatusChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        return ContractStatus.fromValue(value) != null;
    }
}
