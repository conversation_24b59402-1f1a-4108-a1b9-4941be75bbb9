package com.senox.realty.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 校验交款方式
 * <AUTHOR>
 * @date 2021/2/19 15:15
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CostTypeValidator.class)
public @interface CostTypeChecker {

    String message() default "Invalid cost type.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
