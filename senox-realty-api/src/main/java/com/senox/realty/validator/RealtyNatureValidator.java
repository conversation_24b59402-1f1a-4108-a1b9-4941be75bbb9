package com.senox.realty.validator;

import com.senox.realty.constant.RealtyNature;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 物业性质校验
 * <AUTHOR>
 * @Date 2020/12/22 11:16
 */
public class RealtyNatureValidator implements ConstraintValidator<RealtyNatureChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        return  RealtyNature.fromValue(value) != null;
    }
}
