package com.senox.realty.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 校验物业性质
 * <AUTHOR>
 * @Date 2020/12/22 11:14
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = RealtyNatureValidator.class)
public @interface RealtyNatureChecker {

    String message() default "Invalid realty nature.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}

