package com.senox.realty.validator;

import com.senox.realty.constant.ContractType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 合同类型校验
 * <AUTHOR>
 * @date 2021/2/19 14:44
 */
public class ContractTypeValidator implements ConstraintValidator<ContractTypeChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        return ContractType.fromValue(value) != null;
    }
}
