package com.senox.realty.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 校验合同类型
 * <AUTHOR>
 * @date 2021/2/19 14:43
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ContractTypeValidator.class)
public @interface ContractTypeChecker {

    String message() default "Invalid contract type.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
