package com.senox.realty.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 校验合同状态
 * <AUTHOR>
 * @date 2021/2/19 15:01
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ContractStatusValidator.class)
public @interface ContractStatusChecker {

    String message() default "Invalid contract status.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
