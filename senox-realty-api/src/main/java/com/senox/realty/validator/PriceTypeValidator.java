package com.senox.realty.validator;

import com.senox.realty.constant.PriceType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 价格类别校验
 * <AUTHOR>
 * @date 2021/2/19 15:04
 */
public class PriceTypeValidator implements ConstraintValidator<PriceTypeChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        return PriceType.fromValue(value) != null;
    }
}
