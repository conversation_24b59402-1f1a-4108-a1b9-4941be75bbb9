package com.senox.realty.validator;

import com.senox.realty.constant.CostType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 交款方式校验
 * <AUTHOR>
 * @date 2021/2/19 15:19
 */
public class CostTypeValidator implements ConstraintValidator<CostTypeChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        return CostType.fromValue(value) != null;
    }
}
