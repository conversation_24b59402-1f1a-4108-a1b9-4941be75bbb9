package com.senox.realty.validator;

import com.senox.realty.constant.InspectResult;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 巡检结果校验
 * <AUTHOR>
 * @date 2021/2/19 15:04
 */
public class InspectResultValidator implements ConstraintValidator<InspectResultChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        return InspectResult.fromValue(value) != null;
    }
}
