package com.senox.realty.validator;

import com.senox.realty.constant.FeeCategory;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 费项类别校验
 * <AUTHOR>
 * @date 2021/2/19 15:04
 */
public class FeeCategoryValidator implements ConstraintValidator<FeeCategoryChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        return FeeCategory.fromValue(value) != null;
    }
}
