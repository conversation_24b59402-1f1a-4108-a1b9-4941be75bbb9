package com.senox.realty.validator;

import com.senox.realty.constant.InspectCategory;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 巡检类型校验
 * <AUTHOR>
 * @date 2021/2/19 15:04
 */
public class InspectCategoryValidator implements ConstraintValidator<InspectCategoryChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        return InspectCategory.fromValue(value) != null;
    }
}
