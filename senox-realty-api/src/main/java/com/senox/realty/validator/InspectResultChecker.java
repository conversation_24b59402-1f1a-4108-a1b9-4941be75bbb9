package com.senox.realty.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 校验巡检结果
 * <AUTHOR>
 * @date 2021/2/19 15:01
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = InspectResultValidator.class)
public @interface InspectResultChecker {

    String message() default "Invalid inspect result.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
