package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@ApiModel("物业账单发票申请信息")
@Getter
@Setter
public class RealtyBillReceiptApplyInfoVo implements Serializable {
    private static final long serialVersionUID = -5084824489490761827L;

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    private String contractNo;

    /**
     * 物业编号
     */
    @ApiModelProperty("物业编号")
    private String realtySerialNo;

    /**
     * 物业名
     */
    @ApiModelProperty("物业名")
    private String realtyName;

    /**
     * 业主名
     */
    @ApiModelProperty("业主名")
    private String ownerName;

    /**
     * 账单年月
     */
    @ApiModelProperty("账单年月")
    private String billYearMonth;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 代租合同
     */
    @ApiModelProperty("代租合同")
    private Boolean replaceLease;


    @ApiModelProperty("费项")
    private List<FeeVo> feeList;


}
