package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/14 10:44
 */
@ApiModel("一次性收费查询参数")
@Setter
@Getter
public class OneTimeFeeBillSearchVo extends PageRequest {

    private static final long serialVersionUID = 1911424473585362098L;

    @ApiModelProperty("单号")
    private String billNo;

    @ApiModelProperty("年份")
    private Integer billYear;

    @ApiModelProperty("月份")
    private Integer billMonth;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("客户名")
    private String customer;

    @ApiModelProperty("票据号")
    private String billSerial;

    @ApiModelProperty("费项id")
    private Long feeId;

    @ApiModelProperty("费项列表")
    private List<Long> fees;

    @ApiModelProperty("部门")
    private Long departmentId;

    @ApiModelProperty("账单状态")
    private List<Integer> statusList;

    @ApiModelProperty("录入日期开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate operateDateStart;

    @ApiModelProperty("录入日期结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate operateDateEnd;

    @ApiModelProperty("收费时间开始")
    private LocalDateTime tollTimeStart;

    @ApiModelProperty("收费时间结束")
    private LocalDateTime tollTimeEnd;

    @ApiModelProperty("退费时间开始")
    private LocalDateTime refundTimeStart;

    @ApiModelProperty("退费时间结束")
    private LocalDateTime refundTimeEnd;

    @ApiModelProperty("openid")
    private String openid;

    @ApiModelProperty("微信端创建人openid")
    private String wechatCreatorOpenid;
}
