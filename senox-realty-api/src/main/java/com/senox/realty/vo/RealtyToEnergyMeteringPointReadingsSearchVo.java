package com.senox.realty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023-5-22
 */
@EqualsAndHashCode(callSuper = false)
@Getter
@Setter
public class RealtyToEnergyMeteringPointReadingsSearchVo extends EnergyMeteringPointReadingsSearchVo {
    private static final long serialVersionUID = 2402861647172620984L;

    /**
     * 物业编号
     */
    @ApiModelProperty("物业编号")
    private String realtySerialNo;

    /**
     * 物业名
     */
    @ApiModelProperty("物业名")
    private String realtyName;

    /**
     * 存在物业
     */
    @ApiModelProperty("存在物业")
    private Boolean existRealty;
}
