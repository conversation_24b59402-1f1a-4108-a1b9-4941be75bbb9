package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * <AUTHOR>
 * @date 2024/7/15 10:29
 */
@Getter
@Setter
@ToString
@ApiModel("物维日报表")
public class MaintainOrderDayReportVo implements Serializable {

    private static final long serialVersionUID = 207182334788624097L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("报表日期")
    private LocalDate reportDate;

    @ApiModelProperty("部门id")
    private Long managementDeptId;

    @ApiModelProperty("所属部门")
    private String managementDeptName;

    @ApiModelProperty("新增单数")
    private Integer addSingularNumbers;

    @ApiModelProperty("完成单数")
    private Integer completeSingularNumbers;
}
