package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 14:44
 */
@Getter
@Setter
@ToString
@ApiModel("商铺消防安全档案")
public class FirefightingFileVo extends TaskInspection {

    private static final long serialVersionUID = 3707333522294571846L;

    @Min(value = 1, message = "无效的id", groups = Update.class)
    @NotNull(message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的店铺名", groups = Add.class)
    @ApiModelProperty("店铺名")
    private String store;

    @ApiModelProperty("经营户id")
    private Long enterpriseId;

    @NotBlank(message = "无效的店铺负责人", groups = Add.class)
    @ApiModelProperty("店铺负责人")
    private String storeKeyman;

    @ApiModelProperty("店铺联系电话")
    private String storeContact;

    @ApiModelProperty("店铺地址")
    private String storeAddress;

    @ApiModelProperty("店铺建筑面积")
    private BigDecimal buildingArea;

    @ApiModelProperty("店铺出租面积")
    private BigDecimal rentingArea;

    @ApiModelProperty("店铺出租层数")
    private Integer rentingFloor;

    @NotBlank(message = "无效的店铺生产性质", groups = Add.class)
    @ApiModelProperty("店铺生产性质")
    private String productType;

    @ApiModelProperty("工商营业执照办理情况")
    private Boolean businessLicenseIssued;

    @ApiModelProperty("住人情况")
    private String accommodated;

    @ApiModelProperty("明火煮食")
    private String flameCooking;

    @ApiModelProperty("热水器")
    private String heaterEquiped;

    @ApiModelProperty("灭火器")
    private Integer fireExtinguisher;

    @ApiModelProperty("火警报警器")
    private Boolean fireAlarmsDisposed;

    @ApiModelProperty("消防卷盘")
    private Boolean fireReelsDisposed;

    @ApiModelProperty("简易喷淋")
    private Boolean simpleSprinklersDisposed;

    @ApiModelProperty("应急灯")
    private Boolean emergencyLightsDisposed;

    @ApiModelProperty("疏散楼梯")
    private String evacurationStairs;

    @ApiModelProperty("房间分隔")
    private Boolean roomSeparated;

    @ApiModelProperty("逃生出口")
    private Boolean escapeHatchDisposed;

    @ApiModelProperty("配电线路")
    private String distributionLines;

    @ApiModelProperty("其他消防环境")
    private String otherSurroundings;

    @ApiModelProperty("逃生梯")
    private String emergencyStairs;

    @ApiModelProperty("过期灭火器数量")
    private Integer expireFireExtinguisher;

    @ApiModelProperty("煤气管")
    private String gasPipe;

    @ApiModelProperty("阀门")
    private String valve;

    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("整改截止日期")
    private LocalDate rectificationDeadline;

    @ApiModelProperty("整改违规住人情况")
    private String reUnauthorizedResidence;

    @ApiModelProperty("整改灭火器情况")
    private String reFireExtinguisher;

    @ApiModelProperty("整改灭火器数量")
    private Integer reFireExtinguisherNum;

    @ApiModelProperty("整改应急灯情况")
    private String reEmergencyLights;

    @ApiModelProperty("整改消防通道情况")
    private String reThoroughfare;

    @ApiModelProperty("整改电线情况")
    private String reWire;

    @ApiModelProperty("整改热水器情况")
    private String reHeaterEquiped;

    @ApiModelProperty("整改逃生梯情况")
    private String reEmergencyStairs;

    @ApiModelProperty("整改烟感器情况")
    private String reSmokeDetector;

    @ApiModelProperty("整改煤气管情况")
    private String reGasPipe;

    @ApiModelProperty("整改阀门情况")
    private String reValve;

    @ApiModelProperty("场所说明")
    private String venueDescription;

    @ApiModelProperty("情况跟踪")
    private String followUp;

    @ApiModelProperty("复查意见")
    private String reinspectOpinion;

    @ApiModelProperty("复查人员签名")
    private String reinspector;

    @ApiModelProperty("复查日期")
    private LocalDate reinspectDate;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("物业编号")
    private List<String> realtySerials;

    @ApiModelProperty("多媒体资料")
    private List<String> reinspectMedias;

}
