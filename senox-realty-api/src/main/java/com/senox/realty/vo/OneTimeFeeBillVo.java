package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/10/14 11:02
 */
@ApiModel("一次性收费账单")
@Getter
@Setter
public class OneTimeFeeBillVo implements Serializable {

    private static final long serialVersionUID = -9181943407009727437L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("单号")
    private String billNo;

    @ApiModelProperty("年份")
    private Integer billYear;

    @ApiModelProperty("月份")
    private Integer billMonth;

    @ApiModelProperty("一次性费项id")
    @NotNull(message = "无效的收费项目", groups = Add.class)
    @Min(value = 1, message = "无效的收费项目")
    private Long feeId;

    @ApiModelProperty("一次性费项名")
    private String feeName;

    @ApiModelProperty("金额")
    @NotNull(message = "无效的收费金额", groups = Add.class)
    @DecimalMin(value = "0", message = "无效的收费金额")
    private BigDecimal amount;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty("部门名")
    private String departmentName;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("客户")
    private String customer;

    @ApiModelProperty("物业id")
    @Min(value = 0, message = "无效的物业")
    private Long realtyId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("经手人id")
    @Min(value = 0, message = "无效的经手人")
    private String operateBy;

    @ApiModelProperty("经手人")
    private String operator;

    @ApiModelProperty("录入时间")
    @NotNull(message = "无效的录入时间", groups = Add.class)
    private LocalDate operateDate;

    @ApiModelProperty("禁用")
    private Boolean disabled;

    @ApiModelProperty("openid")
    private String openid;

    @ApiModelProperty("手机")
    private Integer telephone;

    @ApiModelProperty("付款订单id")
    private Long remoteOrderId;

    @ApiModelProperty("退款订单id")
    private Long refundOrderId;

    @ApiModelProperty("微信端创建人openid")
    private String wechatCreatorOpenid;
}
