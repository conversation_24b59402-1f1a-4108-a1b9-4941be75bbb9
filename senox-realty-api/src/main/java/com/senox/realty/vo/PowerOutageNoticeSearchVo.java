package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025-05-09
 **/
@ApiModel("停电通知查询参数")
@Getter
@Setter
public class PowerOutageNoticeSearchVo extends PageRequest {
    private static final long serialVersionUID = -4257135254819439191L;

    /**
     * 复查状态
     */
    @ApiModelProperty("复查状态")
    private Boolean reviewStatus;
}
