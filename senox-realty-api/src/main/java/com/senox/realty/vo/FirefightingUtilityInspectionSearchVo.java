package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/13 16:55
 */
@Getter
@Setter
@ToString
@ApiModel("公共消防设施巡检查询")
public class FirefightingUtilityInspectionSearchVo extends PageRequest {

    private static final long serialVersionUID = -1419454756057222547L;

    @ApiModelProperty("区域id")
    private Long regionId;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("消防栓")
    private String fireHydrant;

    @ApiModelProperty("消防水带")
    private String fireHose;

    @ApiModelProperty("灭火器")
    private String fireExtinguisher;

    @ApiModelProperty("水枪")
    private String waterGun;

    @ApiModelProperty("阀门")
    private String valve;

    @ApiModelProperty("消防通道")
    private String fireExits;

    @ApiModelProperty("巡检日期起")
    private LocalDate inspectDateStart;

    @ApiModelProperty("巡检日期止")
    private LocalDate inspectDateEnd;
}
