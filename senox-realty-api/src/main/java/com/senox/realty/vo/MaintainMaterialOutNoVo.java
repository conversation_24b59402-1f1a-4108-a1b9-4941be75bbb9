package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/28 14:04
 */
@ApiModel("物料出库单号")
public class MaintainMaterialOutNoVo implements Serializable {

    private static final long serialVersionUID = -1569300934213724656L;

    @ApiModelProperty("物维物料id")
    @Min(value = 1L, message = "无效的物维物料id")
    @NotNull(message = "无效的物维物料id")
    private Long materialId;

    @ApiModelProperty("出库单号")
    @NotBlank(message = "无效的出库单号")
    private String outNo;

    @ApiModelProperty("操作员id")
    private Long operatorId;

    @ApiModelProperty("操作员")
    private String operatorName;

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public String getOutNo() {
        return outNo;
    }

    public void setOutNo(String outNo) {
        this.outNo = outNo;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }
}
