package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/10/22 15:18
 */
@ApiModel("一次性收费账单合计")
public class OneTimeFeeBillSummaryVo implements Serializable {

    private static final long serialVersionUID = 8177564222030687101L;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("退费金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
