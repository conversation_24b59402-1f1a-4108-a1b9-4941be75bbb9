package com.senox.realty.vo;

import com.senox.common.constant.device.DeviceState;
import com.senox.common.constant.device.EnergyType;
import com.senox.common.constant.device.PowerState;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 华立集抄 - 计量点
 * <AUTHOR>
 * @date 2022/10/25 17:15
 */
@ApiModel("计量点")
@Getter
@Setter
public class EnergyMeteringPointVo implements Serializable {
    private static final long serialVersionUID = 2013553869671892459L;

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的编码", groups = {Add.class, Update.class})
    @ApiModelProperty("编码")
    private String code;

    @NotBlank(message = "无效的名称", groups = Add.class)
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("能源类型")
    private EnergyType energyType;

    @ApiModelProperty("集中器编码")
    private String rtuCode;

    @ApiModelProperty("倍率")
    private BigDecimal rate;

    @ApiModelProperty("设备状态")
    private DeviceState state;

    @ApiModelProperty("能源状态")
    private PowerState powerState;
}
