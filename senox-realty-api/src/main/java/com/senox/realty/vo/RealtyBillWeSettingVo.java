package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2022-9-1
 */
@Data
@ApiModel("水电录入")
public class RealtyBillWeSettingVo implements Serializable {
    private static final long serialVersionUID = -7514046819906809895L;
    @ApiModelProperty("档位编号")
    private String realtySerial;
    @ApiModelProperty("副档编号")
    private String realtyAliasSerial;
    @ApiModelProperty("上次水读数")
    private  Integer lastWaterReadings;
    @ApiModelProperty("上次电读数")
    private  Integer lastElectricReadings;
    @ApiModelProperty("本次水读数")
    private Integer waterReadings;
    @ApiModelProperty("本次电读数")
    private Integer electricReadings;


}
