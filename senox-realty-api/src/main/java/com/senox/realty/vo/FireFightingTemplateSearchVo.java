package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/4/23 14:06
 */
@Getter
@Setter
@ToString
@ApiModel("消防告知单模板查询")
public class FireFightingTemplateSearchVo extends PageRequest {

    private static final long serialVersionUID = -8695235323846942676L;

    @ApiModelProperty("模板码")
    private String code;

    @ApiModelProperty("模板状态")
    private Integer status;

    @ApiModelProperty("最小版本号")
    private Integer versionGe;

    @ApiModelProperty("最大版本号")
    private Integer versionLe;

    @ApiModelProperty("标题")
    private String title;

}
