package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023-4-26
 */

@ApiModel("计量点物业查询参数")
@EqualsAndHashCode(callSuper = false)
@Setter
@Getter
public class RealtyToEnergyMeteringPointSearchVo extends EnergyMeterPointSearchVo {
    private static final long serialVersionUID = -7151714160331768054L;

    /**
     * 物业编号
     */
    @ApiModelProperty("物业编号")
    private String realtySerialNo;

    /**
     * 物业名
     */
    @ApiModelProperty("物业名")
    private String realtyName;

    /**
     * 存在物业
     */
    @ApiModelProperty("存在物业")
    private Boolean existRealty;
}
