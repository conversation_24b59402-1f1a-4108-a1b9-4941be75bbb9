package com.senox.realty.vo;

import com.senox.common.annotation.DebounceParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/3 10:48
 */
@ApiModel("派工单")
@Setter
@Getter
public class MaintainJobVo implements Serializable {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("派工单号")
    @DebounceParam
    private String jobNo;

    @ApiModelProperty("维修类型 1:公共土建,2:公共水电,3:公共其他,4:客户土建,5:客户水电,6:客户其他")
    private Integer maintainType;

    @ApiModelProperty("状态,0:初始化,1:已完成,2:无法处理")
    private Integer status;

    @ApiModelProperty("派工类别 0：处理，1：审核")
    private Integer dispatchType;

    @ApiModelProperty("是否需要多个完成")
    private Boolean multipleComplete;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("维修员")
    private List<MaintainJobItemVo> maintainJobItemVos;

    @ApiModelProperty("维修物料")
    private List<MaintainMaterialItemVo> materialItemVos;

    @ApiModelProperty("物维收费单")
    private List<MaintainChargeItemVo> chargeItemVos;

}
