package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/10/19 10:18
 */
@Getter
@Setter
@ApiModel("账单收费")
public class BillTollVo implements Serializable {

    private static final long serialVersionUID = -1227635854785538608L;

    @ApiModelProperty(value = "id", hidden = true)
    private Long id;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("票据号")
    private String serial;

    @ApiModelProperty("操作员")
    private Long operator;

    @ApiModelProperty("操作员姓名")
    private String operatorName;

    @ApiModelProperty(value = "收费状态", hidden = true)
    private Integer status;

}
