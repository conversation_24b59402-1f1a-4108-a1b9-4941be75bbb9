package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/26 10:42
 */
@ApiModel("能源损益编辑信息")
@Getter
@Setter
@ToString
public class EnergyProfitEditVo implements Serializable {

    private static final long serialVersionUID = 5378378158901289173L;

    @Min(value = 1, message = "无效的id")
    @NotNull(message = "无效的id")
    @ApiModelProperty("id")
    private Long id;

    @NotNull(message = "无效的实用抄表时间起")
    @ApiModelProperty("实用抄表时间起")
    private LocalDate recordedStartDate;

    @NotNull(message = "无效的实用抄表时间止")
    @ApiModelProperty("实用抄表时间止")
    private LocalDate recordedEndDate;

    @NotNull(message = "无效的结算抄表时间起")
    @ApiModelProperty("结算抄表时间起")
    private LocalDate balanceStartDate;

    @NotNull(message = "无效的结算抄表时间止")
    @ApiModelProperty("结算抄表时间止")
    private LocalDate balanceEndDate;

    @NotNull(message = "无效的实用能耗")
    @ApiModelProperty("实用能耗")
    private List<EnergyProfitItemVo> systemProfits;

    @NotNull(message = "无效的结算能耗")
    @ApiModelProperty("结算能耗")
    private List<EnergyProfitItemVo> balanceProfits;
}
