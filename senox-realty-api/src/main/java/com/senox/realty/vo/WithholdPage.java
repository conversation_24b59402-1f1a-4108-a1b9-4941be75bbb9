package com.senox.realty.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/1/21 17:03
 */
@ApiModel("托收分页")
public class WithholdPage<T> extends PageResult<T> {

    @ApiModelProperty("报盘记录数")
    private Integer offerCount;

    @ApiModelProperty("回盘记录数")
    private Integer backCount;

    @ApiModelProperty("报盘金额")
    private BigDecimal offerAmount;

    @ApiModelProperty("回盘金额")
    private BigDecimal backAmount;

    public WithholdPage() {
        this.offerCount = 0;
        this.backCount = 0;
        this.offerAmount = BigDecimal.ZERO;
        this.backAmount = BigDecimal.ZERO;
    }

    public WithholdPage(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        this.offerCount = 0;
        this.backCount = 0;
        this.offerAmount = BigDecimal.ZERO;
        this.backAmount = BigDecimal.ZERO;
    }

    public Integer getOfferCount() {
        return offerCount;
    }

    public void setOfferCount(Integer offerCount) {
        this.offerCount = offerCount;
    }

    public Integer getBackCount() {
        return backCount;
    }

    public void setBackCount(Integer backCount) {
        this.backCount = backCount;
    }

    public BigDecimal getOfferAmount() {
        return offerAmount;
    }

    public void setOfferAmount(BigDecimal offerAmount) {
        this.offerAmount = offerAmount;
    }

    public BigDecimal getBackAmount() {
        return backAmount;
    }

    public void setBackAmount(BigDecimal backAmount) {
        this.backAmount = backAmount;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> WithholdPage<T> emptyPage() {
        return new WithholdPage<>();
    }
}
