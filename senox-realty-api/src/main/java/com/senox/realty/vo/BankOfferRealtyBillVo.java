package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物业账单银行报盘对象
 * <AUTHOR>
 * @date 2022/1/14 16:28
 */
@ApiModel("物业账单银行报盘对象")
public class BankOfferRealtyBillVo implements Serializable {

    private static final long serialVersionUID = -800052936613772251L;

    @ApiModelProperty("账单年份")
    private Integer billYear;

    @ApiModelProperty("账单月份")
    private Integer billMonth;

    @ApiModelProperty("账单id")
    private Long billId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("待缴金额")
    private BigDecimal amount;

    @ApiModelProperty("银行名")
    private String bankName;

    @ApiModelProperty("银行账号")
    private String bankAccountNo;

    @ApiModelProperty("银行账户名")
    private String bankAccountName;

    @ApiModelProperty("报盘")
    private Boolean offer;

    @ApiModelProperty("回盘")
    private Boolean back;

    @ApiModelProperty("支付状态")
    private Integer status;


    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getRealtyName() {
        return realtyName;
    }

    public void setRealtyName(String realtyName) {
        this.realtyName = realtyName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public Boolean getOffer() {
        return offer;
    }

    public void setOffer(Boolean offer) {
        this.offer = offer;
    }

    public Boolean getBack() {
        return back;
    }

    public void setBack(Boolean back) {
        this.back = back;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
