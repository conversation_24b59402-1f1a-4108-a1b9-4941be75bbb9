package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/25 10:16
 */
@ApiModel("广告位统计")
@Data
public class AdvertisingStatisticsVo implements Serializable {

    private static final long serialVersionUID = -7897406212162432932L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 统计日期
     */
    @ApiModelProperty("统计日期")
    private LocalDate statisticsDate;

    /**
     * 广告位数量
     */
    @ApiModelProperty("广告位数量")
    private Integer advertisingNum;

    /**
     * 未租赁广告位数量
     */
    @ApiModelProperty("未租赁广告位数量")
    private Integer unRentAdvertisingNum;

    /**
     * 已租赁广告位数量
     */
    @ApiModelProperty("已租赁广告位数量")
    private Integer rentAdvertisingNum;

    /**
     * 已收租数量
     */
    @ApiModelProperty("已收租数量")
    private Integer rentCollectNum;

    /**
     * 已收租金额
     */
    @ApiModelProperty("已收租金额")
    private BigDecimal rentCollectAmount;

    /**
     * 未已收租数量
     */
    @ApiModelProperty("未已收租数量")
    private Integer unRentCollectNum;

    /**
     * 未收租金额
     */
    @ApiModelProperty("未收租金额")
    private BigDecimal unRentCollectAmount;
}
