package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/16 11:01
 */
@Getter
@Setter
@ToString
@ApiModel("消防巡检任务")
public class FirefightingInspectTaskItemSearchVo extends PageRequest {

    private static final long serialVersionUID = 1895719194724351888L;

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("区域id")
    private Long regionId;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("公共消防设施id")
    private Long utilityId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("公共消防设施")
    private Boolean utilityInspect;

    @ApiModelProperty("消防安全告知书")
    private Boolean noticeInspect;

    @ApiModelProperty("消防档案")
    private Boolean fileInspect;

    @ApiModelProperty("店铺巡检")
    private Boolean storeInspect;

    @ApiModelProperty("三小场所巡检")
    private Boolean smallPlacesInspect;

    @ApiModelProperty("违规住人巡检")
    private Boolean accommodateInspect;

    @ApiModelProperty("任务时间起")
    private LocalDate taskDateStart;

    @ApiModelProperty("任务时间止")
    private LocalDate taskDateEnd;

    @ApiModelProperty("任务完成")
    private Boolean taskDone;
}
