package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025-04-27
 **/
@ApiModel("物业区域字典查询参数")
@Getter
@Setter
public class RealtyRegionDictSearchVo extends PageRequest {
    private static final long serialVersionUID = -5604292358622517583L;

    /**
     * 区域
     */
    @ApiModelProperty("区域")
    private String region;

}
