package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@ApiModel("表码查询参数")
@Getter
@Setter
public class EnergyMeteringPointReadingsSearchVo extends PageRequest {
    private static final long serialVersionUID = 1416063106363881587L;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String pointCode;

    /**
     * 设备类型
     */
    @ApiModelProperty("设备类型")
    private Integer pointType;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 实时读数
     */
    @ApiModelProperty("实时读数")
    private Boolean realTime;
}
