package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/17 15:53
 */
@ApiModel("账单下发")
public class RealtyBillSendVo implements Serializable {

    private static final long serialVersionUID = 3833271142273652025L;

    @ApiModelProperty("账单年份")
    private Integer billYear;

    @ApiModelProperty("账单月份")
    private Integer billMonth;

    @ApiModelProperty("账单id列表")
    private List<Long> billIds;

    @ApiModelProperty(value = "修改人id", hidden = true)
    private Long modifierId;

    @ApiModelProperty(value = "修改人姓名", hidden = true)
    private Long modifierName;

    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public List<Long> getBillIds() {
        return billIds;
    }

    public void setBillIds(List<Long> billIds) {
        this.billIds = billIds;
    }

    public Long getModifierId() {
        return modifierId;
    }

    public void setModifierId(Long modifierId) {
        this.modifierId = modifierId;
    }

    public Long getModifierName() {
        return modifierName;
    }

    public void setModifierName(Long modifierName) {
        this.modifierName = modifierName;
    }
}
