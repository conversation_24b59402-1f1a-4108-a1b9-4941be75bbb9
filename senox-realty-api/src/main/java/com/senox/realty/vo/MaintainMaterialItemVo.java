package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/25 10:30
 */
@ApiModel("物维物料明细")
@Setter
@Getter
public class MaintainMaterialItemVo implements Serializable {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("维修物料id")
    private Long materialId;

    @ApiModelProperty("订单Id")
    private Long orderId;

    @ApiModelProperty("派工单Id")
    private Long jobId;

    @ApiModelProperty("派工单号")
    private String jobNo;

    @ApiModelProperty("物料编码")
    private Long materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料成本单价")
    private BigDecimal price;

    @ApiModelProperty("物料数量")
    private Integer quantity;

    @ApiModelProperty("物料金额")
    private BigDecimal amount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("出库单号")
    private String outNo;

    @ApiModelProperty("领料状态")
    private Integer outStatus;
}
