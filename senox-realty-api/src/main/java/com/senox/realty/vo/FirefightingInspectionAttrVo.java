package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/6 14:34
 */
@Getter
@Setter
@ToString
@ApiModel("消防巡检变量")
public class FirefightingInspectionAttrVo implements Serializable {

    private static final long serialVersionUID = 4213431106489429507L;

    @ApiModelProperty("模板编码")
    private String templateCode;

    @ApiModelProperty("模板版本")
    private Integer templateVersion;

    @ApiModelProperty("变量名")
    private String attrName;

    @ApiModelProperty("变量类型")
    private String attrType;

    @ApiModelProperty("变量值")
    private String attrValue;
}
