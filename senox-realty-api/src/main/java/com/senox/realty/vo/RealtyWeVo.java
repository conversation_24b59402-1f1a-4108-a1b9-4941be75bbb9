package com.senox.realty.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.senox.realty.constant.BillStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/24 15:17
 */
@ApiModel("水电读数")
@Getter
@Setter
public class RealtyWeVo implements Serializable {

    private static final long serialVersionUID = 7952071897725344608L;


    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("年")
    private Integer billYear;

    @ApiModelProperty("月")
    private Integer billMonth;

    @ApiModelProperty("类型")
    private Integer type;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty(hidden = true)
    private String realtyAliasSerial;

    @ApiModelProperty("物业名称")
    private String realtyName;

    @ApiModelProperty("上次水读数")
    private Long lastWaterReadings;

    @ApiModelProperty("本次水读数")
    private Long waterReadings;

    @ApiModelProperty("水耗")
    private Long waterCost;

    @ApiModelProperty("水基数")
    private Long waterBase;

    @ApiModelProperty("上次电读数")
    private Long lastElectricReadings;

    @ApiModelProperty("本次电读数")
    private Long electricReadings;

    @ApiModelProperty("电耗")
    private Long electricCost;

    @ApiModelProperty("电基数")
    private Long electricBase;

    @ApiModelProperty(value = "水电账单id", hidden = true)
    private Long weBillId;

    @ApiModelProperty(value = "水电账单状态", hidden = true)
    private Integer weBillStatus;

    @ApiModelProperty("是否人工产生")
    private Boolean manMade;

    @JsonIgnore
    public boolean isWeBillPaid() {
        return getWeBillStatus() != null && BillStatus.fromStatus(getWeBillStatus()) == BillStatus.PAID;
    }

}
