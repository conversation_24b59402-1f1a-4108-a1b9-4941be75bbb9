package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/8 15:59
 */
@ApiModel("物业账单查询参数")
@Getter
@Setter
public class RealtyBillSearchVo extends PageRequest {

    private static final long serialVersionUID = 2105508401950694971L;

    @ApiModelProperty("关键字")
    private String  keyword;

    @ApiModelProperty("账单年份")
    private Integer billYear;

    @ApiModelProperty("账单月份")
    private Integer billMonth;

    @ApiModelProperty(value = "账单日", hidden = true)
    private LocalDate billDate;

    @ApiModelProperty("账单开始年月")
    private Integer billStartYearMonth;

    @ApiModelProperty("账单截止年份月")
    private Integer billEndYearMonth;

    @ApiModelProperty("物业id")
    private Long realtyId;

    @ApiModelProperty("经营区域")
    private Long realtyRegion;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同类别")
    private Integer contractType;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("减免滞纳金")
    private Boolean penaltyIgnore;

    @ApiModelProperty("下发")
    private Boolean send;

    @ApiModelProperty("人工调整")
    private Boolean manualModified;

    @ApiModelProperty("账单状态")
    private Integer status;

    @ApiModelProperty("滞纳金开始时间")
    private LocalDate penaltyDateStart;

    @ApiModelProperty("滞纳金结束时间")
    private LocalDate penaltyDateEnd;

    @ApiModelProperty("支付时间始")
    private LocalDateTime paidTimeStart;

    @ApiModelProperty("支付时间止")
    private LocalDateTime paidTimeEnd;

    @ApiModelProperty("微信用户id")
    private String wxOpenid;

    @ApiModelProperty("是否包含合同类型")
    private Boolean containContractType;

    @ApiModelProperty("合同类型")
    private List<Integer> contractTypes;

    @ApiModelProperty("发票")
    private Boolean receipt;

    @ApiModelProperty("发票时间起")
    private LocalDateTime receiptTimeStart;

    @ApiModelProperty("发票时间止")
    private LocalDateTime receiptTimeEnd;

    @ApiModelProperty("发票备注")
    private String receiptRemark;

    @ApiModelProperty("包含客户详细信息")
    private Boolean containCustomerDetail;
}
