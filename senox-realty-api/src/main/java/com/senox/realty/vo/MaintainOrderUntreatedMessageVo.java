package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/20 9:44
 */
@ApiModel("维修主管未处理提醒消息")
@Data
public class MaintainOrderUntreatedMessageVo implements Serializable {

    private static final long serialVersionUID = -3877782987862795326L;

    /**
     * 管理员id
     */
    @ApiModelProperty("管理员id")
    private Long adminUserId;
    /**
     * 未分配数量
     */
    @ApiModelProperty("未分配数量")
    private Integer untreatedCount;
    /**
     * 通知时间
     */
    @ApiModelProperty("通知时间")
    private LocalDateTime noticeTime;
}
