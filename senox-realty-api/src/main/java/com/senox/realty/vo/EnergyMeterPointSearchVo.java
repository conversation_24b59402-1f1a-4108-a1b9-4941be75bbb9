package com.senox.realty.vo;

import com.senox.common.constant.device.DeviceState;
import com.senox.common.constant.device.PowerState;
import com.senox.common.vo.PageRequest;
import com.senox.common.constant.device.EnergyType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Getter
@Setter
public class EnergyMeterPointSearchVo extends PageRequest {
    private static final long serialVersionUID = -8902884311158508445L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("编码集")
    private List<String> codes;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("能源类型")
    private EnergyType energyType;

    @ApiModelProperty("集中器编码")
    private String rtuCode;

    @ApiModelProperty("设备状态")
    private DeviceState state;

    @ApiModelProperty("能源状态")
    private PowerState powerState;
}
