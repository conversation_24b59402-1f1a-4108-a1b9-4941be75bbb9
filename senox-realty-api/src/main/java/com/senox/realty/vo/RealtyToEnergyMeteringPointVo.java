package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 计量点物业vo
 *
 * <AUTHOR>
 * @date 2022-11-1
 */
@ApiModel("计量点物业")
@Setter
@Getter
public class RealtyToEnergyMeteringPointVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 编码
     */
    @NotBlank(message = "无效的编码", groups = {Add.class, Update.class})
    @ApiModelProperty("编码")
    private String code;

    /**
     * 名称
     */
    @NotBlank(message = "无效的名称", groups = Add.class)
    @ApiModelProperty("名称")
    private String name;

    /**
     * 能源类型
     */
    @ApiModelProperty("能源类型")
    private Integer energyType;

    /**
     * 集中器编码
     */
    @ApiModelProperty("集中器编码")
    private String rtuCode;

    /**
     * 倍率
     */
    @ApiModelProperty("倍率")
    private BigDecimal rate;

    /**
     * 设备状态
     */
    @ApiModelProperty("设备状态")
    private Integer state;

    /**
     * 能源状态
     */
    @ApiModelProperty("能源状态")
    private Integer powerState;

    /**
     * 物业名
     */
    @ApiModelProperty("物业名")
    private String realtyName;

    /**
     * 物业编号
     */
    @ApiModelProperty("物业编号")
    private String realtySerialNo;

}
