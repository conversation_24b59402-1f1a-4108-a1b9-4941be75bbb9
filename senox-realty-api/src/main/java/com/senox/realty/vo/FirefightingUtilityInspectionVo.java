package com.senox.realty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13 17:20
 */
@Getter
@Setter
@ToString
public class FirefightingUtilityInspectionVo extends TaskInspection {

    private static final long serialVersionUID = -2346999137674206697L;

    @ApiModelProperty("id")
    private Long id;

    @Min(value = 1, message = "无效的公共消防设施")
    @NotNull(message = "无效的公共消防设施")
    @ApiModelProperty("公共消防设施id")
    private Long utilityId;

    @ApiModelProperty("公共消防设施区域")
    private String utilityRegion;

    @ApiModelProperty("公共消防设施街道")
    private String utilityStreet;

    @ApiModelProperty("公共消防设施位置")
    private String utilityLocation;

    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("消防栓")
    private String fireHydrant;

    @ApiModelProperty("消防水带")
    private String fireHose;

    @ApiModelProperty("灭火器")
    private String fireExtinguisher;

    @ApiModelProperty("水枪")
    private String waterGun;

    @ApiModelProperty("阀门")
    private String valve;

    @ApiModelProperty("消防通道")
    private String fireExits;

    @ApiModelProperty("灭火器数量")
    private Integer fireExtinguisherNum;

    @ApiModelProperty("过期灭火器数量")
    private Integer expireFireExtinguisher;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("多媒体资料")
    private List<String> medias;
}
