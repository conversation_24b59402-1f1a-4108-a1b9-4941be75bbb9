package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/10/19 16:02
 */
@Getter
@Setter
@ToString
@ApiModel("一次性收费账单列表")
public class OneTimeFeeBillTradeVo implements Serializable {

    private static final long serialVersionUID = -5835405332045592530L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("单号")
    private String billNo;

    @ApiModelProperty("年份")
    private Integer billYear;

    @ApiModelProperty("月份")
    private Integer billMonth;

    @ApiModelProperty("收费项目")
    private String fee;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("客户")
    private String customer;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("支付方式")
    private Integer payWay;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("经手人")
    private String operator;

    @ApiModelProperty("经手日期")
    private LocalDate operateDate;

    @ApiModelProperty("录入人")
    private String creator;

    @ApiModelProperty("收费票据号")
    private String tollSerial;

    @ApiModelProperty("收费员")
    private String tollMan;

    @ApiModelProperty("收费时间")
    private LocalDateTime tollTime;

    @ApiModelProperty("退费金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退费票据号")
    private String refundSerial;

    @ApiModelProperty("退费人")
    private String refundMan;

    @ApiModelProperty("退费时间")
    private LocalDateTime refundTime;

    @ApiModelProperty("合计金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("openid")
    private String openid;

}
