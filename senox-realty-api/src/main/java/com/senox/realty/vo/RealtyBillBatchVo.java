package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9 13:54
 */
@Getter
@Setter
@ToString
@ApiModel("物业批量操作")
public class RealtyBillBatchVo implements Serializable {

    private static final long serialVersionUID = -4827386294054265601L;

    @ApiModelProperty("账单id")
    private Long id;

    @ApiModelProperty("账单id列表")
    private List<Long> ids;

    @ApiModelProperty("开票")
    private Boolean receipt;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private Long modifierId;

    @ApiModelProperty(value = "操作人名", hidden = true)
    private String modifierName;


}
