package com.senox.realty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/25 17:13
 */
@Getter
@Setter
public class EnergyProfitItemVo implements Serializable {

    private static final long serialVersionUID = -7926383907208101127L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("数据来源 1 实用 2 结算")
    private Integer source;

    @ApiModelProperty("能源消耗单位key")
    private String unitKey;

    @ApiModelProperty("能源消耗单位名")
    private String unitName;

    @ApiModelProperty("能源消耗量")
    private Integer cost;
}
