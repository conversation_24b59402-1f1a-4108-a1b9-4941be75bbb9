package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 11:29
 */
@ApiModel("维修单评价")
@Data
public class MaintainOrderEvaluateVo implements Serializable {
    private static final long serialVersionUID = 5703297569965305608L;

    @ApiModelProperty("订单id")
    private Long id;

    /**
     * 评价星级
     */
    @ApiModelProperty("评价星级")
    private Integer evaluateRating;
    /**
     * 评价时间
     */
    @ApiModelProperty("评价时间")
    private LocalDateTime evaluateTime;
    /**
     * 评价人openid
     */
    @ApiModelProperty("评价人openid")
    private String evaluateOpenid;
    /**
     * 评价内容
     */
    @ApiModelProperty("评价内容")
    private String evaluate;

    /**
     * 评价媒体信息
     */
    @ApiModelProperty("评价媒体信息")
    private List<String> evaluateMedias;
}
