package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/24 9:15
 */
@ApiModel("物业统计报表生成")
@Getter
@Setter
public class StatisticsGenerateVo implements Serializable {

    private static final long serialVersionUID = -8436976931961174277L;
    /**
     * 年
     */
    @ApiModelProperty("年")
    private Integer year;

    /**
     * 月
     */
    @ApiModelProperty("月")
    private Integer month;

    /**
     * 日
     */
    @ApiModelProperty("日")
    private Integer day;
}
