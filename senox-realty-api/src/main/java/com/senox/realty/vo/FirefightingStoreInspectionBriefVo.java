package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/25 16:18
 */
@Getter
@Setter
@ToString
@ApiModel("商铺消防巡检记录简要信息")
public class FirefightingStoreInspectionBriefVo implements Serializable {

    private static final long serialVersionUID = 8570864476993682186L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("检查单位")
    private String inspectAgency;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("商铺")
    private String store;

    @ApiModelProperty("商铺经营类别")
    private String storeBusinessType;

    @ApiModelProperty("商铺业主")
    private String storeOwner;

    @ApiModelProperty("商铺经营者")
    private String storeRunner;

    @ApiModelProperty("违规住人")
    private Boolean storeAccommodated;

    @ApiModelProperty("消防设施配齐")
    private Boolean firefightingFacilitiesSatisfied;

    @ApiModelProperty("砖墙保护")
    private Boolean firefightingWallSatisfied;

    @ApiModelProperty("楼梯保护")
    private Boolean firefightingStairsSatisfied;

    @ApiModelProperty("配电线路保护")
    private Boolean firefightingLinesSatisfied;

    @ApiModelProperty("火灾报警器配齐")
    private Boolean firefightingAlarmSatisfied;

    @ApiModelProperty("安全出口保护")
    private Boolean firefightingExitSatisfied;

    @ApiModelProperty("整改截止日期")
    private LocalDate rectificationDeadline;

    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡查人员")
    private String inspector;

    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("复查人员")
    private String reinspector;

    @ApiModelProperty("复查日期")
    private LocalDate reinspectDate;
}
