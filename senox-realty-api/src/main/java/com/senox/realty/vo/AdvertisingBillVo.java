package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/7/26 12:00
 */
@Getter
@Setter
@ToString
@ApiModel("广告应收账单视图")
public class AdvertisingBillVo implements Serializable {

    private static final long serialVersionUID = 7998801034589811169L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("广告位编号")
    private String spaceSerial;

    @ApiModelProperty("广告位名")
    private String spaceName;

    @ApiModelProperty("区域")
    private String spaceRegion;

    @ApiModelProperty("路段")
    private String spaceStreet;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("联系人")
    private String customerUser;

    @ApiModelProperty("联系电话")
    private String customerContact;

    @ApiModelProperty("优惠方式")
    private String presentMonths;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("应收")
    private BigDecimal amount;

    @ApiModelProperty("实收")
    private BigDecimal paidAmount;

    @ApiModelProperty("票据号")
    private String tollSerial;

    @ApiModelProperty("收费员")
    private String tollMan;

    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private Integer status;

}
