package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/6/7 14:11
 */
@Getter
@Setter
@ToString
@ApiModel("租赁合同列表视图")
public class LeaseContractListVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("物业id")
    private String realtyId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户编码")
    private String customerSerial;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("客户联系方式")
    private String customerContact;

    @ApiModelProperty("签订日期")
    private LocalDate signDate;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("合同状态")
    private Integer status;

    @ApiModelProperty("租金金额")
    private BigDecimal rentAmount;

    @ApiModelProperty("代租合同号")
    private String rentProxyContractNo;

    @ApiModelProperty("押金状态")
    private Integer depositStatus;

    @ApiModelProperty("面积")
    private BigDecimal area;

    @ApiModelProperty("管理费")
    private BigDecimal manageAmount;

    @ApiModelProperty("押金费")
    private BigDecimal depositAmount;

    @ApiModelProperty("业主名")
    private String ownerName;

    @ApiModelProperty("业主联系方式")
    private String ownerContact;

    @ApiModelProperty("创建人姓名")
    private String creatorRealName;
}
