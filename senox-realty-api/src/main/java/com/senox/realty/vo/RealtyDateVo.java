package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 物业合同日期对象
 * <AUTHOR>
 * @date 2021/4/13 17:16
 */
@ApiModel("物业合同日期查询对象")
public class RealtyDateVo implements Serializable {

    private static final long serialVersionUID = -6592596993395613492L;

    @ApiModelProperty("物业id")
    @NotNull(message = "物业id不能为空")
    @Min(value = 1, message = "无效id")
    private Long realtyId;

    @ApiModelProperty("日期")
    @NotNull(message = "日期不能为空")
    private LocalDate date;


    public Long getRealtyId() {
        return realtyId;
    }

    public void setRealtyId(Long realtyId) {
        this.realtyId = realtyId;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }
}
