package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.validator.PriceTypeChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/3/9 15:37
 */
@ApiModel("水电价格类别")
public class WaterElectricPriceTypeVo implements Serializable {

    private static final long serialVersionUID = -8128697115037602304L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("类别名称")
    @NotBlank(message = "类别名称不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("类别")
    @NotNull(message = "类别不能为空", groups = Add.class)
    @PriceTypeChecker(message = "无效的类别")
    private Integer type;

    @ApiModelProperty("价格")
    @DecimalMin(value = "0", message = "价格要求不嫩为负")
    @NotNull(message = "价格不能为空", groups = Add.class)
    private BigDecimal price;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("禁用")
    private Boolean disabled;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }
}
