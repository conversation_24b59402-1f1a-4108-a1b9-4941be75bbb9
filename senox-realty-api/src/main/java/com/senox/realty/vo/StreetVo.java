package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/23 16:52
 */
@ApiModel("街道")
public class StreetVo implements Serializable {

    private static final long serialVersionUID = -5050810561236131920L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("街道")
    @NotBlank(message = "街道不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("区域id")
    private Long regionId;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer orderNum;

    @ApiModelProperty(value = "禁用", hidden = true)
    private Boolean disabled;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "StreetVo{"
                + "id=" + id
                + ", name='" + name + '\''
                + ", regionId=" + regionId
                + ", disabled=" + disabled
                + '}';
    }
}
