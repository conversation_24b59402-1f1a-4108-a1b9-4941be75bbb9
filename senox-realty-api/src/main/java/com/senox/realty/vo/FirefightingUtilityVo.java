package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/13 10:35
 */
@Getter
@Setter
@ToString
@ApiModel("公共消防设施")
public class FirefightingUtilityVo implements Serializable {

    private static final long serialVersionUID = -594827567350392702L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("区域id")
    private Long regionId;

    @NotBlank(message = "无效的区域名")
    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("街道名")
    private String streetName;

    @NotBlank(message = "无效的位置")
    @ApiModelProperty("位置")
    private String location;

}
