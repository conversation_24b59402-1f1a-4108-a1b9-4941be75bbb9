package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 合同停用
 * <AUTHOR>
 * @date 2023/7/20 11:33
 */
@Getter
@Setter
@ToString
@ApiModel("合同停用")
public class ContractSuspendDto implements Serializable {

    private static final long serialVersionUID = 2019362761764931769L;


    @ApiModelProperty("合同号")
    @NotBlank(message = "无效的合同号")
    private String contractNo;

    @ApiModelProperty("终止日期")
    @NotNull(message = "无效的终止日期")
    private LocalDate suspendDate;

    @ApiModelProperty(value = "操作员id", hidden = true)
    private Long operatorId;

    @ApiModelProperty(value = "操作员姓名", hidden = true)
    private String operatorName;
}
