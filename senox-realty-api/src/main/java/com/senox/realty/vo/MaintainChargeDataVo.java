package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/6 16:07
 */
@Data
@ApiModel("物维收费帐单")
public class MaintainChargeDataVo implements Serializable {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("收费单号")
    private String chargeNo;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("任务id")
    private Long jobId;

    @ApiModelProperty("收费年份")
    private Integer chargeYear;

    @ApiModelProperty("收费月份")
    private Integer chargeMonth;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("维修类型 1:公共土建,2:公共水电,3:公共其他,4:客户土建,5:客户水电,6:客户其他")
    private Integer maintainType;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("派工单号")
    private String jobNo;

    @ApiModelProperty("维修单号")
    private String orderNo;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("已收金额")
    private BigDecimal paidAmount;

    @ApiModelProperty("人工费")
    private BigDecimal laborAmount;

    @ApiModelProperty("物料费")
    private BigDecimal materialAmount;

    @ApiModelProperty("收费状态：0未支付完；1已支付完")
    private Integer status;

    @ApiModelProperty("领料人")
    private String receivePerson;

    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    @ApiModelProperty("支付方式")
    private Integer payWay;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;
}
