package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 物业查询参数
 * <AUTHOR>
 * @Date 2020/12/16 11:57
 */
@Getter
@Setter
@ToString
@ApiModel("物业查询参数")
public class RealtySearchVo extends PageRequest {

    private static final long serialVersionUID = 6834779188426364261L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("编号")
    private String serialNo;

    @ApiModelProperty("编号集")
    private List<String> serialNos;

    @ApiModelProperty("区域")
    private Long regionId;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("业主id")
    private String ownerId;

    @ApiModelProperty("有租金税率")
    private Boolean haveRentTaxRate;

    @ApiModelProperty("是否担保物业")
    private Boolean isGuarantee;

    public RealtySearchVo() {
        super();
    }

    public RealtySearchVo(int pageNo, int pageSize) {
        setPageNo(pageNo);
        setPageSize(pageSize);
    }

}
