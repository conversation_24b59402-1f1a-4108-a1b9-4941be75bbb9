package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/3/31 15:42
 */
@ApiModel("维修单查询参数")
@Setter
@Getter
public class MaintainOrderSearchVo extends PageRequest {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("维修类型 1:公共土建,2:公共水电,3:公共其他,4:客户土建,5:客户水电,6:客户其他")
    private Integer maintainType;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("状态")
    private List<Integer> status;

    @ApiModelProperty("createOpenid")
    private String createOpenid;

    @ApiModelProperty("管理所属部门id")
    private List<Long> managementDeptList;

    @ApiModelProperty("时间起")
    private LocalDateTime dateStart;

    @ApiModelProperty("时间止")
    private LocalDateTime dateEnd;

    @ApiModelProperty("完成时间起")
    private LocalDateTime finishStart;

    @ApiModelProperty("完成时间止")
    private LocalDateTime finishEnd;

    @ApiModelProperty("是否维修主管")
    private Boolean isMaintainManager;

    @ApiModelProperty("支付状态")
    private Integer payStatus;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("是否评价过")
    private Boolean evaluated;

    @ApiModelProperty("评价星级")
    private List<Integer> evaluateRatingList;
}
