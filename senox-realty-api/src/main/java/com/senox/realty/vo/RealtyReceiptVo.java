package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-3-23
 */
@ApiModel("物业发票")
@Getter
@Setter
public class RealtyReceiptVo implements Serializable {

    private static final long serialVersionUID = -855538187746066564L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 抬头
     */
    @ApiModelProperty("抬头")
    private String taxHeader;

    /**
     * 抬头类型
     */
    @ApiModelProperty("抬头类型")
    private Integer headerCategory;

    /**
     * 申请金额
     */
    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    /**
     * 申请状态
     */
    @ApiModelProperty("申请状态")
    private Integer applyStatus;

    /**
     * 申请人姓名
     */
    @ApiModelProperty("申请人姓名")
    private String applyManName;

    /**
     * 申请人名称
     */
    @ApiModelProperty("申请人名称")
    private String applyUserName;

    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    private LocalDateTime applyTime;

    /**
     * 审核人姓名
     */
    @ApiModelProperty("审核人姓名")
    private String auditManName;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    /**
     * 开票时间
     */
    @ApiModelProperty("开票时间")
    private LocalDateTime receiptTime;

    /**
     * 审核备注
     */
    @ApiModelProperty("审核备注")
    private String auditRemark;

    /**
     * 下发
     */
    @ApiModelProperty("下发")
    private Boolean send;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty("纳税人识别号")
    private String taxSerial;

    /**
     * 企业注册地址
     */
    @ApiModelProperty("企业注册地址")
    private String registerAddress;

    /**
     * 企业注册电话
     */
    @ApiModelProperty("企业注册电话")
    private String registerMobile;

    /**
     * 企业开户银行
     */
    @ApiModelProperty("企业开户银行")
    private String depositBank;

    /**
     * 企业银行账号
     */
    @ApiModelProperty("企业银行账号")
    private String enterpriseBankAccount;
}
