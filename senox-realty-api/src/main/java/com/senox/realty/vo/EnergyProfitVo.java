package com.senox.realty.vo;

import com.senox.common.constant.device.EnergyType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/25 16:03
 */
@ApiModel("能源损益")
@Getter
@Setter
@ToString
public class EnergyProfitVo implements Serializable {

    private static final long serialVersionUID = 6678156907307215627L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("年份")
    private String billTime;

    @ApiModelProperty("能源类别")
    private EnergyType energyType;

    @ApiModelProperty("实用抄表时间起")
    private LocalDate recordedStartDate;

    @ApiModelProperty("实用抄表时间止")
    private LocalDate recordedEndDate;

    @ApiModelProperty("实用间隔天数")
    private Integer recordedDays;

    @ApiModelProperty("实用能源实耗")
    private Integer recordedCost;

    @ApiModelProperty("结算抄表时间起")
    private LocalDate balanceStartDate;

    @ApiModelProperty("结算抄表时间止")
    private LocalDate balanceEndDate;

    @ApiModelProperty("结算间隔天数")
    private Integer balanceDays;

    @ApiModelProperty("结算能源实耗")
    private Integer balanceCost;

    @ApiModelProperty("抄表差异天数")
    private Integer diffDays;

    @ApiModelProperty("抄表差异度数")
    private Integer diffCost;

    @ApiModelProperty("能源损益度数")
    private Integer profitsCost;

    @ApiModelProperty("能源损益率")
    private BigDecimal profitsRate;
}
