package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/8/4 11:02
 */
@Getter
@Setter
@ToString
@ApiModel("广告应付账单视图")
public class AdvertisingPayoffVo implements Serializable {

    private static final long serialVersionUID = -1847610628921422478L;

    @Min(value = 1, message = "无效的id")
    @NotNull(message = "无效的id")
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("业主id")
    private Long customerId;

    @ApiModelProperty("业主名")
    private String customerName;

    @ApiModelProperty("分成金额")
    private BigDecimal amount;

    @ApiModelProperty("账单状态")
    private Integer status;

    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

}
