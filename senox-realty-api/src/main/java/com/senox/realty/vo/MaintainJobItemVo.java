package com.senox.realty.vo;

import com.senox.common.annotation.DebounceParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 16:13
 */
@ApiModel("派工人员")
@Getter
@Setter
public class MaintainJobItemVo implements Serializable {
    private static final long serialVersionUID = -2822840270415186778L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("派工id")
    @DebounceParam
    private Long jobId;

    @ApiModelProperty("处理人id")
    private Long handlerId;

    @ApiModelProperty("处理人")
    private String handlerName;

    @ApiModelProperty("状态,0:初始化,1:已完成,2:无法处理")
    private Integer handlerStatus;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("媒体地址")
    private List<String> mediaUrls;
}
