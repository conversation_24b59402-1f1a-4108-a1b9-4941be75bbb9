package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 14:55
 */
@Getter
@Setter
@ToString
@ApiModel("消防表单模板")
public class FirefightingTemplateVo implements Serializable {

    private static final long serialVersionUID = -9068759162633269490L;

    @Min(value = 1, message = "无效的id", groups = Update.class)
    @NotNull(message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的编码", groups = Add.class)
    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("模板版本号")
    private Integer version;

    @ApiModelProperty("模板状态")
    private Integer status;

    @ApiModelProperty("生效日期")
    private LocalDate validDate;

    @ApiModelProperty("失效日期")
    private LocalDate invalidDate;

    @NotBlank(message = "无效的标题", groups = Add.class)
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("模板变量")
    private List<FirefightingTemplateVariablesVo> attrs;
}
