package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/5/13 14:51
 */
@Getter
@Setter
@ToString
@ApiModel("公共消防设施查询参数")
public class FirefightingUtilitySearchVo extends PageRequest {

    private static final long serialVersionUID = -43855548301600143L;

    @ApiModelProperty("区域")
    private Long regionId;

    @ApiModelProperty("街道")
    private Long streetId;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("位置")
    private String location;
}
