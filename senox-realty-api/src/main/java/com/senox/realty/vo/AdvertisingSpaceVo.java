package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18 13:33
 */
@Getter
@Setter
@ToString
@ApiModel("广告位")
public class AdvertisingSpaceVo implements Serializable {

    private static final long serialVersionUID = 5019673250161225320L;

    @NotNull(message = "无效的id", groups = Update.class)
    @Min(value = 1, message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("编号")
    private String serialNo;

    @ApiModelProperty("名称")
    @NotBlank(message = "无效的名称", groups = Add.class)
    private String name;

    @ApiModelProperty("位置")
    private String address;

    @ApiModelProperty("长-米")
    private BigDecimal length;

    @ApiModelProperty("宽-米")
    private BigDecimal width;

    @ApiModelProperty("面积-平方米")
    private BigDecimal size;

    @ApiModelProperty("区域街道")
    private List<AdvertisingSpacePositionVo> positions;

}
