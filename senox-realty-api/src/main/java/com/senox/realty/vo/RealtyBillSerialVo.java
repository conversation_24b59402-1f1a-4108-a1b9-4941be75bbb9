package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/28 16:23
 */
@ApiModel("物业账单票据号")
public class RealtyBillSerialVo implements Serializable {

    private static final long serialVersionUID = 8881567079323477155L;

    @NotNull(message = "无效的账单id")
    @Min(value = 1, message = "无效的账单id")
    @ApiModelProperty("账单id")
    private Long billId;

    @NotBlank(message = "无效的票据号")
    @ApiModelProperty("票据号")
    private String billSerial;

    //@NotNull(message = "无效的收费员id")
    @Min(value = 1, message = "无效的收费员id")
    @ApiModelProperty("收费员id")
    private Long tollManId;


    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public String getBillSerial() {
        return billSerial;
    }

    public void setBillSerial(String billSerial) {
        this.billSerial = billSerial;
    }

    public Long getTollManId() {
        return tollManId;
    }

    public void setTollManId(Long tollManId) {
        this.tollManId = tollManId;
    }
}
