package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/7/3 14:09
 */
@Data
@ApiModel("维修单费用统计")
public class MaintainOrderStatisticVo implements Serializable {

    private static final long serialVersionUID = -8585952706596836687L;

    @ApiModelProperty("订单id")
    private Long id;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("维修类型")
    private Integer maintainType;

    @ApiModelProperty("0 待受理; 1 正在处理； 10 已处理；50 无法处理；99 取消订单；")
    private Integer status;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("问题描述")
    private String problem;

    @ApiModelProperty("0未支付 1已支付")
    private Integer payStatus;

    @ApiModelProperty("成本价")
    private BigDecimal costPrice;

    @ApiModelProperty("人工费")
    private BigDecimal laborAmount;

    @ApiModelProperty("材料费")
    private BigDecimal incomeAmount;

    @ApiModelProperty("总费用")
    private BigDecimal totalAmount;
}
