package com.senox.realty.vo;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-10-25
 */
@Getter
@RequiredArgsConstructor
public enum RealtyWeType {
    /**
     * 月缴费账单
     */
    MONTH_PAYMENT_BILL(1),
    /**
     * 中止合同
     */
    SUSPEND_CONTRACT(2)
    ;
    private final int value;

    public static RealtyWeType fromValue(Integer value) {
        if (value != null) {
            for (RealtyWeType item : values()) {
                if (item.getValue() == value) {
                    return item;
                }
            }
        }
        return null;
    }
}
