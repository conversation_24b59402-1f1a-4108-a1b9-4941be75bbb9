package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/20 16:31
 */
@Getter
@Setter
@ToString
@ApiModel("广告合同")
public class AdvertisingContractVo implements Serializable {

    private static final long serialVersionUID = -8128247591553603364L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("广告位id")
    private Long spaceId;

    @ApiModelProperty("广告位名")
    private String spaceName;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户联系人")
    private String customerUser;

    @ApiModelProperty("客户联系方式")
    private String customerContact;

    @ApiModelProperty("赠送时长（月）")
    private Integer presentMonths;

    @ApiModelProperty("租赁时长（月）")
    private Integer rentMonths;

    @ApiModelProperty("签约日期")
    private LocalDate signDate;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("合同金额")
    private BigDecimal amount;

    @ApiModelProperty("成本")
    private BigDecimal cost;

    @ApiModelProperty("合同状态")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("已支付")
    private Boolean paid;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("广告媒体内容")
    private List<String> medias;

    @ApiModelProperty("收益分成")
    private List<AdvertisingProfitShareVo> shares;
}
