package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/5/16 16:37
 */
@Data
@ApiModel("物业担保")
public class RealtyGuaranteeVo implements Serializable {

    private static final long serialVersionUID = -46345293501535028L;

    @ApiModelProperty("物业id")
    private Long realtyId;

    @ApiModelProperty("担保开始日期")
    private LocalDate guaranteeStartDate;

    @ApiModelProperty("担保结束日期")
    private LocalDate guaranteeEndDate;

    @ApiModelProperty(value = "修改人id", hidden = true)
    private Long modifierId;

    @ApiModelProperty(value = "修改人名", hidden = true)
    private String modifierName;
}
