package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/25 10:16
 */
@Getter
@Setter
public class MaintainMaterialSearchVo extends PageRequest {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("出库单号")
    private String outNo;

    @ApiModelProperty("派工单号")
    private String jobNo;

    @ApiModelProperty("收费状态：0未支付完；1已支付完")
    private Integer status;

    @ApiModelProperty("支付时间起")
    private LocalDateTime startTime;

    @ApiModelProperty("支付时间止")
    private LocalDateTime endTime;

    @ApiModelProperty("创建时间起")
    private LocalDateTime createTimeStart;

    @ApiModelProperty("创建时间止")
    private LocalDateTime createTimeEnd;

    @ApiModelProperty("出库状态：0未出库；1已出库")
    private Integer state;

}
