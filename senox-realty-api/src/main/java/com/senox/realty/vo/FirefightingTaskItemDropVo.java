package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 13:42
 */
@Getter
@Setter
@ToString
@ApiModel("消防任务删除")
public class FirefightingTaskItemDropVo implements Serializable {

    private static final long serialVersionUID = 8353449378494182064L;

    @ApiModelProperty("任务id")
    @NotNull(message = "无效的任务")
    @Min(value = 1, message = "无效的任务")
    private Long taskId;

    @ApiModelProperty("子任务id")
    @NotNull(message = "无效的子任务")
    private List<Long> taskItems;

    @ApiModelProperty("级联删除巡检任务")
    private boolean deleteInspection;
}
