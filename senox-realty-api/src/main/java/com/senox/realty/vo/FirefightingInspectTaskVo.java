package com.senox.realty.vo;

import com.senox.realty.constant.InspectionType;
import com.senox.realty.validator.InspectCategoryChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/14 15:24
 */
@Getter
@Setter
@ToString
@ApiModel("巡检任务")
public class FirefightingInspectTaskVo implements Serializable {

    private static final long serialVersionUID = 3071367374735397404L;

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的任务名")
    @ApiModelProperty("任务名")
    private String name;

    @NotNull(message = "无效的巡检类型")
    @InspectCategoryChecker(message = "无效的巡检类型")
    @ApiModelProperty("巡检类型")
    private Integer category;

    @NotNull(message = "无效的开始日期")
    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("巡检类型")
    private List<InspectionType> types;

    @ApiModelProperty("巡检目标")
    private InspectionTargetVo inspectTarget;

    @ApiModelProperty("已完成任务数")
    private Long taskDoneCount;

    @ApiModelProperty("任务总数")
    private Long taskCount;

    @ApiModelProperty("任务实例总数")
    private Long taskEntityCount;

    @ApiModelProperty("完成任务实例总数")
    private Long taskDoneEntityCount;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}
