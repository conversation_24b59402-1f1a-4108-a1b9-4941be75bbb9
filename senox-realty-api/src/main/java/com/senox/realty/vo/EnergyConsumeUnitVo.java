package com.senox.realty.vo;

import com.senox.realty.validator.EnergyTypeChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/31 14:35
 */
@Getter
@Setter
@ToString
@ApiModel("能源消费单元")
public class EnergyConsumeUnitVo implements Serializable {

    private static final long serialVersionUID = 3683825246887517344L;

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的消费单元")
    @ApiModelProperty("消费单元")
    private String unit;

    @NotNull(message = "无效的类别")
    @EnergyTypeChecker(message = "无效的类别")
    @ApiModelProperty("能源类别")
    private Integer type;

    @NotBlank(message = "无效的消费单位名")
    @ApiModelProperty("消费单位名称")
    private String name;

    @ApiModelProperty("备注")
    private String remark;
}
