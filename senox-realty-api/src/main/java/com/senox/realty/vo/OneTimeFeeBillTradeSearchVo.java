package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/22 9:48
 */
@Getter
@Setter
@ToString
@ApiModel("一次性收费交易查询")
public class OneTimeFeeBillTradeSearchVo extends PageRequest {

    private static final long serialVersionUID = 830102447912337479L;

    @ApiModelProperty("客户名")
    private String customer;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("费项")
    private Long fee;

    @ApiModelProperty("项目")
    private Long department;

    @ApiModelProperty("经手人")
    private Long operator;

    @ApiModelProperty("录入人")
    private Long creator;

    @ApiModelProperty("收费员")
    private Long tollMan;

    @ApiModelProperty("支付方式")
    private Integer payWay;

    @ApiModelProperty("支付列表")
    private List<Integer> payWays;

    @ApiModelProperty("账单状态")
    private List<Integer> statusList;

    @ApiModelProperty("交易时间起")
    private LocalDateTime tradeTimeStart;

    @ApiModelProperty("交易时间止")
    private LocalDateTime tradeTimeEnd;

}
