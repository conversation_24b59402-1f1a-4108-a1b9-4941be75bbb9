package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.realty.validator.InspectResultChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 15:03
 */
@Getter
@Setter
@ToString
@ApiModel("商铺消防巡检记录")
public class FirefightingStoreInspectionVo extends TaskInspection {

    private static final long serialVersionUID = -609656677700886580L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("检查单位")
    private String inspectAgency;

    @ApiModelProperty("经营户id")
    private Long enterpriseId;

    @NotBlank(message = "无效的商铺", groups = Add.class)
    @ApiModelProperty("商铺")
    private String store;

    @ApiModelProperty("商铺经营类别")
    private String storeBusinessType;

    @ApiModelProperty("商铺业主")
    private String storeOwner;

    @ApiModelProperty("商铺业主联系方式")
    private String storeOwnerContact;

    @ApiModelProperty("商铺经营者")
    private String storeRunner;

    @ApiModelProperty("商铺经营者联系方式")
    private String storeRunnerContact;

    @ApiModelProperty("违规住人")
    private Boolean storeAccommodated;

    @ApiModelProperty("居住人数")
    private Integer storeAccommodatedCount;

    @ApiModelProperty("消防设施配齐")
    private Boolean firefightingFacilitiesSatisfied;

    @ApiModelProperty("砖墙保护")
    private Boolean firefightingWallSatisfied;

    @ApiModelProperty("楼梯保护")
    private Boolean firefightingStairsSatisfied;

    @ApiModelProperty("配电线路保护")
    private Boolean firefightingLinesSatisfied;

    @ApiModelProperty("火灾报警器配齐")
    private Boolean firefightingAlarmSatisfied;

    @ApiModelProperty("安全出口保护")
    private Boolean firefightingExitSatisfied;

    @InspectResultChecker(message = "无效的巡检结果")
    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡检意见")
    private String inspectOpinions;

    @ApiModelProperty("整改期限")
    private Integer rectificationTimeLimit;

    @ApiModelProperty("整改截止日期")
    private LocalDate rectificationDeadline;

    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("被巡检单位负责人签名")
    private String inspectedSignature;

    @ApiModelProperty("巡查人员")
    private String inspector;

    @ApiModelProperty("巡查服务人员")
    private String inspectorAssistant;

    @ApiModelProperty("被巡察场所负责人签名")
    private String storeKeymanSignature;

    @ApiModelProperty("复查意见")
    private String reinspectOpinions;

    @ApiModelProperty("复查人员")
    private String reinspector;

    @ApiModelProperty("复查日期")
    private LocalDate reinspectDate;

    @ApiModelProperty("物业编号")
    private List<String> realtySerials;

    @ApiModelProperty("多媒体资料")
    private List<String> medias;

    @ApiModelProperty("复查多媒体资料")
    private List<String> reinspectMedias;
}
