package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/10/21 8:46
 */
@ApiModel("一次性收费")
public class TollSerialVo implements Serializable {

    private static final long serialVersionUID = -8873715801738007720L;

    @ApiModelProperty("id")
    @NotNull(message = "无效id")
    @Min(value = 1, message = "无效id")
    private Long id;

    @ApiModelProperty("票据号")
    @NotBlank(message = "无效的票据号")
    private String serial;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private Long operatorId;

    @ApiModelProperty(value = "操作人名", hidden = true)
    private String operatorName;

    @ApiModelProperty("退费")
    private Boolean refund = false;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Boolean getRefund() {
        return refund;
    }

    public void setRefund(Boolean refund) {
        this.refund = refund;
    }

    @Override
    public String toString() {
        return "OneTimeFeeBillSerialVo{"
                + "id=" + id
                + ", serial='" + serial + '\''
                + ", operatorId=" + operatorId
                + ", operatorName=" + operatorName
                + ", refund=" + refund
                + '}';
    }
}
