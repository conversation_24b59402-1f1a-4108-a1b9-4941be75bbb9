package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/3 10:20
 */
@Getter
@Setter
@ApiModel("物业押金查询")
public class RealtyDepositSearchVo extends PageRequest {

    private static final long serialVersionUID = -6829439641518196885L;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("票据号")
    private String billSerial;

    @ApiModelProperty("账单状态")
    private List<Integer> statusList;

    @ApiModelProperty("操作日期开始")
    private LocalDateTime operateDateStart;

    @ApiModelProperty("操作日期结束")
    private LocalDateTime operateDateEnd;

    @ApiModelProperty("收款支付方式")
    private Integer tollPayWay;

    @ApiModelProperty("收费时间开始")
    private LocalDateTime tollTimeStart;

    @ApiModelProperty("收费时间结束")
    private LocalDateTime tollTimeEnd;

    @ApiModelProperty("收费员")
    private Long tollMan;

    @ApiModelProperty("退款支付方式")
    private Integer refundPayWay;

    @ApiModelProperty("退费时间开始")
    private LocalDateTime refundTimeStart;

    @ApiModelProperty("退费时间结束")
    private LocalDateTime refundTimeEnd;

    @ApiModelProperty("退费员")
    private Long refundMan;

}
