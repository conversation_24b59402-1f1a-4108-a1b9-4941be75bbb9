package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-04-28
 **/
@ApiModel("物业区域字典")
@Getter
@Setter
public class RealtyRegionDictVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 区域
     */
    @ApiModelProperty("区域")
    private String region;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String creatorName;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}
