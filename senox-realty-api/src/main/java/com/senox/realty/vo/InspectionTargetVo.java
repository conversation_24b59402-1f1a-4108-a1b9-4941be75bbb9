package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/14 15:25
 */
@Getter
@Setter
@ToString
@ApiModel("巡检对象")
public class InspectionTargetVo implements Serializable {

    private static final long serialVersionUID = -5984836850430812640L;

    @ApiModelProperty("区域id")
    private Long regionId;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("全区域")
    private Boolean wholeArea;

    @ApiModelProperty("设施id")
    private List<Long> utilityIds;

    @ApiModelProperty("经营户id")
    private List<Long> enterpriseIds;

    @ApiModelProperty("物业编码")
    private List<String> serials;

    @ApiModelProperty("经营户")
    private Boolean enterprise;

    @ApiModelProperty("重点消防")
    private Boolean emphasis;

}
