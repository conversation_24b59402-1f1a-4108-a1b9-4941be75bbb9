package com.senox.realty.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/7 8:41
 */
public class MaintainChargePageResult<T> extends PageResult<T> {

    @ApiModelProperty("应收金额合计")
    private BigDecimal receivableTotalAmount;

    @ApiModelProperty("已收金额")
    private BigDecimal paidAmount;

    @ApiModelProperty("人工费合计")
    private BigDecimal laborTotalAmount;

    @ApiModelProperty("物料费合计")
    private BigDecimal materialTotalAmount;

    public MaintainChargePageResult() {
        init();
    }

    public MaintainChargePageResult(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.receivableTotalAmount = BigDecimal.ZERO;
        this.paidAmount = BigDecimal.ZERO;
        this.laborTotalAmount = BigDecimal.ZERO;
        this.materialTotalAmount = BigDecimal.ZERO;
    }


    public BigDecimal getReceivableTotalAmount() {
        return receivableTotalAmount;
    }

    public void setReceivableTotalAmount(BigDecimal receivableTotalAmount) {
        this.receivableTotalAmount = receivableTotalAmount;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getLaborTotalAmount() {
        return laborTotalAmount;
    }

    public void setLaborTotalAmount(BigDecimal laborTotalAmount) {
        this.laborTotalAmount = laborTotalAmount;
    }

    public BigDecimal getMaterialTotalAmount() {
        return materialTotalAmount;
    }

    public void setMaterialTotalAmount(BigDecimal materialTotalAmount) {
        this.materialTotalAmount = materialTotalAmount;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> MaintainChargePageResult<T> emptyPage() {
        return new MaintainChargePageResult<>();
    }
}
