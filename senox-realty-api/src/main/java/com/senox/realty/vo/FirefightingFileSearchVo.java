package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 9:37
 */
@Getter
@Setter
@ToString
public class FirefightingFileSearchVo extends PageRequest {

    private static final long serialVersionUID = -5408110787314254784L;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("区域")
    private Long regionId;

    @ApiModelProperty("街道")
    private Long streetId;

    @ApiModelProperty("店铺")
    private String store;

    @ApiModelProperty("店铺负责人")
    private String storeKeyman;

    @ApiModelProperty("店铺联系方式")
    private String storeContact;

    @ApiModelProperty("工商营业执照办理情况")
    private Boolean businessLicenseIssued;

    @ApiModelProperty("住人情况")
    private String accommodated;

    @ApiModelProperty("明火煮食")
    private String flameCooking;

    @ApiModelProperty("热水器")
    private String heaterEquiped;

    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡检日期起")
    private LocalDate inspectDateStart;

    @ApiModelProperty("巡检日期止")
    private LocalDate inspectDateEnd;

    @ApiModelProperty("整改截止日期起")
    private LocalDate rectificationDeadlineStart;

    @ApiModelProperty("整改截止日期止")
    private LocalDate rectificationDeadlineEnd;

    @ApiModelProperty("复查日期起")
    private LocalDate reinspectDateStart;

    @ApiModelProperty("复查日期止")
    private LocalDate reinspectDateEnd;

    @ApiModelProperty("巡检结果列表")
    private List<Integer> inspectResults;


}
