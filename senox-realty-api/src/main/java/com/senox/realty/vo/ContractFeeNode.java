package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 合同费项
 * <AUTHOR>
 * @date 2021/2/23 9:09
 */
@ApiModel("合同费项")
public class ContractFeeNode implements Serializable {

    private static final long serialVersionUID = -3084311648805037393L;

    @ApiModelProperty("费项id")
    private Long feeId;

    @ApiModelProperty(value = "费项类别", hidden = true)
    private Integer category;

    @ApiModelProperty("期间")
    private Integer period;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("免租期")
    private Integer rentFreePeriod;

    @ApiModelProperty("开始时间")
    private LocalDate startDate;

    @ApiModelProperty("结束时间")
    private LocalDate endDate;

    @ApiModelProperty("费项明细")
    private List<ContractFeeNode> detail;

    public Long getFeeId() {
        return feeId;
    }

    public void setFeeId(Long feeId) {
        this.feeId = feeId;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getRentFreePeriod() {
        return rentFreePeriod;
    }

    public void setRentFreePeriod(Integer rentFreePeriod) {
        this.rentFreePeriod = rentFreePeriod;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public List<ContractFeeNode> getDetail() {
        return detail;
    }

    public void setDetail(List<ContractFeeNode> detail) {
        this.detail = detail;
    }
}
