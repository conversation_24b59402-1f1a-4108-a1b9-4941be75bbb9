package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28
 **/
@ApiModel("反诈宣传工作记录")
@Getter
@Setter
public class AntiFraudWorkRecordVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 区域名
     */
    @ApiModelProperty("区域名")
    private String regionName;

    /**
     * 街道名
     */
    @ApiModelProperty("街道名")
    private String streetName;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String fullAddress;

    /**
     * 完成人数
     */
    @ApiModelProperty("完成人数")
    private Integer completedPeople;

    /**
     * 剩余人数
     */
    @ApiModelProperty("剩余人数")
    private Integer remainingPeople;

    /**
     * 总人数
     */
    @ApiModelProperty("总人数")
    private Integer totalPeople;

    /**
     * 处理情况描述
     */
    @ApiModelProperty("处理情况描述")
    private String description;

    /**
     * 是否配合
     */
    @ApiModelProperty("是否配合")
    private Boolean cooperative;

    /**
     * 配合备注
     */
    @ApiModelProperty("配合备注")
    private String cooperativeNotes;

    /**
     * 登记人
     */
    @ApiModelProperty("登记人")
    private String registrant;

    /**
     * 登记日期
     */
    @ApiModelProperty("登记日期")
    private LocalDateTime registrationTime;

    /**
     * 店面图片集
     */
    @ApiModelProperty("店面图片集")
    private List<AntiFraudWorkRecordMediaVo> storeImages;

    /**
     * 反诈绿码图集
     */
    @ApiModelProperty("反诈绿码图集")
    private List<AntiFraudWorkRecordMediaVo> antiFraudGreenQrCodeImages;

    /**
     * 宣传图片集
     */
    @ApiModelProperty("宣传图片集")
    private List<AntiFraudWorkRecordMediaVo> promoImages;

    /**
     * openid
     */
    @ApiModelProperty("openid")
    private String openid;
}
