package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/8/10 14:06
 */
@Getter
@Setter
@ToString
@ApiModel("广告成本")
public class AdvertisingCostVo implements Serializable {

    private static final long serialVersionUID = 1732069296063976682L;

    @NotNull(message = "无效的合同")
    @Min(value = 1, message = "无效的合同")
    @ApiModelProperty("合同id")
    private Long contractId;

    @NotNull(message = "无效的成本金额")
    @DecimalMin(value = "0", message = "无效的成本金额")
    @ApiModelProperty("成本")
    private BigDecimal cost;

    @ApiModelProperty(value = "操作员id", hidden = true)
    private Long operatorId;

    @ApiModelProperty(value = "操作员姓名", hidden = true)
    private String operatorName;
}
