package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.validator.InspectResultChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/9 14:39
 */
@Getter
@Setter
@ToString
@ApiModel("违规住人消防巡检")
public class FirefightingAccommodateInspectionVo extends TaskInspection {

    private static final long serialVersionUID = -5117293896274399476L;

    @Min(value = 1, message = "无效的id", groups = Update.class)
    @NotNull(message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的地址", groups = Add.class)
    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("经营类别")
    private String businessType;

    @ApiModelProperty("经营户id")
    private Long enterpriseId;

    @ApiModelProperty("违规住人情况")
    private Boolean accommodated;

    @ApiModelProperty("居住人数")
    private Integer accommodatedCount;

    @ApiModelProperty("经营者")
    private String runner;

    @ApiModelProperty("联系方式")
    private String runnerContact;

    @ApiModelProperty("无物理分割，无甲级防火门，无安装防火门、闭门器的")
    private Boolean fireDoorsDisqualified;

    @ApiModelProperty("无逃生窗口，无逃生梯的")
    private Boolean evacurationDisqualified;

    @ApiModelProperty("无应急灯、无消防器材、有消防器材但过期没有更换的")
    private Boolean fireFacilitiesDisqualified;

    @ApiModelProperty("无滑防通道的")
    private Boolean fireExitDisqualified;

    @ApiModelProperty("电线乱接乱拉的")
    private Boolean electricLinesDisqualified;

    @ApiModelProperty("无烟感检测器")
    private Boolean smokeDetectorDisqualified;

    @ApiModelProperty("无自动喷淋的")
    private Boolean sprinklerDisqualified;

    @ApiModelProperty("巡检意见")
    private String inspectOpinions;

    @ApiModelProperty("巡检意见模板")
    private String inspectOpinionCode;

    @ApiModelProperty("巡检意见版本")
    private Integer inspectOpinionVersion;

    @InspectResultChecker(message = "无效的巡检结果")
    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡查人员")
    private String inspector;

    @NotNull(message = "无效的巡检日期", groups = Add.class)
    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("巡检单位")
    private String inspectUnit;

    @ApiModelProperty("被巡检单位负责人签名")
    private String inspectedSignature;

    @ApiModelProperty("整改截止日期")
    private LocalDate rectificationDeadline;

    @ApiModelProperty("整改意见")
    private String rectification;

    @ApiModelProperty("整改意见模板")
    private String rectificationCode;

    @ApiModelProperty("整改意见版本")
    private Integer rectificationVersion;

    @ApiModelProperty("整改监督人")
    private String rectificationSupervisor;

    @ApiModelProperty("被整改负责人签名")
    private String rectificationSignature;

    @ApiModelProperty("复查意见")
    private String reinspectOpinions;

    @ApiModelProperty("复查人")
    private String reinspector;

    @ApiModelProperty("整改日期")
    private LocalDate reinspectDate;

    @ApiModelProperty("巡检模板变量")
    private List<FirefightingInspectionAttrVo> attrs;

    @ApiModelProperty("物业")
    private List<String> realtySerials;

    @ApiModelProperty("多媒体资料")
    private List<String> medias;

    @ApiModelProperty("复查多媒体资料")
    private List<String> reinspectMedias;
}
