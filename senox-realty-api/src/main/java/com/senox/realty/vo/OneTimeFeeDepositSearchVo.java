package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/30 10:40
 */
@ApiModel("押金查询")
public class OneTimeFeeDepositSearchVo implements Serializable {

    private static final long serialVersionUID = -4143714875578505543L;

    @ApiModelProperty("押金费项")
    private Long depositFee;

    @ApiModelProperty("客户列表")
    private List<String> customers;


    public Long getDepositFee() {
        return depositFee;
    }

    public void setDepositFee(Long depositFee) {
        this.depositFee = depositFee;
    }

    public List<String> getCustomers() {
        return customers;
    }

    public void setCustomers(List<String> customers) {
        this.customers = customers;
    }
}
