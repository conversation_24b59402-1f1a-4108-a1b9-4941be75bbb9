package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024-10-31
 **/
@ApiModel("计量点刷新结果")
@Getter
@Setter
public class EnergyPointRefreshResult {

    /**
     * 新增数
     */
    @ApiModelProperty("新增数")
    private Integer newCount = 0;

    /**
     * 更新数
     */
    @ApiModelProperty("更新数")
    private Integer updateCount = 0;
}
