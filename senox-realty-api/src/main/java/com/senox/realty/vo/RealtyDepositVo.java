package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/11/3 9:28
 */
@Getter
@Setter
@ApiModel("物业押金")
public class RealtyDepositVo implements Serializable {

    private static final long serialVersionUID = 4409755437046435114L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("物业id")
    private Long realtyId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("费项id")
    private Long feeId;

    @ApiModelProperty("费项名")
    private String feeName;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("操作日期")
    private LocalDate operateDate;

    @ApiModelProperty("收费票据号")
    private String tollSerial;

    @ApiModelProperty("收费人")
    private String tollMan;

    @ApiModelProperty("收费时间")
    private LocalDateTime tollTime;

    @ApiModelProperty("收款支付方式")
    private Integer tollPayWay;

    @ApiModelProperty("支付订单id")
    private Long remoteOrderId;

    @ApiModelProperty("退费金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退费票据号")
    private String refundSerial;

    @ApiModelProperty("退费人")
    private String refundMan;

    @ApiModelProperty("退费时间")
    private LocalDateTime refundTime;

    @ApiModelProperty("退款支付方式")
    private Integer refundPayWay;

    @ApiModelProperty("")
    private Long refundOrderId;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("操作员")
    private String creatorName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("禁用")
    private Boolean disabled;


}
