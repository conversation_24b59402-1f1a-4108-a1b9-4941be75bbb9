package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/23 11:19
 */
@ApiModel("物维账单备注")
@Data
public class MaintainChargeRemarkVo implements Serializable {

    private static final long serialVersionUID = -5975367265856176250L;

    @Min(value = 1, message = "无效的账单id")
    @NotNull(message = "无效的账单id")
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private Long operatorId;

    @ApiModelProperty(value = "操作人姓名", hidden = true)
    private String operatorName;
}
