package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/13 8:54
 */
@ApiModel("物维收费单费项")
@Getter
@Setter
public class MaintainChargeItemVo implements Serializable {
    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("订单Id")
    private Long orderId;

    @ApiModelProperty("派工单Id")
    private Long jobId;

    @ApiModelProperty("派工单号")
    private String jobNo;

    @ApiModelProperty("物维收费id")
    private Long chargeId;

    @ApiModelProperty("费用类别")
    private Long feeId;

    @ApiModelProperty("费用名")
    private String feeTitle;

    @ApiModelProperty("费项编码")
    private Long feeItemCode;

    @ApiModelProperty("费项名称")
    private String feeItemName;

    @ApiModelProperty("物料单价")
    private BigDecimal price;

    @ApiModelProperty("物料数量")
    private Integer quantity;

    @ApiModelProperty("物理金额")
    private BigDecimal amount;

    @ApiModelProperty("收费状态：0未支付完；1已支付完")
    private Integer status;

}
