package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/26 17:18
 */
@Getter
@Setter
@ToString
@ApiModel("消防表单模板变量")
public class FirefightingTemplateVariablesVo implements Serializable {

    private static final long serialVersionUID = 5546040084678670390L;

    @ApiModelProperty("变量名")
    private String attrName;

    @ApiModelProperty("变量类型")
    private String attrType;
}
