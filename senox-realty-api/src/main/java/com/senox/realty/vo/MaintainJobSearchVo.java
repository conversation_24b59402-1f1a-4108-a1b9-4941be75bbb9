package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4 14:41
 */
@ApiModel("派工单查询参数")
@Setter
@Getter
public class MaintainJobSearchVo extends PageRequest {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("处理人id")
    private Long handlerId;

    @ApiModelProperty("处理人")
    private String handlerName;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("维修类型 1:公共土建,2:公共水电,3:公共其他,4:客户土建,5:客户水电,6:客户其他")
    private Integer maintainType;

    @ApiModelProperty("派工单状态")
    private List<Integer> status;

    @ApiModelProperty("处理状态")
    private List<Integer> handlerStatus;

    @ApiModelProperty("时间起")
    private LocalDateTime dateStart;

    @ApiModelProperty("时间止")
    private LocalDateTime dateEnd;

    @ApiModelProperty("支付状态")
    private Integer payStatus;

    @ApiModelProperty("是否账单详细")
    private Boolean isChargeDetail;

    @ApiModelProperty("是否维修主管")
    private Boolean isMaintainManager;

    @ApiModelProperty("管理所属部门id")
    private Long managementDeptId;

}
