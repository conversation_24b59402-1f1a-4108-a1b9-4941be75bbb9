package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/11/23 15:04
 */
@Data
@ApiModel("应付账单")
public class RealtyPayoffVo implements Serializable {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("账单年份")
    private Integer billYear;

    @ApiModelProperty("账单月份")
    private Integer billMonth;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("租金")
    private BigDecimal rentAmount;

    @ApiModelProperty("首月手续费")
    private BigDecimal firstCharge;

    @ApiModelProperty("业主手续费")
    private BigDecimal ownerCharge;

    @ApiModelProperty("应付租金")
    private BigDecimal rentToPay;

    @ApiModelProperty("账单状态")
    private Integer status;

    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    @ApiModelProperty("备注")
    private String remark;

}
