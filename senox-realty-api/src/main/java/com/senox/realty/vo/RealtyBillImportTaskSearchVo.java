package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/6/11 11:03
 */
public class RealtyBillImportTaskSearchVo extends PageRequest {

    private static final long serialVersionUID = -113085867933782006L;

    /**
     * 账单年份
     */
    private Integer billYear;
    /**
     * 账单月份
     */
    private Integer billMonth;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 档主编号
     */
    private String realtyOwnerSerial;
    /**
     * 收费状态
     */
    private String paidStatus;
    /**
     * 任务状态
     */
    private Integer taskStatus;
    /**
     * 导入开始时间
     */
    private LocalDateTime importStartTime;
    /**
     * 导入结束时间
     */
    private LocalDateTime importEndTime;
    /**
     * 任务处理开始时间
     */
    private LocalDateTime taskExecStartTime;
    /**
     * 任务处理结束时间
     */
    private LocalDateTime taskExecEndTime;


    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getRealtyOwnerSerial() {
        return realtyOwnerSerial;
    }

    public void setRealtyOwnerSerial(String realtyOwnerSerial) {
        this.realtyOwnerSerial = realtyOwnerSerial;
    }

    public String getPaidStatus() {
        return paidStatus;
    }

    public void setPaidStatus(String paidStatus) {
        this.paidStatus = paidStatus;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public LocalDateTime getImportStartTime() {
        return importStartTime;
    }

    public void setImportStartTime(LocalDateTime importStartTime) {
        this.importStartTime = importStartTime;
    }

    public LocalDateTime getImportEndTime() {
        return importEndTime;
    }

    public void setImportEndTime(LocalDateTime importEndTime) {
        this.importEndTime = importEndTime;
    }

    public LocalDateTime getTaskExecStartTime() {
        return taskExecStartTime;
    }

    public void setTaskExecStartTime(LocalDateTime taskExecStartTime) {
        this.taskExecStartTime = taskExecStartTime;
    }

    public LocalDateTime getTaskExecEndTime() {
        return taskExecEndTime;
    }

    public void setTaskExecEndTime(LocalDateTime taskExecEndTime) {
        this.taskExecEndTime = taskExecEndTime;
    }
}
