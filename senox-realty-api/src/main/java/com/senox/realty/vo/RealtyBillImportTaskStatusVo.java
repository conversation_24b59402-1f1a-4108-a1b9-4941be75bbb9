package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/6/16 10:24
 */
@ApiModel("物业导入账单任务状态")
public class RealtyBillImportTaskStatusVo implements Serializable {

    private static final long serialVersionUID = 5171389813432213489L;

    @NotNull(message = "无效得任务id")
    @Min(value = 1, message = "无效的任务id")
    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("备注")
    private String remark;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
