package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-8-12
 */
@Getter
@Setter
@ToString
@ApiModel("物业别名")
public class RealtyAliasVo implements Serializable {

    private static final long serialVersionUID = 238854355913062074L;

    @NotBlank(message = "档位编号不能为空")
    @ApiModelProperty("档位编号")
    private String serialNo;

    @NotBlank(message = "档位名称不能为空")
    @ApiModelProperty("档位名称")
    private String name;

    @ApiModelProperty("水表读数")
    private Integer waterReadings;

    @ApiModelProperty("电表读数")
    private Integer electricReadings;

}
