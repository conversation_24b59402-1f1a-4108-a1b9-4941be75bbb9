package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/1/24 10:24
 */
@ApiModel("回盘数据")
public class WithholdBackVo implements Serializable {

    private static final long serialVersionUID = -8157908185691957041L;

    @NotNull(message = "无效的年份")
    @ApiModelProperty("年份")
    private Integer billYear;

    @NotNull(message = "无效的月份")
    @ApiModelProperty("月份")
    private Integer billMonth;

    @NotBlank(message = "无效的物业编号")
    @ApiModelProperty("物业编号")
    private String realtySerial;

    @NotNull(message = "无效的金额")
    @DecimalMin(value = "0", message = "无效的金额")
    @ApiModelProperty("金额")
    private BigDecimal amount;

    @NotBlank(message = "无效的账号")
    @ApiModelProperty("账号")
    private String accountNo;

    @ApiModelProperty("户名")
    private String accountName;

    @ApiModelProperty(value = "请求ip", hidden = true)
    private String requestIp;

    @ApiModelProperty(value = "操作员", hidden = true)
    private Long tollMan;


    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getRequestIp() {
        return requestIp;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }

    public Long getTollMan() {
        return tollMan;
    }

    public void setTollMan(Long tollMan) {
        this.tollMan = tollMan;
    }
}
