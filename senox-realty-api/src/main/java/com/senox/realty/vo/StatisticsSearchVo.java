package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;


/**
 * <AUTHOR>
 * @date 2024/4/24 11:13
 */
@ApiModel("物业统计查询参数")
@Getter
@Setter
public class StatisticsSearchVo extends PageRequest {

    private static final long serialVersionUID = 8996114843048016136L;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;
}
