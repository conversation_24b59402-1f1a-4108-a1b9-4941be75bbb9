package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.pm.constant.ReceiptType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 物业账单发票管理
 *
 * <AUTHOR>
 * @date 2023-3-21
 */
@ApiModel("物业账单发票管理")
@Getter
@Setter
public class RealtyReceiptMangerVo implements Serializable {
    private static final long serialVersionUID = 4979684203015359149L;

    @ApiModelProperty("用户id")
    private String openid;

    @ApiModelProperty("申请人名称")
    private String applyUserName;

    @ApiModelProperty("发票抬头id")
    @NotNull(message = "发票抬头不能为空", groups = Add.class)
    @Min(value = 0, message = "无效的发票抬头id")
    private Long taxHeaderId;

    @ApiModelProperty("发票类型")
    @NotNull(message = "发票类型不能为空", groups = Add.class)
    private ReceiptType receiptType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("手机号")
    private Long mobile;

    @ApiModelProperty("邮箱")
    private String purchaseEmail;

    @ApiModelProperty("账单列表")
    @NotEmpty(message = "账单列表不能为空", groups = Add.class)
    private List<BillReceipt> billReceiptList;


    @Getter
    @Setter
    public static class BillReceipt{
        private Long billId;
        private List<Long> fees;
    }


}
