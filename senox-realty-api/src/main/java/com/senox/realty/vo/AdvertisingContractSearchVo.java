package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/7/24 8:53
 */
@Getter
@Setter
@ToString
public class AdvertisingContractSearchVo extends PageRequest {

    private static final long serialVersionUID = -1605837366633382153L;

    @ApiModelProperty("广告位编号")
    private String spaceSerial;

    @ApiModelProperty("广告位区域")
    private Long spaceRegionId;

    @ApiModelProperty("广告位区域")
    private Long spaceStreetId;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("签约时间起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate signDateBegin;

    @ApiModelProperty("签约时间止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate signDateEnd;

    @ApiModelProperty("合同开始时间起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDateBegin;

    @ApiModelProperty("合同开始时间止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDateEnd;

    @ApiModelProperty("合同结束时间始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDateBegin;

    @ApiModelProperty("合同结束时间止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDateEnd;

    @ApiModelProperty("合同状态")
    private Integer status;

    @ApiModelProperty("合同状态列表")
    private List<Integer> statusList;

    @ApiModelProperty("支付状态")
    private Boolean paid;

    @ApiModelProperty("返回应付")
    private Boolean withPayoff;


}
