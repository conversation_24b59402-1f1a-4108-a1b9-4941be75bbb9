package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.validator.RealtyNatureChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 物业视图对象
 *
 * <AUTHOR>
 * @Date 2020/12/21 15:10
 */
@Getter
@Setter
@ToString
@ApiModel("物业信息")
public class RealtyVo implements Serializable {

    private static final long serialVersionUID = 2662986309406089100L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty(value = "编号", notes = "长度为9，格式如AB0000001", example = "PB0000001")
    @NotBlank(message = "编号不能为空", groups = Add.class)
    @Length(min = 9, max = 9, message = "编号长度应该为9")
    private String serialNo;

    @ApiModelProperty(value = "物业名", example = "横一路001")
    @NotBlank(message = "物业名不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty(value = "物业性质", notes = "0公司；1销售；2安置；3长租")
    @RealtyNatureChecker(message = "无效的物业性质")
    private Integer nature;

    @ApiModelProperty("面积")
    @DecimalMin(value = "0", message = "无效的面积")
    private BigDecimal area;

    @ApiModelProperty("区域id")
    @Min(value = 0, message = "无效的区域id")
    private Long regionId;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道id")
    @Min(value = 0, message = "无效的街道id")
    private Long streetId;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("区域1")
    private String region1;

    @ApiModelProperty("区域2")
    private String region2;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("行业id")
    @Min(value = 0, message = "无效的行业id")
    private Long professionId;

    @ApiModelProperty("行业名称")
    private String professionName;

    @ApiModelProperty("业主id")
    @Min(value = 0, message = "无效的业主id")
    private Long ownerId;

    @ApiModelProperty("业主姓名")
    private String ownerName;

    @ApiModelProperty("业主编号")
    private String ownerSerial;

    @ApiModelProperty("水表读数")
    private Integer waterReadings;

    @ApiModelProperty("电表读数")
    private Integer electricReadings;

    @ApiModelProperty("水单价")
    private BigDecimal waterPrice;

    @ApiModelProperty("电单价")
    private BigDecimal electricPrice;

    @ApiModelProperty("水消费单元")
    private Integer waterConsumeUnit;

    @ApiModelProperty("电消费单元")
    private Integer electricConsumeUnit;

    @ApiModelProperty("租金税收编码")
    private String rentTaxCode;

    @ApiModelProperty("担保开始日期")
    private LocalDate guaranteeStartDate;

    @ApiModelProperty("担保结束日期")
    private LocalDate guaranteeEndDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("禁用")
    private Boolean disabled;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

}
