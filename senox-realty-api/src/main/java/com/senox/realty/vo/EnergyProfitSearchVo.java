package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import com.senox.common.constant.device.EnergyType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/9/25 16:28
 */
@Getter
@Setter
@ApiModel("能源损益查询")
public class EnergyProfitSearchVo extends PageRequest {

    private static final long serialVersionUID = -8084444333157028916L;

    @ApiModelProperty("账单年月起")
    private String billTimeStart;

    @ApiModelProperty("账单年月止")
    private String billTimeEnd;

    @ApiModelProperty("能源类别")
    private EnergyType energyType;
}
