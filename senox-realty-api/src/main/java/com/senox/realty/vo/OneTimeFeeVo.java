package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12 8:26
 */
@ApiModel("一次性收费项目")
@Data
public class OneTimeFeeVo implements Serializable {

    private static final long serialVersionUID = 4009239030653587771L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("项目名")
    @NotBlank(message = "费项不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("部门")
    private String departments;

    @ApiModelProperty(value = "部门id字符串", hidden = true)
    private String departmentIdStr;

    @ApiModelProperty("部门id")
    @NotNull(message = "部门不能为空", groups = Add.class)
    private List<Long> departmentIds;

    @ApiModelProperty("冷藏费项")
    private Boolean refrigeration;

    @ApiModelProperty("移动端显示(false:不显示,true:显示)")
    private Boolean mobileEditable;

    @ApiModelProperty("禁用")
    private Boolean disabled;

}
