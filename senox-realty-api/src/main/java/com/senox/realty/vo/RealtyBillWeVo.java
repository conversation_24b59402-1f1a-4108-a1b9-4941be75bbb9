package com.senox.realty.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.senox.realty.constant.BillStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022-7-20
 */
@ApiModel("物业账单水电")
public class RealtyBillWeVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("年份")
    private Integer billYear;

    @ApiModelProperty("月份")
    private Integer billMonth;

    @ApiModelProperty("账单id")
    private Long billId;

    @ApiModelProperty("主档编号")
    private String realtySerial;

    @ApiModelProperty("档位名称")
    private String realtyName;

    @ApiModelProperty("副档编号")
    private String realtyAliasSerial;

    @ApiModelProperty("副档名称")
    private String realtyAliasName;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("上次水读数")
    private Long lastWaterReadings;

    @ApiModelProperty("本次水读数")
    private Long waterReadings;

    @ApiModelProperty("水公摊")
    private Integer waterShare;

    @ApiModelProperty("用水量")
    private Long waterCost;

    @ApiModelProperty("水价格")
    private BigDecimal waterPrice;

    @ApiModelProperty("水费")
    private BigDecimal waterAmount;

    @ApiModelProperty("上次电读数")
    private Long lastElectricReadings;

    @ApiModelProperty("本次电读数")
    private Long electricReadings;

    @ApiModelProperty("电公摊")
    private Integer electricShare;

    @ApiModelProperty("用电量")
    private Long electricCost;

    @ApiModelProperty("电价格")
    private BigDecimal electricPrice;

    @ApiModelProperty("本月用电金额")
    private BigDecimal electricAmount;

    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("支付状态")
    private Integer status;

    @ApiModelProperty("上次抄表时间")
    private LocalDate lastRecordDate;

    @ApiModelProperty("本次抄表时间")
    private LocalDate recordDate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getRealtyName() {
        return realtyName;
    }

    public void setRealtyName(String realtyName) {
        this.realtyName = realtyName;
    }

    public String getRealtyAliasSerial() {
        return realtyAliasSerial;
    }

    public void setRealtyAliasSerial(String realtyAliasSerial) {
        this.realtyAliasSerial = realtyAliasSerial;
    }

    public String getRealtyAliasName() {
        return realtyAliasName;
    }

    public void setRealtyAliasName(String realtyAliasName) {
        this.realtyAliasName = realtyAliasName;
    }

    public String getCustomerSerial() {
        return customerSerial;
    }

    public void setCustomerSerial(String customerSerial) {
        this.customerSerial = customerSerial;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Long getLastWaterReadings() {
        return lastWaterReadings;
    }

    public void setLastWaterReadings(Long lastWaterReadings) {
        this.lastWaterReadings = lastWaterReadings;
    }

    public Long getWaterReadings() {
        return waterReadings;
    }

    public void setWaterReadings(Long waterReadings) {
        this.waterReadings = waterReadings;
    }

    public Integer getWaterShare() {
        return waterShare;
    }

    public void setWaterShare(Integer waterShare) {
        this.waterShare = waterShare;
    }

    public Long getWaterCost() {
        return waterCost;
    }

    public void setWaterCost(Long waterCost) {
        this.waterCost = waterCost;
    }

    public BigDecimal getWaterPrice() {
        return waterPrice;
    }

    public void setWaterPrice(BigDecimal waterPrice) {
        this.waterPrice = waterPrice;
    }

    public BigDecimal getWaterAmount() {
        return waterAmount;
    }

    public void setWaterAmount(BigDecimal waterAmount) {
        this.waterAmount = waterAmount;
    }

    public Long getLastElectricReadings() {
        return lastElectricReadings;
    }

    public void setLastElectricReadings(Long lastElectricReadings) {
        this.lastElectricReadings = lastElectricReadings;
    }

    public Long getElectricReadings() {
        return electricReadings;
    }

    public void setElectricReadings(Long electricReadings) {
        this.electricReadings = electricReadings;
    }

    public Integer getElectricShare() {
        return electricShare;
    }

    public void setElectricShare(Integer electricShare) {
        this.electricShare = electricShare;
    }

    public Long getElectricCost() {
        return electricCost;
    }

    public void setElectricCost(Long electricCost) {
        this.electricCost = electricCost;
    }

    public BigDecimal getElectricPrice() {
        return electricPrice;
    }

    public void setElectricPrice(BigDecimal electricPrice) {
        this.electricPrice = electricPrice;
    }

    public BigDecimal getElectricAmount() {
        return electricAmount;
    }

    public void setElectricAmount(BigDecimal electricAmount) {
        this.electricAmount = electricAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDate getLastRecordDate() {
        return lastRecordDate;
    }

    public void setLastRecordDate(LocalDate lastRecordDate) {
        this.lastRecordDate = lastRecordDate;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
    }

    @Override
    public String toString() {
        return "RealtyBillWeVo{"
                + "id=" + id
                + ", billYear=" + billYear
                + ", billMonth=" + billMonth
                + ", billId=" + billId
                + ", realtySerial='" + realtySerial + '\''
                + ", realtyName='" + realtyName + '\''
                + ", realtyAliasSerial='" + realtyAliasSerial + '\''
                + ", realtyAliasName='" + realtyAliasName + '\''
                + ", customerSerial='" + customerSerial + '\''
                + ", customerName='" + customerName + '\''
                + ", lastWaterReadings=" + lastWaterReadings
                + ", waterReadings=" + waterReadings
                + ", waterShare=" + waterShare
                + ", waterCost=" + waterCost
                + ", waterPrice=" + waterPrice
                + ", waterAmount=" + waterAmount
                + ", lastElectricReadings=" + lastElectricReadings
                + ", electricReadings=" + electricReadings
                + ", electricShare=" + electricShare
                + ", electricCost=" + electricCost
                + ", electricPrice=" + electricPrice
                + ", electricAmount=" + electricAmount
                + ", totalAmount=" + totalAmount
                + ", status=" + status
                + ", lastRecordDate=" + lastRecordDate
                + ", recordDate=" + recordDate
                + '}';
    }

    @JsonIgnore
    public boolean isBillPaid() {
        return BillStatus.fromStatus(getStatus()) == BillStatus.PAID;
    }
}

