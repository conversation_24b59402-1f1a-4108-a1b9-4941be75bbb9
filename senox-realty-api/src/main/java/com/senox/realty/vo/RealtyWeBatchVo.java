package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 15:27
 */
@ApiModel("水电数据批量操作")
public class RealtyWeBatchVo implements Serializable {

    private static final long serialVersionUID = 3478848310411055885L;

    @Min(value = 1, message = "无效的年份")
    @NotNull(message = "无效的年份")
    @ApiModelProperty("年")
    private Integer year;

    @Min(value = 1, message = "无效的月份")
    @Max(value = 12, message = "无效的月份")
    @NotNull(message = "无效的年份")
    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("覆盖")
    private Boolean isOverWrite = true;

    @NotNull(message = "无效的水电数据")
    @ApiModelProperty("数据")
    private List<RealtyWeVo> data;


    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Boolean getOverWrite() {
        return isOverWrite;
    }

    public void setOverWrite(Boolean overWrite) {
        isOverWrite = overWrite;
    }

    public List<RealtyWeVo> getData() {
        return data;
    }

    public void setData(List<RealtyWeVo> data) {
        this.data = data;
    }
}
