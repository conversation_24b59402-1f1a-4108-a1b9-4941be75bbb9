package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/8/4 14:48
 */
@Getter
@Setter
@ToString
@ApiModel("广告应付账单详细")
public class AdvertisingPayoffDetailVo implements Serializable {

    private static final long serialVersionUID = 8838963954088936034L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("广告位编号")
    private String spaceSerial;

    @ApiModelProperty("广告位名")
    private String spaceName;

    @ApiModelProperty("区域")
    private String spaceRegion;

    @ApiModelProperty("路段")
    private String spaceStreet;

    @ApiModelProperty("客户")
    private String customer;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("业主")
    private String realtyOwner;

    @ApiModelProperty("合同金额")
    private BigDecimal amount;

    @ApiModelProperty("分润金额")
    private BigDecimal shareAmount;

    @ApiModelProperty("账单状态")
    private Integer status;

    @ApiModelProperty("支付方式")
    private Integer payway;

    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    @ApiModelProperty("付款人")
    private String tollMan;


}
