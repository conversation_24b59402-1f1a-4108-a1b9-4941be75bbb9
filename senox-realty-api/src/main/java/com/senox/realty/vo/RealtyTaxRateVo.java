package com.senox.realty.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-31
 **/
@ApiModel("物业税率")
@Getter
@Setter
public class RealtyTaxRateVo {

    /**
     * 物业编号集
     */
    @ApiModelProperty("物业编号集")
    private List<String> realtySerials;

    /**
     * 税收编码
     */
    @ApiModelProperty("税收编码")
    private String taxCode;

}
