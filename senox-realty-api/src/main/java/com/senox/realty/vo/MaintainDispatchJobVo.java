package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 8:11
 */
@ApiModel("派工单")
@Getter
@Setter
public class MaintainDispatchJobVo implements Serializable {
    private static final long serialVersionUID = -8707201737177373822L;
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("派工维修员id单号")
    private Long jobItemId;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("派工单号")
    private String jobNo;

    @ApiModelProperty("维修类型 1:公共土建,2:公共水电,3:公共其他,4:客户土建,5:客户水电,6:客户其他")
    private Integer maintainType;

    @ApiModelProperty("处理人id")
    private Long handlerId;

    @ApiModelProperty("处理人")
    private String handlerName;

    @ApiModelProperty("派工类别 0：处理，1：审核")
    private Integer dispatchType;

    @ApiModelProperty("是否需要多个完成")
    private Boolean multipleComplete;

    @ApiModelProperty("状态,0:初始化,1:已完成,2:无法处理")
    private Integer status;

    @ApiModelProperty("状态,0:初始化,1:已完成,2:无法处理")
    private Integer handlerStatus;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("支付状态 0：未支付 1：已支付")
    private Integer payStatus;

    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("问题描述")
    private String problem;

    @ApiModelProperty("领料状态")
    private Integer outStatus;

    @ApiModelProperty("管理所属部门id")
    private Long managementDeptId;

    @ApiModelProperty("管理所属部门")
    private String managementDeptName;

    /**
     * 评价星级
     */
    @ApiModelProperty("评价星级")
    private Integer evaluateRating;
    /**
     * 评价时间
     */
    @ApiModelProperty("评价时间")
    private LocalDateTime evaluateTime;
    /**
     * 评价内容
     */
    @ApiModelProperty("评价内容")
    private String evaluate;

    @ApiModelProperty("派工单媒体地址")
    private List<String> jobMediaUrls;

    @ApiModelProperty("其他处理人")
    private List<String> handlerNameList;

    @ApiModelProperty("评价媒体地址")
    private List<String> evaluateMediaUrls;
}
