package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 物业水电查询参数
 * <AUTHOR>
 * @date 2022-10-25
 */
@ApiModel("水电查询")
@Getter
@Setter
public class RealtyWeSearchVo  extends PageRequest {

    private static final long serialVersionUID = -6419855423187941662L;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("水电类型")
    private RealtyWeType weType;

    /**
     * 是否人工产生
     */
    @ApiModelProperty("是否人工产生")
    private Boolean manMade;

    @ApiModelProperty("支付状态")
    private Boolean paid;

    @ApiModelProperty("物业列表")
    private List<String> realtySerials;

    @ApiModelProperty(value = "排除的年", hidden = true)
    private Integer excludeYear;

    @ApiModelProperty(value = "排除的月", hidden = true)
    private Integer excludeMonth;

    @ApiModelProperty(value = "排除的类型", hidden = true)
    private RealtyWeType excludeWeType;
}
