package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 11:46
 */
@Getter
@Setter
@ToString
@ApiModel("店铺消防安全责任告知单")
public class FirefightingNoticeVo extends TaskInspection {

    private static final long serialVersionUID = 7332526871690609119L;

    @Min(value = 1, groups = Update.class)
    @NotNull(message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的模板编码", groups = Add.class)
    @ApiModelProperty("模板编码")
    private String templateCode;

    @Min(value = 1, message = "无效的模板版本")
    @NotNull(message = "无效的模板版本", groups = Add.class)
    @ApiModelProperty("模板版本号")
    private Integer templateVersion;

    @ApiModelProperty("告知书标题")
    private String title;

    @ApiModelProperty("告知书内容")
    private String content;

    @ApiModelProperty("单位地址")
    private String store;

    @ApiModelProperty("经营户id")
    private Long enterpriseId;

    @ApiModelProperty("负责人")
    private String storeKeyman;

    @ApiModelProperty("负责人联系方式")
    private String storeContact;

    @ApiModelProperty("巡检员")
    private String inspector;

    @NotNull(message = "无效的通知日期", groups = Add.class)
    @ApiModelProperty("通知日期")
    private LocalDate notifyDate;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("物业编号")
    private List<String> realtySerials;
}
