package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/7/24 10:33
 */
@Getter
@Setter
@ToString
@ApiModel("广告合同列表视图")
public class AdvertisingContractListVo implements Serializable {

    private static final long serialVersionUID = 7880721025222095263L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("广告位编号")
    private String spaceSerial;

    @ApiModelProperty("广告位名")
    private String spaceName;

    @ApiModelProperty("广告区域")
    private String spaceRegion;

    @ApiModelProperty("广告街道")
    private String spaceStreet;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("客户联系人")
    private String customerUser;

    @ApiModelProperty("客户联系方式")
    private String customerContact;

    @ApiModelProperty("优惠月份")
    private Integer presentMonths;

    @ApiModelProperty("租赁月份")
    private Integer rentMonths;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("支付")
    private Boolean paid;

    @ApiModelProperty("备注")
    private String remark;

}
