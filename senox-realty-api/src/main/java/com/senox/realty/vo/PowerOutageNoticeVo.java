package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-08
 **/
@ApiModel("停电通知视图")
@Getter
@Setter
public class PowerOutageNoticeVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 区域id
     */
    @ApiModelProperty("区域id")
    private Long regionId;

    /**
     * 区域名
     */
    @ApiModelProperty("区域名")
    private String regionName;

    /**
     * 街道id
     */
    @ApiModelProperty("街道id")
    private Long streetId;

    /**
     * 街道名
     */
    @ApiModelProperty("街道名")
    private String streetName;

    /**
     * 档口地址
     */
    @ApiModelProperty("档口地址")
    private String stallAddress;

    /**
     * 停电原因
     */
    @ApiModelProperty("停电原因")
    private String poserOutageReason;

    /**
     * 整改前时间
     */
    @ApiModelProperty("整改前时间")
    private LocalDateTime preTime;

    /**
     * 整改前检查人签名
     */
    @ApiModelProperty("整改前检查人签名")
    private String preInspectorSign;

    /**
     * 整改前档口负责人签名
     */
    @ApiModelProperty("整改前档口负责人签名")
    private String preOwnerSign;

    /**
     * 整改后时间
     */
    @ApiModelProperty("整改后时间")
    private LocalDateTime postTime;

    /**
     * 整改后检查人签名
     */
    @ApiModelProperty("整改后检查人签名")
    private String postInspectorSign;

    /**
     * 整改后档口负责人签名
     */
    @ApiModelProperty("整改后档口负责人签名")
    private String postOwnerSign;

    /**
     * 复查状态
     */
    @ApiModelProperty("复查状态")
    private Boolean reviewStatus;

    /**
     * 整改前媒体资源集
     */
    @ApiModelProperty("整改前媒体资源集")
    private List<PowerOutageNoticeMediaVo> preMedias;

    /**
     * 整改后媒体资源集
     */
    @ApiModelProperty("整改后媒体资源集")
    private List<PowerOutageNoticeMediaVo> postMedias;
}
