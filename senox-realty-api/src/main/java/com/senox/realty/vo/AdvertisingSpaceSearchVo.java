package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <AUTHOR>
 * @date 2023/7/18 13:56
 */
@Getter
@Setter
@ToString
@ApiModel("广告位查询")
public class AdvertisingSpaceSearchVo extends PageRequest {

    private static final long serialVersionUID = 17906825678099975L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("编号")
    private String serialNo;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("区域id")
    private Long regionId;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("出租")
    private Boolean rent;


}
