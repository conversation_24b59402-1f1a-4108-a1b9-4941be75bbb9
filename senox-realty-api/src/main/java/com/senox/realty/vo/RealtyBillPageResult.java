package com.senox.realty.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/9/23 11:36
 */
@ApiModel("物业账单明细页")
public class RealtyBillPageResult<T> extends PageResult<T> {

    @ApiModelProperty("管理费合计")
    private BigDecimal manageAmount;

    @ApiModelProperty("租金合计")
    private BigDecimal rentAmount;

    @ApiModelProperty("水费合计")
    private BigDecimal waterAmount;

    @ApiModelProperty("电费合计")
    private BigDecimal electricAmount;

    @ApiModelProperty("应收滞纳金合计")
    private BigDecimal penaltyAmount;

    @ApiModelProperty("实收滞纳金合计")
    private BigDecimal penaltyPaidAmount;

    @ApiModelProperty("实收金额合计")
    private BigDecimal paidAmount;

    @ApiModelProperty("待收金额合计")
    private BigDecimal paidStillAmount;

    @ApiModelProperty("退费金额合计")
    private BigDecimal refundAmount;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("总计合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("免滞纳金总计合计")
    private BigDecimal totalAmountIgnorePenalty;

    public RealtyBillPageResult() {
        init();
    }

    public RealtyBillPageResult(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.manageAmount = BigDecimal.ZERO;
        this.rentAmount = BigDecimal.ZERO;
        this.waterAmount = BigDecimal.ZERO;
        this.electricAmount = BigDecimal.ZERO;
        this.penaltyAmount = BigDecimal.ZERO;
        this.penaltyPaidAmount = BigDecimal.ZERO;
        this.paidAmount = BigDecimal.ZERO;
        this.paidStillAmount = BigDecimal.ZERO;
        this.refundAmount = BigDecimal.ZERO;
        this.receivableAmount = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
        this.totalAmountIgnorePenalty = BigDecimal.ZERO;
    }


    public BigDecimal getManageAmount() {
        return manageAmount;
    }

    public void setManageAmount(BigDecimal manageAmount) {
        this.manageAmount = manageAmount;
    }

    public BigDecimal getRentAmount() {
        return rentAmount;
    }

    public void setRentAmount(BigDecimal rentAmount) {
        this.rentAmount = rentAmount;
    }

    public BigDecimal getWaterAmount() {
        return waterAmount;
    }

    public void setWaterAmount(BigDecimal waterAmount) {
        this.waterAmount = waterAmount;
    }

    public BigDecimal getElectricAmount() {
        return electricAmount;
    }

    public void setElectricAmount(BigDecimal electricAmount) {
        this.electricAmount = electricAmount;
    }

    public BigDecimal getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public BigDecimal getPenaltyPaidAmount() {
        return penaltyPaidAmount;
    }

    public void setPenaltyPaidAmount(BigDecimal penaltyPaidAmount) {
        this.penaltyPaidAmount = penaltyPaidAmount;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getPaidStillAmount() {
        return paidStillAmount;
    }

    public void setPaidStillAmount(BigDecimal paidStillAmount) {
        this.paidStillAmount = paidStillAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmountIgnorePenalty() {
        return totalAmountIgnorePenalty;
    }

    public void setTotalAmountIgnorePenalty(BigDecimal totalAmountIgnorePenalty) {
        this.totalAmountIgnorePenalty = totalAmountIgnorePenalty;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> RealtyBillPageResult<T> emptyPage() {
        return new RealtyBillPageResult<>();
    }
}
