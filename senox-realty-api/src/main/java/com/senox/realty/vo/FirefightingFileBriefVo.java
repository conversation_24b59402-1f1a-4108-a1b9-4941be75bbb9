package com.senox.realty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/22 9:46
 */
@Getter
@Setter
@ToString
public class FirefightingFileBriefVo implements Serializable {

    private static final long serialVersionUID = 5075359939290410137L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("店铺名")
    private String store;

    @ApiModelProperty("店铺负责人")
    private String storeKeyman;

    @ApiModelProperty("店铺联系电话")
    private String storeContact;

    @ApiModelProperty("店铺地址")
    private String storeAddress;

    @ApiModelProperty("工商营业执照办理情况")
    private Boolean businessLicenseIssued;

    @ApiModelProperty("住人情况")
    private String accommodated;

    @ApiModelProperty("明火煮食")
    private String flameCooking;

    @ApiModelProperty("热水器")
    private String heaterEquiped;

    @ApiModelProperty("灭火器")
    private Integer fireExtinguisher;

    @ApiModelProperty("火警报警器")
    private Boolean fireAlarmsDisposed;

    @ApiModelProperty("消防卷盘")
    private Boolean fireReelsDisposed;

    @ApiModelProperty("简易喷淋")
    private Boolean simpleSprinklersDisposed;

    @ApiModelProperty("应急灯")
    private Boolean emergencyLightsDisposed;

    @ApiModelProperty("疏散楼梯")
    private String evacurationStairs;

    @ApiModelProperty("房间分隔")
    private Boolean roomSeparated;

    @ApiModelProperty("逃生出口")
    private Boolean escapeHatchDisposed;

    @ApiModelProperty("配电线路")
    private String distributionLines;

    @ApiModelProperty("配电线路")
    private Integer inspectResult;

    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("整改截止日期")
    private LocalDate rectificationDeadline;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
