package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 合同银行代扣信息
 * <AUTHOR>
 * @date 2022/1/11 12:00
 */
@ApiModel("合同银行代扣信息")
public class ContractBankVo implements Serializable {

    private static final long serialVersionUID = -7393797250939387128L;

    @ApiModelProperty("合同id")
    private Long id;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("合同类型")
    private Integer contractType;

    @ApiModelProperty("合同开始时间")
    private LocalDate startDate;

    @ApiModelProperty("合同结束时间")
    private LocalDate endDate;

    @ApiModelProperty("物业编码")
    private String realtySerial;

    @ApiModelProperty("物业区域")
    private String realtyRegion;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账号")
    private String bankAccountNo;

    @ApiModelProperty("银行户名")
    private String bankAccountName;

    @ApiModelProperty("银行账户身份证")
    private String bankAccountIdcard;

    @ApiModelProperty("银行代扣/代付")
    private Boolean bankDelegate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public Integer getContractType() {
        return contractType;
    }

    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getRealtyRegion() {
        return realtyRegion;
    }

    public void setRealtyRegion(String realtyRegion) {
        this.realtyRegion = realtyRegion;
    }

    public String getRealtyName() {
        return realtyName;
    }

    public void setRealtyName(String realtyName) {
        this.realtyName = realtyName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankAccountIdcard() {
        return bankAccountIdcard;
    }

    public void setBankAccountIdcard(String bankAccountIdcard) {
        this.bankAccountIdcard = bankAccountIdcard;
    }

    public Boolean getBankDelegate() {
        return bankDelegate;
    }

    public void setBankDelegate(Boolean bankDelegate) {
        this.bankDelegate = bankDelegate;
    }
}
