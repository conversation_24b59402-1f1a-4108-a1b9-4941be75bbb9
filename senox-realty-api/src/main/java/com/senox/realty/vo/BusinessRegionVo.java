package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/23 14:23
 */
@ApiModel("经营区域")
public class BusinessRegionVo implements Serializable {

    private static final long serialVersionUID = -2846071948624551126L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("经营区域名")
    @NotBlank(message = "经营去域名不能为空", groups = Add.class)
    private String name;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer orderNum;

    @ApiModelProperty("号牌前缀")
    private String cycleNo;

    @ApiModelProperty(value = "禁用", hidden = true)
    private Boolean disabled;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getCycleNo() {
        return cycleNo;
    }

    public void setCycleNo(String cycleNo) {
        this.cycleNo = cycleNo;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "BusinessRegionVo{"
                + "id=" + id
                + ", name='" + name + '\''
                + ", cycleNo='" + cycleNo + '\''
                + ", disabled=" + disabled
                + '}';
    }
}
