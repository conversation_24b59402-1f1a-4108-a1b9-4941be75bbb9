package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/6 14:10
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("三小场所、出租屋消防巡检简要记录")
public class FirefightingSmallPlacesInspectionBriefVo implements Serializable {

    private static final long serialVersionUID = 5640688017662354949L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("受检单位")
    private String inspectedPlace;

    @ApiModelProperty("负责人")
    private String keyman;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("场所类型")
    private String placeType;

    @ApiModelProperty("工商注册情况")
    private String businessLicense;

    @ApiModelProperty("建筑结构安全情况")
    private String buildingSafety;

    @ApiModelProperty("生产经营范围")
    private String businessScope;

    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡检人员")
    private String inspector;

    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("整改截止日期")
    private LocalDate rectificationDeadline;

    @ApiModelProperty("复查人员")
    private String reinspector;

    @ApiModelProperty("复查日期")
    private LocalDate reinspectDate;
}
