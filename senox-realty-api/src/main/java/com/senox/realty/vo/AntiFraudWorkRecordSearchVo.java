package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-03-28
 **/
@ApiModel("反诈宣传工作记录查询参数")
@Getter
@Setter
public class AntiFraudWorkRecordSearchVo extends PageRequest {
    private static final long serialVersionUID = -2209820220765964896L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 区域名
     */
    @ApiModelProperty("区域名")
    private String regionName;

    /**
     * 街道名
     */
    @ApiModelProperty("街道名")
    private String streetName;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String fullAddress;

    /**
     * 登记人
     */
    @ApiModelProperty("登记人")
    private String registrant;

    /**
     * 登记日期起
     */
    @ApiModelProperty("登记日期起")
    private LocalDateTime registrationTimeStart;

    /**
     * 登记日期止
     */
    @ApiModelProperty("登记日期止")
    private LocalDateTime registrationTimeEnd;

    /**
     * openid
     */
    @ApiModelProperty("openid")
    private String openid;

}
