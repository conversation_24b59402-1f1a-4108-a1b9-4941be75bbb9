package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.validator.FeeCategoryChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020/12/23 16:52
 */
@ApiModel("费项")
public class FeeVo implements Serializable {

    private static final long serialVersionUID = -5050810561236131920L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("费项")
    @NotBlank(message = "费项不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("费项key")
    @NotBlank(message = "费项key不能为空", groups = Add.class)
    private String alias;

    @ApiModelProperty("费项类别")
    @NotNull(message = "费项类别", groups = Add.class)
    @FeeCategoryChecker(message = "无效的费项类别")
    private Integer category;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "禁用", hidden = true)
    private Boolean disabled;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "FeeItemVo{"
                + "id=" + id
                + ", name='" + name + '\''
                + ", alias='" + alias + '\''
                + ", category=" + category
                + ", amount=" + amount
                + ", disabled=" + disabled
                + '}';
    }
}
