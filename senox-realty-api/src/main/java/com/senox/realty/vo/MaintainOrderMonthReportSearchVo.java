package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/7/15 10:31
 */
@Getter
@Setter
@ToString
@ApiModel("物维月报表查询")
public class MaintainOrderMonthReportSearchVo extends PageRequest {

    private static final long serialVersionUID = -1665392343278705267L;

    @ApiModelProperty("部门id")
    private List<Long> managementDeptList;

    @ApiModelProperty("报表起")
    private String reportYearMonthStart;

    @ApiModelProperty("报表止")
    private String reportYearMonthEnd;
}
