package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/10 15:54
 */
@Getter
@Setter
@ToString
@ApiModel("广告收益")
public class AdvertisingIncomeVo implements Serializable {

    private static final long serialVersionUID = -6426858956420645065L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("广告位编号")
    private String spaceSerial;

    @ApiModelProperty("广告位置")
    private String spaceName;

    @ApiModelProperty("广告区域")
    private String spaceRegion;

    @ApiModelProperty("广告街道")
    private String spaceStreet;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("开始时间")
    private LocalDate startDate;

    @ApiModelProperty("结束时间")
    private LocalDate endDate;

    @ApiModelProperty("收入")
    private BigDecimal amount;

    @ApiModelProperty("成本")
    private BigDecimal cost;

    @ApiModelProperty("分成金额")
    private BigDecimal shareAmount;

    @ApiModelProperty("已支付分成金额")
    private BigDecimal paidShareAmount;

    @ApiModelProperty("分成")
    private List<AdvertisingPayoffVo> shares;
}
