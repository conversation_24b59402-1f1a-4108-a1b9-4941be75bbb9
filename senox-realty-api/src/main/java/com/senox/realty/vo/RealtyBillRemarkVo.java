package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/8/23 15:05
 */
@ApiModel("物业账单备注")
public class RealtyBillRemarkVo implements Serializable {

    private static final long serialVersionUID = -8579428972067554799L;

    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    @Min(value = 1, message = "无效的id")
    private Long id;

    @ApiModelProperty("备注")
    private String remark;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
