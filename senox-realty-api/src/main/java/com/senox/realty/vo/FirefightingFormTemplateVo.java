package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/28 15:10
 */
@Getter
@Setter
@ToString
@ApiModel("表单模板配置")
public class FirefightingFormTemplateVo implements Serializable {

    private static final long serialVersionUID = -7963694432498704236L;

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的表单")
    @ApiModelProperty("表单")
    private String form;

    @ApiModelProperty("属性")
    private String formAttr;

    @NotBlank(message = "无效的模板")
    @ApiModelProperty("模板")
    private String templateCode;

    @NotNull(message = "无效的模板版本")
    @ApiModelProperty("模板")
    private Integer templateVersion;
}
