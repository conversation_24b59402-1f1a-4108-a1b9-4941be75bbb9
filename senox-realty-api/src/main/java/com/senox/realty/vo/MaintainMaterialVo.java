package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 14:06
 */
@ApiModel("维修物料")
@Getter
@Setter
public class MaintainMaterialVo implements Serializable {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("订单id")
    private Long orderId;
    @ApiModelProperty("派单id")
    private Long jobId;
    @ApiModelProperty("出库单号")
    private String outNo;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("物维物料明细")
    private List<MaintainMaterialItemVo> itemVos;

}
