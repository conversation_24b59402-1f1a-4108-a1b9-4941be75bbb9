package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.validator.InspectResultChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/6 14:10
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("三小场所、出租屋消防巡检记录")
public class FirefightingSmallPlacesInspectionVo extends TaskInspection {

    private static final long serialVersionUID = -3160615749191393759L;

    @Min(value = 1, message = "无效的id", groups = Update.class)
    @NotNull(message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的受检单位", groups = Add.class)
    @ApiModelProperty("受检单位")
    private String inspectedPlace;

    @ApiModelProperty("经营户id")
    private Long enterpriseId;

    @NotBlank(message = "无效的负责人", groups = Add.class)
    @ApiModelProperty("负责人")
    private String keyman;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("场所类型")
    private String placeType;

    @ApiModelProperty("工商注册情况")
    private String businessLicense;

    @ApiModelProperty("建筑结构安全情况")
    private String buildingSafety;

    @ApiModelProperty("生产经营范围")
    private String businessScope;

    @ApiModelProperty("建筑物数量")
    private Integer buildingCount;

    @ApiModelProperty("最高建筑层数")
    private Integer buildingFloors;

    @ApiModelProperty("建筑占地面积")
    private BigDecimal buildingArea;

    @ApiModelProperty("建筑结构")
    private String buildingStructure;

    @ApiModelProperty("建筑结构其他")
    private String buildingStructureOther;

    @ApiModelProperty("建筑使用情况")
    private String buildingUsage;

    @ApiModelProperty("经营者使用面积")
    private BigDecimal runnerUsageArea;

    @ApiModelProperty("员工数量")
    private Integer runnerStaffs;

    @ApiModelProperty("疏散出口楼梯")
    private String evacurationStaris;

    @ApiModelProperty("疏散出口直通天面")
    private String evacurationRoofStraight;

    @ApiModelProperty("疏散安全出口")
    private String evacurationExit;

    @ApiModelProperty("疏散逃生窗口")
    private String evacurationEscapeWindow;

    @ApiModelProperty("灭火器")
    private Integer fireExtinguisher;

    @ApiModelProperty("应急照明")
    private Integer emergencyLights;

    @ApiModelProperty("消防卷盘")
    private Boolean fireReelsDisposed;

    @ApiModelProperty("紧急逃生措施")
    private Boolean escapeFacilitiesDisposed;

    @ApiModelProperty("简易喷淋")
    private Boolean simpleSprinklersDisposed;

    @ApiModelProperty("火警报警器")
    private Boolean fireAlarmsDisposed;

    @ApiModelProperty("过期灭火器数量")
    private Integer expireFireExtinguisher;

    @ApiModelProperty("违规住人")
    private String unauthorizedResidence;

    @ApiModelProperty("整改灭火器情况")
    private String reFireExtinguisher;

    @ApiModelProperty("整改灭火器数量")
    private Integer reFireExtinguisherNum;

    @ApiModelProperty("整改应急灯情况")
    private String reEmergencyLights;

    @ApiModelProperty("整改消防报警器情况")
    private String reFireAlarmsDisposed;

    @ApiModelProperty("整改电线情况")
    private String reWire;

    @ApiModelProperty("整改违规住人情况")
    private String reUnauthorizedResidence;

    @InspectResultChecker(message = "无效的巡检结果")
    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡检人员")
    private String inspector;

    @NotNull(message = "无效的巡检日期", groups = Add.class)
    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("被检查场所负责人签名")
    private String keymanInspectedSignature;

    @ApiModelProperty("整改截止日期")
    private LocalDate rectificationDeadline;

    @ApiModelProperty("复查人员")
    private String reinspector;

    @ApiModelProperty("复查日期")
    private LocalDate reinspectDate;

    @ApiModelProperty("被检查场所复查负责人签名")
    private String keymanReinspectedSignature;

    @ApiModelProperty("1层用途")
    private String floor1Usage;

    @ApiModelProperty("2层用途")
    private String floor2Usage;

    @ApiModelProperty("3层用途")
    private String floor3Usage;

    @ApiModelProperty("4层用途")
    private String floor4Usage;

    @ApiModelProperty("5层用途")
    private String floor5Usage;

    @ApiModelProperty("6层用途")
    private String floor6Usage;

    @ApiModelProperty("其他层用途")
    private String floorOthersUsage;

    @ApiModelProperty("经营者1层用途")
    private String floor1RunnerUsage;

    @ApiModelProperty("经营者2层用途")
    private String floor2RunnerUsage;

    @ApiModelProperty("经营者3层用途")
    private String floor3RunnerUsage;

    @ApiModelProperty("经营者4层用途")
    private String floor4RunnerUsage;

    @ApiModelProperty("经营者5层用途")
    private String floor5RunnerUsage;

    @ApiModelProperty("经营者6层用途")
    private String floor6RunnerUsage;

    @ApiModelProperty("经营者其他层用途")
    private String floorOthersRunnerUsage;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("检查意见")
    private String inspectOpinion;

    @ApiModelProperty("检查意见模板")
    private String inspectOpinionCode;

    @ApiModelProperty("检查意见模板版本")
    private Integer inspectOpinionVersion;

    @ApiModelProperty("复查意见")
    private String reinspectOpinion;

    @ApiModelProperty("复查意见模板")
    private String reinspectOpinionCode;

    @ApiModelProperty("检查意见模板版本")
    private Integer reinspectOpinionVersion;

    @ApiModelProperty("小商铺整改意见")
    private String storeRectification;

    @ApiModelProperty("小商铺整改意见模板")
    private String storeRectificationCode;

    @ApiModelProperty("小商铺整改意见模板版本")
    private Integer storeRectificationVersion;

    @ApiModelProperty("小作坊整改意见")
    private String workshopRectification;

    @ApiModelProperty("小作坊整改意见模板")
    private String workshopRectificationCode;

    @ApiModelProperty("小作坊整改意见模板版本")
    private String workshopRectificationVersion;

    @ApiModelProperty("出租屋整改意见")
    private String rentalRectification;

    @ApiModelProperty("出租屋整改意见模板")
    private String rentalRectificationCode;

    @ApiModelProperty("出租屋整改意见模板版本")
    private Integer rentalRectificationVersion;

    @ApiModelProperty("巡检模板变量")
    private List<FirefightingInspectionAttrVo> attrs;

    @ApiModelProperty("物业编号")
    private List<String> realtySerials;

    @ApiModelProperty("多媒体资料")
    private List<String> medias;
}
