package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2024/7/15 10:29
 */
@Getter
@Setter
@ToString
@ApiModel("物维月报表")
public class MaintainOrderMonthReportVo implements Serializable {

    private static final long serialVersionUID = 207182334788624097L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("月报年月")
    private String reportYearMonth;

    @ApiModelProperty("部门id")
    private Long managementDeptId;

    @ApiModelProperty("所属部门")
    private String managementDeptName;

    @ApiModelProperty("新增单数")
    private Integer addSingularNumbers;

    @ApiModelProperty("完成单数")
    private Integer completeSingularNumbers;
}
