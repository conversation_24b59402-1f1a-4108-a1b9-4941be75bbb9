package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/26 14:43
 */
@Getter
@Setter
@ApiModel("物业水电读数")
public class RealtyReadingsVo implements Serializable {

    private static final long serialVersionUID = 7575748014068381078L;

    @NotBlank(message = "无效的物业编号")
    @ApiModelProperty("物业编号")
    private String realtySerial;

    @NotNull(message = "无效的水表读数")
    @Min(value = 0, message = "无效的水表读数")
    @ApiModelProperty("水表读数")
    private Long waterReadings;

    @NotNull(message = "无效的电表读数")
    @Min(value = 0, message = "无效的电表读数")
    @ApiModelProperty("电表读数")
    private Long electricReadings;

    @ApiModelProperty(value = "是否副档")
    private Boolean alias;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private Long modifierId;

    @ApiModelProperty(value = "操作人姓名", hidden = true)
    private String modifierName;

}
