package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/25 14:50
 */
@Getter
@Setter
@ToString
@ApiModel("广告利润分成")
public class AdvertisingProfitShareVo implements Serializable {

    private static final long serialVersionUID = -4299724498242147215L;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("金额")
    private BigDecimal shareAmount;

    @ApiModelProperty("备注")
    private String remark;
}
