package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/6 16:16
 */
@Getter
@Setter
@ToString
@ApiModel("三小场所、出租屋消防巡检记录查询")
public class FirefightingSmallPlacesInspectionSearchVo extends PageRequest {

    private static final long serialVersionUID = 483745715644078391L;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("区域")
    private Long regionId;

    @ApiModelProperty("街道")
    private Long streetId;

    @ApiModelProperty("受检单位")
    private String inspectedPlace;

    @ApiModelProperty("负责人")
    private String keyman;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("场所类型")
    private String placeType;

    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡检日期起")
    private LocalDate inspectDateStart;

    @ApiModelProperty("巡检日期止")
    private LocalDate inspectDateEnd;

    @ApiModelProperty("整改截止日期起")
    private LocalDate rectificationDeadlineStart;

    @ApiModelProperty("整改截止日期止")
    private LocalDate rectificationDeadlineEnd;

    @ApiModelProperty("复查日期起")
    private LocalDate reinspectDateStart;

    @ApiModelProperty("复查日期止")
    private LocalDate reinspectDateEnd;

    @ApiModelProperty("巡检结果列表")
    private List<Integer> inspectResults;
}
