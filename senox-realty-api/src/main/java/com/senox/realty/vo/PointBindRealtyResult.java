package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024-10-25
 **/
@ApiModel("计量点绑定物业结果")
@Setter
@Getter
public class PointBindRealtyResult {

    /**
     * 总数
     */
    @ApiModelProperty("总数")
    private Integer totalCount = 0;

    /**
     * 成功数
     */
    @ApiModelProperty("成功数")
    private Integer successCount = 0;

    /**
     * 失败数
     */
    @ApiModelProperty("失败数")
    private Integer failCount = 0;
}
