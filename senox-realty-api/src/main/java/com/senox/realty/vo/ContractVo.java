package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.validator.ContractStatusChecker;
import com.senox.realty.validator.ContractTypeChecker;
import com.senox.realty.validator.CostTypeChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/19 14:04
 */
@ApiModel("合同信息")
@Setter
@Getter
public class ContractVo implements Serializable {

    private static final long serialVersionUID = 1090552555069973498L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("手工合同编号")
    private String orderNo;

    @ApiModelProperty("合同类型")
    @ContractTypeChecker(message = "无效的合同类型")
    @NotNull(message = "合同类型不能为空", groups = Add.class)
    private Integer type;

    @ApiModelProperty("合同类别")
    private Integer category;

    @ApiModelProperty("物业id")
    @Min(value = 0, message = "无效的物业")
    @NotNull(message = "物业不能为空", groups = Add.class)
    private Long realtyId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("客户id")
    @Min(value = 0, message = "无效的客户")
    @NotNull(message = "客户不能为空", groups = Add.class)
    private Long customerId;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("客户联系方式")
    private String customerContact;

    @ApiModelProperty("签约日期")
    @NotNull(message = "签约日期不能为空", groups = Add.class)
    private LocalDate signDate;

    @ApiModelProperty("合同开始日期")
    @NotNull(message = "合同开始日期不能为空", groups = Add.class)
    private LocalDate startDate;

    @ApiModelProperty("合同结束日期")
    @NotNull(message = "合同结束日期不能为空", groups = Add.class)
    private LocalDate endDate;

    @ApiModelProperty("临租")
    private Boolean temporaryRent;

    @ApiModelProperty("转名")
    private Boolean renamed;

    @ApiModelProperty("合同状态")
    @ContractStatusChecker(message = "无效的合同状态")
    @NotNull(message = "合同状态不能为空", groups = Add.class)
    private Integer status;

    @ApiModelProperty("交款方式")
    @CostTypeChecker(message = "无效的交款方式")
    private Integer costType;

    @ApiModelProperty("银行名")
    private String bankName;

    @ApiModelProperty("银行账号")
    private String bankAccountNo;

    @ApiModelProperty("银行账户名")
    private String bankAccountName;

    @ApiModelProperty("银行身份证")
    private String bankAccountIdcard;

    @ApiModelProperty("首月手续费率")
    @DecimalMin(value = "0", message = "无效的首月手续费率")
    private BigDecimal firstRate;

    @ApiModelProperty("每月手续费率")
    @DecimalMin(value = "0", message = "无效的每月手续费率")
    private BigDecimal monthlyRate;

    @ApiModelProperty("每月手续费绝对值")
    @DecimalMin(value = "0", message = "无效的每月手续费绝对值")
    private BigDecimal monthlyFeeAbs;

    @ApiModelProperty("水价类别")
    private Integer waterPriceType;

    @ApiModelProperty("电价类别")
    private Integer electricPriceType;

    @ApiModelProperty("滞纳金开始日期")
    private Integer penaltyStartDate;

    @ApiModelProperty("滞纳金比例")
    private BigDecimal penaltyRate;

    @ApiModelProperty("归档url")
    private String archiveUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("费项")
    private List<ContractFeeNode> fees;

    @ApiModelProperty("禁用")
    private Boolean disabled;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

    @ApiModelProperty(value = "水价", hidden = true)
    private WaterElectricPriceTypeVo waterPrice;

    @ApiModelProperty(value = "电价" ,hidden = true)
    private WaterElectricPriceTypeVo electricPrice;

    @ApiModelProperty("营业执照名称")
    private String businessLicenseName;

    @ApiModelProperty("税号")
    private String taxSerial;
}
