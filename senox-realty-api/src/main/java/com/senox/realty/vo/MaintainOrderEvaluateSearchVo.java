package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024/11/5 11:37
 */
@ApiModel("维修单评价查询参数")
@Getter
@Setter
public class MaintainOrderEvaluateSearchVo extends PageRequest {

    private static final long serialVersionUID = 5341497718394550947L;

    /**
     * 评价人openid
     */
    @ApiModelProperty("评价人openid")
    private String evaluateOpenid;

    @ApiModelProperty("评价时间起")
    private LocalDateTime evaluateTimeStart;

    @ApiModelProperty("评价时间止")
    private LocalDateTime evaluateTimeEnd;
}
