package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12 10:13
 */
@ApiModel("一次性收费查询参数")
public class OneTimeFeeSearchVo extends PageRequest {

    private static final long serialVersionUID = 2306161524028855718L;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty("部门id列表")
    private List<Long> departmentIds;

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public List<Long> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Long> departmentIds) {
        this.departmentIds = departmentIds;
    }
}
