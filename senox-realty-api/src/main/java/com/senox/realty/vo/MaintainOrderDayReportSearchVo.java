package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/7/15 10:29
 */
@Getter
@Setter
@ToString
@ApiModel("物维日报表查询")
public class MaintainOrderDayReportSearchVo extends PageRequest {

    private static final long serialVersionUID = -8501921539108573981L;

    @ApiModelProperty("部门id")
    private List<Long> managementDeptList;

    @ApiModelProperty("报表日起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportDateStart;

    @ApiModelProperty("报表日止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportDateEnd;
}
