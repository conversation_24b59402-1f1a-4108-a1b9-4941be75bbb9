package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/11/11 15:38
 */
@ApiModel("维修单评价消息")
@Data
public class MaintainEvaluateMessageVo implements Serializable {
    private static final long serialVersionUID = -4279775853670699689L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("openid")
    private String openid;

    @ApiModelProperty("维修单号")
    private String orderNo;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("维修地址")
    private String address;

    @ApiModelProperty("客户名")
    private String customerName;
}
