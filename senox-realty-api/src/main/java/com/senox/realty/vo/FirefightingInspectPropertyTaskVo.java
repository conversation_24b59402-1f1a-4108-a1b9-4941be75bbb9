package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/5/15 17:11
 */
@Getter
@Setter
@ToString
@ApiModel("物业消防巡检任务")
public class FirefightingInspectPropertyTaskVo implements Serializable {

    private static final long serialVersionUID = 6006835017773124233L;

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("任务名")
    private String taskName;

    @ApiModelProperty("公共消防设施id")
    private Long utilityId;

    @ApiModelProperty("公共消防设施位置")
    private String utilityName;

    @ApiModelProperty("经营户id")
    private Long enterpriseId;

    @ApiModelProperty("经营户名")
    private String enterpriseName;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("公共消防设施巡检任务id")
    private Long utilityTaskId;

    @ApiModelProperty("公共消防设施巡检")
    private Boolean utilityInspect;

    @ApiModelProperty("公共消防设施巡检id")
    private Long utilityInspectId;

    @ApiModelProperty("消防巡检告知书任务id")
    private Long noticeTaskId;

    @ApiModelProperty("消防巡检告知书")
    private Boolean noticeInspect;

    @ApiModelProperty("消防巡检告知书id")
    private Long noticeInspectId;

    @ApiModelProperty("档案任务id")
    private Long fileTaskId;

    @ApiModelProperty("档案")
    private Boolean fileInspect;

    @ApiModelProperty("档案id")
    private Long fileInspectId;

    @ApiModelProperty("商铺巡检任务id")
    private Long storeTaskId;

    @ApiModelProperty("商户巡检")
    private Boolean storeInspect;

    @ApiModelProperty("商户巡检id")
    private Long storeInspectId;

    @ApiModelProperty("三小场所巡检任务id")
    private Long smallPlacesTaskId;

    @ApiModelProperty("三小场所巡检")
    private Boolean smallPlacesInspect;

    @ApiModelProperty("三小场所巡检id")
    private Long smallPlacesInspectId;

    @ApiModelProperty("违规住人")
    private Long accommodateTaskId;

    @ApiModelProperty("违规住人巡检")
    private Boolean accommodateInspect;

    @ApiModelProperty("违规住人巡检id")
    private Long accommodateInspectId;

    @ApiModelProperty("开始时间")
    private LocalDate startDate;

    @ApiModelProperty("结束时间")
    private LocalDate endDate;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}
