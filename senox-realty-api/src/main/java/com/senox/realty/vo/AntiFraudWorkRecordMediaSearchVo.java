package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import com.senox.realty.constant.AntiFraudWorkRecordMediaSource;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28
 **/
@ApiModel("反诈宣传工作记录媒体资源查询参数")
@Getter
@Setter
public class AntiFraudWorkRecordMediaSearchVo extends PageRequest {
    private static final long serialVersionUID = 5933340731616657982L;

    /**
     * 产品id集
     */
    private List<Long> productIds;

    /**
     * 资源集
     */
    private List<AntiFraudWorkRecordMediaSource> sources;
}
