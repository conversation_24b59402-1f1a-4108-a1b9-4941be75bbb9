package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/8/3 15:33
 */
@Getter
@Setter
@ToString
@ApiModel("广告列表视图")
public class AdvertisingSpaceListVo implements Serializable {

    private static final long serialVersionUID = -6460942170728882399L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("编号")
    private String serialNo;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("区域")
    private String region;

    @ApiModelProperty("街道")
    private String street;

    @ApiModelProperty("位置")
    private String address;

    @ApiModelProperty("长（米）")
    private BigDecimal length;

    @ApiModelProperty("宽（米）")
    private BigDecimal width;

    @ApiModelProperty("面积（平方米）")
    private BigDecimal size;

    @ApiModelProperty("租赁合同")
    private String rentContract;

    @ApiModelProperty("存在资源")
    private Boolean existResources;
}
