package com.senox.realty.vo;

import com.senox.common.constant.device.EnergyType;
import com.senox.common.validation.groups.Add;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2023-4-26
 */
@Data
public class RealtyBindEnergyMeteringPointVo implements Serializable {
    private static final long serialVersionUID = -7925636491656703366L;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    @NotBlank(message = "无效的设备编码", groups = Add.class)
    private String pointCode;

    /**
     * 物业编号
     */
    @ApiModelProperty("物业编号")
    private String realtySerialNo;

    /**
     * 物业名称
     */
    @ApiModelProperty("物业名称")
    private String realtyName;

    /**
     * 能源类型
     */
    @ApiModelProperty("能源类型")
    private EnergyType energyType;
}
