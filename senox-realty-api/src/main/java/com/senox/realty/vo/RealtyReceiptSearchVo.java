package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-3-23
 */
@ApiModel("物业发票查询参数")
@Getter
@Setter
public class RealtyReceiptSearchVo extends PageRequest {

    private static final long serialVersionUID = 3088263970087310196L;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 抬头
     */
    @ApiModelProperty("抬头")
    private String taxHeader;

    /**
     * 抬头类型
     * {@link com.senox.pm.constant.TaxCategory}
     */
    @ApiModelProperty("抬头类型")
    private Integer headerCategory;

    /**
     * 审核状态
     * {@link com.senox.pm.constant.ReceiptStatus}
     */
    @ApiModelProperty("审核状态")
    private Integer applyStatus;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applyMan;

    /**
     * 申请开始时间
     */
    @ApiModelProperty("申请开始时间")
    private LocalDateTime applyStartTime;

    /**
     * 申请结束时间
     */
    @ApiModelProperty("申请结束时间")
    private LocalDateTime applyEndTime;

    /**
     * 下发
     */
    private Boolean send;

}
