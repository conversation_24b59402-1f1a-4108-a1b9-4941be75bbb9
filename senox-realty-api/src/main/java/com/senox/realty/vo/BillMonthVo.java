package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/6/24 14:51
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("账单月份")
public class BillMonthVo implements Serializable {

    private static final long serialVersionUID = -6197377374856326799L;

    @ApiModelProperty("年")
    @NotNull(message = "年份不允许为空")
    @Min(value = 1, message = "无效的年份")
    private Integer year;

    @ApiModelProperty("月")
    @NotNull(message = "月份不允许为空")
    @Range(min = 1, max = 12, message = "无效的月份")
    private Integer month;

    @ApiModelProperty(value = "账单id", hidden = true)
    private Long billId;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("物业编号")
    private String realtySerial;


    public BillMonthVo(Integer year, Integer month) {
        this.year = year;
        this.month = month;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BillMonthVo that = (BillMonthVo) o;
        return Objects.equals(year, that.year)
                && Objects.equals(month, that.month);
    }

    @Override
    public int hashCode() {
        return Objects.hash(year, month);
    }
}
