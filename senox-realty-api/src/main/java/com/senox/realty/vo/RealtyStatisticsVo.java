package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/24 9:37
 */
@Data
@ApiModel("物业统计")
public class RealtyStatisticsVo implements Serializable {

    private static final long serialVersionUID = 5802958842237138927L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 统计日期
     */
    @ApiModelProperty("统计日期")
    private LocalDate statisticsDate;

    /**
     * 物业数量
     */
    @ApiModelProperty("物业数量")
    private Integer realtyNum;

    /**
     * 未租赁公司物业数量
     */
    @ApiModelProperty("未租赁公司物业数量")
    private Integer unRentCompanyRealtyNum;

    /**
     * 已租赁公司物业数量
     */
    @ApiModelProperty("已租赁公司物业数量")
    private Integer rentCompanyRealtyNum;

    /**
     * 物业合同数量
     */
    @ApiModelProperty("物业合同数量")
    private Integer realtyContractNum;

    /**
     * 代租合同数量
     */
    @ApiModelProperty("代租合同数量")
    private Integer rentContractNum;

    /**
     * 已收租数量
     */
    @ApiModelProperty("已收租数量")
    private Integer rentCollectNum;

    /**
     * 已收租金额
     */
    @ApiModelProperty("已收租金额")
    private BigDecimal rentCollectAmount;

    /**
     * 未已收租数量
     */
    @ApiModelProperty("未已收租数量")
    private Integer unRentCollectNum;

    /**
     * 未收租金额
     */
    @ApiModelProperty("未收租金额")
    private BigDecimal unRentCollectAmount;

    /**
     * 滞纳金
     */
    @ApiModelProperty("滞纳金")
    private BigDecimal penaltyAmount;
}
