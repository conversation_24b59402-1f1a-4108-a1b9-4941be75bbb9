package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/6/11 14:11
 */
@ApiModel("物业账单导入任务")
public class RealtyBillImportTaskVo implements Serializable {

    private static final long serialVersionUID = -7560257915119667258L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("账单年份")
    private Integer billYear;

    @ApiModelProperty("账单月份")
    private Integer billMonth;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名称")
    private String realtyName;

    @ApiModelProperty("档主编号")
    private String realtyOwnerSerial;

    @ApiModelProperty("档主名称")
    private String realtyOwnerName;

    @ApiModelProperty("租金")
    private BigDecimal rentAmount;

    @ApiModelProperty("管理费")
    private BigDecimal manageAmount;

    @ApiModelProperty("电费")
    private BigDecimal electricAmount;

    @ApiModelProperty("水费")
    private BigDecimal waterAmount;

    @ApiModelProperty("滞纳金")
    private BigDecimal penaltyAmount;

    @ApiModelProperty("实收滞纳金")
    private BigDecimal penaltyAmountActual;

    @ApiModelProperty("总费用")
    private BigDecimal totalAmount;

    @ApiModelProperty("已支付费用")
    private BigDecimal paidAmount;

    @ApiModelProperty("已退费费用")
    private BigDecimal refundAmount;

    @ApiModelProperty("支付状态")
    private String paidStatus;

    @ApiModelProperty("任务状态")
    private Integer taskStatus;

    @ApiModelProperty("任务执行信息")
    private String taskMessage;

    @ApiModelProperty("任务执行时间")
    private LocalDateTime taskExecTime;

    @ApiModelProperty("任务创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("处理人")
    private String modifierName;

    @ApiModelProperty("处理时间")
    private LocalDateTime modifiedTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getRealtyName() {
        return realtyName;
    }

    public void setRealtyName(String realtyName) {
        this.realtyName = realtyName;
    }

    public String getRealtyOwnerSerial() {
        return realtyOwnerSerial;
    }

    public void setRealtyOwnerSerial(String realtyOwnerSerial) {
        this.realtyOwnerSerial = realtyOwnerSerial;
    }

    public String getRealtyOwnerName() {
        return realtyOwnerName;
    }

    public void setRealtyOwnerName(String realtyOwnerName) {
        this.realtyOwnerName = realtyOwnerName;
    }

    public BigDecimal getRentAmount() {
        return rentAmount;
    }

    public void setRentAmount(BigDecimal rentAmount) {
        this.rentAmount = rentAmount;
    }

    public BigDecimal getManageAmount() {
        return manageAmount;
    }

    public void setManageAmount(BigDecimal manageAmount) {
        this.manageAmount = manageAmount;
    }

    public BigDecimal getElectricAmount() {
        return electricAmount;
    }

    public void setElectricAmount(BigDecimal electricAmount) {
        this.electricAmount = electricAmount;
    }

    public BigDecimal getWaterAmount() {
        return waterAmount;
    }

    public void setWaterAmount(BigDecimal waterAmount) {
        this.waterAmount = waterAmount;
    }

    public BigDecimal getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public BigDecimal getPenaltyAmountActual() {
        return penaltyAmountActual;
    }

    public void setPenaltyAmountActual(BigDecimal penaltyAmountActual) {
        this.penaltyAmountActual = penaltyAmountActual;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getPaidStatus() {
        return paidStatus;
    }

    public void setPaidStatus(String paidStatus) {
        this.paidStatus = paidStatus;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getTaskMessage() {
        return taskMessage;
    }

    public void setTaskMessage(String taskMessage) {
        this.taskMessage = taskMessage;
    }

    public LocalDateTime getTaskExecTime() {
        return taskExecTime;
    }

    public void setTaskExecTime(LocalDateTime taskExecTime) {
        this.taskExecTime = taskExecTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getModifierName() {
        return modifierName;
    }

    public void setModifierName(String modifierName) {
        this.modifierName = modifierName;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }
}
