package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-9-6
 */
@Getter
@Setter
@ToString
@ApiModel("合同停止参数")
public class RealtyContractSuspendRequestDto extends ContractSuspendDto {

    private static final long serialVersionUID = 8149769819455531386L;

    @ApiModelProperty("水电表抄数")
    @NotNull(message = "无效的水电表抄数")
    private List<RealtyWeVo> weList;

}
