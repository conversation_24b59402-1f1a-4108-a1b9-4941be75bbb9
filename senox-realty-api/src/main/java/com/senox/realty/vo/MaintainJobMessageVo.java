package com.senox.realty.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/7/20 8:48
 */
@Getter
@Setter
@ApiOperation("派工消息")
public class MaintainJobMessageVo implements Serializable {

    private static final long serialVersionUID = -5050810561236131920L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("派工单号")
    private String jobNo;

    @ApiModelProperty("派工维修员id单号")
    private Long jobItemId;

    @ApiModelProperty("处理人id")
    private Long handlerId;

    @ApiModelProperty("处理人")
    private String handlerName;

    @ApiModelProperty("派工时间")
    private LocalDateTime createTime;
}
