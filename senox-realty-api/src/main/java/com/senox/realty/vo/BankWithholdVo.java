package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/1/17 15:36
 */
@ApiModel("银行托收报盘")
public class BankWithholdVo implements Serializable {

    private static final long serialVersionUID = -120213002741535316L;

    @NotNull(message = "无效的年份")
    @Min(value = 2020, message = "无效的年份")
    @ApiModelProperty("年份")
    private Integer billYear;

    @NotNull(message = "无效的月份")
    @Min(value = 1, message = "无效的月份")
    @Max(value = 12, message = "无效的月份")
    @ApiModelProperty("月份")
    private Integer billMonth;

    @ApiModelProperty("报盘")
    private Boolean offer;

    @ApiModelProperty("报盘时间")
    private LocalDateTime offerTime;

    @ApiModelProperty("回盘")
    private Boolean back;

    @ApiModelProperty("回盘时间")
    private LocalDateTime backTime;


    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public Boolean getOffer() {
        return offer;
    }

    public void setOffer(Boolean offer) {
        this.offer = offer;
    }

    public LocalDateTime getOfferTime() {
        return offerTime;
    }

    public void setOfferTime(LocalDateTime offerTime) {
        this.offerTime = offerTime;
    }

    public Boolean getBack() {
        return back;
    }

    public void setBack(Boolean back) {
        this.back = back;
    }

    public LocalDateTime getBackTime() {
        return backTime;
    }

    public void setBackTime(LocalDateTime backTime) {
        this.backTime = backTime;
    }
}
