package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel("维修单")
@Getter
@Setter
public class MaintainOrderVo implements Serializable {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("维修订单号")
    private String orderNo;

    @ApiModelProperty("维修类型 1:公共土建,2:公共水电,3:公共其他,4:客户土建,5:客户水电,6:客户其他")
    private Integer maintainType;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("问题描述")
    private String problem;

    @ApiModelProperty("0 待受理; 1 正在处理； 10 已处理；50 无法处理；99 取消订单；")
    private Integer status;

    @ApiModelProperty("处理人")
    private String handlerName;

    @ApiModelProperty("处理人部门id")
    private Long handlerDeptId;

    @ApiModelProperty("处理人部门")
    private String handlerDeptName;

    @ApiModelProperty("管理所属部门id")
    private Long managementDeptId;

    @ApiModelProperty("管理所属部门")
    private String managementDeptName;

    /**
     * 评价星级
     */
    @ApiModelProperty("评价星级")
    private Integer evaluateRating;
    /**
     * 评价人openid
     */
    @ApiModelProperty("评价人openid")
    private String evaluateOpenid;
    /**
     * 评价内容
     */
    @ApiModelProperty("评价内容")
    private String evaluate;

    @ApiModelProperty("微信单创建用户")
    private String createOpenid;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

    @ApiModelProperty("媒体地址")
    private List<String> mediaUrls;

    @ApiModelProperty("评价媒体地址")
    private List<String> evaluateMediaUrls;
}
