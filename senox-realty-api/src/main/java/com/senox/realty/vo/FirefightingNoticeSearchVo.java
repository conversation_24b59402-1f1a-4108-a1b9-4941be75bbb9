package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/24 10:53
 */
@Getter
@Setter
@ToString
@ApiModel("消防安全责任告知单")
public class FirefightingNoticeSearchVo extends PageRequest {

    private static final long serialVersionUID = -6133479235637291525L;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("区域")
    private Long regionId;

    @ApiModelProperty("街道")
    private Long streetId;

    @ApiModelProperty("店铺")
    private String store;

    @ApiModelProperty("店铺负责人")
    private String storeKeyman;

    @ApiModelProperty("店铺联系方式")
    private String storeContact;

    @ApiModelProperty("模板编码")
    private String templateCode;

    @ApiModelProperty("模板版本起")
    private Integer templateVersionGe;

    @ApiModelProperty("模板版本止")
    private Integer templateVersionLe;

    @ApiModelProperty("巡查员")
    private String inspector;

    @ApiModelProperty("通知日期起")
    private LocalDate notifyDateStart;

    @ApiModelProperty("通知日期止")
    private LocalDate notifyDateEnd;
}
