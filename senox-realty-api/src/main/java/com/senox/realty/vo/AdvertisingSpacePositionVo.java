package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/3 14:43
 */
@Getter
@Setter
@ToString
@ApiModel("广告区域位置")
public class AdvertisingSpacePositionVo implements Serializable {

    private static final long serialVersionUID = -1798678751835316545L;

    @ApiModelProperty("区域id")
    private Long regionId;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("街道名")
    private String streetName;
}
