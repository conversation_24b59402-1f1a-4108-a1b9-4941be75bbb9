package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import com.senox.realty.constant.SecurityEvent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/27 9:34
 */
@Data
@ApiModel("安保日志查询")
public class SecurityJournalSearchVo extends PageRequest {

    private static final long serialVersionUID = -3808017050325397728L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("事件类型")
    private List<SecurityEvent> eventTypes;

    @ApiModelProperty("日期起")
    private LocalDate journalDateStart;

    @ApiModelProperty("日期止")
    private LocalDate journalDateEnd;
}
