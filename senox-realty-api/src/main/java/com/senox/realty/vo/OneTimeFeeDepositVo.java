package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/1/30 10:35
 */
@ApiModel("冷藏押金")
public class OneTimeFeeDepositVo implements Serializable {

    private static final long serialVersionUID = -6897660972390130636L;

    /**
     * 客户编号
     */
    private String customerSerial;
    /**
     * 押金金额
     */
    private BigDecimal depositAmount;


    public String getCustomerSerial() {
        return customerSerial;
    }

    public void setCustomerSerial(String customerSerial) {
        this.customerSerial = customerSerial;
    }

    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }
}
