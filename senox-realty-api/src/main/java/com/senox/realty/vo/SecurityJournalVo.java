package com.senox.realty.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.constant.SecurityEvent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/27 8:52
 */
@Data
@ApiModel("安保日志")
public class SecurityJournalVo implements Serializable {

    private static final long serialVersionUID = 7679168145830960939L;

    @Min(value = 1, message = "无效的id")
    @NotNull(message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    @NotNull(message = "无效的日期", groups = Add.class)
    @ApiModelProperty("日期")
    private LocalDate journalDate;

    @NotNull(message = "无效的事件类型", groups = Add.class)
    @ApiModelProperty("事件类型")
    private SecurityEvent eventType;

    @NotNull(message = "无效的发生时间", groups = Add.class)
    @ApiModelProperty("发生时间")
    private LocalDateTime eventTime;

    @ApiModelProperty("设备")
    private String equipment;

    @ApiModelProperty("简述")
    private String description;

    @ApiModelProperty("费用")
    private BigDecimal expense;

    @ApiModelProperty("处理结果")
    private String processingResult;

    @ApiModelProperty("复查结果")
    private String reprocessingResult;

    @ApiModelProperty("登记人")
    private String recorder;

    @ApiModelProperty("多媒体资料")
    private List<String> medias;

    @ApiModelProperty("复查多媒体资料")
    private List<String> reprocessMedias;

    @ApiModelProperty("创建人id")
    private Long creatorId;
}
