package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/8/4 14:57
 */
@Getter
@Setter
@ToString
@ApiModel("广告应付账单查询")
public class AdvertisingPayoffSearchVo extends PageRequest {

    private static final long serialVersionUID = 5456497504372789882L;

    @ApiModelProperty("广告位编号")
    private String spaceSerial;

    @ApiModelProperty("广告位名")
    private String spaceName;

    @ApiModelProperty("广告位区域id")
    private Long spaceRegionId;

    @ApiModelProperty("广告位街道id")
    private Long spaceStreetId;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("物业业主")
    private String realtyOwner;

    @ApiModelProperty("开始时间起")
    private LocalDate startDateBegin;

    @ApiModelProperty("开始时间止")
    private LocalDate startDateEnd;

    @ApiModelProperty("结束时间起")
    private LocalDate endDateBegin;

    @ApiModelProperty("结束时间止")
    private LocalDate endDateEnd;

    @ApiModelProperty("支付方式")
    private Integer payway;

    @ApiModelProperty("支付时间起")
    private LocalDateTime paidTimeBegin;

    @ApiModelProperty("支付时间止")
    private LocalDateTime paidTimeEnd;

    @ApiModelProperty("付款人")
    private Long tollManId;

    @ApiModelProperty("账单状态")
    private Integer status;
}
