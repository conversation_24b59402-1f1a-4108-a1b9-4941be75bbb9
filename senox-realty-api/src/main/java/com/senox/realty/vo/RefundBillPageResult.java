package com.senox.realty.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/10/22 15:29
 */
@ApiModel("退费交易明细页")
public class RefundBillPageResult<T> extends PageResult<T> {

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("退费金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    public RefundBillPageResult() {
        this.amount = BigDecimal.ZERO;
        this.refundAmount = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
    }

    public RefundBillPageResult(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        this.amount = BigDecimal.ZERO;
        this.refundAmount = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> RefundBillPageResult<T> emptyPage() {
        return new RefundBillPageResult<>();
    }
}
