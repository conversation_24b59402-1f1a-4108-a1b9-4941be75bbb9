package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/6 14:56
 */
@ApiModel("物维收费单")
@Setter
@Getter
public class MaintainChargeVo implements Serializable {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("收费单号")
    private String chargeNo;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("任务id")
    private Long jobId;

    @ApiModelProperty("收费年份")
    private Integer chargeYear;

    @ApiModelProperty("收费月份")
    private Integer chargeMonth;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("收费状态：0初始化；1已支付")
    private Integer status;

    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    @ApiModelProperty("支付账单id")
    private Long remoteOrderId;

    @ApiModelProperty("票据号")
    private String tollSerial;

    @ApiModelProperty("收费员")
    private Long tollManId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("维修费用费项")
    private List<MaintainChargeItemVo> chargeItemVos;
}
