package com.senox.realty.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.senox.realty.constant.RealtyFee;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/6/8 16:07
 */
@Getter
@Setter
@ToString
@ApiModel("物业账单视图对象")
public class RealtyBillVo implements Serializable {

    private static final long serialVersionUID = -565969891409748471L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("账单年份")
    @Min(value = 0, message = "无效的账单年份")
    private Integer billYear;

    @ApiModelProperty("账单月份")
    @Min(value = 0, message = "无效的账单月份")
    private Integer billMonth;

    @ApiModelProperty("物业区域")
    private String realtyRegion;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("物业区域1")
    private String realtyRegion1;

    @ApiModelProperty("物业区域2")
    private String realtyRegion2;

    @ApiModelProperty("档主")
    private String realtyOwnerName;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同性质")
    private Integer contractType;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("租金")
    @DecimalMin(value = "0", message = "无效的租金")
    private BigDecimal rentAmount;

    @ApiModelProperty("租金支付状态")
    private Integer rentPaidStatus;

    @ApiModelProperty("管理费")
    @DecimalMin(value = "0", message = "无效的管理费")
    private BigDecimal manageAmount;

    @ApiModelProperty("管理费支付状态")
    private Integer managePaidStatus;

    @ApiModelProperty("电费")
    @DecimalMin(value = "0", message = "无效的电费")
    private BigDecimal electricAmount;

    @ApiModelProperty("电费时段开始日期")
    private String electricStartDate;

    @ApiModelProperty("电费时段结束日期")
    private String electricEndDate;

    @ApiModelProperty("电费支付状态")
    private Integer electricPaidStatus;

    @ApiModelProperty("水费")
    @DecimalMin(value = "0", message = "无效的水费")
    private BigDecimal waterAmount;

    @ApiModelProperty("水费时段开始日期")
    private String waterStartDate;

    @ApiModelProperty("水费时段结束日期")
    private String waterEndDate;

    @ApiModelProperty("水费支付状态")
    private Integer waterPaidStatus;

    @ApiModelProperty("滞纳金")
    private BigDecimal penaltyAmount;

    @ApiModelProperty("滞纳金费率")
    private BigDecimal penaltyRate;

    @ApiModelProperty("滞纳金起始日")
    private LocalDate penaltyDate;

    @ApiModelProperty("减免滞纳金")
    private Boolean penaltyIgnore;

    @ApiModelProperty("滞纳金减免金额")
    private BigDecimal penaltyIgnoreAmount;

    // 未缴费，则不包括滞纳金，已缴费则取缴费金额
    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("总金额")
    @DecimalMin(value = "0", message = "无效的总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("免滞纳金总金额")
    private BigDecimal totalAmountIgnorePenalty;

    @ApiModelProperty("已支付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty("待收费金额")
    private BigDecimal paidStillAmount;

    @ApiModelProperty("退费金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("实付滞纳金")
    private BigDecimal penaltyPaidAmount;

    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    @ApiModelProperty("远程下单id")
    private Long remoteOrderId;

    @ApiModelProperty("交易流水号")
    private String tradeNo;

    @ApiModelProperty("账单状态")
    private Integer status;

    @ApiModelProperty("报盘")
    private Boolean offer;

    @ApiModelProperty("回盘")
    private Boolean back;

    @ApiModelProperty("下发")
    private Boolean send;

    @ApiModelProperty("下发时间")
    private LocalDateTime sendTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("收费员id")
    private Long tollManId;

    @ApiModelProperty("收费员")
    private String tollMan;

    @ApiModelProperty("支付方式")
    private Integer payWay;

    @ApiModelProperty("票据号")
    private String billSerial;

    @ApiModelProperty("发票")
    private Boolean receipt;

    @ApiModelProperty("开票人")
    private String receiptMan;

    @ApiModelProperty("发票备注")
    private String receiptRemark;

    @ApiModelProperty("开票时间")
    private LocalDateTime receiptTime;

    @ApiModelProperty("账单生成时间")
    private LocalDateTime createTime;

    @ApiModelProperty("返租合同")
    private Boolean backLease;

    @ApiModelProperty("代租合同")
    private Boolean replaceLease;

    @ApiModelProperty("代收租合同")
    private Boolean collectionLease;

    @ApiModelProperty("管理费发票状态")
    private Integer manageReceiptStatus;

    @ApiModelProperty("租金发票状态")
    private Integer rentReceiptStatus;

    @ApiModelProperty("水费发票状态")
    private Integer waterReceiptStatus;

    @ApiModelProperty("电费发票状态")
    private Integer electricReceiptStatus;

    @ApiModelProperty("营业执照名称")
    private String businessLicenseName;

    @ApiModelProperty("税号")
    private String taxSerial;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("客户联系方式")
    private String customerContact;

    @ApiModelProperty("客户相联地址")
    private String customerAddress;

    @ApiModelProperty("客户公司统一代码")
    private String customerEnterpriseCode;

    @ApiModelProperty("客户公司法人")
    private String customerLegalPerson;

    @ApiModelProperty("客户身份证")
    private String customerIdCard;

    @ApiModelProperty("业主名")
    private String ownerName;

    @ApiModelProperty("业主联系方式")
    private String ownerContact;

    @ApiModelProperty("业主公司统一代码")
    private String ownerEnterpriseCode;

    @ApiModelProperty("业主公司法人")
    private String ownerLegalPerson;

    @ApiModelProperty("业主身份证")
    private String ownerIdCard;

    @ApiModelProperty("担保物业")
    private String guaranteeRealty;

    /**
     * 获取费项金额
     *
     * @param fee
     * @return
     */
    @JsonIgnore
    public BigDecimal getFeeAmount(RealtyFee fee) {
        if (fee == null) {
            return BigDecimal.ZERO;
        }
        switch (fee) {
            case MANAGE:
                return getManageAmount();
            case RENT:
                return getRentAmount();
            case WATER:
                return getWaterAmount();
            case ELECTRIC:
                return getElectricAmount();
            case PENALTY:
                return getPenaltyAmount();
        }
        return BigDecimal.ZERO;
    }

    public void setFeeAmount(RealtyFee fee, BigDecimal amount) {
        if (fee == null) {
            return;
        }
        switch (fee) {
            case MANAGE:
                setManageAmount(amount);
                break;
            case RENT:
                setRentAmount(amount);
                break;
            case WATER:
                setWaterAmount(amount);
                break;
            case ELECTRIC:
                setElectricAmount(amount);
                break;
            case PENALTY:
                setPenaltyAmount(amount);
                break;
            default:
                break;
        }
    }
}
