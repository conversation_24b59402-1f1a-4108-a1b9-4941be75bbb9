package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/6 16:32
 */
@ApiModel("物维账单查询参数")
@Getter
@Setter
public class MaintainChargeSearchVo extends PageRequest {

    private static final long serialVersionUID = 2105508401950694971L;

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("支付时间起")
    private LocalDateTime startTime;

    @ApiModelProperty("支付时间止")
    private LocalDateTime endTime;

    @ApiModelProperty("收费年份")
    private Integer chargeYear;

    @ApiModelProperty("收费月份")
    private Integer chargeMonth;

    @ApiModelProperty("账单状态")
    private Integer status;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

    @ApiModelProperty("收费单号")
    private String chargeNo;

    @ApiModelProperty("支付方式")
    private Integer payWay;

    @ApiModelProperty("维修类型 1:公共土建,2:公共水电,3:公共其他,4:客户土建,5:客户水电,6:客户其他")
    private Integer maintainType;

    @ApiModelProperty("联系方式")
    private String contact;

}
