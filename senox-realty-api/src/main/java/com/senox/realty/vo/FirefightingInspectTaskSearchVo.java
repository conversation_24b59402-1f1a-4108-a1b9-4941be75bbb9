package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import com.senox.realty.validator.InspectCategoryChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/15 11:49
 */
@Getter
@Setter
@ToString
@ApiModel("消防巡检任务查询")
public class FirefightingInspectTaskSearchVo extends PageRequest {

    private static final long serialVersionUID = 8594128676068068289L;

    @InspectCategoryChecker(message = "无效的任务类型")
    @ApiModelProperty("任务类型")
    private Integer category;

    @ApiModelProperty("区域")
    private Long regionId;

    @ApiModelProperty("街道")
    private Long streetId;

    @ApiModelProperty("任务完成")
    private Boolean taskDone;

    @ApiModelProperty("任务开始时间")
    private LocalDate taskDateStart;

    @ApiModelProperty("任务结束时间")
    private LocalDate taskDateEnd;

}
