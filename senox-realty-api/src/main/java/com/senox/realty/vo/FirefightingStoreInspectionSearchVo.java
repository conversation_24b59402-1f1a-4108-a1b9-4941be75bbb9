package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 13:37
 */
@Getter
@Setter
@ToString
@ApiModel("商铺消防巡检记录查询")
public class FirefightingStoreInspectionSearchVo extends PageRequest {

    private static final long serialVersionUID = 6796204185238666274L;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("区域")
    private Long regionId;

    @ApiModelProperty("街道")
    private Long streetId;

    @ApiModelProperty("商铺")
    private String store;

    @ApiModelProperty("违规住人")
    private Boolean storeAccommodated;

    @ApiModelProperty("消防设施配齐")
    private Boolean firefightingFacilitiesSatisfied;

    @ApiModelProperty("砖墙保护")
    private Boolean firefightingWallSatisfied;

    @ApiModelProperty("楼梯保护")
    private Boolean firefightingStairsSatisfied;

    @ApiModelProperty("配电线路保护")
    private Boolean firefightingLinesSatisfied;

    @ApiModelProperty("火灾报警器配齐")
    private Boolean firefightingAlarmSatisfied;

    @ApiModelProperty("安全出口保护")
    private Boolean firefightingExitSatisfied;

    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡检日期起")
    private LocalDate inspectDateStart;

    @ApiModelProperty("巡检日期止")
    private LocalDate inspectDateEnd;

    @ApiModelProperty("整改截止日期起")
    private LocalDate rectificationDeadlineStart;

    @ApiModelProperty("整改截止日期止")
    private LocalDate rectificationDeadlineEnd;

    @ApiModelProperty("复查日期起")
    private LocalDate reInspectDateStart;

    @ApiModelProperty("复查日期止")
    private LocalDate reInspectDateEnd;

    @ApiModelProperty("巡检结果列表")
    private List<Integer> inspectResults;
}
