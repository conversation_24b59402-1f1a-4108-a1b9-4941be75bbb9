package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-5-22
 */
@ApiModel("物业计量设备表码")
@Data
@EqualsAndHashCode(callSuper = false)
public class RealtyToEnergyMeteringPointReadingsVo implements Serializable {
    private static final long serialVersionUID = -1369099097218870733L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 物业名
     */
    @ApiModelProperty("物业名")
    private String realtyName;

    /**
     * 物业编号
     */
    @ApiModelProperty("物业编号")
    private String realtySerialNo;

    /**
     * 终端编码
     */
    @ApiModelProperty("终端编码")
    private String rtuCode;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String pointCode;

    /**
     * 读数
     */
    @ApiModelProperty("读数")
    private BigDecimal readings;

    /**
     * 数据时间
     */
    @ApiModelProperty("数据时间")
    private LocalDateTime dataTime;

    /**
     * 计量点倍率
     */
    @ApiModelProperty("计量点倍率")
    private BigDecimal pointRate;

    /**
     * 计量点类型
     */
    @ApiModelProperty("计量点类型")
    private Integer pointType;

    /**
     * 抓取时间
     */
    @ApiModelProperty("抓取时间")
    private LocalDateTime grabTime;
}
