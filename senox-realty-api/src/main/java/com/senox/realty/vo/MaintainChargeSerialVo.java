package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/20 9:56
 */
@ApiModel("收费票据")
public class MaintainChargeSerialVo implements Serializable {
    private static final long serialVersionUID = -1569300934213724656L;
    @ApiModelProperty("物维收费id")
    @Min(value = 1L, message = "无效的物维收费id")
    @NotNull(message = "无效的物维收费id")
    private Long chargeId;
    @ApiModelProperty("票据号")
    @NotBlank(message = "无效的票据号")
    private String chargeSerial;
    @ApiModelProperty("操作员id")
    private Long operatorId;

    public Long getChargeId() {
        return chargeId;
    }

    public void setChargeId(Long chargeId) {
        this.chargeId = chargeId;
    }

    public String getChargeSerial() {
        return chargeSerial;
    }

    public void setChargeSerial(String chargeSerial) {
        this.chargeSerial = chargeSerial;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }
}
