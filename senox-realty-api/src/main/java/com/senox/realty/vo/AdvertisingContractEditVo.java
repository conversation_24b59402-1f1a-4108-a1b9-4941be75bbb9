package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/20 15:21
 */
@Getter
@Setter
@ToString
@ApiModel("广告合同编辑")
public class AdvertisingContractEditVo implements Serializable {

    private static final long serialVersionUID = -1179537594006986557L;

    @ApiModelProperty("id")
    private Long id;

    @Min(value = 1, message = "无效的广告位")
    @NotNull(message = "无效的广告位")
    @ApiModelProperty("广告位id")
    private Long spaceId;

    @NotBlank(message = "无效的客户名称")
    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户联系人")
    private String customerUser;

    @ApiModelProperty("客户联系方式")
    private String customerContact;

    @ApiModelProperty("赠送时间（月）")
    private Integer presentMonths;

    @Min(value = 1, message = "无效的租赁时长")
    @NotNull(message = "无效的租赁时长")
    @ApiModelProperty("租赁时长（月）")
    private Integer rentMonths;

    @NotNull(message = "无效的签约日期")
    @ApiModelProperty("签约日期")
    private LocalDate signDate;

    @NotNull(message = "无效的开始日期")
    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @DecimalMin(value = "0", message = "无效的合同金额")
    @NotNull(message = "无效的合同金额")
    @ApiModelProperty("合同金额")
    private BigDecimal amount;

    @ApiModelProperty("成本")
    private BigDecimal cost;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("广告媒体资料")
    private List<String> medias;

    @ApiModelProperty("收益分成")
    private List<AdvertisingProfitShareVo> shares;

}
