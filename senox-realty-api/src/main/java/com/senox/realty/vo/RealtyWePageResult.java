package com.senox.realty.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @date 2022/11/1 13:58
 */
@ApiModel("水电读数明细页")
public class RealtyWePageResult<T> extends PageResult<T> {

    @ApiModelProperty("期初水读数")
    private Long lastWaterReadings;

    @ApiModelProperty("期末水读数")
    private Long waterReadings;

    @ApiModelProperty("水实耗")
    private Long waterCost;

    @ApiModelProperty("期初电读数")
    private Long lastElectricReadings;

    @ApiModelProperty("期末电读数")
    private Long electricReadings;

    @ApiModelProperty("电实耗")
    private Long electricCost;

    public RealtyWePageResult() {
        init();
    }

    public RealtyWePageResult(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.lastWaterReadings = 0L;
        this.waterReadings = 0L;
        this.waterCost = 0L;
        this.lastElectricReadings = 0L;
        this.electricReadings = 0L;
        this.electricCost = 0L;
    }


    public Long getLastWaterReadings() {
        return lastWaterReadings;
    }

    public void setLastWaterReadings(Long lastWaterReadings) {
        this.lastWaterReadings = lastWaterReadings;
    }

    public Long getWaterReadings() {
        return waterReadings;
    }

    public void setWaterReadings(Long waterReadings) {
        this.waterReadings = waterReadings;
    }

    public Long getWaterCost() {
        return waterCost;
    }

    public void setWaterCost(Long waterCost) {
        this.waterCost = waterCost;
    }

    public Long getLastElectricReadings() {
        return lastElectricReadings;
    }

    public void setLastElectricReadings(Long lastElectricReadings) {
        this.lastElectricReadings = lastElectricReadings;
    }

    public Long getElectricReadings() {
        return electricReadings;
    }

    public void setElectricReadings(Long electricReadings) {
        this.electricReadings = electricReadings;
    }

    public Long getElectricCost() {
        return electricCost;
    }

    public void setElectricCost(Long electricCost) {
        this.electricCost = electricCost;
    }


    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> RealtyWePageResult<T> emptyPage() {
        return new RealtyWePageResult<>();
    }
}
