package com.senox.realty.vo;

import com.senox.pm.constant.ReceiptStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-4-24
 */
@Getter
@Setter
public class ReceiptApplyAuditVo implements Serializable {

    private static final long serialVersionUID = -9221563191442557115L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 发票状态
     */
    @ApiModelProperty("发票状态")
    private ReceiptStatus status;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}
