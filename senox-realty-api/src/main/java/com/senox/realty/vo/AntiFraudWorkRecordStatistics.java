package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025-03-31
 **/
@ApiModel("反扎宣传工作统计")
@Getter
@Setter
public class AntiFraudWorkRecordStatistics {

    /**
     * 总完成人数
     */
    @ApiModelProperty("总完成人数")
    private Integer totalCompletedPeople;

    /**
     * 总剩余人数
     */
    @ApiModelProperty("总剩余人数")
    private Integer totalRemainingPeople;

    /**
     * 总人数
     */
    @ApiModelProperty("总人数")
    private Integer totalPeople;
}
