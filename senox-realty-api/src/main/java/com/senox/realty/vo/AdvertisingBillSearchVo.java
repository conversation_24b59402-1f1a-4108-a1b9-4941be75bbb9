package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/7/26 14:50
 */
@Getter
@Setter
@ToString
@ApiModel("广告应收账单查询")
public class AdvertisingBillSearchVo extends PageRequest {

    private static final long serialVersionUID = 1793130241294848198L;

    @ApiModelProperty("广告位编号")
    private String spaceSerial;

    @ApiModelProperty("广告位名")
    private String spaceName;

    @ApiModelProperty("广告位区域id")
    private Long spaceRegionId;

    @ApiModelProperty("广告位街道id")
    private Long spaceStreetId;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("账单状态")
    private Integer status;

    @ApiModelProperty("开始时间起")
    private LocalDate startDateBegin;

    @ApiModelProperty("开始时间止")
    private LocalDate startDateEnd;

    @ApiModelProperty("结束时间起")
    private LocalDate endDateBegin;

    @ApiModelProperty("结束时间止")
    private LocalDate endDateEnd;

    @ApiModelProperty("支付时间起")
    private LocalDateTime paidTimeBegin;

    @ApiModelProperty("支付时间止")
    private LocalDateTime paidTimeEnd;

    @ApiModelProperty("开票")
    private Boolean receipt;

    @ApiModelProperty("开票时间起")
    private LocalDateTime receiptTimeBegin;

    @ApiModelProperty("开票时间止")
    private LocalDateTime receiptTimeEnd;

}
