package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/9 14:15
 */
@Getter
@Setter
@ToString
@ApiModel("消防违规住人巡检")
public class FirefightingAccommodateInspectionBriefVo implements Serializable {

    private static final long serialVersionUID = -5726135125989041568L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("经营类别")
    private String businessType;

    @ApiModelProperty("违规住人情况")
    private String accommodate;

    @ApiModelProperty("居住人数")
    private Integer accommodateCount;

    @ApiModelProperty("业主")
    private String owner;

    @ApiModelProperty("业主联系方式")
    private String ownerContact;

    @ApiModelProperty("经营者")
    private String runner;

    @ApiModelProperty("联系方式")
    private String runnerContact;

    @ApiModelProperty("巡检结果")
    private Integer inspectResult;

    @ApiModelProperty("巡查人员")
    private String inspector;

    @ApiModelProperty("巡检日期")
    private LocalDate inspectDate;

    @ApiModelProperty("整改截止日期")
    private LocalDate rectificationDeadline;
}
