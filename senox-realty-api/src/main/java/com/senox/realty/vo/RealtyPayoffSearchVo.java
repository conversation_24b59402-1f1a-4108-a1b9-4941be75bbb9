package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2022/11/25 9:03
 */
public class RealtyPayoffSearchVo extends PageRequest {

    private static final long serialVersionUID = 2611383284384441518L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("账单年份")
    private Integer billYear;

    @ApiModelProperty("账单月份")
    private Integer billMonth;

    @ApiModelProperty("物业id")
    private Long realtyId;

    @ApiModelProperty("经营区域")
    private Long realtyRegion;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("账单状态")
    private Integer status;


    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public Long getRealtyId() {
        return realtyId;
    }

    public void setRealtyId(Long realtyId) {
        this.realtyId = realtyId;
    }

    public Long getRealtyRegion() {
        return realtyRegion;
    }

    public void setRealtyRegion(Long realtyRegion) {
        this.realtyRegion = realtyRegion;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
