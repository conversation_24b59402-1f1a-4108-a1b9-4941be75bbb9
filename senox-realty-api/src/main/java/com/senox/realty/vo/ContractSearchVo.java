package com.senox.realty.vo;

import com.senox.common.vo.PageRequest;
import com.senox.realty.validator.ContractTypeChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 合同列表查询参数
 * <AUTHOR>
 * @date 2021/2/8 16:01
 */
@ApiModel("合同查询")
@Setter
@Getter
public class ContractSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = -7685830936684699102L;


    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同类型")
    @ContractTypeChecker(message = "无效的合同类型")
    private Integer type;

    @ApiModelProperty("物业id")
    private Long realtyId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("合同状态")
    private Integer status;

    @ApiModelProperty("签约时间起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate signDateBegin;

    @ApiModelProperty("签约时间止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate signDateEnd;

    @ApiModelProperty("合同开始时间起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDateBegin;

    @ApiModelProperty("合同开始时间止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDateEnd;

    @ApiModelProperty("合同结束时间起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDateBegin;

    @ApiModelProperty("合同结束时间止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDateEnd;

    @ApiModelProperty("合同类型")
    private List<Integer> types;

    @ApiModelProperty("合同类别")
    private Integer category;

    @ApiModelProperty("是否归档")
    private Boolean archived;

    @ApiModelProperty("租金金额")
    private Boolean rentAmount;

    @ApiModelProperty("租赁合同号")
    private Boolean leaseContractNo;

    @ApiModelProperty("押金状态")
    private Boolean depositStatus;

    @ApiModelProperty("面积")
    private Boolean area;

    @ApiModelProperty("管理费")
    private Boolean manageAmount;

    @ApiModelProperty("押金费")
    private Boolean depositAmount;

    @ApiModelProperty("代租合同号")
    private Boolean rentProxyContractNo;

    @ApiModelProperty("包含客户编码")
    private Boolean containCustomerSerial;

    @ApiModelProperty("业主名")
    private Boolean ownerName;

    @ApiModelProperty("业主联系方式")
    private Boolean ownerContact;
}
