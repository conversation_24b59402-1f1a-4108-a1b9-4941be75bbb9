package com.senox.realty.constant;

/**
 * <AUTHOR>
 * @date 2024/7/8 14:53
 */
public enum InspectResult {
    /**
     * 未处理
     */
    INIT(0),
    /**
     * 通过
     */
    PASS(1),
    /**
     * 不通过
     */
    FAIL(99),
    /**
     * 复检通过
     */
    REINSPECT_PASS(101),
    /**
     * 复检不通过
     */
    REINSPECT_FAIL(199),
    ;

    private final int value;

    InspectResult(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static InspectResult fromValue(Integer result) {
        if (result == null) {
            return null;
        }

        for (InspectResult item : values()) {
            if (item.getValue() == result) {
                return item;
            }
        }

        return null;
    }
}
