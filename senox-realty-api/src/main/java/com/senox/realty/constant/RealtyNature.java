package com.senox.realty.constant;

/**
 * 物业性质
 * <AUTHOR>
 * @Date 2020/12/18 9:16
 */
public enum RealtyNature {

    /**
     * 未知
     */
    UNKNOWN(""),
    /**
     * 公司
     */
    COMPANY("公司"),
    /**
     * 销售
     */
    SALE("销售"),
    /**
     * 安置
     */
    SETTLE("安置"),
    /**
     * 长租
     */
    LONG_RENT("长租"),
    ;

    private final String name;

    RealtyNature(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static RealtyNature fromValue(Integer value) {
        if (value != null) {
            for (RealtyNature item : values()) {
                if (item.ordinal() == value) {
                    return item;
                }
            }
        }

        return UNKNOWN;
    }
}
