package com.senox.realty.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-08
 **/
@Getter
public enum PowerOutageNoticeMediaType {

    /**
     * 整改前
     */
    RECTIFICATION_PRE(1, "整改前"),
    /**
     * 整改后
     */
    RECTIFICATION_POST(2, "整改后"),
    ;

    private final int number;
    private final String name;

    PowerOutageNoticeMediaType(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static PowerOutageNoticeMediaType fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (PowerOutageNoticeMediaType item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static PowerOutageNoticeMediaType fromName(String name) {
        if (null == name) {
            return null;
        }
        for (PowerOutageNoticeMediaType item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}
