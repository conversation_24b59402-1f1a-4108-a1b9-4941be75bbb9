package com.senox.realty.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-28
 **/
@Getter
public enum AntiFraudWorkRecordMediaSource {

    /**
     * 店面图片
     */
    STORE_IMAGE(1, "店面图片"),

    /**
     * 反诈绿码图片
     */
    ANTI_FRAUD_GREEN_QR_CODE_IMAGE(2, "反诈绿码图片"),

    /**
     * 宣传图片
     */
    PROMO_IMAGE(3, "宣传图片"),
    ;

    private final int number;
    private final String name;

    AntiFraudWorkRecordMediaSource(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static AntiFraudWorkRecordMediaSource fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (AntiFraudWorkRecordMediaSource item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static AntiFraudWorkRecordMediaSource fromName(String name) {
        if (null == name) {
            return null;
        }
        for (AntiFraudWorkRecordMediaSource item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}
