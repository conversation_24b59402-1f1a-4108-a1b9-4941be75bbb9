package com.senox.realty.constant;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/2/8 14:40
 */
public enum ContractType {

    /**
     * 租赁合同
     */
    LEASE(1, "租赁"),
    /**
     * 物业合同
     */
    ESTATE(2, "物业"),
    /**
     * 返租合同
     */
    LEASEBACK(3, "返租"),
    /**
     * 代租合同
     */
    RENT_PROXY(4, "代租"),
    /**
     * 代收租合同
     */
    RENT_COLLECTION_PROXY(5, "代收租"),
    ;

    private final int value;

    private final String chName;

    ContractType(int value, String chName) {
        this.value = value;
        this.chName = chName;
    }

    public int getValue() {
        return value;
    }

    public String getChName() {
        return chName;
    }

    public static ContractType fromValue(Integer value) {
        if (value != null) {
            for (ContractType item : values()) {
                if (Objects.equals(value, item.getValue())) {
                    return item;
                }
            }
        }
        return null;
    }

    public static ContractType fromChName(String name) {
        if (name != null) {
            for (ContractType item : values()) {
                if (Objects.equals(name, item.getChName())) {
                    return item;
                }
            }
        }
        return null;
    }
}
