package com.senox.realty.constant;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/15 9:34
 */
public enum MaintainChargeStatus {

    /**
     * 初始化
     */
    INIT(0,"未支付"),
    /**
     * 已支付
     */
    PAID(1, "已支付"),
    ;

    private final int status;
    private final String value;

    MaintainChargeStatus(int status, String value) {
        this.status = status;
        this.value = value;
    }

    public int getStatus() {
        return status;
    }

    public String getValue() {
        return value;
    }


    public static MaintainChargeStatus fromValue(String value) {
        if (value != null) {
            for (MaintainChargeStatus item : values()) {
                if (Objects.equals(item.getValue(), value)) {
                    return item;
                }
            }
        }
        return INIT;
    }


    public static MaintainChargeStatus fromStatus(Integer status) {
        if (status != null) {
            for (MaintainChargeStatus item : values()) {
                if (Objects.equals(item.getStatus(), status)) {
                    return item;
                }
            }
        }
        return null;
    }
}
