package com.senox.realty.constant;

/**
 * <AUTHOR>
 * @date 2021/2/8 14:49
 */
public enum ContractStatus {

    /**
     * 停用
     */
    SUSPEND(0, "停用"),
    /**
     * 未生效
     */
    INEFFECTIVE(1, "未生效"),
    /**
     * 启用
     */
    EFFECTIVE(2, "启用"),
    ;

    private final int status;
    private final String value;

    ContractStatus(int status, String value) {
        this.status = status;
        this.value = value;
    }

    public int getStatus() {
        return status;
    }

    public String getValue() {
        return value;
    }


    public static ContractStatus fromValue(Integer value) {
        if (value != null) {
            for (ContractStatus item : values()) {
                if (item.ordinal() == value) {
                    return item;
                }
            }
        }
        return null;
    }
}
