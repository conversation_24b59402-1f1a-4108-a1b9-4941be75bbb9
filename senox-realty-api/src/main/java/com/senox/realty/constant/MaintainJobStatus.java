package com.senox.realty.constant;

/**
 * <AUTHOR>
 * @date 2023/4/23 11:35
 */
public enum MaintainJobStatus {

    /**
     * 初始化
     */
    INIT(0, "初始化"),
    /**
     * 已完成
     */
    DONE(1, "已完成"),
    /**
     * 无法处理
     */
    INCAPABLE(2, "无法处理"),
    ;

    private final int value;
    private final String description;

    MaintainJobStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static MaintainJobStatus fromValue(Integer value) {
        if (value != null) {
            for (MaintainJobStatus status : values()) {
                if (status.getValue() == value) {
                    return status;
                }
            }
        }

        return null;
    }
}
