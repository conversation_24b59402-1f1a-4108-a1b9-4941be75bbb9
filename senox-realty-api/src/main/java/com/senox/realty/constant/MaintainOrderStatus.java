package com.senox.realty.constant;

/**
 * <AUTHOR>
 * @date 2023/2/23 14:43
 */
public enum MaintainOrderStatus {

    /**
     * 初始化
     */
    INIT(0, "初始化"),
    /**
     * 正在处理
     */
    DOING(1, "正在处理"),
    /**
     * 已完成
     */
    DONE(10, "已完成"),
    /**
     * 无法处理
     */
    INCAPABLE(50, "无法处理"),
    /**
     * 已取消
     */
    CANCEL(99, "已取消"),
    ;

    private final int value;
    private final String description;


    MaintainOrderStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static MaintainOrderStatus fromValue(Integer value) {
        if (value != null) {
            for (MaintainOrderStatus status : values()) {
                if (status.getValue() == value) {
                    return status;
                }
            }
        }

        return null;
    }
}
