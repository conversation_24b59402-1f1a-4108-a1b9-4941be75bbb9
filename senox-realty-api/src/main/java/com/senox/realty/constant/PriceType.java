package com.senox.realty.constant;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/4/29 10:35
 */
public enum PriceType {

    /**
     * 水
     */
    WATER(1),
    /**
     * 电
     */
    ELECTRIC(2),
    ;

    private final int value;

    PriceType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PriceType fromValue(Integer value) {
        if (value != null) {
            for (PriceType item : values()) {
                if (Objects.equals(value, item.getValue())) {
                    return item;
                }
            }
        }
        return null;
    }

}
