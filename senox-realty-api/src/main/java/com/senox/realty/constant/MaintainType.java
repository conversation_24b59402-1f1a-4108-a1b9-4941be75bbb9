package com.senox.realty.constant;

/**
 * 维护类型
 * <AUTHOR>
 * @date 2022/12/9 14:28
 */
public enum MaintainType {

    /**
     * 公共土建
     */
    PUBLIC_CONSTRUCTION(1, "公共土建"),
    /**
     * 公共水电
     */
    PUBLIC_POWER(2, "公共水电"),
    /**
     * 公共其他
     */
    PUBLIC_OTHERS(3, "公共其他"),
    /**
     * 客户土建
     */
    CUSTOMER_CONSTRUCTION(4, "客户土建"),
    /**
     * 客户水电
     */
    CUSTOMER_POWER(5, "客户水电"),
    /**
     * 客户其他
     */
    CUSTOMER_OTHERS(6, "客户其他"),
    ;

    private final int value;
    private final String description;

    MaintainType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static MaintainType fromValue(Integer value) {
        if (value != null) {
            for (MaintainType item : values()) {
                if (item.getValue() == value) {
                    return item;
                }
            }
        }

        return null;
    }
}
