package com.senox.realty.constant;

import java.util.Objects;

/**
 * 物业账单状态
 * <AUTHOR>
 * @date 2021/6/8 10:42
 */
public enum BillStatus {

    /**
     * 初始化
     */
    INIT(0,"未收"),
    /**
     * 已支付
     */
    PAID(1, "已收"),
    /**
     * 已退费
     */
    REFUND(10, "退费"),
    /**
     * 未知
     */
    UNKNOWN(99,"未知")
    ;

    private final int status;
    private final String value;

    BillStatus(int status, String value) {
        this.status = status;
        this.value = value;
    }

    public int getStatus() {
        return status;
    }

    public String getValue() {
        return value;
    }


    public static BillStatus fromValue(String value) {
        if (value != null) {
            for (BillStatus item : values()) {
                if (Objects.equals(item.getValue(), value)) {
                    return item;
                }
            }
        }
        return INIT;
    }


    public static BillStatus fromStatus(Integer status) {
        if (status != null) {
            for (BillStatus item : values()) {
                if (Objects.equals(item.getStatus(), status)) {
                    return item;
                }
            }
        }
        return null;
    }
}
