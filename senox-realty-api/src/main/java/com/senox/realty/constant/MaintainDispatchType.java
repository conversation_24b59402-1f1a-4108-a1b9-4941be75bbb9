package com.senox.realty.constant;

/**
 * <AUTHOR>
 * @date 2023/4/23 11:35
 */
public enum MaintainDispatchType {

    /**
     * 处理
     */
    HANDLE(0, "处理"),
    /**
     * 审核
     */
    EXAMINE(1, "审核"),
    ;

    private final int value;
    private final String description;


    MaintainDispatchType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static MaintainDispatchType fromValue(Integer value) {
        if (value != null) {
            for (MaintainDispatchType status : values()) {
                if (status.getValue() == value) {
                    return status;
                }
            }
        }

        return null;
    }
}
