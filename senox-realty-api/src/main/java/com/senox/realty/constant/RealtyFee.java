package com.senox.realty.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 物业费项
 * <AUTHOR>
 * @date 2021/6/9 14:43
 */
@Getter
public enum RealtyFee {

    /**
     * 租金
     */
    RENT(2, "租金"),
    /**
     * 管理费
     */
    MANAGE(1, "管理费"),
    /**
     * 电费
     */
    ELECTRIC( 6, "电费"),
    /**
     * 水费
     */
    WATER(4, "水费"),
    /**
     * 滞纳金
     */
    PENALTY(8,  "滞纳金"),
    /**
     * 首月手续费
     */
    FIRST_CHARGE(23, "首月手续费"),
    /**
     * 业主手续费
     */
    OWNER_CHARGE(24, "业主手续费"),
    ;


    private final int feeId;

    private final String name;



    RealtyFee(int feeId, String name) {
        this.feeId = feeId;
        this.name = name;
    }


    public static RealtyFee fromFeeId(Integer feeId) {
        if (feeId != null) {
            for (RealtyFee item : values()) {
                if (Objects.equals(feeId, item.getFeeId())) {
                    return item;
                }
            }
        }
        return null;
    }

    public static RealtyFee fromFeeName(String feeName) {
        if (null != feeName) {
            for (RealtyFee item : values()) {
                if (Objects.equals(feeName, item.getName())) {
                    return item;
                }
            }
        }
        return null;
    }
}
