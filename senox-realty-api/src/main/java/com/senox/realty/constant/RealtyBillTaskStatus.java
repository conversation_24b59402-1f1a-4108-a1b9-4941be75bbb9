package com.senox.realty.constant;

/**
 * <AUTHOR>
 * @date 2021/6/16 10:32
 */
public enum RealtyBillTaskStatus {

    /**
     * 作废
     */
    ABANDON(-2),
    /**
     * 失败
     */
    FAIL(-1),
    /**
     * 初始化
     */
    INIT(0),
    /**
     * 成功
     */
    SUCCESS(1),
    ;

    final int value;

    RealtyBillTaskStatus(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static RealtyBillTaskStatus fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (RealtyBillTaskStatus item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }
}
