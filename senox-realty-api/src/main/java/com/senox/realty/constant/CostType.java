package com.senox.realty.constant;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/2/19 8:49
 */
public enum CostType {
    /**
     * 现金
     */
    CASH(1),
    /**
     * 银行托收
     */
    BANK_COLLECTION(2),
    /**
     * 支票
     */
    CHECK(3),
    /**
     * 转账
     */
    TRANSFER(4),
    ;

    private final int value;

    CostType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static CostType fromValue(Integer value) {
        if (value != null) {
            for (CostType item : values()) {
                if (Objects.equals(value, item.getValue())) {
                    return item;
                }
            }
        }
        return null;
    }
}
