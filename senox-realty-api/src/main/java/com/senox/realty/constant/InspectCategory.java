package com.senox.realty.constant;

/**
 * 巡检类型
 * <AUTHOR>
 * @date 2024/5/14 15:52
 */
public enum InspectCategory {
    /**
     * 公共消防设施
     */
    UTILITY(1),
    /**
     * 物业
     */
    REALTY(2),
    ;

    private final int value;

    InspectCategory(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static InspectCategory fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (InspectCategory item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }
}
