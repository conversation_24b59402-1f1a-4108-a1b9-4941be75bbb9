package com.senox.realty.constant;

/**
 * <AUTHOR>
 * @date 2021/5/8 14:30
 */
public enum FeeCategory {

    /**
     * 停车
     */
    PARKING(1),
    /**
     * 物业
     */
    REALTY(2),
    /**
     * 三轮车管理
     */
    CYCLE_MANAGE(3),
    /**
     * 冷藏费
     */
    REFRIGERATION(4),
    /**
     * 物维
     */
    MAINTAIN(5)
    ;

    private final int value;

    FeeCategory(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }


    public static FeeCategory fromValue(Integer value) {
        if (value != null) {
            for (FeeCategory item : values()) {
                if (item.getValue() == value) {
                    return item;
                }
            }
        }
        return null;
    }
}
