package com.senox.realty.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/6 11:34
 */

@Getter
public enum MaintainFee {

    /**
     * 人工费
     */
    RENT(18, "人工费"),
    /**
     * 材料费
     */
    MANAGE(19, "材料费"),
    ;


    private final long feeId;

    private final String name;

    MaintainFee(int feeId, String name) {
        this.feeId = feeId;
        this.name = name;
    }


    public long getFeeId() {
        return feeId;
    }

    public String getName() {
        return name;
    }


    public static MaintainFee fromFeeId(Long feeId) {
        if (feeId != null) {
            for (MaintainFee item : values()) {
                if (Objects.equals(feeId, item.getFeeId())) {
                    return item;
                }
            }
        }
        return null;
    }

    public static MaintainFee fromFeeName(String feeName) {
        if (null != feeName) {
            for (MaintainFee item : values()) {
                if (Objects.equals(feeName, item.getName())) {
                    return item;
                }
            }
        }
        return null;
    }
}
