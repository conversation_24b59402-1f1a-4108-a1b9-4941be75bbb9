package com.senox.realty.constant;

/**
 * 安防事件
 * <AUTHOR>
 * @date 2025/3/26 14:01
 */
public enum SecurityEvent {

    /**
     * 车辆违停
     */
    PARKING_VIOLATE("车辆违规停放"),
    /**
     * 二、三轮电动车违规充占车位
     */
    TRICYCLE_VIOLATE("二、三轮电动车违规充占车位"),
    /**
     * 交通事故
     */
    TRAFFIC_ACCIDENT("交通事故"),
    /**
     * 消防巡检
     */
    FIRE_INSPECT("消防巡检"),
    /**
     * 消防事故
     */
    FIRE_ACCIDENT("消防事故"),
    /**
     * 车辆维修
     */
    VEHICLE_MAINTENANCE("消防车、警用车维修"),
    /**
     * 治安纠纷
     */
    CIVIL_DISPUTES("治安纠纷")
    ;


    private final String description;


    SecurityEvent(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
