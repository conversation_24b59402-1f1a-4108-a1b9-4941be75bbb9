package com.senox.realty.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/26 8:47
 */
@Getter
public enum MaintainOrderDepartment {

    /**
     * 初始化
     */
    NONE(0L, "无部门"),
    /**
     * 信息技术中心
     */
    INFORMATION(4L, "信息技术中心"),
    /**
     * 物维部
     */
    MAINTAIN(17L, "客服中心-物维部"),
    /**
     * 制冷工程部
     */
    REFRIGERATION(18L, "冷链物流中心-制冷工程部");

    private final long value;
    private final String description;


    MaintainOrderDepartment(long value, String description) {
        this.value = value;
        this.description = description;
    }

    public long getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static MaintainOrderDepartment fromValue(Long value) {
        if (value != null) {
            for (MaintainOrderDepartment status : values()) {
                if (status.getValue() == value) {
                    return status;
                }
            }
        }

        return null;
    }
}
