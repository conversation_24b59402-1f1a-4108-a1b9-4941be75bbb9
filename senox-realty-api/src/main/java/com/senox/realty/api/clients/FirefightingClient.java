package com.senox.realty.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/4/22 11:31
 */
@FeignClient(value = RealtyServiceUrl.SERVICE_NAME)
public interface FirefightingClient {

    /**
     * 添加公共消防设施
     * @param utility
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_UTILITY_ADD)
    Long addUtility(@RequestBody FirefightingUtilityVo utility);

    /**
     * 更新公共消防设施
     * @param utility
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_UTILITY_UPDATE)
    void updateUtility(@RequestBody FirefightingUtilityVo utility);

    /**
     * 删除公共消防设施
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_UTILITY_DELETE)
    void deleteUtility(@PathVariable Long id);

    /**
     * 获取公共消防设施
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_UTILITY_GET)
    FirefightingUtilityVo findUtilityById(@PathVariable Long id);

    /**
     * 公共消防设施列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_UTILITY_LIST)
    List<FirefightingUtilityVo> listUtility(@RequestBody FirefightingUtilitySearchVo search);

    /**
     * 公共消防设施页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_UTILITY_PAGE)
    PageResult<FirefightingUtilityVo> listUtilityPage(@RequestBody FirefightingUtilitySearchVo search);

    /**
     * 添加店铺消防安全档案
     * @param file
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_FILE_ADD)
    Long addFile(@RequestBody FirefightingFileVo file);

    /**
     * 更新店铺消防安全档案
     * @param file
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_FILE_UPDATE)
    void updateFile(@RequestBody FirefightingFileVo file);

    /**
     * 删除店铺消防安全档案
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_FILE_DELETE)
    void deleteFile(@PathVariable Long id);

    /**
     * 获取店铺消防安全档案详情
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_FILE_GET)
    FirefightingFileVo findFileById(@PathVariable Long id);

    /**
     * 店铺消防安全档案统计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_FILE_COUNT)
    int countFile(@RequestBody FirefightingFileSearchVo search);

    /**
     * 店铺消防安全档案列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_FILE_PAGE)
    PageResult<FirefightingFileBriefVo> listFileBriefPage(@RequestBody FirefightingFileSearchVo search);

    /**
     * 添加店铺消防安全告知单模板
     * @param template
     * @param newVersion
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_ADD)
    Long addTemplate(@RequestBody FirefightingTemplateVo template, @RequestParam Boolean newVersion);

    /**
     * 更新店铺消防安全告知单模板
     * @param template
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_UPDATE)
    void updateTemplate(@RequestBody FirefightingTemplateVo template);

    /**
     * 启用店铺消防安全告知单模板
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_ENABLE)
    void enableTemplate(@PathVariable Long id);

    /**
     * 停用店铺消防安全告知单模板
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_DISABLE)
    void disableTemplate(@PathVariable Long id);

    /**
     * 删除店铺消防安全告知单模板
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_DELETE)
    void deleteTemplate(@PathVariable Long id);

    /**
     * 获取店铺消防安全告知单模板
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_GET)
    FirefightingTemplateVo findTemplateById(@PathVariable Long id);

    /**
     * 获取最新店铺消防安全告知单模板
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_LATEST)
    FirefightingTemplateVo findLatestTemplateByCode(@RequestBody FireFightingTemplateSearchVo search);

    /**
     * 店铺消防安全告知单模板页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_PAGE)
    PageResult<FirefightingTemplateVo> listTemplatePage(@RequestBody FireFightingTemplateSearchVo search);

    /**
     * 添加消防表单模板
     * @param formTemplate
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_FORM_ADD)
    Long addFormTemplate(@RequestBody FirefightingFormTemplateVo formTemplate);

    /**
     * 修改消防表单模板
     * @param formTemplate
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_FORM_UPDATE)
    void updateFormTemplate(@RequestBody FirefightingFormTemplateVo formTemplate);

    /**
     * 删除消防表单模板
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_FORM_DELETE)
    void deleteFormTemplate(@PathVariable Long id);

    /**
     * 获取消防表单模板
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_FORM_GET)
    FirefightingFormTemplateVo findFormTemplateById(@PathVariable Long id);

    /**
     * 消防表单模板列表
     * @param form
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TEMPLATE_FORM_LIST)
    List<FirefightingFormTemplateVo> listFormTemplate(@RequestParam String form);

    /**
     * 添加店铺消防安全责任告知单
     * @param notice
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_NOTICE_ADD)
    Long addNotice(@RequestBody FirefightingNoticeVo notice);

    /**
     * 更新店铺消防安全责任告知单
     * @param notice
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_NOTICE_UPDATE)
    void updateNotice(@RequestBody FirefightingNoticeVo notice);

    /**
     * 删除店铺消防安全责任告知单
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_NOTICE_DELETE)
    void deleteNotice(@PathVariable Long id);

    /**
     * 获取店铺消防安全责任告知单
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_NOTICE_GET)
    FirefightingNoticeVo findNoticeById(@PathVariable Long id);

    /**
     * 店铺消防安全责任告知单页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_NOTICE_PAGE)
    PageResult<FirefightingNoticeVo> listNoticePage(@RequestBody FirefightingNoticeSearchVo search);

    /**
     * 添加公共消防设施巡检记录
     * @param inspection
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_UTILITY_ADD)
    Long addUtilityInspection(@RequestBody FirefightingUtilityInspectionVo inspection);

    /**
     * 更新公共消防设施巡检记录
     * @param inspection
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_UTILITY_UPDATE)
    void updateUtilityInspection(@RequestBody FirefightingUtilityInspectionVo inspection);

    /**
     * 删除公共消防设施巡检记录
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_UTILITY_DELETE)
    void deleteUtilityInspection(@PathVariable Long id);

    /**
     * 获取公共消防设施巡检记录
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_UTILITY_GET)
    FirefightingUtilityInspectionVo findUtilityInspectionById(@PathVariable Long id);

    /**
     * 公共消防设施巡检记录页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_UTILITY_PAGE)
    PageResult<FirefightingUtilityInspectionVo> listUtilityInspectionPage(@RequestBody FirefightingUtilityInspectionSearchVo search);


    /**
     * 添加商铺消防巡检记录
     * @param inspection
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_STORE_ADD)
    Long addStoreInspection(@RequestBody FirefightingStoreInspectionVo inspection);

    /**
     * 更新商铺消防巡检记录
     * @param inspection
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_STORE_UPDATE)
    Long updateStoreInspection(@RequestBody FirefightingStoreInspectionVo inspection);

    /**
     * 删除商铺消防巡检记录
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_STORE_DELETE)
    void deleteStoreInspection(@PathVariable Long id);

    /**
     * 获取商铺消防巡检记录
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_STORE_GET)
    FirefightingStoreInspectionVo findStoreInspectionById(@PathVariable Long id);

    /**
     * 商铺消防巡检记录数统计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_STORE_COUNT)
    int countStoreInspection(@RequestBody FirefightingStoreInspectionSearchVo search);

    /**
     * 商铺消防巡检记录页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_STORE_PAGE)
    PageResult<FirefightingStoreInspectionBriefVo> listStoreInspectionPage(@RequestBody FirefightingStoreInspectionSearchVo search);

    /**
     * 添加三小场所、出租屋消防巡检记录
     * @param inspection
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_SMALL_PLACES_ADD)
    Long addSmallPlacesInspection(@RequestBody FirefightingSmallPlacesInspectionVo inspection);

    /**
     * 更新三小场所、出租屋消防巡检记录
     * @param inspection
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_SMALL_PLACES_UPDATE)
    void updateSmallPlacesInspection(@RequestBody FirefightingSmallPlacesInspectionVo inspection);

    /**
     * 删除三小场所、出租屋消防巡检记录
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_SMALL_PLACES_DELETE)
    void deleteSmallPlacesInspection(@PathVariable Long id);

    /**
     * 获取三小场所、出租屋消防巡检记录
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_SMALL_PLACES_GET)
    FirefightingSmallPlacesInspectionVo findSmallPlacesInspectionById(@PathVariable Long id);

    /**
     * 三小场所、出租屋消防巡检记录数统计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_SMALL_PLACES_COUNT)
    int countSmallPlacesInspection(@RequestBody FirefightingSmallPlacesInspectionSearchVo search);

    /**
     * 三小场所、出租屋消防巡检记录列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_SMALL_PLACES_PAGE)
    PageResult<FirefightingSmallPlacesInspectionBriefVo> listSmallPlacesInspectionPage(@RequestBody FirefightingSmallPlacesInspectionSearchVo search);

    /**
     * 添加违规住人消防巡检记录
     * @param inspection
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_ACCOMMODATE_ADD)
    Long addAccommodateInspection(@RequestBody FirefightingAccommodateInspectionVo inspection);

    /**
     * 更新违规住人消防巡检记录
     * @param inspection
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_ACCOMMODATE_UPDATE)
    void updateAccommodateInspection(@RequestBody FirefightingAccommodateInspectionVo inspection);

    /**
     * 删除违规住人消防巡检记录
     * @param id
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_ACCOMMODATE_DELETE)
    void deleteAccommodateInspection(@PathVariable Long id);

    /**
     * 获取违规住人消防巡检记录
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_ACCOMMODATE_GET)
    FirefightingAccommodateInspectionVo findAccommodateInspectionById(@PathVariable Long id);

    /**
     * 违规住人消防巡检记录数统计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_ACCOMMODATE_COUNT)
    int countAccommodateInspection(@RequestBody FirefightingAccommodateInspectionSearchVo search);

    /**
     * 违规住人消防巡检记录列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_INSPECTION_ACCOMMODATE_PAGE)
    PageResult<FirefightingAccommodateInspectionBriefVo> listAccommodateInspectionPage(@RequestBody FirefightingAccommodateInspectionSearchVo search);

    /**
     * 添加巡检任务
     * @param task
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TASK_ADD)
    Long addInspectTask(FirefightingInspectTaskVo task);

    /**
     * 更新巡检任务
     * @param task
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TASK_UPDATE)
    void updateInspectTask(FirefightingInspectTaskVo task);

    /**
     * 获取巡检任务详情
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FIREFIGHTING_TASK_GET)
    FirefightingInspectTaskVo findTaskById(@PathVariable Long id);

    /**
     * 巡检任务列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TASK_PAGE)
    PageResult<FirefightingInspectTaskVo> listTaskPage(FirefightingInspectTaskSearchVo search);

    /**
     * 删除巡检任务明细
     * @param drop
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TASK_ITEM_DELETE)
    void deleteTaskItem(@RequestBody FirefightingTaskItemDropVo drop);

    /**
     * 巡检任务明细页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.FIREFIGHTING_TASK_ITEM_PAGE)
    PageResult<FirefightingInspectPropertyTaskVo> listTaskItemPage(@RequestBody FirefightingInspectTaskItemSearchVo search);

}
