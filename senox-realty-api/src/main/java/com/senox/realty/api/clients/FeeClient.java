package com.senox.realty.api.clients;

import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/18 10:37
 */
@FeignClient(value = RealtyServiceUrl.SERVICE_NAME)
public interface FeeClient {

    /**
     * 添加费项
     * @param feeVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.FEE_ITEM_ADD)
    Long addFee(@RequestBody FeeVo feeVo);

    /**
     * 更新费项
     * @param feeVo
     */
    @PostMapping(RealtyServiceUrl.FEE_ITEM_UPDATE)
    void updateFee(@RequestBody FeeVo feeVo);

    /**
     * 获取费项
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.FEE_ITEM_GET)
    FeeVo getFee(@PathVariable Long id);

    /**
     * 费项列表
     * @param category
     * @return
     */
    @PostMapping(RealtyServiceUrl.FEE_ITEM_LIST)
    List<FeeVo> listFee(@RequestParam(required = false) Integer category);

    /**
     * 添加水电价类别
     * @param priceType
     * @return
     */
    @PostMapping(RealtyServiceUrl.WATER_ELECTRIC_PRICE_TYPE_ADD)
    Long addPriceType(@RequestBody WaterElectricPriceTypeVo priceType);

    /**
     * 更新水电价类别
     * @param priceType
     */
    @PostMapping(RealtyServiceUrl.WATER_ELECTRIC_PRICE_TYPE_UPDATE)
    void updatePriceType(@RequestBody WaterElectricPriceTypeVo priceType);

    /**
     * 根据id获取水电价类别详情
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.WATER_ELECTRIC_PRICE_TYPE_GET)
    WaterElectricPriceTypeVo getPriceType(@PathVariable Long id);

    /**
     * 水电价类别列表
     * @param type
     * @return
     */
    @PostMapping(RealtyServiceUrl.WATER_ELECTRIC_PRICE_TYPE_LIST)
    List<WaterElectricPriceTypeVo> listPriceType(@RequestParam(required = false) Integer type);

    /**
     * 添加一次性收费项目
     * @param fee
     * @return
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_ADD)
    Long addOneTimeFee(@RequestBody OneTimeFeeVo fee);

    /**
     * 更新一次性收费项目
     * @param fee
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_UPDATE)
    void updateOneTimeFee(@RequestBody OneTimeFeeVo fee);

    /**
     * 根据id获取一次性收费项目详情
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.ONE_TIME_FEE_GET)
    OneTimeFeeVo findOneTimeFeeById(@PathVariable Long id);

    /**
     * 一次性收费项目列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_LIST)
    PageResult<OneTimeFeeVo> listOneTimeFee(@RequestBody OneTimeFeeSearchVo search);

    /**
     * 部门一次性收入项目
     * @param departmentIds
     * @return
     */
    @GetMapping(RealtyServiceUrl.ONE_TIME_FEE_LIST_BY_DEPARTMENT)
    List<OneTimeFeeVo> listDepartmentOneTimeFee(@RequestParam List<Long> departmentIds);

    /**
     * 添加一次性收费账单
     * @param bill
     * @return
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_ADD)
    Long addOneTimeFeeBill(@RequestBody OneTimeFeeBillVo bill);

    /**
     * 更新一次性收费账单
     * @param bill
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_UPDATE)
    void updateOneTimeFeeBill(@RequestBody OneTimeFeeBillVo bill);

    /**
     * 更新一次性收费账单票据号
     * @param serial
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_UPDATE_SERIAL)
    void updateOneTimeFeeBillSerial(@RequestBody TollSerialVo serial);

    /**
     * 支付一次性账单
     * @param id
     * @param toll
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_PAY)
    void payOneTimeFeeBill(@PathVariable Long id, @RequestBody BillTollVo toll);

    /**
     * 撤销支付一次性账单
     * @param id
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_PAY_REVOKE)
    void revokeOneTimeFeeBillPayment(@PathVariable Long id);

    /**
     * 一次性账单退费
     * @param id
     * @param toll
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_REFUND)
    void refundOneTimeFeeBill(@PathVariable Long id, @RequestBody BillTollVo toll);

    /**
     * 撤销一次性账单退费
     * @param id
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_REFUND_REVOKE)
    void revokeOneTimeFeeBillRefund(@PathVariable Long id);

    /**
     * 获取一次性账单信息
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_GET)
    OneTimeFeeBillVo findOneTimeFeeBillById(@PathVariable Long id);


    /**
     * 根据id列表获取一次性长的那信息
     * @param ids
     * @return
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_LIST_BY_IDS)
    List<OneTimeFeeBillVo> listOneTimeFeeBillByIds(@RequestBody List<Long> ids);

    /**
     * 获取一次性账单信息及缴费退费信息
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_GET_DETAIL)
    OneTimeFeeBillTradeVo findOneTimeFeeBillDetailById(@PathVariable Long id);

    /**
     * 一次性账单列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_LIST)
    RefundBillPageResult<OneTimeFeeBillTradeVo> listOneTimeFeeBill(@RequestBody OneTimeFeeBillSearchVo search);

    /**
     * 一次性账单交易列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_TRADE_LIST)
    RefundBillPageResult<OneTimeFeeBillTradeVo> listOneTimeFeeBillTrade(@RequestBody OneTimeFeeBillTradeSearchVo search);

    /**
     * 更新一次性费用账单支付结果
     * @param billPaid
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_PAID_UPDATE)
    void updateOneTimeFeeBillStatus(@RequestBody BillPaidVo billPaid);

    /**
     * 一次性收费押金列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ONE_TIME_FEE_BILL_DEPOSIT_LIST)
    List<OneTimeFeeDepositVo> listOneTimeFeeDeposit(@RequestBody OneTimeFeeDepositSearchVo search);
}
