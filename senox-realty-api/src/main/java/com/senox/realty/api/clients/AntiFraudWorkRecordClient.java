package com.senox.realty.api.clients;

import com.senox.common.vo.PageStatisticsResult;
import com.senox.realty.vo.AntiFraudWorkRecordSearchVo;
import com.senox.realty.vo.AntiFraudWorkRecordStatistics;
import com.senox.realty.vo.AntiFraudWorkRecordVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.realty.api.AntiFraudWorkRecordUrl.*;
import static com.senox.realty.api.RealtyServiceUrl.SERVICE_NAME;

/**
 * <AUTHOR>
 * @date 2025-04-01
 **/
@FeignClient(SERVICE_NAME)
public interface AntiFraudWorkRecordClient {

    /**
     * 添加
     * @param workRecord 工作记录参数集
     */
    @PostMapping(ANTI_FRAUD_WORK_RECORD_ADD)
    void add(@RequestBody AntiFraudWorkRecordVo workRecord);

    /**
     * 更新
     * @param workRecord 工作记录参数
     */
    @PostMapping(ANTI_FRAUD_WORK_RECORD_UPDATE)
    void update(@RequestBody AntiFraudWorkRecordVo workRecord);

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的数据
     */
    @PostMapping(ANTI_FRAUD_WORK_RECORD_FIND_BY_ID)
    AntiFraudWorkRecordVo findById(@PathVariable Long id);

    /**
     * 根据id批量删除
     * @param ids id集
     */
    @PostMapping(ANTI_FRAUD_WORK_RECORD_DELETE_BY_ID)
    void deleteByIds(@RequestBody List<Long> ids);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    @PostMapping(ANTI_FRAUD_WORK_RECORD_LIST_COUNT)
    int countList(@RequestBody AntiFraudWorkRecordSearchVo search);

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    @PostMapping(ANTI_FRAUD_WORK_RECORD_LIST)
    List<AntiFraudWorkRecordVo> list(@RequestBody AntiFraudWorkRecordSearchVo search);

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    @PostMapping(ANTI_FRAUD_WORK_RECORD_LIST_PAGE)
    PageStatisticsResult<AntiFraudWorkRecordVo, AntiFraudWorkRecordStatistics> pageList(@RequestBody AntiFraudWorkRecordSearchVo search);
}
