package com.senox.realty.api.clients;

import com.senox.common.vo.BillCancelVo;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/2 15:11
 */
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface RealtyClient {

    /**
     * 添加物业
     *
     * @param realty
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_ADD)
    Long addRealty(@RequestBody RealtyVo realty);

    /**
     * 更新物业
     *
     * @param realty
     */
    @PostMapping(RealtyServiceUrl.REALTY_UPDATE)
    void updateRealty(@RequestBody RealtyVo realty);

    /**
     * 更新物业担保
     * @param guarantee
     */
    @PostMapping(RealtyServiceUrl.REALTY_GUARANTEE_UPDATE)
    void updateRealtyGuarantee(@RequestBody RealtyGuaranteeVo guarantee);

    /**
     * 根据id获取物业
     *
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_GET)
    RealtyVo getRealty(@PathVariable Long id);

    /**
     * 根据物业编号获取物业
     *
     * @param serial
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_GET_BY_SERIAL)
    RealtyVo getRealtyBySerial(@RequestParam String serial);

    /**
     * 获取物业与业主信息
     *
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_GET_WITH_OWNER)
    RealtyVo getRealtyWithOwner(@PathVariable Long id);

    /**
     * 物业最新水电读数
     * @param realtyId
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_WE_LATEST)
    List<RealtyReadingsVo> getRealtyReadings(@PathVariable Long realtyId);

    /**
     * 更新物业读数
     * @param list
     */
    @PostMapping(RealtyServiceUrl.REALTY_WE_UPDATE)
    void updateRealtyReadings(@RequestBody List<RealtyReadingsVo> list);

    /**
     * 物业分页列表
     *
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_LIST)
    PageResult<RealtyVo> listRealtyPage(@RequestBody RealtySearchVo searchVo);

    /**
     * 新增物业别名
     *
     * @param realtyId
     * @param aliasList
     */
    @PostMapping(RealtyServiceUrl.REALTY_ALIAS_SAVE)
    void saveRealtyAlias(@PathVariable Long realtyId, @RequestBody List<RealtyAliasVo> aliasList);

    /**
     * 获取物业别名
     *
     * @param realtyId 主档id
     * @return List<RealtyAliasVo>
     */
    @GetMapping(RealtyServiceUrl.REALTY_ALIAS_LIST)
    List<RealtyAliasVo> listRealtyAlias(@PathVariable Long realtyId);

    /**
     * 生成应收账单
     * @param month
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_GENERATE)
    void generateBill(@RequestBody BillMonthVo month);

    /**
     * 重新生成应收账单
     * @param month
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_REGENERATE)
    void regenerateBill(@RequestBody BillMonthVo month);

    /**
     * 保存应收账单
     *
     * @param bill
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_UPDATE)
    void updateRealtyBill(@RequestBody RealtyBillVo bill);

    /**
     * 保存应收账单备注
     *
     * @param remark
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_REMARK_SAVE)
    void saveRealtyBillRemark(@RequestBody RealtyBillRemarkVo remark);

    /**
     * 保存账单票据号
     *
     * @param billSerial
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_SERIAL_SAVE)
    void saveRealtyBillSerial(@RequestBody RealtyBillSerialVo billSerial);

    /**
     * 下发的应收账单
     *
     * @param sendVo
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_SEND)
    void sendRealtyBill(@RequestBody RealtyBillSendVo sendVo);

    /**
     * 撤销支付
     * @param cancel
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_CANCEL)
    void cancelRealtyBill(@RequestBody BillCancelVo cancel);

    /**
     * 删除应收账单
     * @param id
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_DELETE)
    void deleteRealtyBill(@PathVariable Long id);

    /**
     * 忽略滞纳金
     *
     * @param penaltyIgnore
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_IGNORE_PENALTY)
    void ignoreRealtyBillPenalty(@RequestBody BillPenaltyIgnoreVo penaltyIgnore);

    /**
     * 开票
     * @param batchBills
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_RECEIPT)
    void receiptBill(@RequestBody RealtyBillBatchVo batchBills);

    /**
     * 更新应收账单状态
     *
     * @param billPaid
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_PAID_UPDATE)
    void updateRealtyBillStatus(@RequestBody BillPaidVo billPaid);

    /**
     * 获取应收账单明细
     *
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_BILL_GET)
    RealtyBillVo getRealtyBill(@PathVariable Long id);

    /**
     * 根据id列表获取应收账单列表
     *
     * @param ids
     * @param withDetail
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_BILL_LIST_BY_ID)
    List<RealtyBillVo> listRealtyBillById(@RequestParam("ids") List<Long> ids,
                                          @RequestParam(name = "withDetail", required = false) Boolean withDetail);

    /**
     * 应收账单列表
     *
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_LIST)
    RealtyBillPageResult<RealtyBillVo> listRealtyBill(@RequestBody RealtyBillSearchVo search);

    /**
     * 物业发票账单列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_RECEIPT_LIST)
    RealtyBillPageResult<RealtyBillVo> listRealtyReceiptBill(@RequestBody RealtyBillSearchVo search);

    /**
     * 应收账单明细列表
     *
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_DETAIL_LIST)
    RealtyBillPageResult<RealtyBillVo> listRealtyBillWithDetail(@RequestBody RealtyBillSearchVo search);

    /**
     * 应收账单明细页信息
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_DETAIL_INFO_PAGE)
    PageResult<RealtyBillVo> listBillDetailInfoPage(@Validated @RequestBody RealtyBillSearchVo search);

    /**
     * 应收账单明细列表信息
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_DETAIL_INFO_LIST)
    RealtyBillPageResult<RealtyBillVo> listBillDetailInfo(@Validated @RequestBody RealtyBillSearchVo search);

    /**
     * 应收账单明细合计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_DETAIL_SUM)
    RealtyBillVo sumBillDetail(@RequestBody RealtyBillSearchVo search);

    /**
     * 应收账单明细页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_DETAIL_PAGE)
    PageResult<RealtyBillVo> listBillDetailPage(@RequestBody RealtyBillSearchVo search);

    /**
     * 查找物业月账单列表
     * @param monthVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_MONTHLY_BILL_LIST)
    List<RealtyBillVo> monthlyBillList(@RequestBody BillMonthVo monthVo);

    /**
     * 新增物业账单
     * @param monthVo
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_SAVE_BILL)
    void saveBill(@RequestBody BillMonthVo monthVo);

    /**
     * 账单银行托收报盘
     *
     * @param withhold
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WITHHOLD_APPLY)
    void applyBillWithhold(@RequestBody BankWithholdVo withhold);

    /**
     * 取消账单银行托收报盘
     *
     * @param withhold
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WITHHOLD_CANCEL)
    void cancelBillWithhold(@RequestBody BankWithholdVo withhold);

    /**
     * 账单银行托收回盘
     *
     * @param withhold
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WITHHOLD_BACK)
    void backBillWithhold(@RequestBody BankWithholdVo withhold);

    /**
     * 账单银行托收支付
     *
     * @param withholdBack
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WITHHOLD_PAY)
    void payBillWithhold(@RequestBody WithholdBackVo withholdBack);

    /**
     * 获取银行托收报盘记录
     *
     * @param year
     * @param month
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_BILL_WITHHOLD_GET)
    BankWithholdVo getBankWithHold(@RequestParam("year") Integer year, @RequestParam("month") Integer month);

    /**
     * 账单银行托收列表
     *
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WITHHOLD_PAGE)
    WithholdPage<BankOfferRealtyBillVo> listBillWithholdPage(@RequestBody RealtyBillSearchVo search);

    /**
     * 账单银行托收列表 (不分页)
     *
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WITHHOLD_LIST)
    List<BankOfferRealtyBillVo> listBillWithhold(@RequestBody RealtyBillSearchVo search);

    /**
     * 生成应付账单
     * @param month
     */
    @PostMapping(RealtyServiceUrl.REALTY_PAYOFF_GENERATE)
    void generatePayoff(@RequestBody BillMonthVo month);

    /**
     * 更新应付账单
     * @param payoff
     */
    @PostMapping(RealtyServiceUrl.REALTY_PAYOFF_UPDATE)
    void updatePayoff(@RequestBody RealtyPayoffVo payoff);

    /**
     * 删除应付账单
     * @param id
     */
    @PostMapping(RealtyServiceUrl.REALTY_PAYOFF_DELETE)
    void deletePayoff(@PathVariable Long id);

    /**
     * 获取应付账单详情
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_PAYOFF_GET)
    RealtyPayoffVo getPayoff(@PathVariable Long id);

    /**
     * 应付账单合计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_PAYOFF_SUM)
    RealtyPayoffVo sumPayoff(@RequestBody RealtyPayoffSearchVo search);

    /**
     * 应付账单列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_PAYOFF_LIST)
    List<RealtyPayoffVo> listPayoff(@RequestBody RealtyPayoffSearchVo search);

    /**
     * 应付账单页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_PAYOFF_PAGE)
    PageResult<RealtyPayoffVo> listPayoffPage(@RequestBody RealtyPayoffSearchVo search);

    /**
     * 添加物业押金
     *
     * @param deposit
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_ADD)
    Long addRealtyDeposit(@RequestBody RealtyDepositVo deposit);

    /**
     * 批量添加物业押金
     * @param depositList
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_BATCH_ADD)
    Long addRealtyDepositBatch(@RequestBody List<RealtyDepositVo> depositList);

    /**
     * 更新物业押金
     *
     * @param deposit
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_UPDATE)
    void updateRealtyDeposit(@RequestBody RealtyDepositVo deposit);

    /**
     * 更新押金支付结果
     * @param billPaid
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_PAID_UPDATE)
    void updateRealtyDepositStatus(@RequestBody BillPaidVo billPaid);

    /**
     * 更新物业押金票据号
     *
     * @param serial
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_UPDATE_SERIAL)
    void updateRealtyDepositSerial(@RequestBody TollSerialVo serial);

    /**
     * 支付物业押金
     *
     * @param id
     * @param toll
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_PAY)
    void payRealtyDeposit(@PathVariable Long id, @RequestBody BillTollVo toll);

    /**
     * 撤销支付物业押金
     *
     * @param id
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_PAY_REVOKE)
    void revokeRealtyDepositPayment(@PathVariable Long id);

    /**
     * 退档物业押金
     *
     * @param id
     * @param toll
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_REFUND)
    void refundRealtyDeposit(@PathVariable Long id, @RequestBody BillTollVo toll);

    /**
     * 撤销退档物业押金
     *
     * @param id
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_REFUND_REVOKE)
    void revokeRealtyDepositRefund(@PathVariable Long id);

    /**
     * 获取物业押金详情
     *
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_DEPOSIT_GET)
    RealtyDepositVo findRealtyDepositById(@PathVariable Long id);

    /**
     * 获取物业押金
     * @param ids
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_LIST_BY_IDS)
    List<RealtyDepositVo> listRealtyDepositByIds(@RequestBody List<Long> ids);

    /**
     * 物业合同押金
     * @param contractId
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_DEPOSIT_CONTRACT_LIST)
    List<RealtyDepositVo> listRealtyContractDeposit(@PathVariable Long contractId);

    /**
     * 物业押金列表
     *
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_DEPOSIT_LIST)
    RefundBillPageResult<RealtyDepositVo> listDepositPage(@RequestBody RealtyDepositSearchVo search);

    /**
     * 应收账单水电明细列表
     *
     * @param billId
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_BILL_WE)
    List<RealtyBillWeVo> listRealtyBillWeDetail(@PathVariable Long billId);

    /**
     * 同步应收账单水电数据
     * @param billTime
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WE_SYNC)
    void syncBillWeData(@RequestBody BillMonthVo billTime);

    /**
     * 物业水电账单生成/更新
     * @param month
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WE_GENERATE)
    void generateRealtyWeBill(@RequestBody BillMonthVo month);

    /**
     * 物业水电账单更新
     * @param bill
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WE_UPDATE)
    void updateRealtyWeBill(@RequestBody RealtyBillWeVo bill);

    /**
     * 删除物业水电账单
     * @param id
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WE_DELETE)
    void deleteRealtyWeBill(@PathVariable Long id);

    /**
     * 同步水电账单至应收账单
     * @param id
     * @param billMonth
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WE_SYNC_ONE)
    void syncWeBill2RealtyBill(@PathVariable Long id, @RequestBody BillMonthVo billMonth);

    /**
     * 根据id获取水电账单
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_BILL_WE_GET)
    RealtyBillWeVo findRealtyWeBillById(@PathVariable Long id);

    /**
     * 物业水电账单合计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WE_SUM)
    RealtyBillWeVo sumRealtyWeBill(@RequestBody RealtyBillWeSearchVo search);

    /**
     * 物业水电账单列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_BILL_WE_LIST)
    PageResult<RealtyBillWeVo> listRealtyWeBill(@RequestBody RealtyBillWeSearchVo search);

    /**
     * 添加物业税率
     *
     * @param realtyTaxRate 税率参数
     */
    @PostMapping(RealtyServiceUrl.REALTY_TAX_RATE_SAVE)
    void saveRealtyTaxRate(@RequestBody RealtyTaxRateVo realtyTaxRate);

    /**
     * 取消物业税率
     *
     * @param realtyTaxRate 税率参数
     */
    @PostMapping(RealtyServiceUrl.REALTY_TAX_RATE_CANCEL)
    void cancelRealtyTaxRate(@RequestBody RealtyTaxRateVo realtyTaxRate);

    /**
     * 物业税率分页列表
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    @PostMapping(RealtyServiceUrl.REALTY_TAX_RATE_LIST_PAGE)
    PageResult<RealtyVo> pageListRealtyTaxRate(@RequestBody RealtySearchVo search);

}
