package com.senox.realty.api.clients;

import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.StreetVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/18 10:37
 */
@FeignClient(value = RealtyServiceUrl.SERVICE_NAME)
public interface StreetClient {

    /**
     * 添加街道
     * @param streetVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.STREET_ADD)
    Long addStreet(@RequestBody StreetVo streetVo);

    /**
     * 更新街道
     * @param streetVo
     */
    @PostMapping(RealtyServiceUrl.STREET_UPDATE)
    void updateStreet(@RequestBody StreetVo streetVo);

    /**
     * 获取街道
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.STREET_GET)
    StreetVo getStreet(@PathVariable Long id);

    /**
     * 区域街道列表
     * @param regionId
     * @return
     */
    @PostMapping(RealtyServiceUrl.STREET_LISTRS)
    List<StreetVo> listRegionStreet(@PathVariable Long regionId);

    /**
     * 街道列表
     * @return
     */
    @PostMapping(RealtyServiceUrl.STREET_LIST)
    List<StreetVo> listStreet();
}
