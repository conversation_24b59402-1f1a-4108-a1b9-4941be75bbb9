package com.senox.realty.api;

/**
 * <AUTHOR>
 * @date 2022-12-2
 */
public class RealtyEnergyServiceUrl {
    private RealtyEnergyServiceUrl() {
    }

    public static final String ENERGY_METERING_POINT_BIND = "/realty/energy/metering/point/bind";
    public static final String ENERGY_METERING_POINT_LIST_COUNT = "/realty/energy/metering/point/list/count";
    public static final String ENERGY_METERING_POINT_LIST = "/realty/energy/metering/point/list";
    public static final String ENERGY_METERING_POINT_READING_LIST = "/realty/energy/metering/point/readings/list";
    public static final String ENERGY_METER_POINT_READINGS_SYNC = "/realty/energy/metering/point/readings/sync";
    public static final String ENERGY_METER_POINT_AUTOMATION_BIND = "/realty/energy/metering/point/automatic/bind";
    public static final String ENERGY_METER_POINT_CANCEL = "/realty/energy/metering/point/cancel/{meteringPointCode}";
    public static final String ENERGY_METER_POINT_HISTORICAL_READINGS_SYNC = "/realty/energy/metering/point/historical/readings/sync";


    /**
     * 添加能源消费单元
     */
    public static final String CONSUME_UNIT_ADD = "/energy/consumeUnit/add";
    /**
     * 更新能源消费单元
     */
    public static final String CONSUME_UNIT_UPDATE = "/energy/consumeUnit/update";
    /**
     * 获取能源消费单元详情
     */
    public static final String CONSUME_UNIT_GET = "/energy/consumeUnit/get/{id}";
    /**
     * 能源消费单元列表
     */
    public static final String CONSUME_UNIT_LIST = "/energy/consumeUnit/list";

    /**
     * 能源损益生成
     */
    public static final String PROFIT_GENERATE = "/energy/profit/generate";
    /**
     * 能源损益保存
     */
    public static final String PROFIT_SAVE = "/energy/profit/save";
    /**
     * 能源损益获取
     */
    public static final String PROFIT_GET = "/energy/profit/get/{id}";
    /**
     * 能源损益页
     */
    public static final String PROFIT_PAGE = "/energy/profit/page";
    /**
     * 能源损益明细
     */
    public static final String PROFIT_ITEM_LIST = "/energy/profit/item/list/{profitId}";
}
