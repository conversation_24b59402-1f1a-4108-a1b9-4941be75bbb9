package com.senox.realty.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.RealtyRegionDictSearchVo;
import com.senox.realty.vo.RealtyRegionDictVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static com.senox.realty.api.RealtyRegionDictUrl.*;

/**
 * <AUTHOR>
 * @date 2025-04-28
 **/
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface RealtyRegionDictClient {

    /**
     * 添加字典
     * @param regionDict 字典信息
     */
    @PostMapping(REALTY_REGION_DICT_ADD)
    void add(@RequestBody RealtyRegionDictVo regionDict);

    /**
     * 根据id删除字典
     * @param id 字典id
     */
    @GetMapping(REALTY_REGION_DICT_DELETE_BY_ID)
    void deleteById(@PathVariable Long id);

    /**
     * 根据id更新字典
     * @param regionDict 字典信息
     */
    @PostMapping(REALTY_REGION_DICT_UPDATE_BY_ID)
    void updateById(@RequestBody RealtyRegionDictVo regionDict);

    /**
     * 分页查询字典
     * @param search 查询条件
     * @return 分页结果
     */
    @PostMapping(REALTY_REGION_DICT_LIST_PAGE)
    PageResult<RealtyRegionDictVo> pageList(@RequestBody RealtyRegionDictSearchVo search);

}

