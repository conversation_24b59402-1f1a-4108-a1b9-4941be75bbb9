package com.senox.realty.api.clients;

import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillReceiptBriefVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/27 15:27
 */
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface AdvertisingClient {

    /**
     * 添加广告位
     * @param space
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_SPACE_ADD)
    Long addSpace(@RequestBody AdvertisingSpaceVo space);

    /**
     * 更新广告位
     * @param space
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_SPACE_UPDATE)
    void updateSpace(@RequestBody AdvertisingSpaceVo space);

    /**
     * 删除广告位
     * @param id
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_SPACE_DELETE)
    void deleteSpace(@PathVariable Long id);

    /**
     * 获取广告位详情
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.ADVERTISING_SPACE_GET)
    AdvertisingSpaceVo getSpace(@PathVariable Long id);

    /**
     * 统计广告位
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_SPACE_COUNT)
    int countSpace(@RequestBody AdvertisingSpaceSearchVo search);

    /**
     * 广告位列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_SPACE_PAGE)
    PageResult<AdvertisingSpaceListVo> listSpacePage(@RequestBody AdvertisingSpaceSearchVo search);

    /**
     * 添加广告合同
     * @param contract
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_ADD)
    Long addContract(AdvertisingContractEditVo contract);

    /**
     * 更新广告合同
     * @param contract
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_UPDATE)
    void updateContract(AdvertisingContractEditVo contract);

    /**
     * 更新已缴费广告合同
     * @param contract
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_PAID_UPDATE)
    void updatePaidContract(AdvertisingContractEditVo contract);

    /**
     * 更新广告合同成本
     * @param cost
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_COST_UPDATE)
    void updateContractCost(AdvertisingCostVo cost);

    /**
     * 停用合同
     * @param suspend
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_SUSPEND)
    void suspendContract(@RequestBody ContractSuspendDto suspend);

    /**
     * 删除合同
     * @param id
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_DELETE)
    void deleteContract(@PathVariable Long id);

    /**
     * 获取合同详情
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_GET)
    AdvertisingContractVo getContract(@PathVariable Long id);

    /**
     * 统计合同
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_COUNT)
    int countContract(@RequestBody AdvertisingContractSearchVo search);

    /**
     * 合同列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_CONTRACT_PAGE)
    PageResult<AdvertisingContractListVo> listContractPage(@RequestBody AdvertisingContractSearchVo search);

    /**
     * 广告收益合计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_INCOME_SUM)
    AdvertisingIncomeVo sumContractIncome(@RequestBody AdvertisingContractSearchVo search);

    /**
     * 广告收益列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_INCOME_LIST)
    List<AdvertisingIncomeVo> listContractIncome(@RequestBody AdvertisingContractSearchVo search);

    /**
     * 广告收益列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_INCOME_PAGE)
    PageResult<AdvertisingIncomeVo> listContractIncomePage(@RequestBody AdvertisingContractSearchVo search);

    /**
     * 更新广告应收账单状态
     * @param billPaid
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_BILL_PAID_UPDATE)
    void updateBillStatus(@RequestBody BillPaidVo billPaid);

    /**
     * 更新广告应收账单票据号
     * @param tollSerial
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_BILL_SERIAL_UPDATE)
    void updateBillTollSerial(@RequestBody TollSerialVo tollSerial);

    /**
     * 广告账单开票
     * @param receipt
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_BILL_RECEIPT_ADD)
    void addBillReceipt(@RequestBody BillReceiptBriefVo receipt);

    /**
     * 广告账单取消开票
     * @param ids
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_BILL_RECEIPT_CANCEL)
    void cancelBillReceipt(@RequestBody List<Long> ids);

    /**
     * 获取广告应收账单详情
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.ADVERTISING_BILL_GET)
    AdvertisingBillVo getBill(@PathVariable Long id);

    /**
     * 根据id获取广告应收账单详情
     * @param ids
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_BILL_LIST_BY_IDS)
    List<AdvertisingBillVo> listBillByIds(List<Long> ids);

    /**
     * 广告应收账单合计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_BILL_SUM)
    AdvertisingBillVo sumBill(@RequestBody AdvertisingBillSearchVo search);

    /**
     * 广告应收账单列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_BILL_PAGE)
    PageResult<AdvertisingBillVo> listBillPage(@RequestBody AdvertisingBillSearchVo search);

    /**
     * 生成广告应付账单
     * @param contractNo
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_PAYOFF_GENERATE)
    void generatePayoff(@RequestParam("contractNo") String contractNo);

    /**
     * 更新广告应付账单
     * @param payoff
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_PAYOFF_UPDATE)
    void updatePayoff(@RequestBody AdvertisingPayoffVo payoff);

    /**
     * 删除广告应付账单
     * @param id
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_PAYOFF_DELETE)
    void deletePayoff(@PathVariable Long id);

    /**
     * 更新应付账单状态
     * @param billPaid
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_PAYOFF_PAID_UPDATE)
    void updatePayoffStatus(@RequestBody BillPaidVo billPaid, @RequestParam("payway") int payway);

    /**
     * 广告合同应付账单
     * @param contractNo
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_PAYOFF_LIST_BY_CONTRACT)
    List<AdvertisingPayoffVo> listPayoffByContract(@RequestParam("contractNo") String contractNo);

    /**
     * 广告应付账单合计
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_PAYOFF_SUM)
    AdvertisingPayoffDetailVo sumPayoff(@RequestBody AdvertisingPayoffSearchVo search);

    /**
     * 广告应付账单页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.ADVERTISING_PAYOFF_PAGE)
    PageResult<AdvertisingPayoffDetailVo> listPayoffPage(@RequestBody AdvertisingPayoffSearchVo search);
}
