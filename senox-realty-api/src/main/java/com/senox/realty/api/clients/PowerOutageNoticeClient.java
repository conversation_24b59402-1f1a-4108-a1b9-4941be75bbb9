package com.senox.realty.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.PowerOutageNoticeSearchVo;
import com.senox.realty.vo.PowerOutageNoticeVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static com.senox.realty.api.PowerOutageNoticeUrl.*;

/**
 * <AUTHOR>
 * @date 2025-05-12
 **/
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface PowerOutageNoticeClient {

    /**
     * 添加
     * @param notice 停电通知参数
     */
    @PostMapping(REALTY_POWER_OUTAGE_NOTICE_ADD)
    void add(@RequestBody PowerOutageNoticeVo notice);

    /**
     * 根据id更新
     * @param notice 停电通知参数
     */
    @PostMapping(REALTY_POWER_OUTAGE_NOTICE_UPDATE_BY_ID)
    void updateById(@RequestBody PowerOutageNoticeVo notice);

    /**
     * 根据id查找
     * @param id id
     * @return 返回查询到的数据
     */
    @GetMapping(REALTY_POWER_OUTAGE_NOTICE_FIND_BY_ID)
    PowerOutageNoticeVo findById(@PathVariable Long id);

    /**
     * 根据id删除
     * @param id 停电通知id
     */
    @GetMapping(REALTY_POWER_OUTAGE_NOTICE_DELETE_BY_ID)
    void deleteById(@PathVariable Long id);

    /**
     * 分页列表
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    @PostMapping(REALTY_POWER_OUTAGE_NOTICE_LIST_PAGE)
    PageResult<PowerOutageNoticeVo> pageList(@RequestBody PowerOutageNoticeSearchVo search);
}
