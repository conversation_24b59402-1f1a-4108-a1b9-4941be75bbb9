package com.senox.realty.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/19 17:06
 */
@FeignClient(value = RealtyServiceUrl.SERVICE_NAME)
public interface ContractClient {

    /**
     * 新增合同
     * @param contract
     * @return
     */
    @PostMapping(RealtyServiceUrl.CONTRACT_ADD)
    Long addContract(@RequestBody ContractVo contract);

    /**
     * 更新合同
     * @param contract
     */
    @PostMapping(RealtyServiceUrl.CONTRACT_UPDATE)
    void updateContract(@RequestBody ContractVo contract);

    /**
     * 启用合同
     * @param contractNo
     */
    @PostMapping(RealtyServiceUrl.CONTRACT_ENABLE)
    void enableContract(@RequestParam String contractNo);

    /**
     * 停用合同
     * @param suspendReq
     */
    @PostMapping(RealtyServiceUrl.CONTRACT_SUSPEND)
    void suspendContract(@RequestBody RealtyContractSuspendRequestDto suspendReq);

    /**
     * 获取合同信息
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.CONTRACT_GET)
    ContractVo getContract(@PathVariable Long id);

    /**
     * 根据合同号获取合同
     * @param contractNo
     * @return
     */
    @GetMapping(RealtyServiceUrl.CONTRACT_GET_BY_CONTRACTNO)
    ContractVo getByContractNo(@RequestParam String contractNo);

    /**
     * 获取续签源合同
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.CONTRACT_RENEW_FROM)
    ContractVo getRenewFrom(@PathVariable Long id);

    /**
     * 获取物业获取最新合同
     * @param realtyId
     * @return
     */
    @GetMapping(RealtyServiceUrl.CONTRACT_REALTY_LIST)
    List<ContractVo> listRealtyContract(@PathVariable Long realtyId);

    /**
     * 查询物业最近的租赁合同
     * @param realtyDateVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.CONTRACT_REALTY_LEASE_GET)
    ContractVo getRealtyLeaseContract(@RequestBody RealtyDateVo realtyDateVo);

    /**
     * 合同列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.CONTRACT_LIST)
    PageResult<ContractVo> listContractPage(ContractSearchVo search);

    /**
     * 租赁合同列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.CONTRACT_LEASE_LIST)
    PageResult<LeaseContractListVo> listLeaseContractPage(ContractSearchVo search);

    /**
     * 合同银行信息列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.CONTRACT_BANK_DELEGATE_LIST)
    PageResult<ContractBankVo> listContractBank(ContractSearchVo search);
}
