package com.senox.realty.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.constant.SecurityEvent;
import com.senox.realty.vo.SecurityJournalSearchVo;
import com.senox.realty.vo.SecurityJournalVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025/3/27 14:22
 */
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface SecurityClient {

    /**
     * 安保日志类别清单
     * @return
     */
    @PostMapping(RealtyServiceUrl.SECURITY_JOURNAL_TYPES)
    SecurityEvent[] listSecurityEventTypes();

    /**
     * 添加安保日志
     * @param journal
     * @return
     */
    @PostMapping(RealtyServiceUrl.SECURITY_JOURNAL_ADD)
    Long addJournal(@RequestBody SecurityJournalVo journal);

    /**
     * 更新安保日志
     * @param journal
     */
    @PostMapping(RealtyServiceUrl.SECURITY_JOURNAL_UPDATE)
    void updateJournal(@RequestBody SecurityJournalVo journal);

    /**
     * 删除安保日志
     * @param id
     */
    @PostMapping(RealtyServiceUrl.SECURITY_JOURNAL_DELETE)
    void deleteJournal(@PathVariable Long id);

    /**
     * 获取安保日志
     * @param id
     */
    @GetMapping(RealtyServiceUrl.SECURITY_JOURNAL_GET)
    SecurityJournalVo findJournalById(@PathVariable Long id);

    /**
     * 安保日志列表页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.SECURITY_JOURNAL_PAGE)
    PageResult<SecurityJournalVo> listJournalPage(@RequestBody SecurityJournalSearchVo search);


}
