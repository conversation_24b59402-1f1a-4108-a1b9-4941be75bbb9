package com.senox.realty.api;

/**
 * <AUTHOR>
 * @Date 2021/1/18 10:41
 */
public class RealtyServiceUrl {

    private RealtyServiceUrl() {
    }

    /**
     * 服务名
     */
    public static final String SERVICE_NAME = "senox-realty";

    // ---------- 物业管理 ---------------
    public static final String REALTY_ADD = "/realty/add";
    public static final String REALTY_UPDATE = "/realty/update";
    public static final String REALTY_GUARANTEE_UPDATE = "/realty/guarantee/update";
    public static final String REALTY_GET = "/realty/get/{id}";
    public static final String REALTY_GET_BY_SERIAL = "/realty/getBySerial";
    public static final String REALTY_GET_WITH_OWNER = "/realty/getWithOwner/{id}";
    public static final String REALTY_WE_LATEST = "/realty/we/{realtyId}";
    public static final String REALTY_WE_UPDATE = "/realty/we/update";
    public static final String REALTY_LIST = "/realty/list";
    public static final String REALTY_ALIAS_SAVE = "/realty/alias/save/{realtyId}";
    public static final String REALTY_ALIAS_LIST = "/realty/alias/list/{realtyId}";
    public static final String REALTY_TAX_RATE_SAVE = "/realty/taxRate/save";
    public static final String REALTY_TAX_RATE_CANCEL = "/realty/taxRate/cancel";
    public static final String REALTY_TAX_RATE_LIST_PAGE = "/realty/taxRate/list/page";


    // ---------- 物业读数 ---------------
    public static final String REALTY_READINGS_WE_CHECK = "/realty/readings/we/check";
    public static final String REALTY_READINGS_WE_ADD = "/realty/readings/we/batchAdd";
    public static final String REALTY_READINGS_WE_UPDATE = "/realty/readings/we/update";
    public static final String REALTY_READINGS_WE_DELETE = "/realty/readings/we/delete";
    public static final String REALTY_READINGS_WE_RESET = "/realty/readings/we/reset";
    public static final String REALTY_READINGS_WE_GET = "/realty/readings/we/get/{id}";
    public static final String REALTY_READINGS_WE_LIST = "/realty/readings/we/list";

    // ------------ 物业账单 -------------
    public static final String REALTY_BILL_GENERATE = "/realtyBill/generate";
    public static final String REALTY_BILL_REGENERATE = "/realtyBill/regenerate";
    public static final String REALTY_BILL_UPDATE = "/realtyBill/update";
    public static final String REALTY_BILL_IGNORE_PENALTY = "/realtyBill/ignorePenalty";
    public static final String REALTY_BILL_RECEIPT = "/realtyBill/receipt";
    public static final String REALTY_BILL_REMARK_SAVE = "/realtyBill/remark/save";
    public static final String REALTY_BILL_SERIAL_SAVE = "/realtyBill/billSerial/save";
    public static final String REALTY_BILL_SEND = "/realtyBill/send";
    public static final String REALTY_BILL_CANCEL = "/realtyBill/cancel";
    public static final String REALTY_BILL_DELETE = "/realtyBill/delete/{id}";
    public static final String REALTY_BILL_PAID_UPDATE = "/realtyBill/paid/update";
    public static final String REALTY_BILL_GET = "/realtyBill/get/{id}";
    public static final String REALTY_BILL_LIST_BY_ID = "/realtyBill/listById";
    public static final String REALTY_BILL_LIST = "/realtyBill/list";

    public static final String REALTY_BILL_RECEIPT_LIST = "/realtyBill/receiptList";
    public static final String REALTY_BILL_DETAIL_LIST = "/realtyBill/detailList";

    public static final String REALTY_BILL_DETAIL_INFO_PAGE = "/realtyBill/detail/info/page";
    public static final String REALTY_BILL_DETAIL_INFO_LIST = "/realtyBill/detail/info/list";
    public static final String REALTY_BILL_DETAIL_SUM = "/realtyBill/detail/sum";
    public static final String REALTY_BILL_DETAIL_PAGE = "/realtyBill/detail/page";

    public static final String REALTY_BILL_MONTHLY_BILL_LIST = "/realtyBill/monthlyBillList";
    public static final String REALTY_BILL_SAVE_BILL = "/realtyBill/saveBill";

    public static final String REALTY_BILL_WITHHOLD_APPLY = "/realtyBill/withhold/apply";
    public static final String REALTY_BILL_WITHHOLD_CANCEL = "/realtyBill/withhold/cancel";
    public static final String REALTY_BILL_WITHHOLD_BACK = "/realtyBill/withhold/back";
    public static final String REALTY_BILL_WITHHOLD_PAY = "/realtyBill/withhold/pay";
    public static final String REALTY_BILL_WITHHOLD_GET = "/realtyBill/withhold/get";
    public static final String REALTY_BILL_WITHHOLD_PAGE = "/realtyBill/withhold/apply/page";
    public static final String REALTY_BILL_WITHHOLD_LIST = "/realtyBill/withhold/apply/list";

    /**
     * 物业水电账单
     */
    public static final String REALTY_BILL_WE = "/realtyBill/we/{billId}";
    public static final String REALTY_BILL_WE_SYNC = "/realtyBill/we/sync";
    public static final String REALTY_BILL_WE_GENERATE = "/realty/bill/we/generate";
    public static final String REALTY_BILL_WE_UPDATE = "/realty/bill/we/update";
    public static final String REALTY_BILL_WE_DELETE = "/realty/bill/we/delete/{id}";
    public static final String REALTY_BILL_WE_SYNC_ONE= "/realty/bill/we/syncOne/{id}";
    public static final String REALTY_BILL_WE_GET = "/realty/bill/we/get/{id}";
    public static final String REALTY_BILL_WE_SUM = "/realty/bill/we/sum";
    public static final String REALTY_BILL_WE_LIST = "/realty/bill/we/list";

    // ---------- 应付账单 --------------
    public static final String REALTY_PAYOFF_GENERATE = "/payoff/generate";
    public static final String REALTY_PAYOFF_UPDATE = "/payoff/update";
    public static final String REALTY_PAYOFF_DELETE = "/payoff/delete/{id}";
    public static final String REALTY_PAYOFF_GET = "/payoff/get/{id}";
    public static final String REALTY_PAYOFF_SUM = "/payoff/sum";
    public static final String REALTY_PAYOFF_LIST = "/payoff/list";
    public static final String REALTY_PAYOFF_PAGE = "/payoff/page";

    // 合同管理
    public static final String CONTRACT_ADD = "/contract/add";
    public static final String CONTRACT_UPDATE = "/contract/update";
    public static final String CONTRACT_GET = "/contract/get/{id}";
    public static final String CONTRACT_RENEW_FROM = "/contract/renew/from/{id}";
    public static final String CONTRACT_REALTY_LIST = "/contract/listRealtyContract/{realtyId}";
    public static final String CONTRACT_GET_BY_CONTRACTNO = "/contract/getByContractNo";
    public static final String CONTRACT_REALTY_LEASE_GET = "/contract/realtyLease/get";
    public static final String CONTRACT_ENABLE = "/contract/enable";
    public static final String CONTRACT_SUSPEND = "/contract/suspend";
    public static final String CONTRACT_LIST = "/contract/list";
    public static final String CONTRACT_LEASE_LIST = "/contract/lease/list";
    public static final String CONTRACT_BANK_DELEGATE_LIST = "/contract/bankDelegate/list";

    // ---------- 字典 接口 --------------
    public static final String FEE_ITEM_ADD = "/fee/add";
    public static final String FEE_ITEM_UPDATE = "/fee/update";
    public static final String FEE_ITEM_GET = "/fee/get/{id}";
    public static final String FEE_ITEM_LIST = "/fee/list";

    public static final String ONE_TIME_FEE_ADD = "/oneTimeFee/add";
    public static final String ONE_TIME_FEE_UPDATE = "/oneTimeFee/update";
    public static final String ONE_TIME_FEE_GET = "/oneTimeFee/get/{id}";
    public static final String ONE_TIME_FEE_LIST = "/oneTimeFee/list";
    public static final String ONE_TIME_FEE_LIST_BY_DEPARTMENT = "/oneTimeFee/listByDepartment";

    public static final String ONE_TIME_FEE_BILL_ADD = "/oneTimeFeeBill/add";
    public static final String ONE_TIME_FEE_BILL_UPDATE = "/oneTimeFeeBill/update";
    public static final String ONE_TIME_FEE_BILL_UPDATE_SERIAL = "/oneTimeFeeBill/serial/update";
    public static final String ONE_TIME_FEE_BILL_PAY = "/oneTimeFeeBill/pay/{id}";
    public static final String ONE_TIME_FEE_BILL_PAY_REVOKE = "/oneTimeFeeBill/payRevoke/{id}";
    public static final String ONE_TIME_FEE_BILL_REFUND = "/oneTimeFeeBill/refund/{id}";
    public static final String ONE_TIME_FEE_BILL_REFUND_REVOKE = "/oneTimeFeeBill/refundRevoke/{id}";
    public static final String ONE_TIME_FEE_BILL_GET = "/oneTimeFeeBill/get/{id}";
    public static final String ONE_TIME_FEE_BILL_LIST_BY_IDS = "/oneTimeFeeBill/listByIds";
    public static final String ONE_TIME_FEE_BILL_GET_DETAIL = "/oneTimeFeeBill/getDetail/{id}";
    public static final String ONE_TIME_FEE_BILL_LIST = "/oneTimeFeeBill/list";
    public static final String ONE_TIME_FEE_BILL_TRADE_LIST = "/oneTimeFeeBill/tradeList";
    public static  final String ONE_TIME_FEE_BILL_PAID_UPDATE = "/oneTimeFeeBill/paid/update";
    public static  final String ONE_TIME_FEE_BILL_DEPOSIT_LIST = "/oneTimeFeeBill/deposit/list";

    public static final String REALTY_DEPOSIT_ADD = "/deposit/realty/add";
    public static final String REALTY_DEPOSIT_BATCH_ADD = "/deposit/realty/batchAdd";
    public static final String REALTY_DEPOSIT_UPDATE = "/deposit/realty/update";
    public static final String REALTY_DEPOSIT_PAID_UPDATE = "/deposit/realty/paid/update";
    public static final String REALTY_DEPOSIT_UPDATE_SERIAL = "/deposit/realty/serial/update";
    public static final String REALTY_DEPOSIT_PAY = "/deposit/realty/pay/{id}";
    public static final String REALTY_DEPOSIT_PAY_REVOKE = "/deposit/realty/payRevoke/{id}";
    public static final String REALTY_DEPOSIT_REFUND = "/deposit/realty/refund/{id}";
    public static final String REALTY_DEPOSIT_REFUND_REVOKE = "/deposit/realty/refundRevoke/{id}";
    public static final String REALTY_DEPOSIT_GET = "/deposit/realty/get/{id}";
    public static final String REALTY_DEPOSIT_LIST_BY_IDS = "/deposit/realty/listByIds";
    public static final String REALTY_DEPOSIT_CONTRACT_LIST = "/deposit/realty/contractDeposit/list/{contractId}";
    public static final String REALTY_DEPOSIT_LIST = "/deposit/realty/list";

    public static final String REGION_ADD = "/businessRegion/add";
    public static final String REGION_UPDATE = "/businessRegion/update";
    public static final String REGION_GET = "/businessRegion/get/{id}";
    public static final String REGION_LIST = "/businessRegion/list";

    public static final String STREET_ADD = "/street/add";
    public static final String STREET_UPDATE = "/street/update";
    public static final String STREET_GET = "/street/get/{id}";
    public static final String STREET_LIST = "/street/list";
    public static final String STREET_LISTRS = "/street/listRS/{regionId}";

    public static final String WATER_ELECTRIC_PRICE_TYPE_ADD = "/waterElectricPriceType/add";
    public static final String WATER_ELECTRIC_PRICE_TYPE_UPDATE = "/waterElectricPriceType/update";
    public static final String WATER_ELECTRIC_PRICE_TYPE_GET = "/waterElectricPriceType/get/{id}";
    public static final String WATER_ELECTRIC_PRICE_TYPE_LIST = "/waterElectricPriceType/list";

    // -------------物维管理-----------------
    public static final String MAINTAIN_ORDER_ADD = "/maintain/order/add";
    public static final String MAINTAIN_ORDER_UPDATE = "/maintain/order/update";
    public static final String MAINTAIN_ORDER_GET = "/maintain/order/get/{id}";
    public static final String MAINTAIN_ORDER_LIST = "/maintain/order/list";
    public static final String MAINTAIN_ORDER_DELETE = "/maintain/order/delete/{id}";
    public static final String MAINTAIN_ORDER_EXPORT_LIST = "/maintain/order/export/list";
    public static final String MAINTAIN_ORDER_COUNT = "/maintain/order/count";
    public static final String MAINTAIN_ORDER_STATISTIC_PAGE = "/maintain/order/statistic/page";
    public static final String MAINTAIN_ORDER_STATISTIC_SUM = "/maintain/order/statistic/sum";
    public static final String MAINTAIN_ORDER_EVALUATE = "/maintain/order/evaluate";
    public static final String MAINTAIN_ORDER_EVALUATE_RESET = "/maintain/order/evaluate/reset/{orderId}";
    public static final String MAINTAIN_ORDER_EVALUATE_PAGE = "/maintain/order/evaluate/page";
    public static final String MAINTAIN_JOB_ADD = "/maintain/job/add";
    public static final String MAINTAIN_JOB_UPDATE = "/maintain/job/update";
    public static final String MAINTAIN_ORDER_ITEM_UPDATE = "/maintain/job/item/update";
    public static final String MAINTAIN_JOB_GET = "/maintain/job/get/{id}";
    public static final String MAINTAIN_JOB_DISPATCH = "/maintain/job/dispatch/{orderId}";
    public static final String MAINTAIN_JOB_DISPATCH_LIST = "/maintain/job/dispatch/list";
    public static final String MAINTAIN_JOB_DISPATCH_GET = "/maintain/job/dispatch/get/{itemId}";
    public static final String MAINTAIN_JOB_DELETE = "/maintain/job/delete/{jobId}";
    public static final String MAINTAIN_MATERIAL_ADD = "/maintain/material/add";
    public static final String MAINTAIN_MATERIAL_DELETE = "/maintain/material/delete/{id}";
    public static final String MAINTAIN_MATERIAL_BATCH_DELETE = "/maintain/material/batch/delete";
    public static final String MAINTAIN_MATERIAL_LIST = "/maintain/material/list";
    public static final String MAINTAIN_MATERIAL_UPDATE_STATE = "/maintain/material/outNo/save";
    public static final String MAINTAIN_MATERIAL_REVOKE = "/maintain/material/revoke/{outNo}";
    public static final String MAINTAIN_MATERIAL_GET = "/maintain/material/get/{orderId}";
    public static final String MAINTAIN_MATERIAL_UPDATE = "/maintain/material/update";
    public static final String MAINTAIN_MATERIAL_BATCH_ADD = "/maintain/material/batch/add";
    public static final String MAINTAIN_CHARGE_ADD = "/maintain/charge/add";
    public static final String MAINTAIN_CHARGE_LIST = "/maintain/charge/list";
    public static final String MAINTAIN_CHARGE_GET = "/maintain/charge/get/{id}";
    public static final String MAINTAIN_CHARGE_GETBYJOBID = "/maintain/charge/getByJobId/{jobId}";
    public static final String MAINTAIN_CHARGE_GET_BY_ORDER_ID = "/maintain/charge/getByOrderId/{orderId}";
    public static final String MAINTAIN_CHARGE_GET_CHARGE_ID = "/maintain/charge/get/chargeId/{chargeId}";
    public static final String MAINTAIN_CHARGE_GET_JOB_ID = "/maintain/charge/get/jobId/{jobId}";
    public static final String MAINTAIN_CHARGE_GET_ORDER_ID = "/maintain/charge/get/orderId/{orderId}";
    public static final String MAINTAIN_CHARGE_LISTBYIDS = "/maintain/charge/listByIds";
    public static final String MAINTAIN_CHARGE_PAID_UPDATE = "/maintain/charge/paid/update";
    public static final String MAINTAIN_CHARGE_SERIAL_SAVE = "/maintain/charge/serial/save";
    public static final String MAINTAIN_CHARGE_REMARK_UPDATE = "/maintain/charge/remark/update";
    public static final String MAINTAIN_CHARGE_BATCH_ADD = "/maintain/charge/batch/add";
    public static final String MAINTAIN_CHARGE_DELETE = "/maintain/charge/delete/{id}";
    public static final String MAINTAIN_CHARGE_ITEM_DELETE = "/maintain/charge/item/delete/{id}";
    public static final String MAINTAIN_ORDER_DAY_PAGE = "/maintain/order/report/day/page";
    public static final String MAINTAIN_ORDER_MONTH_PAGE = "/maintain/order/report/month/page";


    // ---------------------- 广告 ----------------------
    public static final String ADVERTISING_SPACE_ADD = "/advertising/space/add";
    public static final String ADVERTISING_SPACE_UPDATE = "/advertising/space/update";
    public static final String ADVERTISING_SPACE_DELETE = "/advertising/space/delete/{id}";
    public static final String ADVERTISING_SPACE_GET = "/advertising/space/get/{id}";
    public static final String ADVERTISING_SPACE_COUNT = "/advertising/space/count";
    public static final String ADVERTISING_SPACE_PAGE = "/advertising/space/page";

    public static final String ADVERTISING_CONTRACT_ADD = "/advertising/contract/add";
    public static final String ADVERTISING_CONTRACT_UPDATE = "/advertising/contract/update";
    public static final String ADVERTISING_CONTRACT_PAID_UPDATE = "/advertising/contract/paid/update";
    public static final String ADVERTISING_CONTRACT_COST_UPDATE = "/advertising/contract/cost/update";
    public static final String ADVERTISING_CONTRACT_SUSPEND = "/advertising/contract/suspend";
    public static final String ADVERTISING_CONTRACT_DELETE = "/advertising/contract/delete/{id}";
    public static final String ADVERTISING_CONTRACT_GET = "/advertising/contract/get/{id}";
    public static final String ADVERTISING_CONTRACT_COUNT = "/advertising/contract/count";
    public static final String ADVERTISING_CONTRACT_PAGE = "/advertising/contract/page";
    public static final String ADVERTISING_INCOME_SUM = "/advertising/contract/income/sum";
    public static final String ADVERTISING_INCOME_LIST = "/advertising/contract/income/list";
    public static final String ADVERTISING_INCOME_PAGE = "/advertising/contract/income/page";

    public static final String ADVERTISING_BILL_PAID_UPDATE = "/advertising/bill/paid/update";
    public static final String ADVERTISING_BILL_SERIAL_UPDATE = "/advertising/bill/tollSerial/update";
    public static final String ADVERTISING_BILL_RECEIPT_ADD = "/advertising/bill/receipt/add";
    public static final String ADVERTISING_BILL_RECEIPT_CANCEL = "/advertising/bill/receipt/cancel";
    public static final String ADVERTISING_BILL_GET = "/advertising/bill/get/{id}";
    public static final String ADVERTISING_BILL_LIST_BY_IDS = "/advertising/bill/listByIds";
    public static final String ADVERTISING_BILL_SUM = "/advertising/bill/sum";
    public static final String ADVERTISING_BILL_PAGE = "/advertising/bill/page";

    public static final String ADVERTISING_PAYOFF_GENERATE= "/advertising/payoff/generate";
    public static final String ADVERTISING_PAYOFF_UPDATE = "/advertising/payoff/update";
    public static final String ADVERTISING_PAYOFF_DELETE = "/advertising/payoff/delete/{id}";
    public static final String ADVERTISING_PAYOFF_PAID_UPDATE = "/advertising/payoff/paid/update";
    public static final String ADVERTISING_PAYOFF_LIST_BY_CONTRACT = "/advertising/payoff/listByContract";
    public static final String ADVERTISING_PAYOFF_SUM = "/advertising/payoff/sum";
    public static final String ADVERTISING_PAYOFF_PAGE = "/advertising/payoff/page";

    // ------------ 店铺消防档案 ------------
    public static final String FIREFIGHTING_UTILITY_ADD = "/firefighting/utility/add";
    public static final String FIREFIGHTING_UTILITY_UPDATE = "/firefighting/utility/update";
    public static final String FIREFIGHTING_UTILITY_DELETE = "/firefighting/utility/delete/{id}";
    public static final String FIREFIGHTING_UTILITY_GET = "/firefighting/utility/get/{id}";
    public static final String FIREFIGHTING_UTILITY_LIST = "/firefighting/utility/list";
    public static final String FIREFIGHTING_UTILITY_PAGE = "/firefighting/utility/page";

    public static final String FIREFIGHTING_FILE_ADD = "/firefighting/file/add";
    public static final String FIREFIGHTING_FILE_UPDATE = "/firefighting/file/update";
    public static final String FIREFIGHTING_FILE_DELETE = "/firefighting/file/delete/{id}";
    public static final String FIREFIGHTING_FILE_GET = "/firefighting/file/get/{id}";
    public static final String FIREFIGHTING_FILE_COUNT = "/firefighting/file/count";
    public static final String FIREFIGHTING_FILE_PAGE = "/firefighting/file/page";

    public static final String FIREFIGHTING_TEMPLATE_ADD = "/firefighting/template/add";
    public static final String FIREFIGHTING_TEMPLATE_UPDATE = "/firefighting/template/update";
    public static final String FIREFIGHTING_TEMPLATE_ENABLE = "/firefighting/template/enable/{id}";
    public static final String FIREFIGHTING_TEMPLATE_DISABLE = "/firefighting/template/disable/{id}";
    public static final String FIREFIGHTING_TEMPLATE_DELETE = "/firefighting/template/delete/{id}";
    public static final String FIREFIGHTING_TEMPLATE_GET = "/firefighting/template/get/{id}";
    public static final String FIREFIGHTING_TEMPLATE_LATEST = "/firefighting/template/latest";
    public static final String FIREFIGHTING_TEMPLATE_PAGE = "/firefighting/template/page";
    public static final String FIREFIGHTING_TEMPLATE_FORM_ADD = "/firefighting/template/form/add";
    public static final String FIREFIGHTING_TEMPLATE_FORM_UPDATE = "/firefighting/template/form/update";
    public static final String FIREFIGHTING_TEMPLATE_FORM_DELETE = "/firefighting/template/form/delete/{id}";
    public static final String FIREFIGHTING_TEMPLATE_FORM_GET = "/firefighting/template/form/get/{id}";
    public static final String FIREFIGHTING_TEMPLATE_FORM_LIST = "/firefighting/template/form/list";

    public static final String FIREFIGHTING_NOTICE_ADD = "/firefighting/notice/add";
    public static final String FIREFIGHTING_NOTICE_UPDATE = "/firefighting/notice/update";
    public static final String FIREFIGHTING_NOTICE_DELETE = "/firefighting/notice/delete/{id}";
    public static final String FIREFIGHTING_NOTICE_GET = "/firefighting/notice/get/{id}";
    public static final String FIREFIGHTING_NOTICE_PAGE = "/firefighting/notice/page";

    public static final String FIREFIGHTING_INSPECTION_UTILITY_ADD = "/firefighting/inspection/utility/add";
    public static final String FIREFIGHTING_INSPECTION_UTILITY_UPDATE = "/firefighting/inspection/utility/update";
    public static final String FIREFIGHTING_INSPECTION_UTILITY_DELETE = "/firefighting/inspection/utility/delete/{id}";
    public static final String FIREFIGHTING_INSPECTION_UTILITY_GET = "/firefighting/inspection/utility/get/{id}";
    public static final String FIREFIGHTING_INSPECTION_UTILITY_PAGE = "/firefighting/inspection/utility/page";

    public static final String FIREFIGHTING_INSPECTION_STORE_ADD = "/firefighting/inspection/store/add";
    public static final String FIREFIGHTING_INSPECTION_STORE_UPDATE = "/firefighting/inspection/store/update";
    public static final String FIREFIGHTING_INSPECTION_STORE_DELETE = "/firefighting/inspection/store/delete/{id}";
    public static final String FIREFIGHTING_INSPECTION_STORE_GET = "/firefighting/inspection/store/get/{id}";
    public static final String FIREFIGHTING_INSPECTION_STORE_COUNT = "/firefighting/inspection/store/count";
    public static final String FIREFIGHTING_INSPECTION_STORE_PAGE = "/firefighting/inspection/store/page";

    public static final String FIREFIGHTING_INSPECTION_SMALL_PLACES_ADD = "/firefighting/inspection/smallPlaces/add";
    public static final String FIREFIGHTING_INSPECTION_SMALL_PLACES_UPDATE = "/firefighting/inspection/smallPlaces/update";
    public static final String FIREFIGHTING_INSPECTION_SMALL_PLACES_DELETE = "/firefighting/inspection/smallPlaces/delete/{id}";
    public static final String FIREFIGHTING_INSPECTION_SMALL_PLACES_GET = "/firefighting/inspection/smallPlaces/get/{id}";
    public static final String FIREFIGHTING_INSPECTION_SMALL_PLACES_COUNT = "/firefighting/inspection/smallPlaces/count";
    public static final String FIREFIGHTING_INSPECTION_SMALL_PLACES_PAGE = "/firefighting/inspection/smallPlaces/page";

    public static final String FIREFIGHTING_INSPECTION_ACCOMMODATE_ADD = "/firefighting/inspection/accommodate/add";
    public static final String FIREFIGHTING_INSPECTION_ACCOMMODATE_UPDATE = "/firefighting/inspection/accommodate/update";
    public static final String FIREFIGHTING_INSPECTION_ACCOMMODATE_DELETE = "/firefighting/inspection/accommodate/delete/{id}";
    public static final String FIREFIGHTING_INSPECTION_ACCOMMODATE_GET = "/firefighting/inspection/accommodate/get/{id}";
    public static final String FIREFIGHTING_INSPECTION_ACCOMMODATE_COUNT = "/firefighting/inspection/accommodate/count";
    public static final String FIREFIGHTING_INSPECTION_ACCOMMODATE_PAGE = "/firefighting/inspection/accommodate/page";

    public static final String FIREFIGHTING_TASK_ADD = "/firefighting/task/add";
    public static final String FIREFIGHTING_TASK_UPDATE = "/firefighting/task/update";
    public static final String FIREFIGHTING_TASK_GET = "/firefighting/task/get/{id}";
    public static final String FIREFIGHTING_TASK_PAGE = "/firefighting/task/page";
    public static final String FIREFIGHTING_TASK_ITEM_DELETE = "/firefighting/task/item/delete";
    public static final String FIREFIGHTING_TASK_ITEM_PAGE = "/firefighting/task/item/page";

    public static final String SECURITY_JOURNAL_TYPES = "/security/journal/types";
    public static final String SECURITY_JOURNAL_ADD = "/security/journal/add";
    public static final String SECURITY_JOURNAL_UPDATE = "/security/journal/update";
    public static final String SECURITY_JOURNAL_DELETE = "/security/journal/delete/{id}";
    public static final String SECURITY_JOURNAL_GET = "/security/journal/get/{id}";
    public static final String SECURITY_JOURNAL_PAGE = "/security/journal/page";

    // ---------------------- 物业统计 ----------------------
    public static final String STATISTICS_REALTY_GENERATE = "/statistics/realty/generate";
    public static final String STATISTICS_REALTY_PAGE = "/statistics/realty/page";
    public static final String STATISTICS_REALTY_FIND_BY_DATE = "/statistics/realty/findByDate";
    public static final String STATISTICS_ADVERTISING_GENERATE = "/statistics/advertising/generate";
    public static final String STATISTICS_ADVERTISING_PAGE = "/statistics/advertising/page";
    public static final String STATISTICS_ADVERTISING_FIND_BY_DATE = "/statistics/advertising/findByDate";
}
