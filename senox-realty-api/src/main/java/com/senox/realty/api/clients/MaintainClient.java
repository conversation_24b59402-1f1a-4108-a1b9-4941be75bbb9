package com.senox.realty.api.clients;

import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface MaintainClient {

    /**
     * 添加维修单
     *
     * @param maintainOrderVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_ADD)
    Long addMaintainOrder(@RequestBody MaintainOrderVo maintainOrderVo);

    /**
     * 修改维修单
     *
     * @param maintainOrderVo
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_UPDATE)
    void updateMaintainOrder(@RequestBody MaintainOrderVo maintainOrderVo);

    /**
     * 获取维修单
     *
     * @param id
     * @param media 是否详细
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_ORDER_GET)
    MaintainOrderVo findMaintainOrder(@PathVariable Long id, @RequestParam Boolean media);

    /**
     * 查询维修单列表
     *
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_LIST)
    PageResult<MaintainOrderVo> listMaintainOrder(@RequestBody MaintainOrderSearchVo search);

    /**
     * 删除维修单
     * @param id
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_DELETE)
    void deleteMaintainOrder(@PathVariable Long id);

    /**
     * 维修单列表导出所有处理节点
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_EXPORT_LIST)
    List<MaintainOrderVo> exportListMaintainOrder(@RequestBody MaintainOrderSearchVo searchVo);

    /**
     * 维修单数量
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_COUNT)
    int countMaintainOrder(@RequestBody MaintainOrderSearchVo searchVo);

    /**
     * 维修单费用统计分页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_STATISTIC_PAGE)
    PageResult<MaintainOrderStatisticVo> pageOrderStatistic(@RequestBody MaintainOrderSearchVo search);

    /**
     * 维修单费用合计
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_STATISTIC_SUM)
    MaintainOrderStatisticVo sumOrderStatistic(@RequestBody MaintainOrderSearchVo searchVo);

    /**
     * 维修单评价
     * @param evaluateVo
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_EVALUATE)
    void evaluateOrder(@RequestBody MaintainOrderEvaluateVo evaluateVo);

    /**
     * 重置维修单评价
     * @param orderId
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_ORDER_EVALUATE_RESET)
    void resetEvaluate(@PathVariable Long orderId);

    /**
     * 维修单评价分页
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_EVALUATE_PAGE)
    PageResult<MaintainOrderVo> evaluateOrderPage(@RequestBody MaintainOrderEvaluateSearchVo searchVo);

    /**
     * 添加派工单
     *
     * @param maintainJobVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_JOB_ADD)
    Long addMaintainJob(@RequestBody MaintainJobVo maintainJobVo);

    /**
     * 修改派工单
     * @param maintainJobVo
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_JOB_UPDATE)
    void updateMaintainJob(@RequestBody MaintainJobVo maintainJobVo);

    /**
     * 修改派工人员信息
     * @param maintainJobItemVo
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_ITEM_UPDATE)
    void updateMaintainJobItem(@RequestBody MaintainJobItemVo maintainJobItemVo);

    /**
     * 获取派工单
     *
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_JOB_GET)
    MaintainJobVo findMaintainJob(@PathVariable Long id);


    /**
     * 查询派工单
     *
     * @param orderId
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_JOB_DISPATCH)
    List<MaintainJobVo> listDispatchJobByOrderId(@PathVariable Long orderId);

    /**
     * 查询派工单列表
     *
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_JOB_DISPATCH_LIST)
    PageResult<MaintainDispatchJobVo> listDispatchJob(@RequestBody MaintainJobSearchVo searchVo);

    /**
     * 根据派工子单查询
     * @param itemId
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_JOB_DISPATCH_GET)
    MaintainDispatchJobVo findDispatchByJobItemId(@PathVariable Long itemId);

    /**
     * 删除派工单
     * @param jobId
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_JOB_DELETE)
    void deleteMaintainJob(@PathVariable Long jobId);

    /**
     * 添加维修所需物料
     *
     * @param materialVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_ADD)
    Long saveMaintainMaterial(@RequestBody MaintainMaterialVo materialVo);

    /**
     * 删除维修物料
     *
     * @param id
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_DELETE)
    void deleteMaintainMaterial(@PathVariable Long id);

    /**
     * 批量删除物料及明细
     * @param ids
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_BATCH_DELETE)
    void batchDeleteMaterial(@RequestBody List<Long> ids);

    /**
     * 物料列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_LIST)
    PageResult<MaintainMaterialDataVo> listMaintainMaterial(@RequestBody MaintainMaterialSearchVo search);

    /**
     * 更新出库单号
     * @param outNoVo
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_UPDATE_STATE)
    void saveOutNo(@RequestBody MaintainMaterialOutNoVo outNoVo);

    /**
     * 撤销出库
     * @param outNo
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_REVOKE)
    void cancelOutBound(@PathVariable String outNo);

    /**
     * 获取维修所需物料
     *
     * @param orderId
     * @param jobId
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_GET)
    List<MaintainMaterialItemVo> listMaterialByOrderIdAndJobId(@PathVariable Long orderId, @RequestParam(required = false) Long jobId);

    /**
     * 更新维修物料单
     * @param materialVo
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_UPDATE)
    void updateMaintainMaterial(@RequestBody MaintainMaterialVo materialVo);

    /**
     * 批量添加维修所需物料
     * @param materialItemVos
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_MATERIAL_BATCH_ADD)
    void batchSaveMaterial(@RequestBody List<MaintainMaterialItemVo> materialItemVos);

    /**
     * 添加维修收费帐单
     *
     * @param chargeVo
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_CHARGE_ADD)
    void addMaintainCharge(@RequestBody MaintainChargeVo chargeVo);

    /**
     * 维修收费账单列表
     *
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_CHARGE_LIST)
    MaintainChargePageResult<MaintainChargeDataVo> listMaintainCharge(@RequestBody MaintainChargeSearchVo searchVo);

    /**
     * 维修收费账单详情
     *
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_CHARGE_GET)
    MaintainChargeDataVo chargeDataVoById(@PathVariable Long id);

    /**
     * 根据派工单号查询维修收费账单详情
     * @param jobId
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_CHARGE_GETBYJOBID)
    MaintainChargeDataVo chargeDataVoByJobId(@PathVariable Long jobId);

    /**
     * 根据订单号查询维修收费账单详情
     * @param orderId
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_CHARGE_GET_BY_ORDER_ID)
    List<MaintainChargeDataVo> listChargeDataVoByOrderId(@PathVariable Long orderId);

    /**
     * 根据收费单id查询物维收费单费项
     *
     * @param chargeId
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_CHARGE_GET_CHARGE_ID)
    List<MaintainChargeItemVo> chargeItemList(@PathVariable Long chargeId);

    /**
     * 根据派工id查询物维收费单集合
     *
     * @param jobId
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_CHARGE_GET_JOB_ID)
    List<MaintainChargeItemVo> listChargeItemByJobId(@PathVariable Long jobId);

    /**
     * 根据订单id查询物维收费单集合
     * @param orderId
     * @return
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_CHARGE_GET_ORDER_ID)
    List<MaintainChargeItemVo> listChargeItemByOrderId(@PathVariable Long orderId);

    /**
     * 根据id物维收费单集合
     *
     * @param ids
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_CHARGE_LISTBYIDS)
    List<MaintainChargeVo> listMaintainChargeByIds(@RequestBody List<Long> ids);

    /**
     * 更新支付状态
     *
     * @param billPaid
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_CHARGE_PAID_UPDATE)
    void updateChargeStatus(@Validated @RequestBody BillPaidVo billPaid);

    /**
     * 更新维修账单流水号
     *
     * @param chargeSerial
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_CHARGE_SERIAL_SAVE)
    void saveChargeSerial(@Validated @RequestBody MaintainChargeSerialVo chargeSerial);

    /**
     * 更新账单备注
     * @param chargeRemarkList
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_CHARGE_REMARK_UPDATE)
    void updateChargeRemark(@Validated @RequestBody List<MaintainChargeRemarkVo> chargeRemarkList);

    /**
     * 批量添加维修所需账单
     * @param chargeItemVos
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_CHARGE_BATCH_ADD)
    void batchSaveCharge(@RequestBody List<MaintainChargeItemVo> chargeItemVos);

    /**
     * 删除维修账单
     * @param id
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_CHARGE_DELETE)
    void deleteMaintainCharge(@PathVariable Long id);

    /**
     * 删除维修账单明细
     * @param id
     */
    @GetMapping(RealtyServiceUrl.MAINTAIN_CHARGE_ITEM_DELETE)
    void deleteMaintainChargeItem(@PathVariable Long id);

    /**
     * 物维单日报表分页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_DAY_PAGE)
    PageStatisticsResult<MaintainOrderDayReportVo, MaintainOrderDayReportVo> dayListPage(@RequestBody MaintainOrderDayReportSearchVo search);

    /**
     * 物维单月报表分页
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.MAINTAIN_ORDER_MONTH_PAGE)
    PageStatisticsResult<MaintainOrderMonthReportVo, MaintainOrderMonthReportVo> monthListPage(@RequestBody MaintainOrderMonthReportSearchVo search);
}
