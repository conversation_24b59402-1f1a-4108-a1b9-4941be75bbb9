package com.senox.realty.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.AdvertisingStatisticsVo;
import com.senox.realty.vo.StatisticsGenerateVo;
import com.senox.realty.vo.StatisticsSearchVo;
import com.senox.realty.vo.RealtyStatisticsVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/24 11:53
 */
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface StatisticsClient {

    /**
     * 物业统计生成
     * @param generateVo
     */
    @PostMapping(RealtyServiceUrl.STATISTICS_REALTY_GENERATE)
    void generateRealtyStatistics(@RequestBody StatisticsGenerateVo generateVo);

    /**
     * 物业统计分页
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.STATISTICS_REALTY_PAGE)
    PageResult<RealtyStatisticsVo> realtyStatisticsPageResult(@RequestBody StatisticsSearchVo searchVo);

    /**
     * 根据统计日期获取物业统计记录
     * @param statisticsDate
     * @return
     */
    @GetMapping(RealtyServiceUrl.STATISTICS_REALTY_FIND_BY_DATE)
    RealtyStatisticsVo findRealtyStatisticsByDate(@RequestParam(name = "statisticsDate") @DateTimeFormat(pattern="yyyy-MM-d") LocalDate statisticsDate);

    /**
     * 广告位统计生成
     * @param generateVo
     */
    @PostMapping(RealtyServiceUrl.STATISTICS_ADVERTISING_GENERATE)
    void generateAdvertisingStatistics(@RequestBody StatisticsGenerateVo generateVo);

    /**
     * 广告位统计分页
     * @param searchVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.STATISTICS_ADVERTISING_PAGE)
    PageResult<AdvertisingStatisticsVo> advertisingStatisticsPageResult(@RequestBody StatisticsSearchVo searchVo);

    /**
     * 根据统计日期获取广告位统计记录
     * @param statisticsDate
     * @return
     */
    @GetMapping(RealtyServiceUrl.STATISTICS_ADVERTISING_FIND_BY_DATE)
    AdvertisingStatisticsVo findAdvertisingStatisticsByDate(@RequestParam(name = "statisticsDate") @DateTimeFormat(pattern="yyyy-MM-d") LocalDate statisticsDate);
}
