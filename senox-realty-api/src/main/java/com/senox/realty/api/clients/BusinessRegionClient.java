package com.senox.realty.api.clients;

import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.BusinessRegionVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/19 10:41
 */
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface BusinessRegionClient {

    /**
     * 添加费项
     * @param areaVo
     * @return
     */
    @PostMapping(RealtyServiceUrl.REGION_ADD)
    Long addRegion(@RequestBody BusinessRegionVo areaVo);

    /**
     * 更新费项
     * @param regionVo
     */
    @PostMapping(RealtyServiceUrl.REGION_UPDATE)
    void updateRegion(@RequestBody BusinessRegionVo regionVo);

    /**
     * 获取费项
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.REGION_GET)
    BusinessRegionVo getRegion(@PathVariable Long id);

    /**
     * 费项列表
     * @return
     */
    @PostMapping(RealtyServiceUrl.REGION_LIST)
    List<BusinessRegionVo> listRegion();

}
