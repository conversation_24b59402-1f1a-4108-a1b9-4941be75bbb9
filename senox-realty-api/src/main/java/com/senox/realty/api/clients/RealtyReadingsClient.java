package com.senox.realty.api.clients;

import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/1 14:33
 */
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface RealtyReadingsClient {

    /**
     * 批量添加水电读数
     * @param batchWeData
     */
    @PostMapping(RealtyServiceUrl.REALTY_READINGS_WE_ADD)
    void batchAddWe(@RequestBody RealtyWeBatchVo batchWeData);

    /**
     * 更新水电读数
     * @param we
     */
    @PostMapping(RealtyServiceUrl.REALTY_READINGS_WE_UPDATE)
    void updateWe(@RequestBody RealtyWeVo we);

    /**
     * 删除水电读数
     * @param ids
     */
    @PostMapping(RealtyServiceUrl.REALTY_READINGS_WE_DELETE)
    void deleteWe(@RequestBody List<Long> ids);

    /**
     * 重置月水电读数
     * @param month
     */
    @PostMapping(RealtyServiceUrl.REALTY_READINGS_WE_RESET)
    void resetWeReadings(@RequestBody BillMonthVo month);

    /**
     * 校验月水电读数是否已导入
     * @param month
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_READINGS_WE_CHECK)
    boolean checkWeReadings(@RequestBody BillMonthVo month);

    /**
     * 根据id获取水电读数
     * @param id
     * @return
     */
    @GetMapping(RealtyServiceUrl.REALTY_READINGS_WE_GET)
    RealtyWeVo findWeById(@PathVariable Long id);

    /**
     * 水电读数列表
     * @param search
     * @return
     */
    @PostMapping(RealtyServiceUrl.REALTY_READINGS_WE_LIST)
    RealtyWePageResult<RealtyWeVo> listWePage(@RequestBody RealtyWeSearchVo search);
}
