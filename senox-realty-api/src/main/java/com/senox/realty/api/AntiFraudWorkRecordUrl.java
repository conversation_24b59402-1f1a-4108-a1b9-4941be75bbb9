package com.senox.realty.api;

/**
 * <AUTHOR>
 * @date 2025-04-01
 **/
public class AntiFraudWorkRecordUrl {
    private AntiFraudWorkRecordUrl() {
    }

    public static final String ANTI_FRAUD_WORK_RECORD_ADD = "/anti/fraud/work/record/add";
    public static final String ANTI_FRAUD_WORK_RECORD_UPDATE = "/anti/fraud/work/record/update";
    public static final String ANTI_FRAUD_WORK_RECORD_FIND_BY_ID = "/anti/fraud/work/record/findById/{id}";
    public static final String ANTI_FRAUD_WORK_RECORD_DELETE_BY_ID = "/anti/fraud/work/record/deleteById";
    public static final String ANTI_FRAUD_WORK_RECORD_LIST_COUNT = "/anti/fraud/work/record/list/count";
    public static final String ANTI_FRAUD_WORK_RECORD_LIST = "/anti/fraud/work/record/list";
    public static final String ANTI_FRAUD_WORK_RECORD_LIST_PAGE = "/anti/fraud/work/record/list/page";
}
