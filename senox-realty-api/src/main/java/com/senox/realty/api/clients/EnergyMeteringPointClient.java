package com.senox.realty.api.clients;

import com.senox.realty.vo.EnergyPointRefreshResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import static com.senox.realty.api.EnergyMeteringPointUrl.ENERGY_METERING_POINT_REFRESH;
import static com.senox.realty.api.RealtyServiceUrl.SERVICE_NAME;

/**
 * <AUTHOR>
 * @date 2024-12-11
 **/
@FeignClient(SERVICE_NAME)
public interface EnergyMeteringPointClient {

    /**
     * 计量点刷新
     * @param meteringPointCode 计量点编码
     * @return 返回刷新结果
     */
    @GetMapping(ENERGY_METERING_POINT_REFRESH)
    EnergyPointRefreshResult refresh(@RequestParam(required = false) String meteringPointCode);
}
