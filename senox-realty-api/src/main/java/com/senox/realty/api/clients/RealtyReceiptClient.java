package com.senox.realty.api.clients;


import com.senox.common.vo.PageResult;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.realty.api.RealtyServiceUrl;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.realty.api.RealtyReceiptServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2023-3-23
 */
@FeignClient(RealtyServiceUrl.SERVICE_NAME)
public interface RealtyReceiptClient {

    /**
     * 物业发票申请
     *
     * @param receiptManger 物业发票管理
     */
    @PostMapping(APPLY)
    void apply(@Validated @RequestBody RealtyReceiptMangerVo receiptManger);

    /**
     * pc物业发票申请
     *
     * @param receiptManger 物业发票管理
     */
    @PostMapping(PC_APPLY)
    void pcApply(@Validated @RequestBody RealtyReceiptMangerVo receiptManger);

    /**
     * 物业发票申请列表
     *
     * @param search 查询参数
     * @return 分页申请列表
     */
    @PostMapping(APPLY_LIST)
    PageResult<RealtyReceiptVo> applyList(@RequestBody RealtyReceiptSearchVo search);

    /**
     * 物业发票申请账单信息列表
     * @param id 物业发票申请id
     * @return 申请账单信息列表
     */
    @GetMapping(APPLY_BILL_INFO_LIST)
    List<RealtyBillReceiptApplyInfoVo> applyBillInfoList(@PathVariable Long id);


    /**
     * 发票申请列表
     *
     * @param id     物业发票申请id
     * @param detail 是否详细
     * @return 发票申请列表
     */
    @GetMapping(APPLY_INFO_LIST)
    List<ReceiptApplyVo> applyInfoList(@PathVariable Long id, @PathVariable Boolean detail);

    /**
     * 物业发票申请审核
     * @param receiptApplyAudit 发票审核
     */
    @PostMapping(APPLY_AUDIT)
    void applyAudit(@RequestBody ReceiptApplyAuditVo receiptApplyAudit);
}
