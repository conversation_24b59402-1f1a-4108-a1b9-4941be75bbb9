package com.senox.realty.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("r_realty_alias")
public class RealtyAlias extends BaseEntity {
    /**
     * id
     */
    private Long id;
    /**
     * 父id
     */
    private Long parentId;
    /**
     * 档位编号
     */
    private String serialNo;
    /**
     * 档位名称
     */
    private String name;

}
