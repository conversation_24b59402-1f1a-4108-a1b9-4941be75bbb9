package com.senox.realty.controller;


import com.senox.realty.BaseControllerTest;
import com.senox.realty.vo.RealtyDepositVo;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/11/8 14:21
 */
class RealtyDepositControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(RealtyDepositControllerTest.class);

    @Test
    void addDeposit() {
        RealtyDepositVo deposit = mockDeposit();
        HttpEntity<RealtyDepositVo> entity1 = new HttpEntity<>(deposit);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/deposit/realty/add",
                HttpMethod.POST, entity1, new ParameterizedTypeReference<Long>() {});
        logger.info("response1: {}", responseEntity1);
    }

    private RealtyDepositVo mockDeposit() {
        RealtyDepositVo result = new RealtyDepositVo();
        result.setContractId(randLong(1, 100));
        result.setAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(100L), 2));
        return result;
    }
}