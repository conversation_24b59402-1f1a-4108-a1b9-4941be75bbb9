package com.senox.realty.controller;

import com.senox.realty.BaseControllerTest;
import com.senox.realty.vo.BusinessRegionVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/23 15:13
 */
class BusinessRegionControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(BusinessRegionControllerTest.class);

    @Test
    void addAndUpdate() {
        BusinessRegionVo areaVo1 = mockFeeAreaVo();
        HttpEntity<BusinessRegionVo> entity1 = new HttpEntity<>(areaVo1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/businessRegion/add",
                HttpMethod.POST, entity1, new ParameterizedTypeReference<Long>() {});
        logger.info("response1: {}", responseEntity1);

        Long result1 = responseEntity1.getBody();
        Assertions.assertTrue(result1 > 0);

        ResponseEntity<BusinessRegionVo> responseEntity2 = restTemplate.exchange("/businessRegion/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<BusinessRegionVo>() {});
        BusinessRegionVo result2 = responseEntity2.getBody();
        Assertions.assertEquals(areaVo1.getName(), result2.getName());

        BusinessRegionVo areaVo3 = new BusinessRegionVo();
        areaVo3.setId(result1);
        areaVo3.setName(randStr(5));
        HttpEntity<BusinessRegionVo> entity3 = new HttpEntity<>(areaVo3);
        ResponseEntity<Void> responseEntity3 = restTemplate.exchange("/businessRegion/update",
                HttpMethod.POST, entity3, new ParameterizedTypeReference<Void>() {});
        Assertions.assertEquals(HttpStatus.OK, responseEntity3.getStatusCode());

        ResponseEntity<BusinessRegionVo> responseEntity4 = restTemplate.exchange("/businessRegion/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<BusinessRegionVo>() {});
        BusinessRegionVo result4 = responseEntity4.getBody();
        Assertions.assertEquals(areaVo3.getName(), result4.getName());
    }

    @Test
    void list() {
        for (int i = 0; i < 16; i++) {
            BusinessRegionVo areaVo = mockFeeAreaVo();
            HttpEntity<BusinessRegionVo> entity = new HttpEntity<>(areaVo);
            restTemplate.exchange("/businessRegion/add", HttpMethod.POST, entity, new ParameterizedTypeReference<Long>() {});
        }

        ResponseEntity<List<BusinessRegionVo>> responseEntity = restTemplate.exchange("/businessRegion/list", HttpMethod.POST,
                null, new ParameterizedTypeReference<List<BusinessRegionVo>>() {});
        List<BusinessRegionVo> result = responseEntity.getBody();
        Assertions.assertTrue(result.size() >= 16);
    }


    private BusinessRegionVo mockFeeAreaVo() {
        BusinessRegionVo result = new BusinessRegionVo();
        result.setName(randStr(30));
        return result;
    }

}
