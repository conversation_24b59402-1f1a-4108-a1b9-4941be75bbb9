package com.senox.realty.controller;

import com.senox.realty.BaseControllerTest;
import com.senox.realty.constant.PriceType;
import com.senox.realty.vo.WaterElectricPriceTypeVo;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/29 11:47
 */
class WaterElectricPriceTypeControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(WaterElectricPriceTypeControllerTest.class);

    @Test
    void test() {
        // add
        WaterElectricPriceTypeVo type1 = mockWaterElectricPriceType();
        HttpEntity<WaterElectricPriceTypeVo> entity1 = new HttpEntity<>(type1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/waterElectricPriceType/add",
                HttpMethod.POST, entity1, new ParameterizedTypeReference<Long>() {});
        logger.info("response1: {}", responseEntity1);

        // list
        ResponseEntity<List<WaterElectricPriceTypeVo>> responseEntity2 = restTemplate.exchange("/waterElectricPriceType/list",
                HttpMethod.POST, null, new ParameterizedTypeReference<List<WaterElectricPriceTypeVo>>() {});
        logger.info("response2: {}", responseEntity2);

        // delete
        WaterElectricPriceTypeVo type3 = new WaterElectricPriceTypeVo();
        type3.setId(responseEntity1.getBody());
        type3.setDisabled(Boolean.TRUE);
        HttpEntity<WaterElectricPriceTypeVo> entity3 = new HttpEntity<>(type3);
        ResponseEntity<Void> responseEntity3 = restTemplate.exchange("/waterElectricPriceType/update",
                HttpMethod.POST, entity3, new ParameterizedTypeReference<Void>() {});
        logger.info("response1: {}", responseEntity3);
    }

    private WaterElectricPriceTypeVo mockWaterElectricPriceType() {
        WaterElectricPriceTypeVo result = new WaterElectricPriceTypeVo();
        result.setName(randStr(10));
        result.setType(PriceType.fromValue(randInt(1, 2)).getValue());
        result.setPrice(randDecimal(BigDecimal.ZERO, BigDecimal.TEN, 2));
        return result;
    }
}
