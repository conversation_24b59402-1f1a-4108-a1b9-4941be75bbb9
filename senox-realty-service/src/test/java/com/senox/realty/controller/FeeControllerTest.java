package com.senox.realty.controller;

import com.senox.realty.BaseControllerTest;
import com.senox.realty.constant.FeeCategory;
import com.senox.realty.vo.FeeVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/24 8:44
 */
class FeeControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(FeeController.class);

    @Test
    void addAndUpdate() {
        FeeVo feeVo1 = mockFee();
        HttpEntity<FeeVo> entity1 = new HttpEntity<>(feeVo1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/fee/add",
                HttpMethod.POST, entity1, new ParameterizedTypeReference<Long>() {});
        logger.info("response1: {}", responseEntity1);
        Long result1 = responseEntity1.getBody();
        Assertions.assertTrue(result1 > 0);

        ResponseEntity<FeeVo> responseEntity2 = restTemplate.exchange("/fee/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<FeeVo>() {});
        FeeVo result2 = responseEntity2.getBody();
        Assertions.assertEquals(feeVo1.getName(), result2.getName());

        FeeVo feeVo3 = new FeeVo();
        feeVo3.setId(result1);
        feeVo3.setName(randStr(20));
        HttpEntity<FeeVo> entity3 = new HttpEntity<>(feeVo3);
        ResponseEntity<Void> responseEntity3 = restTemplate.exchange("/fee/update",
                HttpMethod.POST, entity3, new ParameterizedTypeReference<Void>() {});
        Assertions.assertEquals(HttpStatus.OK, responseEntity3.getStatusCode());

        ResponseEntity<FeeVo> responseEntity4 = restTemplate.exchange("/fee/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<FeeVo>() {});
        FeeVo result4 = responseEntity4.getBody();
        Assertions.assertEquals(feeVo3.getName(), result4.getName());
    }

    @Test
    void list() {
        for (int i = 0; i < 16; i++) {
            FeeVo feeVo = mockFee();
            HttpEntity<FeeVo> entity = new HttpEntity<>(feeVo);
            restTemplate.exchange("/fee/add", HttpMethod.POST, entity, new ParameterizedTypeReference<Long>() {});
        }

        ResponseEntity<List<FeeVo>> responseEntity = restTemplate.exchange("/fee/list",
                HttpMethod.POST, null, new ParameterizedTypeReference<List<FeeVo>>() {});
        List<FeeVo> result = responseEntity.getBody();
        Assertions.assertTrue(result.size() >= 16);
    }

    private FeeVo mockFee() {
        FeeVo result = new FeeVo();
        result.setId(randLong(1, 100));
        result.setName(randStr(20));
        result.setAlias(randStr(20));
        result.setCategory(randInt(1, FeeCategory.values().length));
        return result;
    }
}
