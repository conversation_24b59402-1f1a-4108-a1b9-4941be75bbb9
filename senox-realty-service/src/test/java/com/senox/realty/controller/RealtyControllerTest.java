package com.senox.realty.controller;

import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.BaseControllerTest;
import com.senox.realty.constant.RealtyNature;
import com.senox.realty.domain.Realty;
import com.senox.realty.service.RealtyService;
import com.senox.realty.vo.RealtySearchVo;
import com.senox.realty.vo.RealtyVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020/12/23 9:54
 */
class RealtyControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(RealtyControllerTest.class);

    @Autowired
    private RealtyService realtyService;

    @Test
    void addRealty() {
        RealtyVo realtyVo1 = mockRealtyVo();
        HttpEntity<RealtyVo> entity1 = new HttpEntity<>(realtyVo1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/realty/add",
                HttpMethod.POST, entity1, new ParameterizedTypeReference<Long>() {});
        logger.info("response1: {}", responseEntity1);
        Assertions.assertEquals(HttpStatus.OK, responseEntity1.getStatusCode());
        Long result1 = responseEntity1.getBody();
        Assertions.assertTrue(result1 > 0L);

        Realty realty = realtyService.findById(result1);
        Assertions.assertEquals(realtyVo1.getSerialNo(), realty.getSerialNo());
        Assertions.assertEquals(realtyVo1.getName(), realty.getName());
        Assertions.assertEquals(realtyVo1.getNature(), realty.getNature());
        Assertions.assertEquals(realtyVo1.getOwnerId(), realty.getOwnerId());
        Assertions.assertEquals(realtyVo1.getOwnerName(), realty.getOwnerName());
    }

    @Test
    void updateRealty() {
        RealtyVo realtyVo1 = mockRealtyVo();
        HttpEntity<RealtyVo> entity1 = new HttpEntity<>(realtyVo1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/realty/add",
                HttpMethod.POST, entity1, new ParameterizedTypeReference<Long>() {});
        Assertions.assertTrue(responseEntity1.getBody() > 0);

        RealtyVo updateRealtyVo = new RealtyVo();
        updateRealtyVo.setId(responseEntity1.getBody());
        updateRealtyVo.setNature(RealtyNature.fromValue(randInt(0, RealtyNature.values().length)).ordinal());
        updateRealtyVo.setArea(BigDecimal.valueOf(35));
        updateRealtyVo.setOwnerId(randLong(1L, 100L));
        updateRealtyVo.setOwnerName(randStr(20));
        HttpEntity<RealtyVo> entity2 = new HttpEntity<>(updateRealtyVo);
        ResponseEntity<Void> responseEntity2 = restTemplate.exchange("/realty/update",
                HttpMethod.POST, entity2, new ParameterizedTypeReference<Void>() {});
        logger.info("response2: {}", responseEntity2);
        Assertions.assertEquals(HttpStatus.OK, responseEntity2.getStatusCode());

        ResponseEntity<RealtyVo> responseEntity3 = restTemplate.exchange("/realty/get/" + responseEntity1.getBody(),
                HttpMethod.GET, null, new ParameterizedTypeReference<RealtyVo>() {});
        logger.info("response3: {}", responseEntity3);
        Assertions.assertTrue(DecimalUtils.equals(updateRealtyVo.getArea(), responseEntity3.getBody().getArea()));
        Assertions.assertEquals(updateRealtyVo.getNature(), responseEntity3.getBody().getNature());
        Assertions.assertEquals(updateRealtyVo.getOwnerId(), responseEntity3.getBody().getOwnerId());
        Assertions.assertEquals(updateRealtyVo.getOwnerName(), responseEntity3.getBody().getOwnerName());
    }

    @Test
    void listRealty() {
        for (int i = 0; i < 16; i++) {
            RealtyVo realtyVo = mockRealtyVo();
            HttpEntity<RealtyVo> entity = new HttpEntity<>(realtyVo);
            restTemplate.exchange("/realty/add",
                    HttpMethod.POST, entity, new ParameterizedTypeReference<Long>() {});
        }

        RealtySearchVo searchVo = new RealtySearchVo(2, 10);
        HttpEntity<RealtySearchVo> entity = new HttpEntity<>(searchVo);
        ResponseEntity<PageResult<RealtyVo>> responseEntity = restTemplate.exchange("/realty/list",
                HttpMethod.POST, entity, new ParameterizedTypeReference<PageResult<RealtyVo>>() {});
        logger.info("response: {}", JsonUtils.object2Json(responseEntity));
        Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());

        PageResult<RealtyVo> result = responseEntity.getBody();
        Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());

        Assertions.assertFalse(result.getDataList().isEmpty());
    }

    private RealtyVo mockRealtyVo() {
        RealtyVo result = new RealtyVo();
        result.setSerialNo(randStr(9));
        result.setName(randStr(20));
        result.setNature(RealtyNature.fromValue(randInt(0, RealtyNature.values().length)).ordinal());
        result.setArea(randDecimal(BigDecimal.TEN, BigDecimal.valueOf(50), 2));
        result.setRegionId(randLong(1L, 100L));
        result.setRegionName(randStr(20));
        result.setStreetId(randLong(1L, 100L));
        result.setStreetName(randStr(20));
        result.setAddress(randStr(30));
        result.setOwnerId(randLong(1L, 100L));
        result.setOwnerName(randStr(20));
        return result;
    }
}
