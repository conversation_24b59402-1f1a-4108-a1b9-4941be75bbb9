package com.senox.realty;


import com.senox.common.utils.JsonUtils;
import com.senox.realty.config.RealtyConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @Date 2020/12/14 15:29
 */
@Slf4j
@SpringBootTest(classes = RealtyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class RealtyApplicationTest {

    @Autowired
    private RealtyConfig realtyConfig;

    @Test
    void contextLoads() {
        log.info("config: {}", JsonUtils.object2Json(realtyConfig.getEnergySources()));
    }

}