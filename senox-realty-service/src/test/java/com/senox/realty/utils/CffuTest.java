package com.senox.realty.utils;

import com.senox.common.exception.BusinessException;
import com.senox.realty.BaseTest;
import io.foldright.cffu.Cffu;
import io.foldright.cffu.CffuFactory;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;

@Slf4j
class CffuTest extends BaseTest {

    @Value("${senox.executor.pool.threadName:senox-realty-thread-}")
    private String executorThreadName;

    @Autowired
    public CffuFactory cffuFactory;

    @Test
    void cffu() {
        List<Cffu<Void>> futures = new ArrayList<>(100);
        final int size = 5;
        for (int i = 0; i < size; i++) {
            futures.add(cffuFactory.runAsync(() -> {
                if (!Thread.currentThread().getName().contains(executorThreadName)) {
                    throw new BusinessException("cffu线程比较异常");
                }
            }));
        }
        cffuFactory.allOf(futures.toArray(new Cffu[0])).join();
    }
}
