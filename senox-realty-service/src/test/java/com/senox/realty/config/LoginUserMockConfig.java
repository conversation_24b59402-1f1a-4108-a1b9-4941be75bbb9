package com.senox.realty.config;

import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.realty.BaseTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.servlet.*;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2021/1/11 14:37
 */
@Configuration
public class LoginUserMockConfig {

    @Bean
    @Profile("test")
    public LoginUserMockFilter adminUserMockFilter() {
        return new LoginUserMockFilter();
    }

    public class LoginUserMockFilter implements Filter {

        @Override
        public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
                throws IOException, ServletException {
            AdminUserDto adminUser = new AdminUserDto();
            adminUser.setUserId(BaseTest.randLong(5, 100));
            adminUser.setUsername(BaseTest.randStr(10));
            adminUser.setToken(BaseTest.randStr(20));
            AdminContext.setUser(adminUser);

            filterChain.doFilter(servletRequest, servletResponse);
        }
    }


}
