package com.senox.realty.service;

import com.senox.common.vo.PageResult;
import com.senox.realty.BaseTest;
import com.senox.realty.convert.MaintainJobConvertor;
import com.senox.realty.domain.MaintainJob;
import com.senox.realty.domain.MaintainOrder;
import com.senox.realty.vo.MaintainDispatchJobVo;
import com.senox.realty.vo.MaintainJobSearchVo;
import com.senox.realty.vo.MaintainJobVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/4 8:48
 */
class MaintainJobServiceTest extends BaseTest {

    @Autowired
    private MaintainJobService maintainJobService;

    @Autowired
    private MaintainOrderService maintainOrderService;

    @Autowired
    private MaintainJobConvertor convertor;

    //@Test
    void addJob() {
        MaintainJobVo jobVo = new MaintainJobVo();
        jobVo.setOrderId(4L);
        jobVo.setMaintainType(1);
        jobVo.setStatus(0);
        MaintainJob job = convertor.toDo(jobVo);
        job.setCreatorId(randLong(1L, 100L));
        job.setCreatorName(randStr(10));
        job.setModifierId(job.getCreatorId());
        job.setModifierName(job.getCreatorName());
        Long aLong = maintainJobService.addMaintainJob(job, jobVo);
        Assertions.assertTrue(aLong > 0L);
        MaintainJob byId = maintainJobService.findById(aLong);
        Assertions.assertEquals(job.getJobNo(), byId.getJobNo());
        MaintainOrder byId1 = maintainOrderService.findById(4L);
        Assertions.assertNotNull(byId1);
    }

    @Test
    void dispatch() {
        List<MaintainJobVo> dispatch = maintainJobService.listDispatchByOrderId(2L);
        Assertions.assertTrue(!dispatch.isEmpty());
    }

    @Test
    void allDispatch() {
        MaintainJobSearchVo jobSearchVo = new MaintainJobSearchVo();
        jobSearchVo.setPageNo(1);
        jobSearchVo.setPageSize(3);
        jobSearchVo.setDateStart(LocalDateTime.of(2023, 3, 2, 12, 30));
        jobSearchVo.setDateEnd(LocalDateTime.of(2023, 4, 6, 12, 39));
//        jobSearchVo.setOrderNo("orderNo1");
//        jobSearchVo.setHandlerName("张晓霞");
//        jobSearchVo.setMaintainType(0);
//        jobSearchVo.setContact("");
//        jobSearchVo.setStatus(0);
        PageResult<MaintainDispatchJobVo> maintainDispatchJobVoPageResult = maintainJobService.listDispatchJob(jobSearchVo);
        Assertions.assertTrue(maintainDispatchJobVoPageResult.getDataList().isEmpty());
    }
}
