package com.senox.realty.service;

import com.senox.realty.BaseTest;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.domain.EnergyConsumeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/8/31 14:07
 */
class EnergyConsumeUnitServiceTest extends BaseTest {

    @Autowired
    private EnergyConsumeUnitService energyConsumeUnitService;

    @Test
    void testConsumeUnit() {
        EnergyConsumeUnit unit1 = mockConsumeUnit();
        Long id1 = energyConsumeUnitService.addUnit(unit1);

        EnergyConsumeUnit dbUnit1 = energyConsumeUnitService.findById(id1);
        Assertions.assertEquals(unit1, dbUnit1);

        List<EnergyConsumeUnit> list = energyConsumeUnitService.listByType(EnergyType.fromValue(unit1.getType()));
        Assertions.assertEquals(1, list.size());
    }

    private EnergyConsumeUnit mockConsumeUnit() {
        EnergyConsumeUnit result = new EnergyConsumeUnit();
        result.setUnit(randStr(3));
        result.setName(randStr(10));
        result.setType(EnergyType.values()[randInt(0, EnergyType.values().length - 1)].getValue());
        result.setCreatorId(randLong(1L, 100L));
        result.setCreatorName(randStr(10));
        result.setModifierId(result.getCreatorId());
        result.setModifierName(result.getCreatorName());
        return result;
    }

}
