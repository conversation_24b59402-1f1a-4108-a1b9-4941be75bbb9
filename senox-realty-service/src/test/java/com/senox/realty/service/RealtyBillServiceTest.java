package com.senox.realty.service;

import com.senox.common.vo.PageResult;
import com.senox.pm.api.clients.ReceiptApplyClient;
import com.senox.pm.api.clients.ReceiptClient;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.constant.ReceiptType;
import com.senox.pm.constant.TaxCategory;
import com.senox.pm.vo.ReceiptApplySearchVo;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.pm.vo.TaxHeaderVo;
import com.senox.realty.BaseTest;
import com.senox.realty.config.ReceiptConfig;
import com.senox.realty.vo.RealtyReceiptMangerVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-7-20
 */
@Slf4j
class RealtyBillServiceTest extends BaseTest {

    @Autowired
    private RealtyReceiptService  realtyReceiptService;
    @Autowired
    private ReceiptConfig receiptConfig;
    @MockBean
    private ReceiptClient receiptClient;
    @Autowired
    private ReceiptApplyClient receiptApplyClient;

   // @BeforeEach
    void receiptConfigTest() {
        Assertions.assertFalse(CollectionUtils.isEmpty(receiptConfig.getGoods()));
        Mockito.doReturn(mockTaxHeader()).when(receiptClient).getTaxHeader(99L);
    }

   // @Test
    void auditTest(){
        realtyReceiptService.audit(1L, ReceiptStatus.AUDIT_APPROVED,"",1L);
    }

    //@Test
    void receiptApplyListTest(){
        ReceiptApplySearchVo search = new ReceiptApplySearchVo();
        search.setPageNo(1);
        search.setPageSize(10);
        search.setPage(false);
        PageResult<ReceiptApplyVo> pageResult = receiptApplyClient.list(search);
        List<ReceiptApplyVo> receiptApplyList = pageResult.getDataList();



    }

    private TaxHeaderVo mockTaxHeader(){
        TaxHeaderVo taxHeaderVo = new TaxHeaderVo();
        taxHeaderVo.setId(99L);
        taxHeaderVo.setHeader("东莞市金永利粮油食品有限公司");
        taxHeaderVo.setSerial("9144190073461310P");
        taxHeaderVo.setRegisterAddress("address");
        taxHeaderVo.setRegisterMobile("***********");
        taxHeaderVo.setEnterpriseBankAccount("11");
        taxHeaderVo.setOpenid("oGxwBv1L5yEEeyaJb5wyZ9TDsQho");
        taxHeaderVo.setCategory(TaxCategory.COMPANY.getValue());
        return taxHeaderVo;
    }
}
