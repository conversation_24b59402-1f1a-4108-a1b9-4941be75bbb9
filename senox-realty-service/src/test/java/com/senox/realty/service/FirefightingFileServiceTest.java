package com.senox.realty.service;

import com.senox.realty.BaseTest;
import com.senox.realty.vo.FirefightingFileSearchVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/3/6 15:03
 */
class FirefightingFileServiceTest extends BaseTest {

    @Autowired
    private FirefightingFileService fileService;

    @Test
    void countFile() {
        fileService.countFile(new FirefightingFileSearchVo());
    }

}