package com.senox.realty.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.realty.BaseTest;
import com.senox.realty.constant.RealtyNature;
import com.senox.realty.domain.Realty;
import com.senox.realty.domain.RealtyExt;
import com.senox.realty.vo.RealtySearchVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/12/16 15:09
 */
class RealtyServiceTest extends BaseTest {

    @Autowired
    public RealtyService realtyService;

    @BeforeEach
    void setUp(){
        // 数字初始化
        String serialNo1 = randStr(12);
        List<Realty> realtyList1 = mockRealty(18);
        realtyList1.get(0).setSerialNo(serialNo1);
        for (Realty item : realtyList1) {
            realtyService.addRealty(item, null);
        }

        Long regionId = randLong(50, 145);
        String regionName = randStr(30);
        List<Realty> realtyList2 = mockRealty(5);
        realtyList2.forEach(x -> {
            x.setRegionId(regionId);
            x.setRegionName(regionName);
            realtyService.addRealty(x, null);
        });
    }

    @Test
    void addRealty() {
        Realty realty = mockRealty();
        RealtyExt ext1 = mockRealtyExt();
        Long result = realtyService.addRealty(realty, ext1);
        Assertions.assertTrue(result > 0);

        Realty dbRealty = realtyService.findById(result);
        Assertions.assertEquals(realty, dbRealty);
        RealtyExt dbExt1 = realtyService.findExtByRealtyId(result);
        Assertions.assertEquals(ext1.getRemark(), dbExt1.getRemark());

        Realty updateRealty = new Realty();
        updateRealty.setId(result);
        updateRealty.setName(randStr(10));
        updateRealty.setNature(RealtyNature.fromValue(randInt(0, RealtyNature.values().length)).ordinal());
        updateRealty.setRegionId(randLong(1, 100));
        updateRealty.setRegionName(randStr(20));
        updateRealty.setProfessionId(randLong(1L, 10L));
        updateRealty.setProfessionName(randStr(20));
        updateRealty.setOwnerId(0L);
        updateRealty.setOwnerName(StringUtils.EMPTY);
        updateRealty.setModifierId(randLong(0, 100));
        updateRealty.setModifierName(randStr(10));
        RealtyExt ext2 = mockRealtyExt();
        Assertions.assertTrue(realtyService.updateRealty(updateRealty, ext2));

        dbRealty = realtyService.findById(result);
        Assertions.assertEquals(updateRealty.getName(), dbRealty.getName());
        Assertions.assertEquals(updateRealty.getNature(), dbRealty.getNature());
        Assertions.assertEquals(updateRealty.getRegionId(), dbRealty.getRegionId());
        Assertions.assertEquals(updateRealty.getRegionName(), dbRealty.getRegionName());
        Assertions.assertEquals(updateRealty.getProfessionId(), dbRealty.getProfessionId());
        Assertions.assertEquals(updateRealty.getProfessionName(), dbRealty.getProfessionName());
        Assertions.assertEquals(updateRealty.getOwnerId(), dbRealty.getOwnerId());
        Assertions.assertEquals(updateRealty.getNature(), dbRealty.getNature());
        Assertions.assertEquals(updateRealty.getOwnerName(), dbRealty.getOwnerName());
        Assertions.assertEquals(realty.getArea(), dbRealty.getArea());
        Assertions.assertEquals(realty.getStreetId(), dbRealty.getStreetId());
        Assertions.assertEquals(realty.getStreetName(), dbRealty.getStreetName());


        RealtyExt dbExt2 = realtyService.findExtByRealtyId(result);
        Assertions.assertEquals(ext2.getRemark(), dbExt2.getRemark());
    }

    @Test
    void testSerialNoUnique() {
        String serialNo = randStr(9);
        Realty realty1 = mockRealty();
        realty1.setSerialNo(serialNo);
        Long result1 = realtyService.addRealty(realty1, null);
        Assertions.assertTrue(result1 > 0);

        Realty realty2 = mockRealty();
        realty2.setSerialNo(serialNo);
        BusinessException exception2 = Assertions.assertThrows(BusinessException.class, () -> {
            realtyService.addRealty(realty2, null);
        });
        Assertions.assertEquals(ResultConst.DUPLICATE_ERROR.getCode(), exception2.getCode());

        Realty realty3 = mockRealty();
        Long result3 = realtyService.addRealty(realty3, null);
        Assertions.assertTrue(result3 > 0);

        Realty updateRealty3 = new Realty();
        updateRealty3.setId(result3);
        updateRealty3.setSerialNo(serialNo);
        updateRealty3.setModifierId(randLong(1, 100));
        updateRealty3.setModifierName(randStr(20));
        BusinessException exception3 = Assertions.assertThrows(BusinessException.class, () -> {
            realtyService.updateRealty(updateRealty3, null);
        });
        Assertions.assertEquals(ResultConst.DUPLICATE_ERROR.getCode(), exception3.getCode());

        updateRealty3.setSerialNo(randStr(9));
        Assertions.assertTrue(realtyService.updateRealty(updateRealty3, null));

        Realty dbRealty3 = realtyService.findById(result3);
        Assertions.assertEquals(updateRealty3.getSerialNo(), dbRealty3.getSerialNo());
    }

    @Test
    void listRealty() {
        // 数字初始化
        String serialNo1 = randStr(12);
        List<Realty> realtyList1 = mockRealty(18);
        realtyList1.get(0).setSerialNo(serialNo1);
        for (Realty item : realtyList1) {
            realtyService.addRealty(item, null);
        }

        Long regionId = randLong(250, 400);
        String regionName = randStr(30);
        List<Realty> realtyList2 = mockRealty(5);
        realtyList2.forEach(x -> {
            x.setRegionId(regionId);
            x.setRegionName(regionName);
            realtyService.addRealty(x, null);
        });

        // 查询
        RealtySearchVo searchVo = new RealtySearchVo(1, 10);
        List<Realty> list = realtyService.listRealty(searchVo);
        Assertions.assertEquals(searchVo.getPageSize(), list.size());

        //
        searchVo.setRegionId(regionId);
        list = realtyService.listRealty(searchVo);
        Assertions.assertEquals(realtyList2.size(), list.size());
        Assertions.assertTrue(list.stream().allMatch(x -> Objects.equals(regionName, x.getRegionName())));
    }

    private List<Realty> mockRealty(int size) {
        List<Realty> resultList = new ArrayList<>(size);
        while (size > 0) {
            resultList.add(mockRealty());
            size--;
        }
        return resultList;
    }

    private Realty mockRealty() {
        Realty result = new Realty();
        result.setSerialNo(randStr(12));
        result.setName(randStr(20));
        result.setNature(RealtyNature.fromValue(randInt(0, RealtyNature.values().length)).ordinal());
        result.setArea(randDecimal(BigDecimal.TEN, BigDecimal.valueOf(50), 2));
        result.setRegionId(randLong(1L, 100L));
        result.setRegionName(randStr(20));
        result.setStreetId(randLong(1L, 100L));
        result.setStreetName(randStr(20));
        result.setAddress(randStr(20));
        result.setProfessionId(randLong(1L, 10L));
        result.setProfessionName(randStr(20));
        result.setOwnerId(randLong(1L, 100L));
        result.setOwnerName(randStr(10));
        result.setCreatorId(randLong(0, 100));
        result.setCreatorName(randStr(10));
        result.setModifierId(randLong(0, 100));
        result.setModifierName(randStr(10));
        return result;
    }

    private RealtyExt mockRealtyExt() {
        RealtyExt result = new RealtyExt();
        result.setRemark(randStr(30));
        return result;
    }

}