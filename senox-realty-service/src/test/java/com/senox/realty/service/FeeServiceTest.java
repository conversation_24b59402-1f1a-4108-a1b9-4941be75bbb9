package com.senox.realty.service;


import com.senox.realty.BaseTest;
import com.senox.realty.constant.FeeCategory;
import com.senox.realty.domain.Fee;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/12/15 13:49
 */
class FeeServiceTest extends BaseTest {

    @Autowired
    private FeeService feeService;

    @Test
    void testFee() {
        Fee fee1 = mockFee();
        Fee fee2 = mockFee();
        long result1 = feeService.addFee(fee1);
        long result2 = feeService.addFee(fee2);
        Assertions.assertTrue(result1 > 0);
        Assertions.assertTrue(result2 > 0);

        List<Fee> list = feeService.listAll();
        Assertions.assertTrue(list.size() >= 2);

        Assertions.assertEquals(fee1, list.stream().filter(x -> Objects.equals(fee1.getId(), x.getId())).findFirst().get());
        Assertions.assertEquals(fee2, list.stream().filter(x -> Objects.equals(fee2.getId(), x.getId())).findFirst().get());

        fee1.setName(randStr(10));
        fee1.setId(null);
        Assertions.assertFalse(feeService.updateFee(fee1));

        fee1.setId(result1);
        Assertions.assertTrue(feeService.updateFee(fee1));
        Fee dbItem1 = feeService.findById(result1);
        Assertions.assertEquals(fee1, dbItem1);

        String str = "2021-07-28--2021-08-28";
        Assertions.assertEquals("2021-07-28", str.substring(0, 10));
        Assertions.assertEquals("2021-08-28", str.substring(str.length() - 10));
    }

    private Fee mockFee() {
        Fee result = new Fee();
        result.setName(randStr(10));
        result.setAlias(randStr(10));
        result.setCategory(randInt(1, FeeCategory.values().length));
        result.setCreatorId(randLong(0, 100));
        result.setCreatorName(randStr(10));
        result.setModifierId(randLong(0, 100));
        result.setModifierName(randStr(10));
        return result;
    }
}