package com.senox.realty.service;

import com.senox.common.utils.DecimalUtils;
import com.senox.realty.BaseTest;
import com.senox.realty.domain.AdvertisingSpace;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/7/18 10:39
 */
class AdvertisingSpaceServiceTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(AdvertisingSpaceServiceTest.class);

    @Autowired
    private AdvertisingSpaceService advertisingSpaceService;

    @Test
    void addSpace() {
        AdvertisingSpace space1 = mockSpace();
        Long id1 = advertisingSpaceService.addSpace(space1);
        Assertions.assertTrue(id1 > 0L);

        AdvertisingSpace dbSpace1 = advertisingSpaceService.findById(id1);
        logger.info("dbSpace1: {}", dbSpace1.toString());
        Assertions.assertEquals(space1, dbSpace1);

        AdvertisingSpace space2 = new AdvertisingSpace();
        space2.setId(id1);
        space2.setAddress(randStr(20));
        space2.setModifierId(randLong(1L, 100L));
        space2.setModifierName(randStr(10));
        advertisingSpaceService.updateSpace(space2);

        AdvertisingSpace dbSpace2 = advertisingSpaceService.findById(space2.getId());
        Assertions.assertEquals(space2.getAddress(), dbSpace2.getAddress());
        Assertions.assertEquals(dbSpace1.getSerialNo(), dbSpace2.getSerialNo());
        Assertions.assertEquals(dbSpace1.getName(), dbSpace2.getName());
    }

    private AdvertisingSpace mockSpace() {
        AdvertisingSpace result = new AdvertisingSpace();
        result.setName(randStr(25));
        result.setAddress(randStr(40));
        result.setLength(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(20L), 2));
        result.setWidth(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(10L), 2));
        result.setSize(DecimalUtils.multiple(result.getLength(), result.getWidth()).setScale(2, BigDecimal.ROUND_HALF_UP));
        result.setCreatorId(randLong(1L, 100L));
        result.setCreatorName(randStr(10));
        return result;
    }
}