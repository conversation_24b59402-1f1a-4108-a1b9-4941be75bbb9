package com.senox.realty.service;

import com.senox.realty.BaseTest;
import com.senox.realty.domain.MaintainMaterial;
import com.senox.realty.domain.MaintainMaterialItem;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 15:32
 */
class MaintainMaterialServiceTest extends BaseTest {

    @Autowired
    private MaintainMaterialService maintainMaterialService;

    @Test
    void maintainMaterialTest() {
        MaintainMaterial material = new MaintainMaterial();
        material.setJobId(1L);
        material.setOrderId(1L);
        material.setCreatorId(randLong(1L, 100L));
        material.setCreatorName(randStr(10));
        material.setModifierId(material.getCreatorId());
        material.setModifierName(material.getCreatorName());
        List<MaintainMaterialItem> list = new ArrayList<>();
        MaintainMaterialItem materialItem = new MaintainMaterialItem();
        materialItem.setMaterialCode(23L);
        materialItem.setMaterialName("物料");
        materialItem.setQuantity(2);
        materialItem.setPrice(BigDecimal.valueOf(22.2));
        materialItem.setCreatorId(randLong(1L, 100L));
        materialItem.setCreatorName(randStr(10));
        materialItem.setModifierId(material.getCreatorId());
        materialItem.setModifierName(material.getCreatorName());
        materialItem.setCreateTime(LocalDateTime.now());
        materialItem.setModifiedTime(LocalDateTime.now());
        list.add(materialItem);
        Long id = maintainMaterialService.saveMaintainMaterial(material, list);
        Assertions.assertTrue(id > 0L);
        MaintainMaterial materialServiceById = maintainMaterialService.findById(id);
        List<MaintainMaterialItem> list1 = new ArrayList<>();
        MaintainMaterialItem materialItem1 = new MaintainMaterialItem();
        materialItem1.setMaterialCode(23L);
        materialItem1.setMaterialName("测试物料");
        materialItem1.setQuantity(3);
        materialItem1.setPrice(BigDecimal.valueOf(22.3));
        materialItem1.setCreatorId(randLong(1L, 100L));
        materialItem1.setCreatorName(randStr(10));
        materialItem1.setModifierId(material.getCreatorId());
        materialItem1.setModifierName(material.getCreatorName());
        materialItem1.setCreateTime(LocalDateTime.now());
        materialItem1.setModifiedTime(LocalDateTime.now());
        list1.add(materialItem1);
        Long id1 = maintainMaterialService.saveMaintainMaterial(material, list1);
    }
}
