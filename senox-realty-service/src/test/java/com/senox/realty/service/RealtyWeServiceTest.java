package com.senox.realty.service;

import com.senox.common.vo.PageResult;
import com.senox.realty.BaseTest;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.vo.RealtyWePageResult;
import com.senox.realty.vo.RealtyWeSearchVo;
import com.senox.realty.vo.RealtyWeType;
import com.senox.realty.vo.RealtyWeVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2022-10-21
 */
@Slf4j
class RealtyWeServiceTest extends BaseTest {

    @Autowired
    private RealtyWeService realtyWeService;


    @Test
    void batchAddWeData() {
        RealtyWe we = mockRealtyWe(2022, 10, RealtyWeType.MONTH_PAYMENT_BILL);
        realtyWeService.batchAddWeData(Collections.singletonList(we), true);

        RealtyWeSearchVo searchVo = new RealtyWeSearchVo();
        searchVo.setYear(we.getBillYear());
        searchVo.setMonth(we.getBillMonth());
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        searchVo.setWeType(RealtyWeType.MONTH_PAYMENT_BILL);
        searchVo.setRealtySerial(we.getRealtySerial());
        PageResult<RealtyWeVo> page = realtyWeService.listReadingsPage(searchVo);

        Assertions.assertEquals(1, page.getTotalSize());
        Assertions.assertEquals(we.getElectricReadings(), page.getDataList().get(0).getElectricReadings());
        Assertions.assertEquals(we.getLastElectricReadings(), page.getDataList().get(0).getLastElectricReadings());
        Assertions.assertEquals(we.getWaterReadings(), page.getDataList().get(0).getWaterReadings());
        Assertions.assertEquals(we.getLastWaterReadings(), page.getDataList().get(0).getLastWaterReadings());


        // 覆盖重新导入
        we.setLastWaterReadings(randLong(0L, 100L));
        we.setWaterReadings(we.getLastWaterReadings() + randInt(0, 500));
        we.setLastElectricReadings(randLong(0L, 100L));
        we.setElectricReadings(we.getLastElectricReadings() + randInt(0, 1000));
        realtyWeService.batchAddWeData(Collections.singletonList(we), true);

        page = realtyWeService.listReadingsPage(searchVo);
        Assertions.assertEquals(1, page.getTotalSize());
        Assertions.assertEquals(we.getElectricReadings(), page.getDataList().get(0).getElectricReadings());
        Assertions.assertEquals(we.getLastElectricReadings(), page.getDataList().get(0).getLastElectricReadings());
        Assertions.assertEquals(we.getWaterReadings(), page.getDataList().get(0).getWaterReadings());
        Assertions.assertEquals(we.getLastWaterReadings(), page.getDataList().get(0).getLastWaterReadings());


        RealtyWe we2 = mockRealtyWe(we.getBillYear(), we.getBillMonth(), RealtyWeType.fromValue(we.getType()));
        we2.setRealtySerial(we.getRealtySerial());
        realtyWeService.batchAddWeData(Collections.singletonList(we), false);
        page = realtyWeService.listReadingsPage(searchVo);
        Assertions.assertEquals(1, page.getTotalSize());
        Assertions.assertNotEquals(we2.getElectricReadings(), page.getDataList().get(0).getElectricReadings());
        Assertions.assertNotEquals(we2.getLastElectricReadings(), page.getDataList().get(0).getLastElectricReadings());
        Assertions.assertNotEquals(we2.getWaterReadings(), page.getDataList().get(0).getWaterReadings());
        Assertions.assertNotEquals(we2.getLastWaterReadings(), page.getDataList().get(0).getLastWaterReadings());
        Assertions.assertEquals(we.getElectricReadings(), page.getDataList().get(0).getElectricReadings());
        Assertions.assertEquals(we.getLastElectricReadings(), page.getDataList().get(0).getLastElectricReadings());
        Assertions.assertEquals(we.getWaterReadings(), page.getDataList().get(0).getWaterReadings());
        Assertions.assertEquals(we.getLastWaterReadings(), page.getDataList().get(0).getLastWaterReadings());
    }

    private RealtyWe mockRealtyWe(Integer year, Integer month, RealtyWeType weType) {
        RealtyWe result = new RealtyWe();
        result.setBillYear(year);
        result.setBillMonth(month);
        result.setRealtySerial(randStr(8));
        result.setLastWaterReadings(randLong(0L, 100L));
        result.setWaterReadings(result.getLastWaterReadings() + randInt(0, 500));
        result.setLastElectricReadings(randLong(0L, 100L));
        result.setElectricReadings(result.getLastElectricReadings() + randInt(0, 1000));
        result.setType(weType.getValue());
        result.setCreatorId(randLong(1L, 10L));
        result.setCreatorName(randStr(10));
        result.setCreateTime(LocalDateTime.now());
        result.setModifierId(result.getModifierId());
        result.setModifierName(result.getModifierName());
        result.setModifiedTime(LocalDateTime.now());
        return result;
    }

}
