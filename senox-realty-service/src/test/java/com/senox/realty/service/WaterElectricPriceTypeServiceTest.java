package com.senox.realty.service;

import com.senox.common.utils.DecimalUtils;
import com.senox.realty.BaseTest;
import com.senox.realty.constant.PriceType;
import com.senox.realty.domain.WaterElectricPriceType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/3/9 15:13
 */
class WaterElectricPriceTypeServiceTest extends BaseTest {

    @Autowired
    private WaterElectricPriceTypeService priceTypeService;

    @Test
    void savePriceType() {
        WaterElectricPriceType priceType1 = mockPriceType();
        Long result1 = priceTypeService.addPriceType(priceType1);
        Assertions.assertTrue(result1 > 0L);

        WaterElectricPriceType dbPriceType1 = priceTypeService.findById(result1);
        Assertions.assertEquals(dbPriceType1, priceType1);

        WaterElectricPriceType priceType2 = new WaterElectricPriceType();
        priceType2.setId(result1);
        priceType2.setName(randStr(20));
        priceType2.setPrice(randDecimal(BigDecimal.ZERO, BigDecimal.valueOf(100L), 3));
        priceType2.setModifierId(randLong(1L, 100L));
        priceType2.setModifierName(randStr(20));
        Assertions.assertTrue(priceTypeService.updatePriceType(priceType2));

        WaterElectricPriceType dbPriceType2 = priceTypeService.findById(result1);
        Assertions.assertEquals(priceType2.getName(), dbPriceType2.getName());
        Assertions.assertTrue(DecimalUtils.equals(priceType2.getPrice(), dbPriceType2.getPrice()));
    }


    private WaterElectricPriceType mockPriceType() {
        WaterElectricPriceType result = new WaterElectricPriceType();
        result.setName(randStr(20));
        result.setType(PriceType.fromValue(randInt(1, 2)).getValue());
        result.setPrice(randDecimal(BigDecimal.ZERO, BigDecimal.valueOf(100L), 2));
        result.setRemark(randStr(50));
        result.setCreatorId(randLong(1L, 100L));
        result.setCreatorName(randStr(20));
        result.setModifierId(randLong(1L, 100L));
        result.setModifierName(randStr(20));
        return result;
    }

}