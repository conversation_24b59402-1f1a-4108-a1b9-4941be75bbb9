package com.senox.realty.service;


import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.realty.BaseTest;
import com.senox.realty.convert.AdvertisingProfitShareConvertor;
import com.senox.realty.domain.AdvertisingBill;
import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.domain.AdvertisingMedia;
import com.senox.realty.domain.AdvertisingProfitShare;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/7/19 15:35
 */
class AdvertisingContractServiceTest extends BaseTest {

    @Autowired
    private AdvertisingContractService contractService;
    @Autowired
    private AdvertisingMediaService mediaService;
    @Autowired
    private AdvertisingProfitShareService shareService;
    @Autowired
    private AdvertisingBillService billService;
    @Autowired
    private AdvertisingProfitShareConvertor shareConvertor;

    @Test
    void addContract() {
        AdvertisingContract contract1 = mockContract();
        Long id1 = contractService.addContract(contract1);
        Assertions.assertTrue(id1 > 0);

        AdvertisingContract dbContract1 = contractService.findById(id1);
        Assertions.assertEquals(contract1, dbContract1);
    }

    @Test
    void saveContract() {
        // add
        AdvertisingContract contract1 = mockContract();
        List<String> medias1 = Arrays.asList(randStr(50), randStr(50), randStr(50));
        List<AdvertisingProfitShare> shares1 = Arrays.asList(mockProfitShare(), mockProfitShare());
        Long id1 = contractService.saveContract(contract1, medias1, shareConvertor.toVo(shares1));
        Assertions.assertTrue(id1 > 0);

        AdvertisingContract dbContract1 = contractService.findById(id1);
        Assertions.assertEquals(contract1, dbContract1);
        List<AdvertisingMedia> dbMedias1 = mediaService.listByContractId(id1);
        Assertions.assertEquals(medias1.size(), dbMedias1.size());
        Assertions.assertTrue(dbMedias1.stream().allMatch(x -> medias1.contains(x.getMediaUrl())));
        List<AdvertisingProfitShare> dbShares1 = shareService.listByContractId(id1);
        Assertions.assertEquals(shares1.size(), dbShares1.size());
        for (AdvertisingProfitShare item : dbShares1) {
            Assertions.assertFalse(
                    shares1.stream().noneMatch(x -> Objects.equals(id1, item.getContractId())
                            && Objects.equals(x.getCustomerId(), item.getCustomerId())
                            && Objects.equals(x.getRealtySerial(), item.getRealtySerial())
                            && DecimalUtils.equals(x.getShareAmount(), item.getShareAmount())
                    )
            );
        }

        // update
        AdvertisingContract updateContract1 = new AdvertisingContract();
        updateContract1.setId(id1);
        updateContract1.setContractNo(dbContract1.getContractNo());
        updateContract1.setRemark(randStr(30));
        updateContract1.setModifierId(randLong(1L, 100L));
        updateContract1.setModifierName(randStr(8));
        updateContract1.setModifiedTime(LocalDateTime.now());
        List<String> updateMedias1 = Arrays.asList(randStr(50), medias1.get(0));
        AdvertisingProfitShare s20 = shares1.get(0);
        s20.setShareAmount(s20.getShareAmount().add(BigDecimal.valueOf(100L)));
        List<AdvertisingProfitShare> updateShares1 = Arrays.asList(s20, mockProfitShare(), mockProfitShare());
        contractService.saveContract(updateContract1, updateMedias1, shareConvertor.toVo(updateShares1));

        AdvertisingContract dbContract2 = contractService.findById(updateContract1.getId());
        Assertions.assertEquals(updateContract1.getRemark(), dbContract2.getRemark());
        List<AdvertisingMedia> dbMedias2 = mediaService.listByContractId(id1);
        Assertions.assertEquals(updateMedias1.size(), dbMedias2.size());
        Assertions.assertTrue(dbMedias2.stream().allMatch(x -> updateMedias1.contains(x.getMediaUrl())));
        List<AdvertisingProfitShare> dbShares2 = shareService.listByContractId(id1);
        Assertions.assertEquals(updateShares1.size(), dbShares2.size());
        for (AdvertisingProfitShare item : dbShares2) {
            Assertions.assertFalse(
                    updateShares1.stream().noneMatch(x -> Objects.equals(updateContract1.getId(), item.getContractId())
                            && Objects.equals(x.getCustomerId(), item.getCustomerId())
                            && Objects.equals(x.getRealtySerial(), item.getRealtySerial())
                            && DecimalUtils.equals(x.getShareAmount(), item.getShareAmount())
                    )
            );
        }

        AdvertisingBill bill1 = billService.findByContractNo(dbContract2.getContractNo());
        Assertions.assertNotNull(bill1);
        Assertions.assertTrue(DecimalUtils.equals(dbContract2.getAmount(), bill1.getAmount()));
    }


    private AdvertisingContract mockContract() {
        AdvertisingContract result = new AdvertisingContract();
        result.setSpaceId(randLong(1L, 100L));
        result.setCustomerName(randStr(20));
        result.setCustomerUser(randStr(20));
        result.setCustomerContact(randNumStr(11));
        result.setPresentMonths(randInt(0, 4));
        result.setRentMonths(randInt(1, 5) * 12);
        result.setSignDate(LocalDate.now());
        result.setStartDate(DateUtils.getFirstDateInMonth(result.getSignDate()).plusMonths(1L));
        result.setAmount(randDecimal(BigDecimal.valueOf(50000), BigDecimal.valueOf(100000), 0));
        result.setRemark(randStr(18));
        result.setCreatorId(randLong(1L, 100L));
        result.setCreatorName(randStr(8));
        return result;
    }

    private AdvertisingProfitShare mockProfitShare() {
        AdvertisingProfitShare result = new AdvertisingProfitShare();
        result.setCustomerId(randLong(1L, 100L));
        result.setCustomerName(randStr(10));
        result.setRealtySerial(randStr(12));
        result.setRealtySerial(randStr(20));
        result.setShareAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(2000L), 0));
        return result;
    }
}