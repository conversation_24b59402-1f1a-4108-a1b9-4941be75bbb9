package com.senox.realty.service;

import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.BaseTest;
import com.senox.realty.vo.RealtyBillWeSearchVo;
import com.senox.realty.vo.RealtyBillWeVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;


@Slf4j
class RealtyBillWeServiceTest extends BaseTest {

    @Autowired
    private RealtyBillWeService realtyBillWeService;

    @Test
    void pageListRealtyBillWeDetailTest() {
        RealtyBillWeSearchVo search = new RealtyBillWeSearchVo();
        search.setPageNo(1);
        search.setPageSize(10);
        search.setBillYear(2022);
        search.setBillMonth(6);
        search.setRealtySerial("PB0177101");
        search.setStatus(1);
        search.setCustomerName("吴泽波");
        PageResult<RealtyBillWeVo> result = realtyBillWeService.listWeBillPage(search);
        log.info("data:{}", JsonUtils.object2Json(result));
    }
}
