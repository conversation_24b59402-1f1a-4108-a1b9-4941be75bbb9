package com.senox.realty.service;

import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.constant.device.DeviceState;
import com.senox.common.constant.device.EnergyType;
import com.senox.dm.constant.HolleyQueryType;
import com.senox.dm.vo.HolleyPointMeterQueryBatchRequest;
import com.senox.realty.BaseTest;
import com.senox.realty.component.DeviceEnergyMeteringPointComponent;
import com.senox.realty.domain.EnergyMeteringPoint;
import com.senox.realty.domain.EnergyMeteringPointReadings;
import com.senox.realty.domain.EnergyRtu;
import com.senox.realty.utils.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-13
 */
@Slf4j
class EnergyServiceTest extends BaseTest {
    @SpyBean
    public EnergyMeteringPointReadingsService energyPointMeterService;
    @SpyBean
    public EnergyRtuService energyRtuService;
    @SpyBean
    public EnergyMeteringPointService energyMeteringPointService;
    @SpyBean
    private DeviceEnergyMeteringPointComponent energyMeteringPointComponent;

    @BeforeEach
    void setUp() {
        List<EnergyMeteringPoint> pointList = pointMockList();
        HolleyPointMeterQueryBatchRequest defaultBatchReq = new HolleyPointMeterQueryBatchRequest();
        defaultBatchReq.setRtuCode("default");
        defaultBatchReq.setType(HolleyQueryType.REAL_TIME.ordinal());
        Mockito.doReturn(pointList).when(energyMeteringPointComponent).remoteList(Mockito.any());
        energyRtuService.addBatch(pointList.stream().map(p -> {
            EnergyRtu rtu = new EnergyRtu();
            rtu.setCode(p.getCode());
            rtu.setName(p.getName());
            rtu.setStatus(1);
            return rtu;
        }).distinct().collect(Collectors.toList()));
        List<EnergyMeteringPointReadings> waterMeteringPointReadings = readingsMock(pointList.stream().filter(p -> p.getType() == EnergyType.WATER.getValue()).collect(Collectors.toList()), Arrays.asList(LocalDateTime.of(2023, 4, 26, 8, 0, 0), LocalDateTime.of(2023, 4, 26, 9, 0, 0)));
        List<EnergyMeteringPointReadings> electricMeteringPointReadings = readingsMock(pointList.stream().filter(p -> p.getType() == EnergyType.ELECTRIC.getValue()).collect(Collectors.toList()), Arrays.asList(LocalDateTime.of(2023, 4, 26, 8, 0, 0), LocalDateTime.of(2023, 4, 26, 9, 0, 0)));
        Mockito.doReturn(waterMeteringPointReadings).when(energyPointMeterService).waterReadingsList(Mockito.any());
        Mockito.doReturn(electricMeteringPointReadings).when(energyPointMeterService).electricReadingsList(Mockito.any());
        energyMeteringPointService.batchRefresh(StringUtils.EMPTY);
        energyPointMeterService.batchSync(defaultBatchReq);
    }

    @Test
    void updateRtuByIdTest() {
        List<EnergyRtu> rtuList = energyRtuService.list(null);
        Assertions.assertFalse(CollectionUtils.isEmpty(rtuList));
        Collections.shuffle(rtuList);
        EnergyRtu updateRtu = rtuList.get(randInt(0, rtuList.size()));
        EnergyRtu updateRtu2 = rtuList.get(randInt(0, rtuList.size()));
        Assertions.assertNotNull(updateRtu);
        Assertions.assertTrue(WrapperClassUtils.biggerThanLong(updateRtu.getId(), 0));
        Assertions.assertNotNull(updateRtu2);
        Assertions.assertTrue(WrapperClassUtils.biggerThanLong(updateRtu2.getId(), 0));
        updateRtu.setName(randStr(20));
        updateRtu.setCode(randStr(20));
        updateRtu.setOnLineTime(LocalDateTime.of(2022, 12, 13, 0, 0, 0));
        updateRtu2.setDisabled(true);
        ContextUtils.initEntityCreator(updateRtu2);
        ContextUtils.initEntityModifier(updateRtu);
        energyRtuService.updateById(updateRtu);
        energyRtuService.updateById(updateRtu2);
        EnergyRtu dbRtu = energyRtuService.findById(updateRtu.getId());
        EnergyRtu dbRtu2 = energyRtuService.findById(updateRtu2.getId());
        Assertions.assertEquals(updateRtu.getId(), dbRtu.getId());
        Assertions.assertEquals(updateRtu.getName(), dbRtu.getName());
        Assertions.assertEquals(updateRtu.getCode(), dbRtu.getCode());
        Assertions.assertEquals(updateRtu.getOnLineTime(), dbRtu.getOnLineTime());
        Assertions.assertEquals(updateRtu.getModifierId(), dbRtu.getModifierId());
        Assertions.assertEquals(updateRtu.getModifierName(), dbRtu.getModifierName());
        Assertions.assertEquals(updateRtu2.getCode(), dbRtu2.getCode());

    }

    @Test
    void updateRtuByCodeTest() {
        List<EnergyRtu> rtuList = energyRtuService.list(null);
        Assertions.assertFalse(CollectionUtils.isEmpty(rtuList));
        Collections.shuffle(rtuList);
        EnergyRtu updateRtu1 = rtuList.get(0);
        EnergyRtu updateRtu2 = rtuList.get(1);
        Assertions.assertNotNull(updateRtu1);
        Assertions.assertTrue(WrapperClassUtils.biggerThanLong(updateRtu1.getId(), 0));
        Assertions.assertNotNull(updateRtu2);
        Assertions.assertTrue(WrapperClassUtils.biggerThanLong(updateRtu2.getId(), 0));
        updateRtu1.setName(randStr(20));
        updateRtu1.setOnLineTime(LocalDateTime.of(2022, randInt(1, 12), 13, 0, 0, 0));
        updateRtu2.setName(randStr(20));
        updateRtu2.setOnLineTime(LocalDateTime.of(2022, randInt(1, 12), 13, 0, 0, 0));
        ContextUtils.initEntityModifier(updateRtu1);
        ContextUtils.initEntityModifier(updateRtu2);
        List<EnergyRtu> updateRtus = Arrays.asList(updateRtu1, updateRtu2);
        energyRtuService.updateBatchByCode(updateRtus);
        EnergyRtu dbCode1 = energyRtuService.findByCode(updateRtu1.getCode());
        EnergyRtu dbCode2 = energyRtuService.findById(updateRtu2.getId());
        Assertions.assertEquals(updateRtu1.getName(), dbCode1.getName());
        Assertions.assertEquals(updateRtu1.getOnLineTime(), dbCode1.getOnLineTime());
        Assertions.assertEquals(updateRtu2.getName(), dbCode2.getName());
        Assertions.assertEquals(updateRtu1.getOnLineTime(), dbCode1.getOnLineTime());
        Assertions.assertEquals(updateRtu1.getModifierId(), dbCode1.getModifierId());
        Assertions.assertEquals(updateRtu1.getModifierName(), dbCode1.getModifierName());
        Assertions.assertEquals(updateRtu2.getOnLineTime(), dbCode2.getOnLineTime());
        Assertions.assertEquals(updateRtu2.getModifierId(), dbCode2.getModifierId());
        Assertions.assertEquals(updateRtu2.getModifierName(), dbCode2.getModifierName());
        dbCode1.setName(randStr(10));
        dbCode1.setOnLineTime(LocalDateTime.of(2022, randInt(1, 12), 14, 0, 0, 0));
        energyRtuService.updateByCode(dbCode1);
        EnergyRtu dbCode3 = energyRtuService.findByCode(dbCode1.getCode());
        Assertions.assertEquals(dbCode1.getName(), dbCode3.getName());
        Assertions.assertEquals(dbCode1.getOnLineTime(), dbCode3.getOnLineTime());
    }

    @Test
    void addPointTest() {
        EnergyMeteringPoint point = new EnergyMeteringPoint();
        point.setCode(randStr(20));
        point.setName(randStr(20));
        List<EnergyRtu> rtuList = energyRtuService.list(null);
        EnergyRtu rtu = rtuList.get(randInt(0, rtuList.size() - 1));
        point.setRtuCode(rtu.getCode());
        point.setRtuName(rtu.getName());
        energyMeteringPointService.add(point);
        EnergyMeteringPoint pointByCode = energyMeteringPointService.findByCode(point.getCode());
        EnergyMeteringPoint pointById = energyMeteringPointService.findById(point.getId());
        Assertions.assertEquals(point.getCode(), pointByCode.getCode());
        Assertions.assertEquals(point.getName(), pointByCode.getName());
        Assertions.assertEquals(point.getRtuCode(), pointByCode.getRtuCode());
        Assertions.assertEquals(point.getRtuName(), pointByCode.getRtuName());
        Assertions.assertEquals(point.getCode(), pointById.getCode());
        Assertions.assertEquals(point.getName(), pointById.getName());
        Assertions.assertEquals(point.getRtuCode(), pointById.getRtuCode());
        Assertions.assertEquals(point.getRtuName(), pointById.getRtuName());
    }

    public List<EnergyRtu> rtuMockList(int count) {
        List<EnergyRtu> rtuList = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            rtuList.add(rtuMock(randStr(18).concat(String.valueOf(i)), randStr(18).concat(String.valueOf(i))));
        }
        return rtuList;
    }

    public EnergyRtu rtuMock(String code, String name) {
        EnergyRtu rtu = new EnergyRtu();
        rtu.setCode(code);
        rtu.setName(name);
        rtu.setStatus(DeviceState.ONLINE.getState());
        rtu.setOnLineTime(LocalDateTime.now());
        ContextUtils.initEntityCreator(rtu);
        ContextUtils.initEntityModifier(rtu);
        return rtu;
    }

    public List<EnergyMeteringPoint> pointMockList() {
        List<EnergyRtu> rtuList = rtuMockList(20);
        List<EnergyMeteringPoint> pointList = new ArrayList<>();
        for (int i = 0; i < 50; i++) {
            EnergyRtu rtu = rtuList.get(randInt(0, rtuList.size() - 1));
            pointList.add(pointMock(randStr(18).concat(String.valueOf(i)), randStr(18).concat(String.valueOf(i)), rtu));
        }
        return pointList;
    }

    public EnergyMeteringPoint pointMock(String code, String name, EnergyRtu rtu) {
        EnergyMeteringPoint point = new EnergyMeteringPoint();
        point.setCode(code);
        point.setName(name);
        point.setRtuCode(rtu.getCode());
        point.setRtuName(rtu.getName());
        point.setStatus(randInt(0, 1));
        point.setType(randInt(1, 2));
        point.setPowerStatus(randInt(0, 1));
        ContextUtils.initEntityCreator(point);
        ContextUtils.initEntityModifier(point);
        return point;
    }

    public List<EnergyMeteringPointReadings> readingsMock(List<EnergyMeteringPoint> pointList, List<LocalDateTime> dateList) {
        if (CollectionUtils.isEmpty(pointList)) {
            return Collections.emptyList();
        }
        List<EnergyMeteringPointReadings> readingsList = new ArrayList<>();
        for (EnergyMeteringPoint point : pointList) {
            for (LocalDateTime date : dateList) {
                EnergyMeteringPointReadings readings = new EnergyMeteringPointReadings();
                readings.setRtuCode(point.getRtuCode());
                readings.setPointCode(point.getCode());
                readings.setPointType(EnergyType.ELECTRIC.getValue());
                readings.setReadings(new BigDecimal(randInt(10, 300)));
                readings.setDataTime(date);
                readings.setGrabTime(LocalDateTime.now());
                readingsList.add(readings);
            }
        }
        return readingsList;
    }
}
