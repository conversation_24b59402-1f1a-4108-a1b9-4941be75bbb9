package com.senox.realty.service;

import com.senox.realty.BaseTest;
import com.senox.realty.domain.RealtyDeposit;
import com.senox.realty.mapper.RealtyDepositMapper;
import com.senox.realty.vo.RealtyDepositSearchVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/8 14:16
 */
class RealtyDepositServiceTest extends BaseTest {

    @Autowired
    private RealtyDepositMapper depositMapper;
    @Autowired
    private RealtyDepositService depositService;


    @Test
    void addDeposit() {
        RealtyDeposit deposit = mockDeposit();
        Assertions.assertTrue(depositService.addDeposit(deposit) > 0);
    }

    @Test
    void batchAddDeposit() {
        List<RealtyDeposit> list = new ArrayList<>(1005);
        for (int i = 0; i < 1005; i++) {
            list.add(mockDeposit());
        }
        depositService.batchAddDeposit(list);


        RealtyDepositSearchVo search = new RealtyDepositSearchVo();
        search.setPageNo(1);
        search.setPageSize(20);
        int total = depositMapper.countDeposit(search);
        Assertions.assertTrue(total >= 1005);
    }

    private RealtyDeposit mockDeposit() {
        RealtyDeposit result = new RealtyDeposit();
        //result.setContractId(randLong(1, 2000));
        result.setCustomerId(randLong(1, 2000));
        result.setCustomerName(randStr(20));
        result.setRealtyId(randLong(1, 2000));
        result.setFeeId(randLong(1, 5));
        result.setFeeName(randStr(10));
        result.setAmount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(20000), 2));
        result.setCreatorId(randLong(1, 100));
        result.setCreatorName(randStr(10));
        result.setModifierId(randLong(1, 100));
        result.setModifierName(randStr(10));
        return result;
    }
}