package com.senox.realty.service;

import com.senox.common.vo.PageResult;
import com.senox.realty.BaseTest;
import com.senox.realty.constant.MaintainType;
import com.senox.realty.domain.MaintainOrder;
import com.senox.realty.vo.MaintainOrderSearchVo;
import com.senox.realty.vo.MaintainOrderVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/2/22 11:38
 */
class MaintainOrderServiceTest extends BaseTest {

    @Autowired
    private MaintainOrderService maintainOrderService;

    @Test
    void maintainOrderTest() {
        MaintainOrder order1 = mockMaintainOrder();
        long id1 = maintainOrderService.addMaintainOrder(order1, null);

        Assertions.assertTrue(id1 > 0L);
        MaintainOrder dbOrder1 = maintainOrderService.findById(id1);
        Assertions.assertEquals(order1.getOrderNo(), dbOrder1.getOrderNo());
        Assertions.assertEquals(order1.getAddress(), dbOrder1.getAddress());
        Assertions.assertEquals(order1.getProblem(), dbOrder1.getProblem());


        MaintainOrder order2 = new MaintainOrder();
        order2.setId(id1);
        order2.setOrderNo(randStr(20));
        order2.setAddress(randStr(20));
        order2.setProblem(randStr(120));
        order2.setModifierId(randLong(1L, 100L));
        order2.setModifierName(randStr(10));
        maintainOrderService.updateMaintainOrder(order2, null);


        MaintainOrder dbOrder2 = maintainOrderService.findById(id1);
        Assertions.assertEquals(order2.getOrderNo(), dbOrder2.getOrderNo());
        Assertions.assertNotEquals(order1.getOrderNo(), dbOrder2.getOrderNo());
        Assertions.assertEquals(order2.getAddress(), dbOrder2.getAddress());
        Assertions.assertEquals(order2.getProblem(), dbOrder2.getProblem());
        Assertions.assertEquals(order1.getCreatorName(), dbOrder2.getCreatorName());
        Assertions.assertEquals(order2.getModifierId(), dbOrder2.getModifierId());
    }

    private MaintainOrder mockMaintainOrder() {
        MaintainOrder result = new MaintainOrder();
        result.setMaintainType(MaintainType.values()[randInt(0, MaintainType.values().length - 1)].getValue());
        result.setCustomerName(randStr(20));
        result.setContact(randNumStr(11));
        result.setRegionName(randStr(20));
        result.setStreetName(randStr(20));
        result.setAddress(randStr(25));
        result.setProblem(randStr(100));
        result.setCreateOpenid(randStr(20));
        result.setCreatorId(randLong(1L, 100L));
        result.setCreatorName(randStr(10));
        result.setModifierId(result.getCreatorId());
        result.setModifierName(result.getCreatorName());

        return result;
    }

    @Test
     void listMaintainOrderTest() {
        MaintainOrderSearchVo searchVo = new MaintainOrderSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        searchVo.setCustomerName("");
        searchVo.setMaintainType(0);
        searchVo.setCreateOpenid("");
        searchVo.setOrderNo("1");
        PageResult<MaintainOrderVo> result = maintainOrderService.pageMaintainOrder(searchVo);
        Assertions.assertTrue(!result.getDataList().isEmpty());
    }
}
