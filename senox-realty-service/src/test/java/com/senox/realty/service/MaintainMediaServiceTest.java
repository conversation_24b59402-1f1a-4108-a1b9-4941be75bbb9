package com.senox.realty.service;

import com.senox.realty.BaseTest;
import com.senox.realty.domain.MaintainMedia;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/2/24 8:47
 */
class MaintainMediaServiceTest extends BaseTest {


    @Autowired
    private MaintainMediaService maintainMediaService;

    @Test
    void saveMaintainMediaTest() {
        Long orderId = randLong(1L, 100L);
        List<String> medias = Lists.newArrayList(randStr(30), randStr(25), randStr(20));

        maintainMediaService.saveMaintainMedia(orderId,0L,0L, medias);

        List<MaintainMedia> dbList = maintainMediaService.listByOrderId(orderId);
        Assertions.assertEquals(medias.size(), dbList.size());
        Assertions.assertTrue(dbList.stream().anyMatch(x -> Objects.equals(x.getMediaUrl(), medias.get(0))));
        Assertions.assertTrue(dbList.stream().anyMatch(x -> Objects.equals(x.getMediaUrl(), medias.get(1))));
        Assertions.assertTrue(dbList.stream().anyMatch(x -> Objects.equals(x.getMediaUrl(), medias.get(2))));

        List<String> medias2 = new ArrayList<>();
        medias2.add(medias.get(2));
        medias2.add(randStr(20));
        maintainMediaService.saveMaintainMedia(orderId,0L, 0L,medias2);
        List<MaintainMedia> dbList2 = maintainMediaService.listByOrderId(orderId);
        Assertions.assertEquals(medias2.size(), dbList2.size());
        Assertions.assertFalse(dbList2.stream().anyMatch(x -> Objects.equals(x.getMediaUrl(), medias.get(0))));
        Assertions.assertFalse(dbList2.stream().anyMatch(x -> Objects.equals(x.getMediaUrl(), medias.get(1))));
        Assertions.assertTrue(dbList2.stream().anyMatch(x -> Objects.equals(x.getMediaUrl(), medias.get(2))));
        Assertions.assertTrue(dbList2.stream().anyMatch(x -> Objects.equals(x.getMediaUrl(), medias2.get(1))));
    }
}