package com.senox.realty.service;

import com.senox.realty.BaseTest;
import com.senox.realty.domain.BusinessRegion;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/12/15 15:34
 */
class BusinessRegionServiceTest extends BaseTest {

    @Autowired
    private BusinessRegionService regionService;

    @Test
    void testFeeArea() {
        BusinessRegion region1 = mockBusinessRegion();
        Long result1 = regionService.addRegion(region1);
        Assertions.assertTrue(result1 > 0L);
        BusinessRegion dbRegion1 = regionService.findById(result1);
        Assertions.assertEquals(region1, dbRegion1);

        BusinessRegion updateRegion1 = new BusinessRegion();
        updateRegion1.setId(result1);
        updateRegion1.setName(randStr(20));
        updateRegion1.setModifierId(randLong(1, 100));
        updateRegion1.setModifierName(randStr(20));
        regionService.updateRegion(updateRegion1);

        BusinessRegion dbRegion2 = regionService.findById(result1);
        Assertions.assertEquals(updateRegion1, dbRegion2);

        BusinessRegion businessRegion2 = mockBusinessRegion();
        Long result2 = regionService.addRegion(businessRegion2);
        Assertions.assertTrue(result2 > 0L);

        List<BusinessRegion> list = regionService.listAll();
        Assertions.assertTrue(list.size() > 0);
        Assertions.assertEquals(updateRegion1, list.stream().filter(x -> Objects.equals(region1.getId(), x.getId())).findFirst().orElse(new BusinessRegion()));
        Assertions.assertEquals(businessRegion2, list.stream().filter(x -> Objects.equals(businessRegion2.getId(), x.getId())).findFirst().orElse(new BusinessRegion()));
    }

    private BusinessRegion mockBusinessRegion() {
        BusinessRegion result = new BusinessRegion();
        result.setName(randStr(20));
        result.setCreatorId(randLong(0, 100));
        result.setCreatorName(randStr(10));
        result.setModifierId(randLong(0, 100));
        result.setModifierName(randStr(10));
        return result;
    }

}