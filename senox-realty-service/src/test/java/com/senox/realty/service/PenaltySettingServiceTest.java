package com.senox.realty.service;

import com.senox.common.constant.BillType;
import com.senox.common.domain.PenaltySetting;
import com.senox.common.service.PenaltySettingService;
import com.senox.common.utils.DateUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PenaltySettingSearch;
import com.senox.realty.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/1/31 16:15
 */
class PenaltySettingServiceTest extends BaseTest {

    @Autowired
    private PenaltySettingService settingService;

    @Test
    void testSetting() {
        PenaltySetting setting1 = mockSetting();
        Long id1 = settingService.addSetting(setting1);
        Assertions.assertTrue(id1 > 0L);
        PenaltySetting dbSetting1 = settingService.getById(id1);
        Assertions.assertEquals(setting1, dbSetting1);

        PenaltySetting setting2 = mockSetting();
        setting2.setBillType(BillType.REFRIGERATION.getValue());
        settingService.addSetting(setting2);

        PenaltySettingSearch search = new PenaltySettingSearch();
        search.setPageNo(1);
        search.setPageSize(10);
        PageResult<PenaltySetting> page = settingService.listSetting(search);
        Assertions.assertEquals(2, page.getTotalSize());
        Assertions.assertEquals(1, page.getTotalPages());

    }

    private PenaltySetting mockSetting() {
        LocalDate nowDate = LocalDate.now();

        PenaltySetting result = new PenaltySetting();
        result.setBillYearMonth(DateUtils.formatYearMonth(nowDate));
        result.setBillType(BillType.REALTY.getValue());
        result.setPenaltyStartDate(DateUtils.parseDate(nowDate.getYear(), nowDate.getMonthValue(), 10));
        result.setPenaltyCalDate(DateUtils.parseDate(nowDate.getYear(), nowDate.getMonthValue(), 10));
        result.setPenaltyFree(randInt(0, 1) == 1);
        result.setCreatorId(randLong(1L, 100L));
        result.setCreatorName(randStr(10));
        result.setCreateTime(LocalDateTime.now());
        result.setModifierId(result.getCreatorId());
        result.setModifierName(result.getCreatorName());
        result.setModifiedTime(result.getCreateTime());
        return result;
    }

}
