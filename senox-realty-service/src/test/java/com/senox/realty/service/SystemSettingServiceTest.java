package com.senox.realty.service;

import com.senox.common.constant.SystemParam;
import com.senox.common.domain.SystemSetting;
import com.senox.common.service.SystemSettingService;
import com.senox.realty.BaseTest;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9 13:57
 */
class SystemSettingServiceTest extends BaseTest {

    @Autowired
    private SystemSettingService settingService;

    @Test
    void listSystemSetting() {
        List<SystemSetting> settings = settingService.listByParams(
                new SystemParam[]{SystemParam.REALTY_BILL_YEAR, SystemParam.REALTY_BILL_MONTH});
        Assertions.assertEquals(2, settings.size());


        settingService.updateBatchById(Lists.newArrayList(
                new SystemSetting(SystemParam.REALTY_BILL_YEAR.name(), String.valueOf(LocalDate.now().getYear())),
                new SystemSetting(SystemParam.REALTY_BILL_MONTH.name(), String.valueOf(LocalDate.now().getMonthValue()))
        ));

        settings = settingService.listByParams(new SystemParam[]{SystemParam.REALTY_BILL_YEAR, SystemParam.REALTY_BILL_MONTH});
        Assertions.assertEquals(2, settings.size());
        Assertions.assertEquals(String.valueOf(LocalDate.now().getYear()), settingService.getParamValueFromSettingList(settings, SystemParam.REALTY_BILL_YEAR));
        Assertions.assertEquals(String.valueOf(LocalDate.now().getMonthValue()), settingService.getParamValueFromSettingList(settings, SystemParam.REALTY_BILL_MONTH));
    }
}
