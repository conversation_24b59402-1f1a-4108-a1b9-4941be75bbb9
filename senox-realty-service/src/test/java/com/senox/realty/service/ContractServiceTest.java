package com.senox.realty.service;


import com.senox.common.utils.DateUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.TaxCategory;
import com.senox.realty.BaseTest;
import com.senox.realty.constant.ContractFeeCategory;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.ContractType;
import com.senox.realty.constant.CostType;
import com.senox.realty.domain.Contract;
import com.senox.realty.domain.ContractExt;
import com.senox.realty.domain.ContractFee;
import com.senox.realty.vo.ContractBillSearchVo;
import com.senox.realty.vo.ContractSearchVo;
import com.senox.realty.vo.ContractVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/9 13:55
 */
@Slf4j
class ContractServiceTest extends BaseTest {

    @Autowired
    private ContractService contractService;

    @Test
    void saveContract() {
        Contract contract1 = mockContract();
        ContractExt ext1 = mockContractExt();
        List<ContractFee> fees1 = Arrays.asList(
                mockContractFee(1L, ContractFeeCategory.DEFAULT, contract1.getStartDate(), contract1.getEndDate()),
                mockContractFee(2L, ContractFeeCategory.DEFAULT, contract1.getStartDate(), contract1.getEndDate()),
                mockContractFee(3L, ContractFeeCategory.DEFAULT, contract1.getStartDate(), contract1.getEndDate()),
                mockContractFee(4L, ContractFeeCategory.DEFAULT, contract1.getStartDate(), contract1.getEndDate()),
                mockContractFee(2L, ContractFeeCategory.PERIOD, contract1.getStartDate(), contract1.getEndDate().minusMonths(5)),
                mockContractFee(2L, ContractFeeCategory.PERIOD, contract1.getEndDate().minusMonths(5).plusDays(1), contract1.getEndDate())
        );
        Long result1 = contractService.addContract(contract1, ext1,  fees1);
        Assertions.assertTrue(result1 > 0L);

        Contract dbContract1 = contractService.findById(result1);
        ContractExt dbExt1 = contractService.findExtByContractId(result1);
        List<ContractFee> dbFees1 = contractService.listContractFee(result1);
        Assertions.assertEquals(contract1, dbContract1);
        Assertions.assertEquals(ext1, dbExt1);
        Assertions.assertEquals(fees1.size(), dbFees1.size());
        Assertions.assertEquals(contract1.getCategory(),dbContract1.getCategory());
        for (ContractFee item : fees1) {
            Assertions.assertTrue(dbFees1.contains(item));
        }

        Contract contract2 = new Contract();
        contract2.setId(result1);
        contract2.setSignDate(LocalDate.now().minusMonths(2));
        contract2.setStatus(ContractStatus.INEFFECTIVE.ordinal());
        contract2.setStopBy(randLong(1L, 100L));
        contract2.setStopTime(LocalDateTime.now());
        contract2.setModifierId(randLong(1L, 100L));
        contract2.setModifierName(randStr(10));
        contract2.setCategory(TaxCategory.COMPANY.getValue());
        ContractExt ext2 = new ContractExt();
        ext2.setCostType(CostType.BANK_COLLECTION.getValue());
        ext2.setBankName(randStr(20));
        ext2.setBankAccountName(randStr(20));
        ext2.setBankAccountNo(randStr(18));
        ext2.setRemark(randStr(20));
        List<ContractFee> fees2 = Arrays.asList(
                fees1.get(0),
                fees1.get(1),
                fees1.get(2),
                fees1.get(3),
                mockContractFee(2L, ContractFeeCategory.PERIOD, contract1.getStartDate(), contract1.getEndDate().minusMonths(8)),
                mockContractFee(2L, ContractFeeCategory.PERIOD, contract1.getEndDate().minusMonths(8).plusDays(1), contract1.getEndDate().minusMonths(4)),
                mockContractFee(2L, ContractFeeCategory.PERIOD, contract1.getEndDate().minusMonths(4).plusDays(1), contract1.getEndDate())
        );
        Assertions.assertTrue(contractService.updateContract(contract2, ext2, fees2));

        Contract dbContract2 = contractService.findById(result1);
        Assertions.assertEquals(contract2.getSignDate(), dbContract2.getSignDate());
        Assertions.assertEquals(contract2.getStatus(), dbContract2.getStatus());
        Assertions.assertEquals(contract2.getStopBy(), dbContract2.getStopBy());
        Assertions.assertEquals(contract2.getCategory(),dbContract2.getCategory());
        Assertions.assertEquals(contract1.getRealtyId(), dbContract2.getRealtyId());
        Assertions.assertEquals(contract1.getRealtyName(), dbContract2.getRealtyName());
        Assertions.assertEquals(contract1.getCustomerId(), dbContract2.getCustomerId());
        Assertions.assertEquals(contract1.getCustomerName(), dbContract2.getCustomerName());

        ContractExt dbExt2 = contractService.findExtByContractId(result1);
        Assertions.assertEquals(ext2.getCostType(), dbExt2.getCostType());
        Assertions.assertEquals(ext2.getBankName(), dbExt2.getBankName());
        Assertions.assertEquals(ext2.getBankAccountName(), dbExt2.getBankAccountName());
        Assertions.assertEquals(ext2.getBankAccountNo(), dbExt2.getBankAccountNo());
        Assertions.assertEquals(ext2.getRemark(), dbExt2.getRemark());
        Assertions.assertEquals(ext1.getFirstRate(), dbExt2.getFirstRate());
        Assertions.assertEquals(ext1.getMonthlyRate(), dbExt2.getMonthlyRate());

        List<ContractFee> dbFees2 = contractService.listContractFee(result1);
        Assertions.assertEquals(fees2.size(), dbFees2.size());
        for (ContractFee item : fees2) {
            Assertions.assertTrue(dbFees2.contains(item));
        }
    }

    @Test
    void listContractPage() {
        long lastRealty = 0L;
        for (int i = 0; i < 20; i++) {
            Contract contract = mockContract(ContractType.LEASE, lastRealty);
            lastRealty = contract.getRealtyId();
            contractService.addContract(contract, mockContractExt(), null);
        }

        ContractSearchVo searchVo = new ContractSearchVo();
        searchVo.setType(ContractType.LEASE.getValue());
        searchVo.setPageNo(2);
        searchVo.setPageSize(10);
        searchVo.setOrderStr("id desc");
        PageResult<ContractVo> page = contractService.listContractPage(searchVo);
        Assertions.assertEquals(searchVo.getPageSize(), page.getDataList().size());
        Assertions.assertTrue(page.getTotalSize() >= 20);
    }

    @Test
    void listBillContract() {
        ContractBillSearchVo search = new ContractBillSearchVo();
        search.setTypes(Arrays.asList(ContractType.LEASE.getValue(), ContractType.ESTATE.getValue()));
        search.setStatus(ContractStatus.EFFECTIVE.ordinal());
        // 账单月第1天
        search.setEndDateBegin(DateUtils.parseDate(LocalDate.now().getYear(), LocalDate.now().getMonthValue(), 1));
        // 账单月最后1天
        search.setStartDateBegin(DateUtils.getLastDateInMonth(LocalDate.now()));

        search.setBillYear(LocalDate.now().getYear());
        search.setBillMonth(LocalDate.now().getMonthValue());
        search.setBillGenerated(Boolean.FALSE);

        Assertions.assertDoesNotThrow(() -> {
            contractService.listBillContract(search);
        });
    }

    @Test
    void listRealtyAvailableContract() {
        Assertions.assertDoesNotThrow(() -> {
            contractService.listRealtyAvailableContract(randLong(1L, 10L));
        });
    }

    private Contract mockContract() {
        return mockContract(null);
    }

    private Contract mockContract(ContractType contractType) {
        return mockContract(contractType, 0L);
    }

    private Contract mockContract(ContractType contractType,boolean isContractNo) {
        Contract contract = mockContract(contractType, 0L);
        contract.setContractNo(randStr(30));
        contract.setStatus(ContractStatus.EFFECTIVE.ordinal());
        return contract;
    }

    private Contract mockContract(ContractType contractType, long seed) {
        contractType = contractType == null ? ContractType.fromValue(randInt(1, ContractType.values().length)) : contractType;

        Contract result = new Contract();
        result.setOrderNo(randStr(12));
        result.setType(contractType.getValue());
        result.setRealtyId(randLong(1L, 100L) + seed);
        result.setRealtyName(randStr(20));
        result.setCustomerId(randLong(1L, 100L));
        result.setCustomerName(randStr(20));
        result.setStatus(ContractStatus.INEFFECTIVE.ordinal());
        result.setSignDate(LocalDate.now());
        result.setStartDate(LocalDate.now());
        result.setEndDate(LocalDate.now().plusYears(1));
        result.setStatus(ContractStatus.fromValue(randInt(1, ContractStatus.values().length)).ordinal());
        result.setCreatorId(randLong(0, 100));
        result.setCreatorName(randStr(10));
        result.setModifierId(randLong(0, 100));
        result.setModifierName(randStr(10));
        result.setCategory(TaxCategory.PERSONAL.getValue());
        return result;
    }

    private ContractExt mockContractExt() {
        ContractExt result = new ContractExt();
        result.setCostType(CostType.fromValue(randInt(1, CostType.values().length)).getValue());
        result.setBankName(randStr(20));
        result.setBankAccountName(randStr(10));
        result.setBankAccountNo(randStr(18));
        result.setBankAccountIdcard(randStr(18));
        result.setFirstRate(randDecimal(BigDecimal.ONE, BigDecimal.TEN, 2));
        result.setMonthlyRate(randDecimal(BigDecimal.ONE, BigDecimal.TEN, 2));
        result.setMonthlyFeeAbs(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(200), 2));
        result.setRemark(randStr(20));
        return result;
    }

    private ContractFee mockContractFee(Long feeId, ContractFeeCategory category, LocalDate startDate, LocalDate endDate) {
        ContractFee result = new ContractFee();
        result.setFeeId(feeId);
        result.setCategory(category.ordinal());
        result.setAmount(randDecimal(BigDecimal.valueOf(100), BigDecimal.valueOf(10000), 2));
        result.setStartDate(startDate);
        result.setEndDate(endDate);
        return result;
    }

    private void checkContract(Contract c1,Contract c2){
        Assertions.assertNotNull(c1);
        Assertions.assertNotNull(c2);
        Assertions.assertEquals(c1.getBusinessLicenseName(), c2.getBusinessLicenseName());
        Assertions.assertEquals(c1.getTaxSerial(), c2.getTaxSerial());
    }

    private void checkContract(Contract c1,ContractVo c2){
        Assertions.assertNotNull(c1);
        Assertions.assertNotNull(c2);
        Assertions.assertEquals(c1.getBusinessLicenseName(), c2.getBusinessLicenseName());
        Assertions.assertEquals(c1.getTaxSerial(), c2.getTaxSerial());
    }
    private void checkContract(ContractVo c1,Contract c2){
        Assertions.assertNotNull(c1);
        Assertions.assertNotNull(c2);
        Assertions.assertEquals(c1.getBusinessLicenseName(), c2.getBusinessLicenseName());
        Assertions.assertEquals(c1.getTaxSerial(), c2.getTaxSerial());
    }
}
