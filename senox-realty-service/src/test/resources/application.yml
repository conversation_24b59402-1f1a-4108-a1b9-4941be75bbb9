server:
  port: 8081

spring:
  profiles:
    active: test
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:test
    username: sa
    password:
  sql:
    init:
      platform: h2
      encoding: UTF-8
      schema-locations: classpath:db/schema-h2.sql
      data-locations: classpath:db/schema-h2-data.sql
  redis:
    host: **************
    port: 6379
    password: WT0UdHepNsSnYb81
    timeout: 10000
    database: 2
  rabbitmq:
    addresses: *************:5672
    username: smart
    password: smart
    virtual-host: /smart
    connection-timeout: 15000
    consumer-threads: 5
  zipkin:
    enabled: false

senox:
  realty:
    receipt:
      config:
        artificial-audit: true
        goods:
          - name: 水费
            title: 水冰雪
            unit: 月
            tax: 0.03
            tax-code: 1100301010000000000
            preferential-tax-mark: USED
            sales-tax-management: TYPE_10
            tax-rate-mark: NORMAL_TAX_RATE
          - name: 电费
            title: 供电
            unit: 月
            tax: 0.13
            tax-code: 1100101020200000000
            preferential-tax-mark: NOT_USED
            tax-rate-mark: NORMAL_TAX_RATE
          - name: 管理费
            title: 企业管理服务
            unit: 月
            tax: 0.06
            tax-code: 3040801010000000000
            preferential-tax-mark: NOT_USED
            tax-rate-mark: NORMAL_TAX_RATE
          - name: 租金
            title: 经营租赁
            unit: 月
            tax: 0.05
            tax-code: 3040502020400000000
            preferential-tax-mark: USED
            sales-tax-management: TYPE_11
            tax-rate-mark: NORMAL_TAX_RATE
            constraint-type: REAL_ESTATE

    config:
      water-base: 2
      electric-base: 5
      energy-sources:
        WATER:
          - 九层楼
          - 蛋品区路口
          - 水果区
        ELECTRIC:
          - 商业
          - 商平
          - 民用
          - 光伏
  contract:
    serial:
      length: 5
      fillChar: 0
  oneTimeFee:
    serial:
      length: 5
      fillChar: 0
  maintain:
    serial:
      prefix: MT
      length: 5
      fill-char: 0
  maintainJob:
    serial:
      prefix: JOB
      length: 5
      fill-char: 0
    default:
      handlerId: 148
      handlerName: 屈志培
      handlerDeptId: 4
      handlerDeptName: 总裁办-信息安全部

logging:
  level:
   com.senox.realty.mapper : debug
