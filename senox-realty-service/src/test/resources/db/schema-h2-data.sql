INSERT INTO r_fee(id, name, alias, amount, category, creator_id, creator_name, create_time,modifier_id, modifier_name, modified_time)
VALUES (7, '保证金', 'DEPOSIT', 0, 0, 1, 'admin', now(), 1, 'admin', now());

INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (1, 14, 'BB0298001A', '综合楼3电梯1', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (2, 14, 'BB0298001B', '综合楼4', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (3, 14, 'BB0298001C', '综合楼5电梯2', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (4, 68, 'BB0599506A', '中国移动', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (5, 78, 'BB0698678A', '综合楼7层', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (6, 78, 'BB0698678B', '综合楼8层', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (7, 268, 'CB0125122A', '38号仓库002A', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (8, 3682, 'QB0989001A', 'D栋中国联通', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (9, 3682, 'QB0989001B', 'E栋中国联通', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (10, 5684, 'XB0152003A', '曾志坚冷库', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (11, 5125, 'XB0128001A', '8号大棚01号B', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin',
        '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (12, 105, 'BB0799710A', '七楼机房A', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (13, 1086, 'GB0296212A', '中国电信', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');
INSERT INTO r_realty_alias (id, realty_id, serial_no, name, is_disabled, creator_id, creator_name, create_time,
                            modifier_id, modifier_name, modified_time)
VALUES (14, 267, 'CB0125121A', '38号仓库001A', 0, 1, 'admin', '2022-08-09 14:51:16', 1, 'admin', '2022-08-09 14:51:16');

insert into r_one_time_fee (id, name, is_disabled, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time, mobile_editable)
values  (1, '蛋品区车辆违约卸货违约金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 1),
        (2, '其他违规违章处理费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (3, '合同转名费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (4, '代租手续费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (5, '购灭火器费用', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (6, '物业缴费卡工本费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (7, '临时场地使用费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 1),
        (8, '遗失合同手续费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (9, '施工单位管理费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (10, '施工人员办证费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (11, '停车临卡丢失赔偿金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (12, '岗亭设施事故赔款', 0, 1, 'admin', '2021-10-28 15:13:41', 2, 'jiangqq', '2022-04-06 10:08:15', 0),
        (13, '物业损坏赔偿', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (14, '遗失按金单手续费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (15, '垃圾堆放费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (16, '装修押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (17, '月卡场地使用费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (18, '月卡工本费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (19, '工本费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (20, '办理工商执照及税务登记证押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (21, '流动商贩场地使用费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 1),
        (22, '流动商贩押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (23, '装修拆墙押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (24, '装修拆楼梯押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (25, '施工单位装修管理押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (26, '交通违约占道使用费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (27, '复印费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (28, '增容费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (29, '车辆信息变更手续费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (30, '垃圾清理费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (31, '市场派发传单处理费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 1),
        (32, '动植物检疫检测费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (33, '市场管理保证金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (34, '资产报废收入', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (35, '食堂客餐收入', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (36, '物业设施恢复押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (37, '会议室租赁使用费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (38, '补交费用', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (39, '资产处置收入', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (40, '其它押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (41, '退费(水电，租管费）', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (42, '物业维修费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (43, '超时停车违约金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (44, '管理手册工本费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (45, '加建冷库保证金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (46, '门禁卡工本费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (47, '门禁卡押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41', 0),
        (48, '滞纳金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (49, '有偿场地证明费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (50, '办理居住证服务费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (51, '消防安全保证金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (52, '购烟感报警器费用', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (53, '冻品蛋品车辆入场卸货押金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (54, '冻品区车辆违约卸货违约金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (55, '设备维修费', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-01 19:00:00', 0),
        (56, '加建冷库经营保证金', 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2022-03-02 19:00:00', 0),
        (57, '岗亭设施事故赔款款_57', 1, 1, 'admin', '2022-03-01 19:00:00', 2, 'jiangqq', '2022-04-06 10:12:38', 0),
        (58, '岗亭设施事故赔款款_58', 1, 1, 'admin', '2022-04-06 19:00:00', 1, 'admin', '2022-04-19 16:15:26', 0);
insert into u_department (id, name, full_name, parent_id, order_no, is_disabled, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time)
values  (1, '总裁办', '总裁办', 0, 50, 0, 1, 'admin', '2021-09-17 14:50:03', 2, 'jiangqq', '2021-10-28 14:37:06'),
        (2, '后勤部', '总裁办-后勤部', 1, 50, 0, 1, 'admin', '2021-09-17 14:50:48', 1, 'admin', '2021-09-17 14:50:48'),
        (3, '人事部', '总裁办-人事部', 1, 50, 0, 1, 'admin', '2021-09-17 14:50:58', 1, 'admin', '2021-09-17 14:50:58'),
        (4, '信息安全部', '总裁办-信息安全部', 1, 50, 0, 1, 'admin', '2021-09-17 14:51:12', 1, 'admin', '2021-10-20 14:52:46'),
        (5, '财务中心', '财务中心', 0, 51, 0, 1, 'admin', '2021-09-17 14:51:48', 1, 'admin', '2021-09-17 14:52:28'),
        (6, '客服中心', '客服中心', 0, 52, 0, 1, 'admin', '2021-09-17 14:53:19', 1, 'admin', '2021-09-17 14:53:19'),
        (7, '冷链物流中心', '冷链物流中心', 0, 54, 0, 1, 'admin', '2021-09-17 14:53:49', 1, 'admin', '2021-09-17 14:55:41'),
        (8, '项目工程部', '项目工程部', 0, 53, 0, 1, 'admin', '2021-09-17 14:54:37', 1, 'admin', '2021-09-17 14:55:34'),
        (9, '信立农批商城', '信立农批商城', 0, 55, 0, 1, 'admin', '2021-09-17 14:54:58', 1, 'admin', '2021-09-17 14:55:47'),
        (10, '鹏翔装卸部', '鹏翔装卸部', 0, 56, 0, 1, 'admin', '2021-09-17 14:56:22', 1, 'admin', '2021-09-17 14:56:22'),
        (11, '收费部', '财务中心-收费部', 5, 50, 0, 1, 'admin', '2021-09-17 14:57:23', 1, 'admin', '2021-09-17 14:57:23'),
        (12, '会计部', '财务中心-会计部', 5, 51, 0, 1, 'admin', '2021-09-17 14:57:50', 1, 'admin', '2021-09-17 14:57:50'),
        (13, '出纳组', '财务中心-出纳组', 5, 52, 0, 1, 'admin', '2021-09-17 14:58:10', 1, 'admin', '2021-09-17 14:58:10'),
        (14, '保安部', '客服中心-保安部', 6, 50, 0, 1, 'admin', '2021-09-17 14:59:15', 1, 'admin', '2021-09-17 14:59:15'),
        (15, '保洁部', '客服中心-保洁部', 6, 51, 0, 1, 'admin', '2021-09-17 14:59:33', 1, 'admin', '2021-09-17 14:59:33'),
        (16, '客服部', '客服中心-客服部', 6, 52, 0, 1, 'admin', '2021-09-17 15:00:37', 1, 'admin', '2021-09-17 15:00:37'),
        (17, '物维部', '客服中心-物维部', 6, 53, 0, 1, 'admin', '2021-09-17 15:01:27', 1, 'admin', '2021-09-17 15:01:27'),
        (18, '制冷工程部', '冷链物流中心-制冷工程部', 36, 50, 0, 1, 'admin', '2021-09-17 15:03:58', 1, 'admin', '2021-09-17 15:03:58'),
        (19, '业务部', '冷链物流中心-业务部', 7, 51, 0, 1, 'admin', '2021-09-17 15:04:15', 1, 'admin', '2021-09-17 15:04:15'),
        (20, '冷藏部', '冷链物流中心-冷藏部', 36, 52, 0, 1, 'admin', '2021-09-17 15:04:26', 1, 'admin', '2021-09-17 15:04:26'),
        (21, '配送部', '冷链物流中心-配送部', 7, 53, 0, 1, 'admin', '2021-09-17 15:04:58', 1, 'admin', '2021-09-17 15:04:58'),
        (22, '干仓部', '冷链物流中心-干仓部', 7, 54, 0, 1, 'admin', '2021-09-17 15:05:19', 1, 'admin', '2021-09-17 15:05:19'),
        (23, '单证部', '冷链物流中心-单证部', 7, 55, 0, 1, 'admin', '2021-09-17 15:05:59', 1, 'admin', '2021-09-17 15:05:59'),
        (24, '岗亭收费', '客服中心-岗亭收费', 6, 50, 0, 1, 'admin', '2022-03-01 18:12:11', 1, 'admin', '2022-03-01 18:12:11'),
        (25, '办公室', '冷链物流中心-办公室', 7, 50, 0, 2, 'jiangqq', '2022-03-24 09:01:18', 2, 'jiangqq', '2022-03-24 09:01:18'),
        (26, '其它', '其它', 0, 58, 0, 72, 'zhangsj', '2022-04-20 14:01:29', 72, 'zhangsj', '2022-04-20 14:07:20'),
        (27, '检测中心', '总裁办-检测中心', 1, 50, 0, 72, 'zhangsj', '2022-04-20 14:02:42', 72, 'zhangsj', '2022-04-20 14:02:42'),
        (28, '办公室', '客服中心-办公室', 6, 50, 0, 72, 'zhangsj', '2022-04-20 14:04:01', 72, 'zhangsj', '2022-04-20 14:04:01'),
        (30, '财务部', '冷链物流中心-财务部', 7, 50, 0, 72, 'zhangsj', '2022-04-20 14:06:22', 72, 'zhangsj', '2022-04-20 14:06:22'),
        (31, '货运部', '冷链物流中心-货运部', 7, 50, 0, 72, 'zhangsj', '2022-04-20 14:06:30', 72, 'zhangsj', '2022-04-20 14:06:30'),
        (32, '运输部', '冷链物流中心-运输部', 7, 50, 0, 72, 'zhangsj', '2022-04-20 14:06:45', 72, 'zhangsj', '2022-04-20 14:06:45'),
        (33, '金融服务中心', '金融服务中心', 0, 57, 0, 72, 'zhangsj', '2022-04-20 14:06:54', 72, 'zhangsj', '2022-04-20 14:07:17'),
        (34, '员工食堂', '后勤部-员工食堂', 2, 50, 0, 72, 'zhangsj', '2022-04-26 15:55:50', 72, 'zhangsj', '2022-04-26 15:55:50'),
        (35, '物流中心', '冷链物流中心-物流中心', 7, 50, 0, 72, 'zhangsj', '2022-05-04 17:06:02', 72, 'zhangsj', '2022-05-04 17:06:02'),
        (36, '冷藏中心', '冷链物流中心-冷藏中心', 7, 50, 0, 72, 'zhangsj', '2022-05-04 17:11:46', 72, 'zhangsj', '2022-05-04 17:11:46');
insert into r_one_time_fee_department (fee_id, department_id, is_disabled, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time)
values  (1, 14, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (1, 16, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (2, 11, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (2, 14, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (2, 16, 0, 1, 'admin', '2022-03-02 15:07:33', 1, 'admin', '2022-03-02 15:07:33'),
        (2, 17, 0, 1, 'admin', '2022-03-02 15:27:22', 1, 'admin', '2022-03-02 15:27:22'),
        (3, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (4, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (5, 14, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (5, 16, 0, 1, 'admin', '2022-03-01 17:24:26', 1, 'admin', '2022-03-01 17:24:26'),
        (6, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (7, 11, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (7, 14, 0, 1, 'admin', '2022-03-01 17:29:44', 1, 'admin', '2022-03-01 17:29:44'),
        (7, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (8, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (9, 17, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (10, 17, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (11, 11, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (11, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (12, 4, 0, 2, 'jiangqq', '2022-04-06 10:08:15', 2, 'jiangqq', '2022-04-06 10:08:15'),
        (12, 14, 0, 2, 'jiangqq', '2022-04-06 10:08:00', 2, 'jiangqq', '2022-04-06 10:08:00'),
        (13, 11, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (13, 14, 0, 1, 'admin', '2022-03-01 17:42:41', 1, 'admin', '2022-03-01 17:42:41'),
        (13, 17, 0, 1, 'admin', '2022-03-01 17:42:41', 1, 'admin', '2022-03-01 17:42:41'),
        (14, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (14, 17, 0, 1, 'admin', '2022-03-01 17:34:47', 1, 'admin', '2022-03-01 17:34:47'),
        (15, 17, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (16, 17, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (17, 11, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (17, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (18, 11, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (18, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (19, 1, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (19, 2, 0, 1, 'admin', '2022-03-02 15:25:37', 1, 'admin', '2022-03-02 15:25:37'),
        (19, 3, 0, 1, 'admin', '2022-03-02 15:19:37', 1, 'admin', '2022-03-02 15:19:37'),
        (19, 11, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (19, 16, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (19, 17, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (20, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (21, 14, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (22, 14, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (23, 17, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (24, 17, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (25, 17, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (26, 14, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (27, 6, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (27, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (28, 17, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (29, 11, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (29, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (30, 17, 0, 1, 'admin', '2022-03-02 15:21:07', 1, 'admin', '2022-03-02 15:21:07'),
        (31, 14, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (32, 2, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (33, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (34, 1, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (34, 3, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (34, 14, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (34, 16, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (34, 17, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (35, 1, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (35, 2, 0, 1, 'admin', '2022-03-01 17:38:54', 1, 'admin', '2022-03-01 17:38:54'),
        (35, 3, 0, 1, 'admin', '2022-03-01 17:38:43', 1, 'admin', '2022-03-01 17:38:43'),
        (36, 17, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (37, 1, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (37, 3, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (38, 6, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (38, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (38, 17, 0, 1, 'admin', '2022-03-01 17:36:55', 1, 'admin', '2022-03-01 17:36:55'),
        (39, 1, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (39, 17, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (40, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (40, 17, 0, 1, 'admin', '2022-03-01 17:37:07', 1, 'admin', '2022-03-01 17:37:07'),
        (41, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (41, 17, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (42, 17, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (43, 14, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (44, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (45, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (46, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (47, 16, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (48, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (49, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (50, 14, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (51, 14, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (51, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (51, 17, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (52, 14, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (53, 14, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (53, 16, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (54, 14, 0, 1, 'admin', '2021-10-28 15:13:41', 1, 'admin', '2021-10-28 15:13:41'),
        (54, 16, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (55, 4, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (55, 11, 0, 1, 'admin', '2022-03-01 19:00:00', 1, 'admin', '2022-03-01 19:00:00'),
        (56, 6, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (56, 11, 0, 1, 'admin', '2022-03-02 19:00:00', 1, 'admin', '2022-03-02 19:00:00'),
        (56, 17, 0, 1, 'admin', '2022-03-01 18:06:24', 1, 'admin', '2022-03-01 18:06:24'),
        (58, 4, 0, 1, 'admin', '2022-04-06 19:00:00', 1, 'admin', '2022-04-06 19:00:00'),
        (58, 14, 0, 1, 'admin', '2022-04-06 19:00:00', 1, 'admin', '2022-04-06 19:00:00');


insert into s_system_setting(name, `value`, modified_time)
values ('REALTY_BILL_YEAR', 2022, now()),('REALTY_BILL_MONTH', 11, now());

INSERT INTO `r_maintain_media` VALUES (2, 2, 0, 1, 'http://test.smart.senox.com.cn/pic/maintain/20230331/e9a4b9b6f4a14a568de74576eca2161f.png', '2023-03-31 09:33:07');
INSERT INTO `r_maintain_media` VALUES (5, 4, 0, 1, '1', '2023-03-31 14:17:04');
INSERT INTO `r_maintain_media` VALUES (6, 4, 0, 1, '2', '2023-03-31 14:17:04');
INSERT INTO `r_maintain_media` VALUES (9, 3, 0, 1, '1', '2023-03-31 14:21:20');
INSERT INTO `r_maintain_media` VALUES (10, 3, 0, 1, '2', '2023-03-31 14:21:20');
INSERT INTO `r_maintain_media` VALUES (15, 5, 0, 1, '1', '2023-03-31 14:27:53');
INSERT INTO `r_maintain_media` VALUES (16, 5, 0, 1, '2', '2023-03-31 14:27:53');

INSERT INTO `r_maintain_order`(id, order_no, maintain_type, customer_name, contact, address, problem, job_no, handler_name, handler_dept_id, handler_dept_name, management_dept_id, management_dept_name, finish_time, status, user_confirm, is_disabled, create_openid, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time)
    VALUES (2, 'orderNo1', 1, 'customerName1', 'contact1', 'address1', 'problem1', '', NULL, 0, NULL, 0, NULL, NULL, 1, 0, 0, 'createOpenid1', 145, 'quzp', '2023-03-31 09:29:04', 145, 'quzp', '2023-03-31 09:33:07'),
            (3, '1', 0, '', '', 'ddd', '', '', NULL, 0, NULL, 0, NULL, NULL, 0, 0, 0, '', 145, 'quzp', '2023-03-31 09:35:14', 145, 'quzp', '2023-03-31 14:21:20'),
            (4, '', 0, '', '', 'ddd', '', '', NULL, 0, NULL, 0, NULL, NULL, 1, 0, 0, '', 145, 'quzp', '2023-03-31 14:15:53', 145, 'quzp', '2023-03-31 14:18:51'),
            (5, '2', 0, '', '', 'ddd', '', '', NULL, 0, NULL, 0, NULL, NULL, 0, 0, 0, '', 145, 'quzp', '2023-03-31 14:26:58', 145, 'quzp', '2023-03-31 14:29:18');

INSERT INTO `r_maintain_job` (`id`, `order_id`, `job_no`, `maintain_type`, `status`, `creator_id`, `creator_name`, `create_time`, `modifier_id`, `modifier_name`, `modified_time`)
VALUES (1, 2, 'JOB2023040300002', 0, 0, 145, 'quzp', '2023-04-03 13:28:48', 145, 'quzp', '2023-04-03 13:28:59'),
       (2, 2, 'JOB2023040300003', 0, 0, 145, 'quzp', '2023-04-03 13:28:48', 145, 'quzp', '2023-04-03 13:28:59'),
       (3, 3, 'JOB2023040300004', 0, 0, 145, 'quzp', '2023-04-03 13:28:48', 145, 'quzp', '2023-04-03 13:28:59'),
       (4, 3, 'JOB2023040300005', 0, 0, 145, 'quzp', '2023-04-03 13:28:48', 145, 'quzp', '2023-04-03 13:28:59'),
       (5, 4, 'JOB2023040300006', 0, 0, 145, 'quzp', '2023-04-03 13:28:48', 145, 'quzp', '2023-04-03 13:28:59'),
       (6, 5, 'JOB2023040300007', 0, 0, 145, 'quzp', '2023-04-03 13:28:48', 145, 'quzp', '2023-04-03 13:28:59'),
       (7, 5, 'JOB2023040300008', 0, 0, 145, 'quzp', '2023-04-03 13:28:48', 145, 'quzp', '2023-04-03 13:28:59'),
       (8, 2, 'JOB2023040300009', 0, 0, 145, 'quzp', '2023-04-03 13:28:48', 145, 'quzp', '2023-04-03 13:28:59');

