nacos:
  server-addr: 10.205.205.22:8848
  namespace: senox-dev

spring:
  application:
    name: senox-realty
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  cloud:
    nacos:
      config:
        namespace: ${nacos.namespace}
        server-addr: ${nacos.server-addr}
        file-extension: yaml
      discovery:
        namespace: ${nacos.namespace}
        server-addr: ${nacos.server-addr}

management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: health,info
      base-path: /guard

info:
  app:
    name: ${spring.application.name}
    version: 1.0.0

