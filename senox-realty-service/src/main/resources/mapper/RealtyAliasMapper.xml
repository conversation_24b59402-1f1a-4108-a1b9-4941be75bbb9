<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyAliasMapper">
    
    <insert id="batchAddAlias" parameterType="com.senox.realty.domain.RealtyAlias">
        INSERT INTO r_realty_alias(
            realty_id, serial_no, name, water_readings, electric_readings, creator_id, creator_name, create_time
            , modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.realtyId}, #{item.serialNo}, #{item.name}, #{item.waterReadings}, #{item.electricReadings}
            , #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <update id="batchUpdateAlias" parameterType="com.senox.realty.domain.RealtyAlias">
        UPDATE r_realty_alias
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="realty_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.realtyId != null">
                        WHEN id = #{item.id} THEN #{item.realtyId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="serial_no = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.serialNo != null">
                        WHEN id = #{item.id} THEN #{item.serialNo}
                    </if>
                </foreach>
            </trim>
            <trim prefix="name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.name != null">
                        WHEN id = #{item.id} THEN #{item.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="water_readings = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.waterReadings != null">
                        WHEN id = #{item.id} THEN #{item.waterReadings}
                    </if>
                </foreach>
            </trim>
            <trim prefix="electric_readings = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.electricReadings != null">
                        WHEN id = #{item.id} THEN #{item.electricReadings}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierId != null">
                        WHEN id = #{item.id} THEN #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierName != null">
                        WHEN id = #{item.id} THEN #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE id IN <foreach collection="list" item="item" separator="," open="(" close=")">#{item.id}</foreach>
    </update>

    <!-- 批量更新水电读数 -->
    <update id="batchUpdateAliasReadings" parameterType="com.senox.realty.vo.RealtyReadingsVo">
        UPDATE r_realty_alias
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="water_readings = CASE" suffix=" ELSE water_readings END,">
                <foreach collection="list" item="item">
                    <if test="item.waterReadings != null">
                        WHEN serial_no = #{item.realtySerial} THEN #{item.waterReadings}
                    </if>
                </foreach>
            </trim>
            <trim prefix="electric_readings = CASE" suffix=" ELSE electric_readings END,">
                <foreach collection="list" item="item">
                    <if test="item.electricReadings != null">
                        WHEN serial_no = #{item.realtySerial} THEN #{item.electricReadings}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierId != null">
                        WHEN serial_no = #{item.realtySerial} THEN #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierName != null">
                        WHEN serial_no = #{item.realtySerial} THEN #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE serial_no IN <foreach collection="list" item="item" separator="," open="(" close=")">#{item.realtySerial}</foreach>
            AND is_disabled = 0
    </update>

</mapper>