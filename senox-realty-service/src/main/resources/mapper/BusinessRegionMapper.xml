<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.BusinessRegionMapper">

    <resultMap id="Result_BusinessRegion" type="com.senox.realty.domain.BusinessRegion">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="cycleNo" jdbcType="VARCHAR" column="cycle_no" />
        <result property="orderNum" column="order_num"/>
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加经营区域 -->
    <insert id="addRegion" parameterType="com.senox.realty.domain.BusinessRegion" useGeneratedKeys="true" keyProperty="id">
        insert into r_business_region(
            name, cycle_no, order_num, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{name}, #{cycleNo}, #{orderNum}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新经营区域 -->
    <update id="updateRegion" parameterType="com.senox.realty.domain.BusinessRegion">
        UPDATE r_business_region
        <set>
            <if test="name != null and name != ''">
                name = #{name}
            </if>
            <if test="cycleNo != null and cycleNo != ''">
                , cycle_no = #{cycleNo}
            </if>
            <if test="orderNum != null">
                , order_num = #{orderNum}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 查找经营区域 -->
    <select id="findById" resultMap="Result_BusinessRegion">
        SELECT id, name, cycle_no, order_num FROM r_business_region WHERE id = #{id}
    </select>

    <!-- 经营区域列表 -->
    <select id="listAll" resultMap="Result_BusinessRegion">
        SELECT id, name, cycle_no, order_num FROM r_business_region WHERE is_disabled = 0
    </select>

</mapper>
