<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingInspectTaskItemMapper">

    <sql id="taskListSql">
        SELECT ti.task_id
            , t.name AS task_name
            , ti.utility_id
            , CONCAT(u.region_name, u.street_name, u.location) as utility_name
            , ti.enterprise_id
            , e.name AS enterprise_name
            , ti.realty_serial
            , r.name AS realty_name
            , IFNUll(r.order_num, 0) order_num
            , SUM(CASE WHEN ti.inspect_type = 'UTILITY' THEN ti.id ELSE 0 END) AS utility_task_id
            , SUM(CASE WHEN ti.inspect_type = 'UTILITY' THEN 1 ELSE 0 END) AS utility_inspect
            , SUM(CASE WHEN ti.inspect_type = 'UTILITY' THEN ti.inspect_id ELSE 0 END) AS utility_inspect_id
            , SUM(CASE WHEN ti.inspect_type = 'NOTICE' THEN ti.id ELSE 0 END) AS notice_task_id
            , SUM(CASE WHEN ti.inspect_type = 'NOTICE' THEN 1 ELSE 0 END) AS notice_inspect
            , SUM(CASE WHEN ti.inspect_type = 'NOTICE' THEN ti.inspect_id  ELSE 0 END) AS notice_inspect_id
            , SUM(CASE WHEN ti.inspect_type = 'FILE' THEN ti.id ELSE 0 END) AS file_task_id
            , SUM(CASE WHEN ti.inspect_type = 'FILE' THEN 1 ELSE 0 END) AS file_inspect
            , SUM(CASE WHEN ti.inspect_type = 'FILE' THEN ti.inspect_id ELSE 0 END) AS file_inspect_id
            , SUM(CASE WHEN ti.inspect_type = 'STORE' THEN ti.id ELSE 0 END) AS store_task_id
            , SUM(CASE WHEN ti.inspect_type = 'STORE' THEN 1 ELSE 0 END) AS store_inspect
            , SUM(CASE WHEN ti.inspect_type = 'STORE' THEN ti.inspect_id ELSE 0 END) AS store_inspect_id
            , SUM(CASE WHEN ti.inspect_type = 'SMALL_PLACES' THEN ti.id ELSE 0 END) AS small_places_task_id
            , SUM(CASE WHEN ti.inspect_type = 'SMALL_PLACES' THEN 1 ELSE 0 END) AS small_places_inspect
            , SUM(CASE WHEN ti.inspect_type = 'SMALL_PLACES' THEN ti.inspect_id ELSE 0 END) AS small_places_inspect_id
            , SUM(CASE WHEN ti.inspect_type = 'ACCOMMODATE' THEN ti.id ELSE 0 END) AS accommodate_task_id
            , SUM(CASE WHEN ti.inspect_type = 'ACCOMMODATE' THEN 1 ELSE 0 END) AS accommodate_inspect
            , SUM(CASE WHEN ti.inspect_type = 'ACCOMMODATE' THEN ti.inspect_id ELSE 0 END) AS accommodate_inspect_id
            , COUNT(ti.id) AS task_total
            , SUM(CASE WHEN ti.inspect_id > 0 THEN 1 ELSE 0 END) AS task_done
            , t.start_date
            , t.end_date
            , t.create_time
        FROM r_firefighting_inspect_task_item ti
            INNER JOIN r_firefighting_inspect_task t on t.id = ti.task_id
            LEFT JOIN r_firefighting_utility u ON u.id = ti.utility_id
            LEFT JOIN u_enterprise e ON e.id = ti.enterprise_id
            LEFT JOIN r_realty r ON r.serial_no = ti.realty_serial
        <where>
            <if test="taskId != null and taskId > 0">
                AND ti.task_id = #{taskId}
            </if>
            <if test="utilityId != null and utilityId > 0">
                AND ti.utility_id = #{utilityId}
            </if>
            <if test="realtySerial != null and realtySerial > 0">
                AND (ti.realty_serial = #{realtySerial} OR EXISTS (SELECT tr1.task_id FROM r_firefighting_inspect_task_realty tr1 WHERE tr1.task_id = ti.task_id AND tr1.realty_serial = #{realtySerial}))
            </if>
            <if test="taskDateStart != null">
                AND t.end_date >= #{taskDateStart}
            </if>
            <if test="taskDateEnd != null">
                AND t.start_date <![CDATA[<=]]> #{taskDateEnd}
            </if>
            <if test="regionId != null and regionId > 0">
                AND (u.region_id = #{regionId} OR r.region_id = #{regionId} OR EXISTS(SELECT er1.id FROM r_realty er1 INNER JOIN r_firefighting_inspect_task_realty tr2 ON tr2.task_id = ti.task_id AND tr2.task_item_id = ti.id AND tr2.realty_serial = er1.serial_no AND er1.region_id = #{regionId}))
            </if>
            <if test="streetId != null and streetId > 0">
                AND (u.street_id = #{streetId} OR r.street_id = #{streetId} OR EXISTS(SELECT er2.id FROM r_realty er2 INNER JOIN r_firefighting_inspect_task_realty tr3 ON tr3.task_id = ti.task_id AND tr3.task_item_id = ti.id AND tr3.realty_serial = er2.serial_no AND er2.street_id = #{streetId}))
            </if>
            <if test="keyword != null and keyword != ''">
                AND (t.name LIKE CONCAT('%', #{keyword}, '%') OR ti.realty_serial LIKE CONCAT('%', #{keyword}, '%') OR r.name LIKE CONCAT('%', #{keyword}, '%') OR e.name LIKE CONCAT('%', #{keyword}, '%') OR CONCAT(u.region_name, u.street_name, u.location) LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        GROUP BY ti.task_id, ti.utility_id, ti.enterprise_id, e.name, ti.realty_serial, r.name, r.order_num, u.street_name, u.street_name, u.location
    </sql>


    <!-- 任务统计 -->
    <select id="countTaskItem" parameterType="com.senox.realty.vo.FirefightingInspectTaskItemSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM (<include refid="taskListSql"/>) t
        <where>
            <if test="utilityInspect != null">
                AND t.utility_inspect = #{utilityInspect}
            </if>
            <if test="noticeInspect != null">
                AND t.notice_inspect = #{noticeInspect}
            </if>
            <if test="fileInspect != null">
                AND t.file_inspect = #{fileInspect}
            </if>
            <if test="storeInspect != null">
                AND t.store_inspect = #{storeInspect}
            </if>
            <if test="smallPlacesInspect != null">
                AND t.small_places_inspect = #{smallPlacesInspect}
            </if>
            <if test="accommodateInspect != null">
                AND t.accommodate_inspect = #{accommodateInspect}
            </if>
            <if test="taskDone != null">
                <choose>
                    <when test="taskDone">AND t.task_total = t.task_done</when>
                    <otherwise>AND t.task_total > t.task_done</otherwise>
                </choose>
            </if>
        </where>
    </select>

    <!-- 任务列表 -->
    <select id="listTaskItem" parameterType="com.senox.realty.vo.FirefightingInspectTaskItemSearchVo"
            resultType="com.senox.realty.vo.FirefightingInspectPropertyTaskVo">
        SELECT t.task_id, t.task_name, t.utility_id, t.utility_name, t.enterprise_id, t.enterprise_name, t.realty_serial, t.realty_name
            , t.utility_task_id, t.utility_inspect, t.utility_inspect_id, t.notice_task_id, t.notice_inspect, t.notice_inspect_id
            , t.file_task_id, t.file_inspect, t.file_inspect_id, t.store_task_id, t.store_inspect, t.store_inspect_id
            , t.small_places_task_id, t.small_places_inspect, t.small_places_inspect_id, t.accommodate_task_id, t.accommodate_inspect, t.accommodate_inspect_id
            , t.task_total, t.task_done, t.start_date, t.end_date, t.create_time
        FROM (<include refid="taskListSql" />) t
        <where>
            <if test="utilityInspect != null">
                AND t.utility_inspect = #{utilityInspect}
            </if>
            <if test="noticeInspect != null">
                AND t.notice_inspect = #{noticeInspect}
            </if>
            <if test="fileInspect != null">
                AND t.file_inspect = #{fileInspect}
            </if>
            <if test="storeInspect != null">
                AND t.store_inspect = #{storeInspect}
            </if>
            <if test="smallPlacesInspect != null">
                AND t.small_places_inspect = #{smallPlacesInspect}
            </if>
            <if test="accommodateInspect != null">
                AND t.accommodate_inspect = #{accommodateInspect}
            </if>
            <if test="taskDone != null">
                <choose>
                    <when test="taskDone">AND t.task_total = t.task_done</when>
                    <otherwise>AND t.task_total > t.task_done</otherwise>
                </choose>
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY t.task_id DESC, t.utility_id, t.order_num, t.realty_serial</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 根据履行的任务查找巡检任务 -->
    <select id="listTaskItemByFulfilledEvent" parameterType="com.senox.realty.event.InspectTaskFulfilledEvent"
            resultType="com.senox.realty.domain.FirefightingInspectTaskItem">
        SELECT ti.id, ti.task_id, ti.utility_id , ti.enterprise_id, ti.realty_serial, ti.inspect_type, ti.inspect_id
        FROM r_firefighting_inspect_task_item ti
        <where>
            <if test="taskId != null and taskId > 0">
                AND ti.id = #{taskId}
            </if>
            <if test="utilityId != null and utilityId > 0">
                AND ti.utility_id = #{utilityId}
            </if>
            <if test="enterpriseId != null and enterpriseId > 0">
                AND ti.enterprise_id = #{enterpriseId}
            </if>
            <if test="realtySerials != null and realtySerials.size() > 0">
                AND EXISTS (
                    SELECT tr.task_id
                    FROM r_firefighting_inspect_task_realty tr
                    WHERE tr.task_id = ti.task_id
                        AND tr.task_item_id = ti.id
                        AND tr.realty_serial IN <foreach collection="realtySerials" item="item" open="(" close=")" separator=",">#{item}</foreach>
                )
            </if>
        </where>
    </select>
</mapper>