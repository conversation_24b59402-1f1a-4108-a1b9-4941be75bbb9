<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainOrderDayReportMapper">


    <select id="count" parameterType="com.senox.realty.vo.MaintainOrderDayReportSearchVo" resultType="int">
        select count(*) from r_maintain_order_day_report
        <where>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="reportDateStart != null">
                AND report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="list" parameterType="com.senox.realty.vo.MaintainOrderDayReportSearchVo" resultType="com.senox.realty.vo.MaintainOrderDayReportVo">
        select id, report_date, management_dept_id, management_dept_name, add_singular_numbers, complete_singular_numbers
            from r_maintain_order_day_report
        <where>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="reportDateStart != null">
                AND report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY report_date desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sum" parameterType="com.senox.realty.vo.MaintainOrderDayReportSearchVo" resultType="com.senox.realty.vo.MaintainOrderDayReportVo">
        select sum(add_singular_numbers) as add_singular_numbers
            , sum(complete_singular_numbers) as complete_singular_numbers
        from r_maintain_order_day_report
        <where>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="reportDateStart != null">
                AND report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
            AND is_disabled = 0
        </where>
    </select>

</mapper>
