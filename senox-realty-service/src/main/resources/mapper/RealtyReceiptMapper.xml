<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyReceiptMapper">

    <resultMap id="applyResultMap" type="com.senox.pm.vo.ReceiptApplyVo">
        <result property="taxHeader" column="tax_header"/>
        <result property="remark" column="remark"/>
        <result property="receiptNumber" column="receipt_number"/>
        <result property="receiptTime" column="receipt_time"/>
        <result property="receiptType" column="receipt_type"/>
        <result property="pdfUrl" column="pdf_url"/>
        <result property="receiptManName" column="requester_name"/>
        <result property="status" column="status"/>
        <result property="serialNo" column="serial_no"/>
        <result property="taxSerial" column="tax_serial"/>
        <result property="registerAddress" column="register_address"/>
        <result property="registerMobile" column="register_mobile"/>
        <result property="depositBank" column="deposit_bank"/>
        <result property="enterpriseBankAccount" column="enterprise_bank_account"/>
        <result property="headerCategory" column="header_category"/>
        <result property="constraintType" column="constraint_type"/>
        <result property="totalPriceTax" column="total_price_tax"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="totalTaxAmount" column="total_tax_amount"/>
        <result property="ofdUrl" column="ofd_url"/>
        <result property="xmlUrl" column="xml_url"/>
        <result property="mobile" column="mobile"/>
        <result property="email" column="email"/>
        <result property="openid" column="openid"/>
        <result property="statusMessage" column="status_message"/>
        <result property="receiptManName" column="receipt_man_name"/>
        <result property="receiptTime" column="receipt_time"/>
        <result property="receiptRemark" column="receipt_remark"/>
        <result property="createTime" column="create_time"/>

        <collection property="itemList" ofType="com.senox.pm.vo.ReceiptApplyItemVo">
            <result property="name" column="name"/>
            <result property="title" column="title"/>
            <result property="number" column="number"/>
            <result property="includedTaxUnitPrice" column="included_tax_unit_price"/>
            <result property="norms" column="norms"/>
            <result property="unit" column="unit"/>
            <result property="tax" column="tax"/>
            <result property="taxCode" column="tax_code"/>
            <result property="salesTaxManagement" column="sales_tax_management"/>
            <result property="taxRateMark" column="tax_rate_mark"/>
            <result property="preferentialTaxMark" column="preferential_tax_mark"/>
            <result property="linType" column="lin_type"/>
        </collection>
    </resultMap>

    <resultMap id="applyBillResultMap" type="com.senox.realty.vo.RealtyBillReceiptApplyInfoVo">
        <result property="contractNo" column="contract_no"/>
        <result property="realtySerialNo" column="serial_no"/>
        <result property="realtyName" column="name"/>
        <result property="ownerName" column="owner_name"/>
        <result property="billYearMonth" column="bill_year_month"/>
        <result property="replaceLease" column="replace_lease"/>
        <collection property="feeList" ofType="com.senox.realty.vo.FeeVo">
            <result property="amount" column="amount"/>
            <result property="name" column="fee_name"/>
        </collection>


    </resultMap>

    <insert id="add" useGeneratedKeys="true" keyProperty="id" parameterType="com.senox.realty.domain.RealtyReceipt">
        insert into r_realty_receipt (title, header_category, apply_amount, apply_man, apply_user_name, apply_time)
        values (#{title}, #{headerCategory}, #{applyAmount}, #{applyMan}, #{applyUserName}, now())
    </insert>
    <insert id="bindReceipt">
        insert into r_realty_receipt_item(realty_receipt_id, receipt_serial_no)
        values
        <foreach collection="receiptSerialNoList" item="item" separator=",">
            (#{realtyReceiptId},#{item})
        </foreach>
    </insert>
    <select id="findById" parameterType="long" resultType="com.senox.realty.domain.RealtyReceipt">
        select id,
        title,
        header_category,
        apply_amount,
        apply_man,
        apply_time,
        audit_man,
        audit_time,
        audit_remark
        from r_realty_receipt
        <where>
            and id = #{id}
            and is_disabled = 0
        </where>
    </select>
    <select id="count" parameterType="com.senox.realty.vo.RealtyReceiptSearchVo" resultType="int">
        select count(id)
        from r_realty_receipt
        <where>
            <if test="null != headerCategory">
                and header_category = #{headerCategory}
            </if>
            <if test="null != title and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="null != applyMan">
                and apply_man = #{applyMan}
            </if>
            <if test="null != applyStartTime">
                and apply_time >= #{applyStartTime}
            </if>
            <if test="null != applyEndTime">
                and apply_time &lt;= #{applyEndTime}
            </if>
            and is_disabled = 0
        </where>
    </select>
    <select id="list" parameterType="com.senox.realty.vo.RealtyReceiptSearchVo"
            resultType="com.senox.realty.vo.RealtyReceiptVo">
       select rr.id,
       title,
       header_category,
       apply_amount,
       wu.nickname  as apply_man_name,
       apply_user_name,
       apply_status,
       apply_time,
       au.real_name as audit_man_name,
       audit_time,
       audit_remark,
       ra.tax_header,
       ra.tax_serial,
       ra.register_address,
       ra.register_mobile,
       ra.deposit_bank,
       ra.enterprise_bank_account
    from r_realty_receipt rr
         left join wx_user wu on wu.openid = rr.apply_man
         left join u_admin_user au on au.id = rr.audit_man
         left join (select rrt.realty_receipt_id,
                           max(ra.tax_header)              as tax_header,
                           max(ra.tax_serial)              as tax_serial,
                           max(ra.register_address)        as register_address,
                           max(ra.register_mobile)         as register_mobile,
                           max(ra.deposit_bank)            as deposit_bank,
                           max(ra.enterprise_bank_account) as enterprise_bank_account,
                           min(ra.receipt_time)            as receipt_time,
                           if(max(ra.status) != 2, 0, 2)   as status
                    from r_realty_receipt_item rrt
                             inner join r_receipt_apply ra on ra.serial_no = rrt.receipt_serial_no
                    where tax_serial is not null
                    group by rrt.realty_receipt_id) ra on ra.realty_receipt_id = rr.id
        <where>
            <if test="null != headerCategory">
                and header_category = #{headerCategory}
            </if>
            <if test="null != title and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="null != taxHeader and taxHeader != ''">
                and ra.tax_header like concat('%', #{taxHeader}, '%')
            </if>
            <if test="null != applyMan">
                and apply_man = #{applyMan}
            </if>
            <if test="null != applyStartTime">
                and apply_time >= #{applyStartTime}
            </if>
            <if test="null != applyEndTime">
                and apply_time &lt;= #{applyEndTime}
            </if>
            <if test="null != applyStatus">
                 and rr.apply_status = #{applyStatus}
            </if>
            <if test="null != send">
                and rr.send = #{send}
            </if>
            <if test="null != status">
                and ra.status = #{status}
            </if>
            and rr.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                order by ${orderStr}
            </when>
            <otherwise>
                order by apply_time desc
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>
    <update id="updateBatch" parameterType="com.senox.realty.domain.RealtyReceipt">
        update r_realty_receipt
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="audit_man = case" suffix="end,">
                <foreach collection="realtyReceiptList" item="item">
                    <if test="null != item.auditMan">
                        when id = #{item.id} then #{item.auditMan}
                    </if>
                </foreach>
            </trim>
            <trim prefix="audit_time = case" suffix="end,">
                <foreach collection="realtyReceiptList" item="item">
                    <if test="null != item.auditMan">
                        when id = #{item.id} then now()
                    </if>
                </foreach>
            </trim>
            <trim prefix="audit_remark = case" suffix="end,">
                <foreach collection="realtyReceiptList" item="item">
                    <if test="null != item.auditRemark and item.auditRemark != ''">
                        when id = #{item.id} then #{item.auditRemark}
                    </if>
                </foreach>
            </trim>
            <trim prefix="apply_status = case" suffix="end,">
                <foreach collection="realtyReceiptList" item="item">
                    <if test="null != item.applyStatus">
                        when id = #{item.id} then #{item.applyStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="send = case" suffix="end,">
                <foreach collection="realtyReceiptList" item="item">
                    <if test="null != item.send">
                        when id = #{item.id} then #{item.send}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_disabled = case" suffix="end,">
                <foreach collection="realtyReceiptList" item="item">
                    <if test="null != item.isDisabled">
                        when id = #{item.id} then #{item.isDisabled}
                    </if>
                </foreach>
            </trim>
        </trim>
        <where>
            and id in
            <foreach collection="realtyReceiptList" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </where>
    </update>
    <select id="receiptSerialNoListById" parameterType="long" resultType="string">
        select receipt_serial_no from r_realty_receipt_item
        <where>
            and realty_receipt_id in
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>

    </select>

    <select id="receiptApplyListByRealtyReceiptId" resultMap="applyResultMap" parameterType="long"
            resultType="com.senox.pm.vo.ReceiptApplyVo">
        select ra.serial_no
        ,ra.tax_header
        , ra.remark
        , ra.receipt_number
        , ra.receipt_time
        , ra.receipt_type
        , ra.pdf_url
        , wu.nickname as requester_name
        , ra.status
        <if test="isDetail">
            , ra.tax_serial
            , ra.register_address
            , ra.register_mobile
            , ra.deposit_bank
            , ra.enterprise_bank_account
            , ra.header_category
            , ra.constraint_type
            , ra.total_price_tax
            , ra.total_amount
            , ra.total_tax_amount
            , ra.ofd_url
            , ra.xml_url
            , ra.mobile
            , ra.email
            , ra.openid
            , ra.status_message
            , ra.receipt_man_name
            , ra.receipt_time
            , ra.receipt_remark
            , ra.create_time
        </if>
        , rai.name
        , rai.number
        , rai.included_tax_unit_price
        <if test="isDetail">
            , rai.title
            , rai.norms
            , rai.unit
            , rai.tax
            , rai.tax_code
            , rai.sales_tax_management
            , rai.tax_rate_mark
            , rai.preferential_tax_mark
            , rai.lin_type
        </if>
        from r_realty_receipt_item rrt
        inner join r_receipt_apply ra on ra.serial_no = rrt.receipt_serial_no
        inner join r_receipt_apply_item rai on rai.serial_no = ra.serial_no
        left join wx_user wu on wu.openid = ra.openid
        <where>
            and rrt.realty_receipt_id = #{id}
        </where>
    </select>

    <select id="applyBillInfoList" resultMap="applyBillResultMap"  resultType="com.senox.realty.vo.RealtyBillReceiptApplyInfoVo">
        select c.contract_no
             , r.serial_no
             , r.name
             , c.customer_name as owner_name
             , rb.bill_year_month
             , if((select count(rp.id)
                   from r_contract rp
                   where rp.realty_id = c.realty_id
                     and rp.`type` = 4
                     and rp.status in (0, 2)
                   limit 1) > 0, 1, 0) AS replace_lease
             , ifnull(bi.amount,0 ) as amount
             , bi.fee_name

        from r_realty_bill_item bi
        left join r_realty_bill rb on rb.id = bi.bill_id
        left join r_realty r on r.id = rb.realty_id
        left join r_contract c on c.contract_no = rb.contract_no
        <where>
            and  bi.bill_id in
            (select distinct sbi.bill_id
            from r_realty_bill_item sbi
            inner join r_realty_receipt_item rrt on rrt.receipt_serial_no = sbi.receipt_serial_no and rrt.realty_receipt_id = #{id})
        </where>
    </select>

    <select id="getBySerialNoList" resultType="com.senox.realty.domain.RealtyReceipt">
        select distinct r.id,
        r.title,
        r.header_category,
        r.apply_amount,
        r.apply_man,
        r.apply_time,
        r.audit_man,
        r.audit_time,
        r.audit_remark
        from r_realty_receipt r
        inner join r_realty_receipt_item ri on ri.realty_receipt_id = r.id
        <where>
            and ri.receipt_serial_no in
            <foreach collection="serialNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and r.is_disabled = 0
        </where>
    </select>

    <insert id="realtyReceiptBindRealtyBill">
        insert into r_realty_bill_receipt(bill_id,receipt_apply_id)
        values
        <foreach collection="realtyBillIds" item="item" index="index" separator=",">
            (#{item},#{receiptId})
        </foreach>
    </insert>

    <delete id="realtyReceiptUnbindRealtyBill">
        delete
        from r_realty_bill_receipt
        where (bill_id,receipt_apply_id) in
        <foreach item="item" index="index" collection="realtyBillIds" separator="," open="(" close=")">
            (#{item},#{receiptId})
        </foreach>
    </delete>

    <update id="refreshBillReceiptStatus">
        update r_realty_bill b
            inner join (select rbi.bill_id              as bill_id,
                               max(ra.tax_header)       as tax_header,
                               min(ra.receipt_time)     as receipt_time,
                               max(ra.receipt_man)      as receipt_man,
                               max(ra.receipt_man_name) as receipt_man_name
                        from r_realty_bill_item rbi
                                 inner join r_receipt_apply ra on ra.serial_no = rbi.receipt_serial_no
                        where tax_serial is not null
                        group by rbi.bill_id) ra on ra.bill_id = b.id
        set b.receipt        = true,
            b.modifier_id    = ra.receipt_man,
            b.modifier_name  = ra.receipt_man_name,
            b.modified_time  = now(),
            b.receipt_remark = ra.tax_header,
            b.receipt_man    = ra.receipt_man,
            b.receipt_time   = ra.receipt_time
        <where>
            and b.receipt = false
            <if test="null != billIds and billIds.size() > 0">
            and b.id in
            <foreach collection="billIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            </if>
            and not exists (select 1
                          from r_realty_bill_item bi
                          where bi.bill_id = b.id
                            and bi.amount > 0
                            and bi.receipt_status != 2)
        </where>
    </update>
</mapper>
