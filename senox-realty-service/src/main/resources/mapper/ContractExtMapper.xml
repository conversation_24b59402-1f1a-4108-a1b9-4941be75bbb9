<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.ContractExtMapper">

    <resultMap id="Result_ContractExt" type="com.senox.realty.domain.ContractExt">
        <result property="contractId" jdbcType="BIGINT" column="contract_id" />
        <result property="costType" jdbcType="TINYINT" column="cost_type" />
        <result property="bankName" jdbcType="VARCHAR" column="bank_name" />
        <result property="bankAccountNo" jdbcType="VARCHAR" column="bank_account_no" />
        <result property="bankAccountName" jdbcType="VARCHAR" column="bank_account_name" />
        <result property="bankAccountIdcard" jdbcType="VARCHAR" column="bank_account_idcard" />
        <result property="firstRate" jdbcType="DECIMAL" column="first_rate" />
        <result property="monthlyRate" jdbcType="DECIMAL" column="monthly_rate" />
        <result property="monthlyFeeAbs" jdbcType="DECIMAL" column="monthly_fee_abs" />
        <result property="waterPriceType" jdbcType="BIGINT" column="water_price_type" />
        <result property="electricPriceType" jdbcType="BIGINT" column="electric_price_type" />
        <result property="penaltyStartDate" jdbcType="TINYINT" column="penalty_start_date" />
        <result property="penaltyRate" jdbcType="DECIMAL" column="penalty_rate" />
        <result property="archiveUrl" jdbcType="VARCHAR" column="archive_url" />
        <result property="remark" jdbcType="VARCHAR" column="remark" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加合同扩展信息 -->
    <insert id="addContractExt" parameterType="com.senox.realty.domain.ContractExt">
        INSERT INTO r_contract_ext(
            contract_id, cost_type, bank_name, bank_account_no, bank_account_name, bank_account_idcard, first_rate, monthly_rate
            , monthly_fee_abs, water_price_type, electric_price_type, penalty_start_date, penalty_rate, archive_url, remark
            , creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{contractId}, #{costType}, #{bankName}, #{bankAccountNo}, #{bankAccountName}, #{bankAccountIdcard}, #{firstRate}, #{monthlyRate}
            , #{monthlyFeeAbs}, #{waterPriceType}, #{electricPriceType}, #{penaltyStartDate}, #{penaltyRate}, #{archiveUrl}, #{remark}
            , #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新合同扩展信息 -->
    <update id="updateContractExt" parameterType="com.senox.realty.domain.ContractExt">
        UPDATE r_contract_ext
        <set>
            <if test="costType != null">
                , cost_type = #{costType}
            </if>
            <if test="bankName != null">
                , bank_name = #{bankName}
            </if>
            <if test="bankAccountNo != null">
                , bank_account_no = #{bankAccountNo}
            </if>
            <if test="bankAccountName != null">
                , bank_account_name = #{bankAccountName}
            </if>
            <if test="bankAccountIdcard != null">
                , bank_account_idcard = #{bankAccountIdcard}
            </if>
            <if test="firstRate != null">
                , first_rate = #{firstRate}
            </if>
            <if test="monthlyRate != null">
                , monthly_rate = #{monthlyRate}
            </if>
            <if test="monthlyFeeAbs != null">
                , monthly_fee_abs = #{monthlyFeeAbs}
            </if>
            <if test="waterPriceType != null">
                , water_price_type = #{waterPriceType}
            </if>
            <if test="electricPriceType != null">
                , electric_price_type = #{electricPriceType}
            </if>
            <if test="penaltyStartDate != null">
                , penalty_start_date = #{penaltyStartDate}
            </if>
            <if test="penaltyRate != null">
                , penalty_rate = #{penaltyRate}
            </if>
            <if test="archiveUrl != null">
                , archive_url = #{archiveUrl}
            </if>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE contract_id = #{contractId}
    </update>

    <!-- 根据合同id获取合同扩展信息 -->
    <select id="findExtByContractId" parameterType="java.lang.Long" resultMap="Result_ContractExt">
        SELECT contract_id, cost_type, bank_name, bank_account_no, bank_account_name, bank_account_idcard, first_rate
            , monthly_rate, monthly_fee_abs, water_price_type, electric_price_type, penalty_start_date, penalty_rate
            , archive_url, remark
        FROM r_contract_ext
        WHERE contract_id = #{contractId}
    </select>
</mapper>