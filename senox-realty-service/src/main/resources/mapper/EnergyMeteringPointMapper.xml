<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.EnergyMeteringPointMapper">

    <!-- 批量添加计量点 -->
    <insert id="addBatch" parameterType="com.senox.realty.domain.EnergyMeteringPoint" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO d_energy_metering_point(
        code, name,type,rtu_code,rtu_name, rate,status,power_status, creator_id, creator_name, create_time, modifier_id, modifier_name,
        modified_time
        ) VALUES
        <foreach collection="pointList" item="item" separator=",">
            (
            #{item.code}, #{item.name},#{item.type}, #{item.rtuCode},#{item.rtuName}, #{item.rate},#{item.status},#{item.powerStatus}, #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId},
            #{item.modifierName}, NOW()
            )
        </foreach>
    </insert>

    <!-- 根据id更新计量点 -->
    <update id="updateById" parameterType="com.senox.realty.domain.EnergyMeteringPoint">
        UPDATE d_energy_metering_point
        <set>
            <if test="code != null">
                , code = #{code}
            </if>
            <if test="name != null">
                , name = #{name}
            </if>
            <if test="type != null">
                , `type` = #{type}
            </if>
            <if test="rtuCode != null">
                , rtu_code = #{rtuCode}
            </if>
            <if test="rate != null">
                , rate = #{rate}
            </if>
            <if test="status != null">
                , status = #{status}
            </if>
            <if test="powerStatus != null">
                , power_status = #{powerStatus}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id} and is_disabled = false
    </update>

    <update id="updateBatchByCode" parameterType="com.senox.realty.domain.EnergyMeteringPoint">
        UPDATE d_energy_metering_point
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="name = CASE" suffix=" ELSE name END,">
                <foreach collection="list" item="item">
                    <if test="item.name != null and item.name != ''">
                        WHEN code = #{item.code} THEN #{item.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="type = CASE" suffix=" ELSE type END,">
                <foreach collection="list" item="item">
                    <if test="item.type != null">
                        WHEN code = #{item.code} THEN #{item.type}
                    </if>
                </foreach>
            </trim>
            <trim prefix="rate = CASE" suffix=" ELSE rate END,">
                <foreach collection="list" item="item">
                    <if test="item.rate != null">
                        WHEN code = #{item.code} THEN #{item.rate}
                    </if>
                </foreach>
            </trim>
            <trim prefix="status = CASE" suffix=" ELSE status END,">
                <foreach collection="list" item="item">
                    <if test="item.status != null">
                        WHEN code = #{item.code} THEN #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="power_status = CASE" suffix=" ELSE power_status END,">
                <foreach collection="list" item="item">
                    <if test="item.powerStatus != null">
                        WHEN code = #{item.code} THEN #{item.powerStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="rtu_code = CASE" suffix=" ELSE rtu_code END,">
                <foreach collection="list" item="item">
                    <if test="item.rtuCode != null and item.rtuCode != ''">
                        WHEN code = #{item.code} THEN #{item.rtuCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_disabled = CASE" suffix=" ELSE disabled END,">
                <foreach collection="list" item="item">
                    <if test="item.disabled != null">
                        WHEN code = #{item.code} THEN #{item.disabled}
                    </if>
                </foreach>
            </trim>
            <trim prefix="rtu_name = CASE" suffix=" ELSE rtu_name END,">
                <foreach collection="list" item="item">
                    <if test="item.rtuName != null and item.rtuName != ''">
                        WHEN code = #{item.code} THEN #{item.rtuName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierId != null">
                        WHEN code = #{item.code} THEN #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierName != null and item.modifierName != ''">
                        WHEN code = #{item.code} THEN #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = now()
        </trim>
        <where>
            and is_disabled = false
            and code in
            <foreach collection="list" item="item" separator="," open="(" close=")">#{item.code}</foreach>
        </where>
    </update>

    <select id="findById" parameterType="java.lang.Long" resultType="com.senox.realty.domain.EnergyMeteringPoint">
        SELECT id, code, name, `type`, rtu_code, rtu_name, status, power_status FROM d_energy_metering_point WHERE id = #{id} and is_disabled = false
    </select>

    <select id="findByCode" parameterType="java.lang.String" resultType="com.senox.realty.domain.EnergyMeteringPoint">
        SELECT id, code, name, `type`, rtu_code, rtu_name, status, power_status FROM d_energy_metering_point WHERE code = #{code} and is_disabled = false
    </select>

    <select id="countList" parameterType="com.senox.realty.vo.EnergyMeterPointSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM d_energy_metering_point
        <where>
            AND is_disabled = false
            <if test="code != null and code != ''">
                AND code like concat('%',#{code},'%')
            </if>
            <if test="null != codes and codes.size() > 0">
                and code in
                <foreach collection="codes" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%',#{name},'%')
            </if>
            <if test="energyType != null">
                AND `type` = #{energyType.value}
            </if>
            <if test="rtuCode != null and rtuCode != ''">
                AND rtu_code like concat('%',#{rtuCode},'%')
            </if>
            <if test="state != null">
                AND status = #{state.state}
            </if>
            <if test="powerState != null">
                AND power_status = #{powerState.state}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (code LIKE CONCAT('%',  #{keyword} ,'%') OR name LIKE CONCAT('%',  #{keyword} ,'%'))
            </if>
        </where>
    </select>

    <select id="list" parameterType="com.senox.realty.vo.EnergyMeterPointSearchVo" resultType="com.senox.realty.domain.EnergyMeteringPoint">
        SELECT id, code, name, `type`, rtu_code,rate, status, power_status
        FROM d_energy_metering_point
        <where>
            AND is_disabled = false
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="null != codes and codes.size() > 0">
                and code in
                <foreach collection="codes" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%',#{name},'%')
            </if>
            <if test="energyType != null">
                AND `type` = #{energyType.value}
            </if>
            <if test="rtuCode != null and rtuCode != ''">
                AND rtu_code like concat('%',#{rtuCode},'%')
            </if>
            <if test="state != null">
                AND status = #{state.state}
            </if>
            <if test="powerState != null">
                AND power_status = #{powerState.state}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (code LIKE CONCAT('%',  #{keyword} ,'%') OR name LIKE CONCAT('%',  #{keyword} ,'%'))
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
