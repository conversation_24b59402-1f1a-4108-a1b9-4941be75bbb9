<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyBillWeMapper">

    <!-- 更新水电账单物业账单id -->
    <update id="updateWeBillId">
        UPDATE r_realty_bill_we SET bill_id = #{billId} WHERE id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 按合同类型更新水电账单物业账单id -->
    <update id="updateWeBillIdByContractType">
        UPDATE r_realty_bill_we w
            INNER JOIN r_realty r ON r.serial_no = w.realty_serial
            INNER JOIN r_contract c ON c.realty_id = r.id AND c.type = #{contractType}
            INNER JOIN r_realty_bill b ON b.contract_no = c.contract_no AND b.bill_year = #{billTime.year} AND b.bill_month = #{billTime.month} AND b.status = 0
        SET w.bill_id = b.id, w.modified_time = now()
        <where>
            AND w.bill_year = #{weTime.year}
            AND w.bill_month = #{weTime.month}
            AND w.bill_id = 0
            <if test="billTime.contractNo != null and billTime.contractNo != ''">
                AND c.contract_no = #{billTime.contractNo}
            </if>
            <if test="billTime.realtySerial != null and billTime.realtySerial != ''">
                AND r.serial_no = #{billTime.realtySerial}
            </if>
            <if test="billTime.billId != null">
                AND b.id = #{billTime.billId}
            </if>
        </where>
    </update>

    <update id="removeAbandonWeBill" parameterType="com.senox.realty.vo.BillMonthVo">
        UPDATE r_realty_bill_we bw
            INNER JOIN r_realty_bill b ON b.id = bw.bill_id AND b.status = 0
        SET bw.is_disabled = 1
        WHERE bw.bill_year = #{year}
            AND bw.bill_month = #{month}
            AND bw.is_disabled = 0
            AND bw.id NOT IN (select w.we_bill_id from r_realty_we w where w.bill_year = #{year} AND w.bill_month = #{month} AND w.is_disabled = 0)
    </update>

    <!-- 解除水电物业账单关联 -->
    <update id="unlinkWeBill" parameterType="java.lang.Long">
        UPDATE r_realty_bill_we bw
            INNER JOIN r_realty_bill b ON b.id = bw.bill_id
        SET bw.bill_id = 0
        WHERE bw.bill_id = #{billId}
    </update>

    <update id="unlinkWeBillByYearMonth">
        UPDATE r_realty_bill_we bw
            INNER JOIN r_realty_bill b ON b.id = bw.bill_id
        SET bw.bill_id = 0
        WHERE b.bill_year = #{year}
            AND b.bill_month = #{month}
            AND b.status = 0
            AND b.paid_amount = 0
            AND bw.bill_id > 0
    </update>

    <!-- 待生成水电账单数据 -->
    <select id="listGeneratingBill" parameterType="com.senox.realty.vo.BillMonthVo"
            resultType="com.senox.realty.domain.RealtyBillWe">
        SELECT w.bill_year
            , w.bill_month
            , 0 AS bill_id
            , r.serial_no AS realty_serial
            , '' AS realty_alias_serial
            , c2.customer_id
            , w.last_water_readings
            , w.water_readings
            , IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0) AS water_cost
            , CASE WHEN w.water_base > (IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0)) THEN w.water_base + (IFNULL(w.last_water_readings, 0) - IFNULL(w.water_readings, 0)) ELSE 0 END AS water_share
            , IFNULL(re.water_price, 0) AS water_price
            , w.last_electric_readings
            , w.electric_readings
            , IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0) AS electric_cost
            , CASE WHEN w.electric_base > (IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0)) THEN w.electric_base + (IFNULL(w.last_electric_readings, 0) - IFNULL(w.electric_readings, 0)) ELSE 0 END AS electric_share
            , IFNULL(re.electric_price, 0) AS electric_price
            , w.last_record_date
            , w.record_date
        FROM r_realty_we w
            INNER JOIN r_realty r ON r.serial_no = w.realty_serial
            INNER JOIN (select MAX(c.id) AS id, c.realty_id from r_contract c where c.type in (1, 2) AND c.status = 2 AND c.end_date >= CONCAT(#{year}, '-', #{month}, '-', '01') GROUP BY c.realty_id) c1 ON c1.realty_id = r.id
            INNER JOIN r_contract c2 ON c2.id = c1.id
            LEFT JOIN r_contract_ext ce ON ce.contract_id = c2.id
            LEFT JOIN r_realty_ext re ON re.realty_id = r.id
        WHERE w.bill_year = #{year}
            AND w.bill_month = #{month}
            <if test="contractNo != null and contractNo != ''">
                AND c2.contract_no = #{contractNo}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no = #{realtySerial}
            </if>
            AND w.`type` = 1
            AND w.we_bill_id = 0
            AND w.is_disabled = 0
    </select>

    <!-- 待生成副表水电账单数据 -->
    <select id="listGeneratingAliasBill" parameterType="com.senox.realty.vo.BillMonthVo"
            resultType="com.senox.realty.domain.RealtyBillWe">
        SELECT w.bill_year
            , w.bill_month
            , 0 AS bill_id
            , r.serial_no AS realty_serial
            , ra.serial_no  AS realty_alias_serial
            , c2.customer_id
            , w.last_water_readings
            , w.water_readings
            , IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0) AS water_cost
            , CASE WHEN w.water_base > (IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0)) THEN w.water_base + (IFNULL(w.last_water_readings, 0) - IFNULL(w.water_readings, 0)) ELSE 0 END AS water_share
            , IFNULL(re.water_price, 0) AS water_price
            , w.last_electric_readings
            , w.electric_readings
            , IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0) AS electric_cost
            , CASE WHEN w.electric_base > (IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0)) THEN w.electric_base + (IFNULL(w.last_electric_readings, 0) - IFNULL(w.electric_readings, 0)) ELSE 0 END AS electric_share
            , IFNULL(re.electric_price, 0) AS electric_price
            , w.last_record_date
            , w.record_date
        FROM r_realty_we w
            INNER JOIN r_realty_alias ra ON ra.serial_no = w.realty_serial
            INNER JOIN r_realty r ON r.id = ra.realty_id
            INNER JOIN (select MAX(c.id) AS id, c.realty_id from r_contract c where c.type in (1, 2) AND c.status = 2 AND c.end_date >= CONCAT(#{year}, '-', #{month}, '-', '01') GROUP BY c.realty_id) c1 ON c1.realty_id = r.id
            INNER JOIN r_contract c2 ON c2.id = c1.id
            LEFT JOIN r_contract_ext ce ON ce.contract_id = c2.id
            LEFT JOIN r_realty_ext re ON re.realty_id = r.id
        WHERE w.bill_year = #{year}
            AND w.bill_month = #{month}
            <if test="contractNo != null and contractNo != ''">
                AND c2.contract_no = #{contractNo}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no = #{realtySerial}
            </if>
            AND w.`type` = 1
            AND w.we_bill_id = 0
            AND w.is_disabled = 0
    </select>

    <!-- 待重新生成水电账单列表 -->
    <select id="listGeneratedBill" parameterType="com.senox.realty.vo.BillMonthVo" resultType="com.senox.realty.domain.RealtyBillWe">
        SELECT bw.id
            , w.bill_year
            , w.bill_month
            , bw.bill_id
            , bw.realty_serial
            , bw.realty_alias_serial
            , bw.customer_id
            , w.last_water_readings
            , w.water_readings
            , IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0) AS water_cost
            , CASE WHEN w.water_base > (IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0)) THEN w.water_base + (IFNULL(w.last_water_readings, 0) - IFNULL(w.water_readings, 0)) ELSE 0 END AS water_share
            , bw.water_price
            , w.last_electric_readings
            , w.electric_readings
            , IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0) AS electric_cost
            , CASE WHEN w.electric_base > (IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0)) THEN w.electric_base + (IFNULL(w.last_electric_readings, 0) - IFNULL(w.electric_readings, 0)) ELSE 0 END AS electric_share
            , bw.electric_price
            , w.last_record_date
            , w.record_date
        FROM r_realty_we w
            INNER JOIN r_realty_bill_we bw ON w.we_bill_id = bw.id
            LEFT JOIN r_realty_bill b ON b.id = bw.bill_id
        WHERE w.bill_year = #{year}
            AND w.bill_month = #{month}
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND w.realty_serial = #{realtySerial}
            </if>
            AND w.`type` = 1
            AND w.is_disabled = 0
            AND bw.is_disabled = 0
            AND (b.id IS NULl OR b.status = 0)
    </select>

    <select id="findById" parameterType="java.lang.Long" resultType="com.senox.realty.vo.RealtyBillWeVo">
        SELECT bw.id
            , bw.bill_year
            , bw.bill_month
            , bw.bill_id
            , bw.realty_serial, r.name AS realty_name
            , bw.realty_alias_serial
            , ra.name AS realty_alias_name
            , u.serial_no AS customer_serial
            , u.name AS customer_name
            , bw.last_water_readings
            , bw.water_readings
            , bw.water_share
            , bw.water_cost
            , bw.water_price
            , bw.water_amount
            , bw.last_electric_readings
            , bw.electric_readings
            , bw.electric_share
            , bw.electric_cost
            , bw.electric_price
            , bw.electric_amount
            , bw.total_amount
            , b.status
            , bw.last_record_date
            , bw.record_date
        FROM r_realty_bill_we bw
            INNER JOIN r_realty r ON r.serial_no = bw.realty_serial
            LEFT JOIN r_realty_alias ra ON ra.serial_no = bw.realty_alias_serial
            LEFT JOIN r_realty_bill b ON b.id = bw.bill_id
            LEFT JOIN u_customer u ON u.id = bw.customer_id
        WHERE bw.id = #{id}
    </select>

    <select id="findLatestWeBill" parameterType="java.lang.String" resultType="com.senox.realty.domain.RealtyBillWe">
        SELECT bill_year, bill_month
        FROM r_realty_bill_we
        WHERE is_disabled = 0
        <if test="realtySerial != null and realtySerial != ''">
            AND (realty_serial = #{realtySerial} OR realty_alias_serial = #{realtySerial})
        </if>
        ORDER BY bill_year DESC, bill_month DESC, id DESC
        LIMIT 1
    </select>

    <select id="sumWeBill" parameterType="com.senox.realty.vo.RealtyBillWeSearchVo" resultType="com.senox.realty.vo.RealtyBillWeVo">
        SELECT SUM(w.last_water_readings) AS last_water_readings
            , SUM(w.water_readings) AS water_readings
            , SUM(w.water_share) AS water_share
            , SUM(w.water_cost) AS water_cost
            , SUM(w.water_amount) AS water_amount
            , SUM(w.last_electric_readings) AS last_electric_readings
            , SUM(w.electric_readings) AS electric_readings
            , SUM(w.electric_share) AS electric_share
            , SUM(w.electric_cost) AS electric_cost
            , SUM(w.electric_amount) AS electric_amount
            , SUM(w.total_amount) AS total_amount
        FROM r_realty_bill_we w
            INNER JOIN r_realty r ON r.serial_no = w.realty_serial
            INNER JOIN u_customer c ON c.id = w.customer_id
            LEFT JOIN r_realty_bill b ON b.id = w.bill_id AND b.is_disabled = 0
            LEFT JOIN r_realty_alias ra ON ra.serial_no = w.realty_alias_serial
        <where>
            <if test="billYear != null">
                AND w.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND w.bill_month = #{billMonth}
            </if>

            <if test="billId != null">
                AND w.bill_id = #{billId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="status != null">
                <choose>
                    <when test="status == 0">
                        AND (b.status = #{status} OR b.id IS NULL)
                    </when>
                    <otherwise>AND b.status = #{status}</otherwise>
                </choose>
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND w.is_disabled = 0
        </where>
    </select>

    <select id="countWeBill" parameterType="com.senox.realty.vo.RealtyBillWeSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(w.id)
        FROM r_realty_bill_we w
            INNER JOIN r_realty r ON r.serial_no = w.realty_serial
            LEFT JOIN r_realty_bill b ON b.id = w.bill_id AND b.is_disabled = 0
            INNER JOIN u_customer c ON c.id = w.customer_id
        <where>
            <if test="billYear != null">
                AND w.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND w.bill_month = #{billMonth}
            </if>
            <if test="billId != null">
                AND w.bill_id = #{billId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="status != null">
                <choose>
                    <when test="status == 0">
                        AND (b.status = #{status} OR b.id IS NULL)
                    </when>
                    <otherwise>AND b.status = #{status}</otherwise>
                </choose>
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND w.is_disabled = 0
        </where>
    </select>

    <select id="listWeBill" parameterType="com.senox.realty.vo.RealtyBillWeVo" resultType="com.senox.realty.vo.RealtyBillWeVo">
        SELECT w.id
            , w.bill_year
            , w.bill_month
            , b.id AS bill_id
            , w.realty_serial
            , r.name AS realty_name
            , w.realty_alias_serial
            , ra.name AS realty_alias_serial_name
            , c.serial_no AS customer_serial
            , c.name AS customer_name
            , w.last_water_readings
            , w.water_readings
            , w.water_share
            , w.water_cost
            , w.water_price
            , w.water_amount
            , w.last_electric_readings
            , w.electric_readings
            , w.electric_share
            , w.electric_cost
            , w.electric_price
            , w.electric_amount
            , w.total_amount
            , b.status
        FROM r_realty_bill_we w
            INNER JOIN r_realty r ON r.serial_no = w.realty_serial
            INNER JOIN u_customer c ON c.id = w.customer_id
            LEFT JOIN r_realty_bill b ON b.id = w.bill_id AND b.is_disabled = 0
            LEFT JOIN r_realty_alias ra ON ra.serial_no = w.realty_alias_serial
        <where>
            <if test="billYear != null">
                AND w.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND w.bill_month = #{billMonth}
            </if>

            <if test="billId != null">
                AND w.bill_id = #{billId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="status != null">
                <choose>
                    <when test="status == 0">
                        AND (b.status = #{status} OR b.id IS NULL)
                    </when>
                    <otherwise>AND b.status = #{status}</otherwise>
                </choose>
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND w.is_disabled = 0
        </where>
        <if test="orderStr != null and orderStr != ''">
            ORDER BY ${orderStr}
        </if>
        <if test="pageSize > 0">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
