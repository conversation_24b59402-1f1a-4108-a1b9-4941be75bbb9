<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyPayoffMapper">

    <!-- 添加应付账单 -->
    <insert id="addPayoff" parameterType="com.senox.realty.domain.RealtyPayoff" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_realty_payoff(
            bill_year_month, bill_year, bill_month, realty_id, contract_no, amount, paid_time, status, creator_id
            , creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{billYearMonth}, #{billYear}, #{billMonth}, #{realtyId}, #{contractNo}, #{amount}, #{paidTime}, #{status}
            , #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新应付账单 -->
    <update id="updatePayoff" parameterType="com.senox.realty.domain.RealtyPayoff">
        UPDATE r_realty_payoff
        <set>
            <if test="amount != null">
                , amount = #{amount}
            </if>
            <if test="status != null">
                , status = #{status}
            </if>
            <if test="paidTime != null">
                , paid_time = #{paidTime}
            </if>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除应付账单 -->
    <update id="deletePayoff" parameterType="java.lang.Long">
        DELETE FROM r_realty_payoff WHERE id = #{id} AND status = 0
    </update>

    <!-- 根据id查找应付账单 -->
    <select id="findById" resultType="com.senox.realty.domain.RealtyPayoff">
        SELECT id, bill_year_month, bill_year, bill_month, realty_id, contract_no, amount, status, paid_time
        FROM r_realty_payoff
        WHERE id = #{id}
            AND bill_year = #{year}
            AND bill_month = #{month}
            AND is_disabled = 0
    </select>

    <select id="findByContractNo" resultType="com.senox.realty.domain.RealtyPayoff">
        SELECT id, bill_year_month, bill_year, bill_month, realty_id, contract_no, amount, status, paid_time
        FROM r_realty_payoff
        WHERE bill_year = #{year}
            AND bill_month = #{month}
            AND contract_no = #{contractNo}
            AND is_disabled = 0
    </select>

    <!-- 查找应付账单详情 -->
    <select id="findDetailById" parameterType="java.lang.Long" resultType="com.senox.realty.vo.RealtyPayoffVo">
        SELECT po.id , po.bill_year, po.bill_month, r.serial_no AS realty_serial, r.name AS realty_name, po.contract_no
            , c.customer_name, po.amount, ri.amount AS rent_amount, IFNULL(fi.amount, 0) AS first_charge, IFNULL(oi.amount, 0) AS owner_charge
            , (ri.amount - ifnull(fi.amount, 0) - ifnull(oi.amount, 0)) as rent_to_pay, po.status, po.paid_time, po.remark
        FROM r_realty_payoff po
            INNER JOIN r_realty r on r.id = po.realty_id
            INNER JOIN r_contract c on c.contract_no  = po.contract_no
            LEFT JOIN r_realty_payoff_item ri ON ri.bill_id = po.id AND ri.fee_id = 2
            LEFT JOIN r_realty_payoff_item fi ON fi.bill_id = po.id AND fi.fee_id = 23
            LEFT JOIN r_realty_payoff_item oi ON oi.bill_id = po.id AND oi.fee_id = 24
        WHERE po.id = #{id}
    </select>

    <!-- 应付账单数合计 -->
    <select id="countPayoff" parameterType="com.senox.realty.vo.RealtyPayoffSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(po.id)
        FROM r_realty_payoff po
            INNER JOIN r_realty r ON r.id = po.realty_id
            INNER JOIN r_contract c ON c.contract_no = po.contract_no
        <where>
            <if test="billYear != null">
                AND po.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND po.bill_month = #{billMonth}
            </if>
            <if test="realtyId != null">
                AND po.realty_id = #{realtyId}
            </if>
            <if test="realtyRegion != null">
                AND r.region_id = #{realtyRegion}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND po.contract_no = #{contractNo}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="status != null">
                AND po.status = #{status}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            AND po.is_disabled = 0
        </where>
    </select>

    <!-- 应付账单合计 -->
    <select id="sumPayoff" parameterType="com.senox.realty.vo.RealtyPayoffSearchVo" resultType="com.senox.realty.vo.RealtyPayoffVo">
        SELECT SUM(po.amount) AS amount
            , SUM(ri.amount) AS rent_amount
            , IFNULL(SUM(fi.amount), 0) AS first_charge
            , IFNULL(SUM(oi.amount), 0) AS owner_charge
            , SUM(ri.amount - IFNULL(fi.amount, 0) - IFNULL(oi.amount, 0)) as rent_to_pay
        FROM r_realty_payoff po
            INNER JOIN r_realty r on r.id = po.realty_id
            INNER JOIN r_contract c on c.contract_no  = po.contract_no
            LEFT JOIN r_realty_payoff_item ri ON ri.bill_id = po.id AND ri.fee_id = 2
            LEFT JOIN r_realty_payoff_item fi ON fi.bill_id = po.id AND fi.fee_id = 23
            LEFT JOIN r_realty_payoff_item oi ON oi.bill_id = po.id AND oi.fee_id = 24
        <where>
            <if test="billYear != null">
                AND po.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND po.bill_month = #{billMonth}
            </if>
            <if test="realtyId != null">
                AND po.realty_id = #{realtyId}
            </if>
            <if test="realtyRegion != null">
                AND r.region_id = #{realtyRegion}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND po.contract_no = #{contractNo}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="status != null">
                AND po.status = #{status}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            AND po.is_disabled = 0
        </where>
    </select>

    <!-- 应付账单列表 -->
    <select id="listPayoff" parameterType="com.senox.realty.vo.RealtyPayoffSearchVo" resultType="com.senox.realty.vo.RealtyPayoffVo">
        SELECT po.id , po.bill_year, po.bill_month, r.serial_no AS realty_serial, r.name AS realty_name, po.contract_no
            , c.customer_name, po.amount, ri.amount AS rent_amount, IFNULL(fi.amount, 0) AS first_charge, IFNULL(oi.amount, 0) AS owner_charge
            , (ri.amount - IFNULL(fi.amount, 0) - IFNULL(oi.amount, 0)) as rent_to_pay, po.status, po.paid_time
        FROM r_realty_payoff po
            INNER JOIN r_realty r on r.id = po.realty_id
            INNER JOIN r_contract c on c.contract_no  = po.contract_no
            LEFT JOIN r_realty_payoff_item ri ON ri.bill_id = po.id AND ri.fee_id = 2
            LEFT JOIN r_realty_payoff_item fi ON fi.bill_id = po.id AND fi.fee_id = 23
            LEFT JOIN r_realty_payoff_item oi ON oi.bill_id = po.id AND oi.fee_id = 24
        <where>
            <if test="billYear != null">
                AND po.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND po.bill_month = #{billMonth}
            </if>
            <if test="realtyId != null">
                AND po.realty_id = #{realtyId}
            </if>
            <if test="realtyRegion != null">
                AND r.region_id = #{realtyRegion}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND po.contract_no = #{contractNo}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="status != null">
                AND po.status = #{status}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            AND po.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY po.id DESC, po.status</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 批量添加应付帐单明细 -->
    <insert id="batchAddItems" parameterType="com.senox.realty.domain.RealtyPayoffItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_realty_payoff_item(
            bill_id, fee_id, fee_name, amount, status, paid_time, creator_id, creator_name, create_time, modifier_id
            , modifier_name, modified_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.billId}, #{item.feeId}, #{item.feeName}, #{item.amount}, #{item.status}, #{item.paidTime}, #{item.creatorId}
            , #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 批量更新应付帐单明细 -->
    <update id="batchUpdateItems" parameterType="com.senox.realty.domain.RealtyPayoffItem">
        UPDATE r_realty_payoff_item
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="amount = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.amount != null">
                        WHEN id = #{item.id} THEN #{item.amount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="status = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.status != null">
                        WHEN id = #{item.id} THEN #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="paid_time = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.paidTime != null">
                        WHEN id = #{item.id} THEN #{item.paidTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierId != null">
                        WHEN id = #{item.id} THEN #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierName != null">
                        WHEN id = #{item.id} THEN #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE id IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item.id}</foreach>
            AND status = 0
    </update>

    <update id="batchDelItems">
        DELETE FROM r_realty_payoff_item
        WHERE bill_id = #{billId}
            AND id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <update id="deleteItems" parameterType="java.lang.Long">
        DELETE FROM r_realty_payoff_item
        WHERE bill_id = #{billId}
    </update>
    
    
    <select id="listPayoffItems" parameterType="java.lang.Long" resultType="com.senox.realty.domain.RealtyPayoffItem">
        SELECT id, bill_id, fee_id, fee_name,amount, status, paid_time
        FROM r_realty_payoff_item
        WHERE bill_id = #{billId}
            AND is_disabled = 0
    </select>
</mapper>