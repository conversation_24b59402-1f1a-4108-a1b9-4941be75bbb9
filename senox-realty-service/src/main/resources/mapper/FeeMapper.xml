<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FeeMapper">

    <resultMap id="Result_FeeItem" type="com.senox.realty.domain.Fee">
        <id property="id" jdbcType="BIGINT" column="id"/>
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="alias" jdbcType="VARCHAR" column="alias" />
        <result property="category" jdbcType="INTEGER" column="category" />
        <result property="amount" jdbcType="DECIMAL" column="amount" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加费项 -->
    <insert id="addFee" parameterType="com.senox.realty.domain.Fee" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_fee(
            name, `alias`, category, amount, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{name}, #{alias}, #{category}, #{amount}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新费项 -->
    <update id="updateFee" parameterType="com.senox.realty.domain.Fee">
        UPDATE r_fee
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="alias != null and alias != ''">
                , `alias` = #{alias}
            </if>
            <if test="category != null">
                , category = #{category}
            </if>
            <if test="amount != null">
                , amount = #{amount}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 查找费项 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_FeeItem">
        SELECT id, name, `alias`, category, amount FROM r_fee WHERE id = #{id}
    </select>

    <select id="findByName" parameterType="java.lang.String" resultMap="Result_FeeItem">
        SELECT id, name, `alias`, category, amount FROM r_fee WHERE name = #{name}
    </select>

    <!-- 费项列表 -->
    <select id="listAll" resultMap="Result_FeeItem">
        SELECT id, name, `alias`, category, amount FROM r_fee WHERE is_disabled = 0
    </select>

</mapper>
