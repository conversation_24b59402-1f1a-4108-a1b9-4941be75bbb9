<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.AdvertisingContractMapper">

    <!-- 查找最大合同号 -->
    <select id="findMaxContractNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT contract_no FROM r_advertising_contract WHERE contract_no LIKE CONCAT(#{prefix}, '%') ORDER BY contract_no DESC LIMIT 1
    </select>

    <!-- 根据id获取合同明细 -->
    <select id="findDetailById" parameterType="java.lang.Long" resultType="com.senox.realty.vo.AdvertisingContractVo">
        SELECT c.id, c.contract_no, c.space_id, s.name AS space_name, c.customer_name, c.customer_user, c.customer_contact
            , c.present_months, c.rent_months, c.sign_date, c.start_date, c.end_date, c.amount, c.cost, c.status, c.remark
            , c.is_paid AS paid, IFNULL(u.real_name, u.username) AS creator, c.create_time
        FROM r_advertising_contract c
            INNER JOIN r_advertising_space s ON s.id = c.space_id
            LEFT JOIN u_admin_user u ON u.id = c.creator_id
        WHERE c.id = #{id}
    </select>

    <!-- 广告收益合计 -->
    <select id="sumContractIncome" parameterType="com.senox.realty.vo.AdvertisingContractSearchVo" resultType="com.senox.realty.vo.AdvertisingIncomeVo">
        SELECT SUM(c.amount) AS amount, SUM(c.cost) AS cost
            , SUM((SELECT SUM(ps.share_amount) FROM r_advertising_profit_share ps WHERE ps.contract_id = c.id)) AS share_amount
            , SUM((SELECT SUM(po.amount) FROM r_advertising_payoff po WHERE po.contract_no = c.contract_no AND po.status = 1)) AS paid_share_amount
        FROM r_advertising_contract c
            INNER JOIN r_advertising_space s ON s.id = c.space_id
        <where>
            <if test="signDateBegin != null">
                AND c.sign_date >= #{signDateBegin}
            </if>
            <if test="signDateEnd != null">
                AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date <![CDATA[<=]]> #{startDateBegin}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND c.status IN <foreach collection="statusList" item="item" open="(" close=")" separator="," >#{item}</foreach>
            </if>
            <if test="paid != null">
                AND c.is_paid = #{paid}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND c.is_disabled = 0
        </where>
    </select>

    <!-- 统计合同 -->
    <select id="countContract" parameterType="com.senox.realty.vo.AdvertisingContractSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(c.id)
        FROM r_advertising_contract c
            INNER JOIN r_advertising_space s ON s.id = c.space_id
        <where>
            <if test="signDateBegin != null">
                AND c.sign_date >= #{signDateBegin}
            </if>
            <if test="signDateEnd != null">
                AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date <![CDATA[<=]]> #{startDateBegin}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND c.status IN <foreach collection="statusList" item="item" open="(" close=")" separator="," >#{item}</foreach>
            </if>
            <if test="paid != null">
                AND c.is_paid = #{paid}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND c.is_disabled = 0
        </where>
    </select>

    <!-- 广告合同视图列表 -->
    <select id="listContractView" parameterType="com.senox.realty.vo.AdvertisingContractSearchVo"
            resultType="com.senox.realty.vo.AdvertisingContractListVo">
        SELECT c.id, c.contract_no, s.serial_no AS space_serial, s.name AS space_name, s.region AS space_region, s.street AS space_street
            , c.customer_name, c.customer_user, c.customer_contact, c.present_months, c.rent_months, c.start_date, c.end_date
            , c.amount, c.cost, c.status, c.is_paid AS paid, c.remark
        FROM r_advertising_contract c
            INNER JOIN r_advertising_space s ON s.id = c.space_id
        <where>
            <if test="signDateBegin != null">
                AND c.sign_date >= #{signDateBegin}
            </if>
            <if test="signDateEnd != null">
                AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date <![CDATA[<=]]> #{startDateBegin}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND c.status IN <foreach collection="statusList" item="item" open="(" close=")" separator="," >#{item}</foreach>
            </if>
            <if test="paid != null">
                AND c.is_paid = #{paid}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND c.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY c.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>


    <select id="listContractIncome" parameterType="com.senox.realty.vo.AdvertisingContractSearchVo" resultType="com.senox.realty.vo.AdvertisingIncomeVo">
        SELECT c.id, c.contract_no, s.serial_no AS space_serial, s.name AS space_name, s.region AS space_region, s.street AS space_street
            , c.customer_name, c.start_date, c.end_date, c.amount, c.cost
            , IFNULL((SELECT SUM(ps.share_amount) FROM r_advertising_profit_share ps WHERE ps.contract_id = c.id), 0) AS share_amount
            , IFNULL((SELECT SUM(po.amount) FROM r_advertising_payoff po WHERE po.contract_no = c.contract_no AND po.status = 1), 0) AS paid_share_amount
        FROM r_advertising_contract c
            INNER JOIN r_advertising_space s ON s.id = c.space_id
        <where>
            <if test="signDateBegin != null">
                AND c.sign_date >= #{signDateBegin}
            </if>
            <if test="signDateEnd != null">
                AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date <![CDATA[<=]]> #{startDateBegin}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND c.status IN <foreach collection="statusList" item="item" open="(" close=")" separator="," >#{item}</foreach>
            </if>
            <if test="paid != null">
                AND c.is_paid = #{paid}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND c.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY c.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>