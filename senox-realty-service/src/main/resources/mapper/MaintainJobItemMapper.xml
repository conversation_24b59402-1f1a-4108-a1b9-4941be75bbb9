<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainJobItemMapper">

    <select id="findLastExamineByOrderId" parameterType="java.lang.Long" resultType="com.senox.realty.domain.MaintainJobItem">
        select ji.id, ji.handler_id, ji.handler_name from r_maintain_job_item ji
            INNER JOIN r_maintain_job j ON ji.job_id = j.id
            where j.dispatch_type = 1 and j.order_id = #{orderId}
        order by ji.id desc
        limit 1
    </select>

</mapper>