<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainJobMapper">

    <resultMap id="Result_MaintainDispatchJob" type="com.senox.realty.vo.MaintainDispatchJobVo">
        <result property="id" jdbcType="BIGINT" column="id"/>
        <result property="jobItemId" column="job_item_id"/>
        <result property="orderId" jdbcType="BIGINT" column="order_id"/>
        <result property="maintainType" jdbcType="TINYINT" column="maintain_type"/>
        <result property="customerName" jdbcType="VARCHAR" column="customer_name"/>
        <result property="contact" jdbcType="VARCHAR" column="contact"/>
        <result property="jobNo" jdbcType="VARCHAR" column="job_no"/>
        <result property="status" jdbcType="TINYINT" column="status"/>
        <result property="handlerId" column="handler_id"/>
        <result property="handlerName" jdbcType="VARCHAR" column="handler_name"/>
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
        <result property="dispatchType" column="dispatch_type"/>
        <result property="multipleComplete" column="multiple_complete"/>
        <result property="payStatus" column="pay_status"/>
        <result property="handlerStatus" column="handler_status"/>
        <result property="remark" column="remark"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="regionName" column="region_name"/>
        <result property="streetName" column="street_name"/>
        <result property="address" column="address"/>
        <result property="problem" column="problem"/>
        <result property="outStatus" column="out_status"/>
        <result property="managementDeptId" column="management_dept_id"/>
        <result property="managementDeptName" column="management_dept_name"/>
        <result property="evaluate" column="evaluate"/>
        <result property="evaluateTime" column="evaluate_time"/>
        <result property="evaluateRating" column="evaluate_rating"/>
    </resultMap>

    <resultMap id="Result_MaintainJob" type="com.senox.realty.vo.MaintainJobVo">
        <result property="id" column="id"/>
        <result property="jobNo" column="job_no"/>
        <result property="maintainType" column="maintain_type"/>
        <result property="orderId" column="order_id"/>
        <result property="status" column="status"/>
        <result property="dispatchType" column="dispatch_type"/>
        <result property="multipleComplete" column="multiple_complete"/>
        <result property="createTime" column="create_time"/>
        <collection property="maintainJobItemVos" ofType="com.senox.realty.vo.MaintainJobItemVo">
            <result property="id" column="j_id"/>
            <result property="jobId" column="id"/>
            <result property="handlerId" column="handler_id"/>
            <result property="handlerName" column="handler_name"/>
            <result property="handlerStatus" column="handler_status"/>
            <result property="remark" column="remark"/>
        </collection>
    </resultMap>

    <insert id="addMaintainJob" parameterType="com.senox.realty.domain.MaintainJob" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_maintain_job(
            order_id, job_no, maintain_type, status, dispatch_type, multiple_complete, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{orderId}, #{jobNo}, #{maintainType}, #{status}, #{dispatchType}, #{multipleComplete}, #{creatorId}, #{creatorName}, #{createTime} , #{modifierId}, #{modifierName}, #{modifiedTime}
        )
    </insert>


    <!-- 获取最大的订单号 -->
    <select id="findMaxJobNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(job_no) FROM r_maintain_job WHERE job_no LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="listDispatchByOrderId" resultMap="Result_MaintainJob">
        select j.id, j.job_no, j.maintain_type, j.order_id, j.creator_name, j.status, j.create_time
            , j.dispatch_type, j.multiple_complete , ji.id as j_id, ji.handler_id, ji.handler_name, ji.handler_status, ji.remark
        from r_maintain_job j
            INNER JOIN r_maintain_order o ON j.order_id = o.id
            LEFT JOIN r_maintain_job_item ji ON ji.job_id = j.id
            <where>
                <if test="orderId != null and orderId != ''">
                    and j.order_id = #{orderId}
                </if>
            </where>
        order by j.id
    </select>

    <select id="findDispatchById" resultMap="Result_MaintainJob">
        select j.id, j.job_no, j.maintain_type, j.order_id, j.creator_name, j.status, j.create_time
            , j.dispatch_type, j.multiple_complete, ji.id as j_id, ji.handler_id, ji.handler_name, ji.handler_status, ji.remark
        from r_maintain_job j
            INNER JOIN r_maintain_order o ON j.order_id = o.id
            LEFT JOIN r_maintain_job_item ji ON ji.job_id = j.id where j.id = #{id}
    </select>

    <select id="listDispatchJob" parameterType="com.senox.realty.vo.MaintainJobSearchVo" resultMap="Result_MaintainDispatchJob">
        select j.id, j.order_id , o.customer_name, o.contact, j.maintain_type, j.job_no, ji.handler_id, ji.handler_name, j.status
              , o.region_name, o.street_name, o.address, o.problem , j.create_time, ji.id as job_item_id, ji.handler_status, ji.remark
            <if test="isChargeDetail != null and isChargeDetail">
                , IFNULL(t.pay_status, 1) as pay_status
                , IFNULL(t.total_amount, 0) as total_amount
            </if>
        from r_maintain_job j
            INNER JOIN r_maintain_order o ON j.order_id = o.id
            INNER JOIN r_maintain_job_item ji ON ji.job_id = j.id
            <if test="isChargeDetail != null and isChargeDetail">
                LEFT JOIN (
                    SELECT c.order_id, sum(total_amount) as total_amount,count( 1 ) = count( CASE WHEN c.`status` = 1 THEN 1 ELSE NULL END )  as pay_status from r_maintain_charge c
                    GROUP BY c.order_id
                ) as t on o.id = t.order_id
            </if>
        <where>
            <if test="maintainType != null">
                AND j.maintain_type = #{maintainType}
            </if>
            <if test="status != null and status.size > 0">
                AND j.status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="handlerStatus != null and handlerStatus.size > 0">
                AND ji.handler_status in <foreach collection="handlerStatus" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="contact != null and contact != ''">
                AND o.contact = #{contact}
            </if>
            <if test="customerName != null and customerName != ''">
                AND o.customer_name like CONCAT('%', #{customerName}, '%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND o.order_no = #{orderNo}
            </if>
            <if test="regionName != null and regionName != ''">
                AND o.region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND o.street_name = #{streetName}
            </if>
            <if test="handlerId != null">
                AND ji.handler_id = #{handlerId}
            </if>
            <if test="handlerName != null and handlerName != ''">
                AND ji.handler_name = #{handlerName}
            </if>
            <if test="dateStart != null">
                AND j.create_time >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND j.create_time <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="isChargeDetail != null and isChargeDetail and payStatus != null">
                AND t.status = #{payStatus}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (o.customer_name LIKE CONCAT('%', #{keyword} ,'%') OR j.job_no LIKE CONCAT('%', #{keyword} ,'%'))
            </if>
            <if test="managementDeptId != null ">
                AND o.management_dept_id in (0, #{managementDeptId})
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}, j.id
            </when>
            <otherwise>
                ORDER BY j.id
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="com.senox.realty.vo.MaintainJobSearchVo">
        select count(1) from r_maintain_job j
            INNER JOIN r_maintain_order o ON j.order_id = o.id
            INNER JOIN r_maintain_job_item ji ON ji.job_id = j.id
            <if test="isChargeDetail != null and isChargeDetail">
                LEFT JOIN (
                    SELECT c.order_id, sum(total_amount) as total_amount,count( 1 ) = count( CASE WHEN c.`status` = 1 THEN 1 ELSE NULL END )  as pay_status from r_maintain_charge c
                    GROUP BY c.order_id
                ) as t on o.id = t.order_id
            </if>
        <where>
            <if test="maintainType != null">
                AND j.maintain_type = #{maintainType}
            </if>
            <if test="status != null and status.size > 0">
                AND j.status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="handlerStatus != null and handlerStatus.size > 0">
                AND ji.handler_status in <foreach collection="handlerStatus" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="contact != null and contact != ''">
                AND o.contact = #{contact}
            </if>
            <if test="customerName != null and customerName != ''">
                AND o.customer_name like CONCAT('%', #{customerName}, '%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND o.order_no = #{orderNo}
            </if>
            <if test="regionName != null and regionName != ''">
                AND o.region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND o.street_name = #{streetName}
            </if>
            <if test="handlerId != null">
                AND ji.handler_id = #{handlerId}
            </if>
            <if test="handlerName != null and handlerName != ''">
                AND ji.handler_name = #{handlerName}
            </if>
            <if test="dateStart != null">
                AND j.create_time >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND j.create_time <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="isChargeDetail != null and isChargeDetail and payStatus != null">
                AND t.status = #{payStatus}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (o.customer_name LIKE CONCAT('%', #{keyword} ,'%') OR j.job_no LIKE CONCAT('%', #{keyword} ,'%'))
            </if>
            <if test="managementDeptId != null ">
                AND o.management_dept_id in (0, #{managementDeptId})
            </if>
        </where>
    </select>

    <select id="findDispatchByJobItemId" parameterType="java.lang.Long" resultMap="Result_MaintainDispatchJob">
        select j.id, j.order_id , o.customer_name, o.contact, o.region_name, o.street_name, o.address, o.problem, j.dispatch_type, j.multiple_complete
               , j.maintain_type, j.job_no, ji.handler_id, ji.handler_name, j.status , j.create_time, ji.id as job_item_id, ji.handler_status
               , ji.remark, m.out_status, c.status as pay_status, o.management_dept_id, o.management_dept_name, o.evaluate, o.evaluate_time, o.evaluate_rating
        from r_maintain_job j
        INNER JOIN r_maintain_order o ON j.order_id = o.id
        INNER JOIN r_maintain_job_item ji ON ji.job_id = j.id
        LEFT JOIN r_maintain_charge c on j.id = c.job_id
        LEFT JOIN (
            SELECT m.job_id
                 , CASE WHEN sum(CASE WHEN m.out_no IS NULL THEN NULL ELSE 1 END) > 0 THEN 1 ELSE 0 END as out_status
            FROM r_maintain_material m
            GROUP BY m.job_id
        ) as m on m.job_id = j.id
        WHERE ji.id = #{itemId}
    </select>

    <select id="findExamineList" resultType="com.senox.realty.domain.MaintainJob">
        select j.id, j.order_id, j.job_no, j.maintain_type, j.status, j.dispatch_type
             , j.multiple_complete, j.creator_id, j.creator_name, j.create_time, j.modifier_id, j.modifier_name, j.modified_time
        from r_maintain_job j
        LEFT JOIN (
            SELECT c.order_id, MIN(c.`status`) as status from r_maintain_charge c
            GROUP BY c.order_id
        ) as c on c.order_id = j.order_id
        where j.status = 0 and j.dispatch_type = 1 and ( c.`status` IS NULL or c.`status` = 1 )
    </select>

    <select id="findLastJobByOrderId" parameterType="java.lang.Long" resultType="com.senox.realty.domain.MaintainJob">
        SELECT id
            , order_id
            , job_no
            , maintain_type
            , status
            , dispatch_type
            , multiple_complete
        from r_maintain_job where order_id = #{orderId} and dispatch_type = 0
        order by id desc
        limit 1
    </select>

</mapper>
