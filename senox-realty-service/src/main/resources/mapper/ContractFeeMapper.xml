<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.ContractFeeMapper">

    <resultMap id="Result_ContractFee" type="com.senox.realty.domain.ContractFee">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="contractId" jdbcType="BIGINT" column="contract_id" />
        <result property="feeId" jdbcType="BIGINT" column="fee_id" />
        <result property="category" jdbcType="BIGINT" column="category" />
        <result property="period" jdbcType="TINYINT" column="period" />
        <result property="amount" jdbcType="DECIMAL" column="amount" />
        <result property="rentFreePeriod" jdbcType="TINYINT" column="rent_free_period" />
        <result property="startDate" jdbcType="TIMESTAMP" column="start_date" />
        <result property="endDate" jdbcType="TIMESTAMP" column="end_date" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 批量添加合同费项 -->
    <insert id="batchAddContractFee" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_contract_fee(
            contract_id, fee_id, category, period, amount, rent_free_period, start_date, end_date, creator_id, creator_name, create_time
            , modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="fees" item="item" separator=",">
        (
            #{item.contractId}, #{item.feeId}, #{item.category}, #{item.period}, #{item.amount}, #{item.rentFreePeriod}, #{item.startDate}, #{item.endDate}
            , #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 批量删除合同费项费用 -->
    <delete id="batchDeleteContractFee">
        DELETE FROM r_contract_fee WHERE contract_id = #{contractId} AND id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </delete>

    <!-- 合同费项列表 -->
    <select id="listContractFee" parameterType="java.lang.Long" resultMap="Result_ContractFee">
        SELECT id, contract_id, fee_id, category, period, amount, rent_free_period, start_date, end_date
        FROM r_contract_fee
        WHERE contract_id = #{contractId}
            AND is_disabled = 0
    </select>

    <select id="listContractFeeByFeeId" resultMap="Result_ContractFee">
        SELECT id, contract_id, fee_id, category, period, amount, rent_free_period, start_date, end_date
        FROM r_contract_fee
        WHERE contract_id = #{contractId}
            AND fee_id = #{feeId}
            AND is_disabled = 0
    </select>


</mapper>