<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyBillMapper">

    <!-- 添加物业账单 -->
    <insert id="addBill" parameterType="com.senox.realty.domain.RealtyBill" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_realty_bill(bill_year_month, bill_year, bill_month, realty_id, contract_no,amount, penalty_date
            , total_amount, paid_still_amount, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{billYearMonth}, #{billYear}, #{billMonth}, #{realtyId}, #{contractNo},#{amount}, #{penaltyDate}
            , #{totalAmount}, #{paidStillAmount}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新物业账单 -->
    <update id="updateBill" parameterType="com.senox.realty.domain.RealtyBill">
        UPDATE r_realty_bill
        <set>
            <if test="amount != null">
                , amount = #{amount}
            </if>
            <if test="penaltyAmount != null">
                , penalty_amount = #{penaltyAmount}
            </if>
            <if test="penaltyDate != null">
                , penalty_date = #{penaltyDate}
            </if>
            <if test="totalAmount != null">
                , total_amount = #{totalAmount}
            </if>
            <if test="refundAmount != null">
                , refund_amount = #{refundAmount}
            </if>
            <if test="amount != null or penaltyAmount != null">
                , paid_still_amount = total_amount - paid_amount - penalty_ignore_amount
            </if>
            <if test="send != null">
                , send = #{send}
            </if>
            <if test="sendTime != null">
                , send_time = #{sendTime}
            </if>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            <if test="tollManId != null">
                , toll_man_id = #{tollManId}
            </if>
            <if test="billSerial != null">
                , bill_serial = #{billSerial}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新账单金额 -->
    <update id="updateBillAmount" parameterType="com.senox.realty.vo.BillMonthVo">
        UPDATE r_realty_bill b
        <if test="realtySerial != null and realtySerial != ''">
            INNER JOIN r_realty r ON r.id = b.realty_id
        </if>
        SET b.amount = IFNULL((select sum(amount) from r_realty_bill_item bi where bi.bill_id = b.id), 0)
            , b.total_amount = b.amount
            , b.penalty_amount = 0
            , b.paid_still_amount = b.amount - b.paid_amount
            , b.total_amount = b.amount
            , b.modified_time = NOW()
        <where>
            <if test="year != null">
                AND b.bill_year = #{year}
            </if>
            <if test="month != null">
                AND b.bill_month = #{month}
            </if>
            <if test="billId != null">
                AND b.id = #{billId}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no = #{realtySerial}
            </if>
            AND b.status = 0
            AND b.is_disabled = 0
        </where>
    </update>

    <!-- 更新账单水电金额 -->
    <update id="updateBillWeAmount" parameterType="com.senox.realty.vo.BillMonthVo">
        UPDATE r_realty_bill b
            INNER JOIN r_realty_bill_item w ON w.bill_id = b.id AND w.fee_id = 4
            INNER JOIN r_realty_bill_item e ON e.bill_id = b.id AND e.fee_id = 6
        <if test="realtySerial != null and realtySerial != ''">
            INNER JOIN r_realty r ON r.id = b.realty_id
        </if>
        SET w.amount = IFNULL((SELECT SUM(w.water_amount) FROM r_realty_bill_we w WHERE w.bill_id = b.id), 0)
            , w.attr1 = (SELECT MIN(w.last_record_date) FROM r_realty_bill_we w WHERE w.bill_id = b.id)
            , w.attr2 = (SELECT MAX(w.record_date) FROM r_realty_bill_we w WHERE w.bill_id = b.id)
            , e.amount = IFNULL((SELECT SUM(w.electric_amount) FROM r_realty_bill_we w WHERE w.bill_id = b.id), 0)
            , e.attr1 = (SELECT MIN(w.last_record_date) FROM r_realty_bill_we w WHERE w.bill_id = b.id)
            , e.attr2 = (SELECT MAX(w.record_date) FROM r_realty_bill_we w WHERE w.bill_id = b.id)
            , w.modified_time = now()
            , e.modified_time = now()
        <where>
            <if test="year != null">
                AND b.bill_year = #{year}
            </if>
            <if test="month != null">
                AND b.bill_month = #{month}
            </if>
            <if test="billId != null">
                AND b.id = #{billId}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no = #{realtySerial}
            </if>
            AND b.status = 0
            AND b.is_disabled = 0
        </where>
    </update>

    <update id="updateBillWeAttr">
        UPDATE r_realty_bill b
            INNER JOIN r_realty_bill_item w ON w.bill_id = b.id AND w.fee_id = 4
            INNER JOIN r_realty_bill_item e ON e.bill_id = b.id AND e.fee_id = 6
        <if test="billTime.realtySerial != null and billTime.realtySerial != ''">
            INNER JOIN r_realty r ON r.id = b.realty_id
        </if>
        SET w.attr1 = CASE WHEN w.attr1 IS NULL THEN #{attr1} ELSE w.attr1 END
            , w.attr2 = CASE WHEN w.attr2 IS NULL THEN #{attr2} ELSE w.attr2 END
            , e.attr1 = CASE WHEN e.attr1 IS NULL THEN #{attr1} ELSE e.attr1 END
            , e.attr2 =  CASE WHEN e.attr2 IS NULL THEN #{attr2} ELSE e.attr2 END
            , w.modified_time = now()
            , e.modified_time = now()
        <where>
            <if test="billTime.year != null">
                AND b.bill_year = #{billTime.year}
            </if>
            <if test="billTime.month != null">
                AND b.bill_month = #{billTime.month}
            </if>
            <if test="billTime.billId != null">
                AND b.id = #{billTime.billId}
            </if>
            <if test="billTime.contractNo != null and billTime.contractNo != ''">
                AND b.contract_no = #{billTime.contractNo}
            </if>
            <if test="billTime.realtySerial != null and billTime.realtySerial != ''">
                AND r.serial_no = #{billTime.realtySerial}
            </if>
            AND b.status = 0
            AND b.is_disabled = 0
        </where>
    </update>

    <!-- 减免滞纳金 -->
    <update id="updateBillPenaltyIgnore" parameterType="com.senox.common.vo.BillPenaltyIgnoreVo">
        UPDATE r_realty_bill
        <set>
            , penalty_ignore = #{penaltyIgnore}
            , modified_time = NOW()
            <if test="operatorId != null">
                , modifier_id = #{operatorId}
            </if>
            <if test="operatorName != null">
                , modifier_name = #{operatorName}
            </if>
            <choose>
                <when test="penaltyIgnore">
                    <trim prefix=", penalty_ignore_amount = CASE" suffix=" ELSE penalty_ignore_amount END">
                        <foreach collection="ignoreDetails" item="item">
                            WHEN id = #{item.id} THEN #{item.ignoreAmount}
                        </foreach>
                    </trim>
                    , paid_still_amount = total_amount - paid_amount - penalty_ignore_amount
                </when>
                <otherwise>
                    , penalty_ignore_amount = 0
                    , paid_still_amount = total_amount - paid_amount - penalty_ignore_amount
                </otherwise>
            </choose>
        </set>
        WHERE id IN <foreach collection="billIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            AND status = 0
            AND is_disabled = 0
    </update>

    <update id="updateBillReceipt" parameterType="com.senox.realty.vo.RealtyBillBatchVo">
        UPDATE r_realty_bill
        <set>
            , receipt = #{receipt}
            <if test="remark != null">
                , receipt_remark = #{remark}
            </if>
            <if test="receipt">
                , receipt_time = NOW()
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
                , receipt_man = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id IN <foreach collection="ids" item="item" separator="," open="(" close=")">#{item}</foreach>
            AND status = 1 AND is_disabled = 0
    </update>

    <!-- 下发物业账单 -->
    <update id="sendBill" parameterType="com.senox.realty.vo.RealtyBillSendVo">
        UPDATE r_realty_bill SET send = 1, send_time = NOW()
        <where>
            <if test="billYear != null">
                AND bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND bill_month = #{billMonth}
            </if>
            <if test="billIds != null and billIds.size() > 0">
                AND id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            AND send = 0 AND is_disabled = 0
        </where>
    </update>

    <update id="suspendBill" parameterType="com.senox.realty.vo.RealtyContractSuspendDto">
        UPDATE r_realty_bill b
            INNER JOIN r_realty_bill_item mb ON mb.bill_id = b.id AND mb.fee_id = 1
            INNER JOIN r_realty_bill_item rb ON rb.bill_id = b.id AND rb.fee_id = 2
        SET b.amount = b.amount - mb.amount - rb.amount
            , b.paid_still_amount = b.paid_still_amount - mb.amount - rb.amount
            , b.total_amount = b.total_amount - mb.amount - rb.amount
            , mb.amount = 0
            , rb.amount = 0
            , b.modifier_id = #{operator.userId}
            , b.modifier_name = #{operator.username}
            , b.modified_time = NOW()
            , mb.modifier_id = #{operator.userId}
            , mb.modifier_name = #{operator.username}
            , mb.modified_time = NOW()
            , rb.modifier_id = #{operator.userId}
            , rb.modifier_name = #{operator.username}
            , rb.modified_time = NOW()
        WHERE b.bill_year = #{year}
            AND b.bill_month = #{month}
            AND b.realty_id = (SELECT realty_id FROM r_contract c WHERE c.contract_no = #{contractNo})
            AND b.status = 0
    </update>

    <update id="updateBillPaidById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_realty_bill b
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = b.id
        <set>
            <if test="orderId != null">
                , b.remote_order_id = #{orderId}
            </if>
            <choose>
                <when test="paid">
                    , b.status = CASE WHEN b.paid_still_amount - oi.total_amount > 0 THEN 0 ELSE 1 END
                    , b.paid_amount = b.paid_amount + oi.total_amount
                    , b.penalty_paid_amount = b.penalty_amount - b.penalty_ignore_amount
                    , b.paid_still_amount = b.paid_still_amount - oi.total_amount
                    , b.paid_time = #{paidTime}
                </when>
                <otherwise>, b.status = 0</otherwise>
            </choose>
            <if test="tollMan != null">
                , b.toll_man_id = #{tollMan}
            </if>
            , b.modified_time = NOW()
        </set>
        WHERE b.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND b.status = 0 AND b.paid_still_amount > 0 AND b.is_disabled = 0
    </update>

    <!-- 更新物业账单状态 -->
    <update id="updateBillPaidByRemoteOrder" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_realty_bill b
        INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = b.id
        <set>
            <choose>
                <when test="paid">
                    , b.status = CASE WHEN b.paid_still_amount - oi.total_amount > 0 THEN 0 ELSE 1 END
                    , b.paid_amount = b.paid_amount + oi.total_amount
                    , b.penalty_paid_amount = b.penalty_amount - b.penalty_ignore_amount
                    , b.paid_still_amount = b.paid_still_amount - oi.total_amount
                    , b.paid_time = #{paidTime}
                </when>
                <otherwise>, b.status = 0</otherwise>
            </choose>
            <if test="tollMan != null">
                , b.toll_man_id = #{tollMan}
            </if>
            , b.modified_time = NOW()
        </set>
        WHERE b.remote_order_id = #{orderId} AND b.status = 0 AND b.paid_still_amount > 0 AND b.is_disabled = 0
    </update>

    <!-- 通过账单id物业退费 -->
    <update id="updateBillRefundById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_realty_bill b
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = b.id
        <set>
            <if test="orderId != null">
                , b.refund_order_id = #{orderId}
            </if>
            <if test="paid">
                , b.status = CASE WHEN b.paid_amount > oi.total_amount THEN b.status ELSE 10 END
                , b.paid_amount = b.paid_amount + oi.total_amount
                , b.penalty_paid_amount = 0
                , b.paid_still_amount = b.paid_still_amount - oi.total_amount
                , b.refund_amount = b.refund_amount + oi.total_amount
                , b.refund_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , b.refund_man_id = #{tollMan}
            </if>
            , b.modified_time = NOW()
        </set>
        WHERE b.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND b.status = 1 AND b.is_disabled = 0
    </update>

    <!-- 通过退费订单id物业退费 -->
    <update id="updateBillRefundByRemoteOrder" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_realty_bill b
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = b.id
        <set>
            <if test="paid">
                , b.status = 10
                , b.paid_amount = b.paid_amount + oi.total_amount
                , b.penalty_paid_amount = 0
                , b.paid_still_amount = b.paid_still_amount - oi.total_amount
                , b.refund_amount = oi.total_amount
                , b.refund_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , b.refund_man_id = #{tollMan}
            </if>
            , b.modified_time = NOW()
        </set>
        WHERE b.refund_order_id = #{orderId} AND b.status = 1 AND b.is_disabled = 0
    </update>

    <update id="cancelBillPaid" parameterType="com.senox.common.vo.BillCancelVo">
        UPDATE r_realty_bill
        SET penalty_ignore = 0
            , penalty_ignore_amount = 0
            , total_amount = amount + penalty_amount
            , paid_amount = 0
            , penalty_paid_amount = 0
            , paid_still_amount = total_amount
            , status = 0
            , remote_order_id = 0
            , paid_time  = null
            , modifier_id = #{operatorId}
            , modifier_name = #{operatorName}
            , modified_time = NOW()
        WHERE id = #{billId} AND status = 1
    </update>

    <update id="deleteBillById" parameterType="java.lang.Long">
        DELETE FROM r_realty_bill
        WHERE id = #{id}
            AND status = 0
            AND paid_amount = 0
    </update>

    <update id="deleteZeroBill" parameterType="com.senox.realty.vo.BillMonthVo">
        DELETE FROM r_realty_bill b
        WHERE b.bill_year = #{year}
            AND b.bill_month = #{month}
            <if test="billId != null">
                AND b.id = #{billId}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.realty_id = (SELECT realty_id FROM r_contract c WHERE c.contract_no = #{contractNo})
            </if>
            AND b.status = 0
            AND b.amount = 0
    </update>

    <update id="deleteBillByYearMonth">
        DELETE FROM r_realty_bill
        WHERE bill_year = #{year}
            AND bill_month = #{month}
            AND status = 0
            AND paid_amount = 0
    </update>

    <!-- 月账单是否已下发 -->
    <select id="findMonthlyBillSend" resultType="java.lang.Long">
        SELECT id FROM r_realty_bill WHERE bill_year = #{year} AND bill_month = #{month} AND send = 1 AND is_disabled = 0 LIMIT 1
    </select>

    <!-- 根据id查找月账单 -->
    <select id="findMonthlyBillById" resultType="com.senox.realty.domain.RealtyBill">
        SELECT id, bill_year_month, bill_year, bill_month, realty_id, contract_no, amount, penalty_amount, penalty_date
            , penalty_ignore, penalty_ignore_amount, total_amount, paid_amount, paid_still_amount, refund_amount
            , penalty_paid_amount, paid_time, remote_order_id, status, send
        FROM r_realty_bill
        WHERE id = #{billId}
            AND bill_year = #{year}
            AND bill_month = #{month}
            AND is_disabled = 0
    </select>

    <!-- 根据合同号查找物业月账单 -->
    <select id="findMonthlyBillByContractNo" resultType="com.senox.realty.domain.RealtyBill">
        SELECT id, bill_year_month, bill_year, bill_month, realty_id, contract_no, amount, penalty_amount, penalty_date
            , penalty_ignore, penalty_ignore_amount, total_amount, paid_amount, paid_still_amount, refund_amount
            , penalty_paid_amount, paid_time, remote_order_id, status, send, send_time
        FROM r_realty_bill
        WHERE contract_no = #{contractNo}
            AND bill_year = #{year}
            AND bill_month = #{month}
            AND is_disabled = 0
        LIMIT 1
    </select>

    <select id="findMonthlyBillListByContractNo" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT id, bill_year_month, bill_year, bill_month, realty_id, contract_no, amount, penalty_amount, penalty_date
             , penalty_ignore, penalty_ignore_amount, total_amount, paid_amount, paid_still_amount, refund_amount
             , penalty_paid_amount, paid_time, remote_order_id, status, send, send_time
        FROM r_realty_bill
        WHERE contract_no = #{contractNo}
          AND bill_year = #{year}
          AND bill_month = #{month}
          AND is_disabled = 0
    </select>

    <!-- 根据id查找物业账单 -->
    <select id="findBillById" parameterType="java.lang.Long" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id
            , b.bill_year
            , b.bill_month
            , b.contract_no
            , r.serial_no                     AS realty_serial
            , r.name                          AS realty_name
            , r.region1                       as realty_region1
            , r.region2                       as realty_region2
            , c.customer_name                 AS realty_owner_name
            , b.amount
            , b.penalty_amount
            , b.penalty_date
            , b.penalty_ignore
            , b.penalty_ignore_amount
            , b.total_amount
            , b.paid_amount
            , b.paid_still_amount
            , b.refund_amount
            , b.penalty_paid_amount
            , b.paid_time
            , b.remote_order_id
            , b.status
            , IFNULL(wh.is_offer, 0)          AS offer
            , IFNULL(bwh.is_back, 0)          AS back
            , b.send
            , b.remark
            , b.bill_serial
            , IFNULL(u.real_name, u.username) AS toll_man
            , b.receipt
            , IFNULL(ru.real_name, ru.username) AS receipt_man
            , b.receipt_remark
            , b.receipt_time
            , b.create_time
        FROM r_realty_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
            LEFT JOIN r_contract c ON c.contract_no = b.contract_no AND b.realty_id = c.realty_id
            LEFT JOIN r_realty_bill_withhold wh on wh.bill_id = b.id
            LEFT JOIN r_bank_withhold bwh ON bwh.bill_year = b.bill_year AND bwh.bill_month = b.bill_month
            LEFT JOIN u_admin_user u on b.toll_man_id = u.id
            LEFT JOIN u_admin_user ru on b.receipt_man = ru.id
        WHERE b.id = #{id}
            AND b.is_disabled = 0
    </select>

    <select id="listBillById" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id, b.bill_year, b.bill_month, b.contract_no, c.type AS contract_type, r.serial_no AS realty_serial, r.name AS realty_name
            , r.region1 as realty_region1
            , r.region2 as realty_region2
            , c.customer_name AS realty_owner_name, b.amount, b.penalty_amount, b.penalty_date, b.penalty_ignore, b.penalty_ignore_amount
            , b.total_amount, b.paid_amount, b.paid_still_amount, b.refund_amount, b.penalty_paid_amount, b.paid_time, b.remote_order_id
            , b.status, IFNULL(wh.is_offer, 0) AS offer, IFNULL(bwh.is_back,0) AS back, b.send, b.remark, b.bill_serial, b.create_time
        <if test="isWithDetail">
            , m.amount AS manage_amount, r2.amount AS rent_amount, w.amount AS water_amount, e.amount AS electric_amount
        </if>
        FROM r_realty_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
            LEFT JOIN r_contract c ON c.contract_no = b.contract_no AND b.realty_id = c.realty_id
            LEFT JOIN r_realty_bill_withhold wh on wh.bill_id = b.id
            LEFT JOIN r_bank_withhold bwh ON bwh.bill_year = b.bill_year AND bwh.bill_month = b.bill_month
        <if test="isWithDetail">
            LEFT JOIN r_realty_bill_item m ON b.id = m.bill_id AND m.fee_id = 1
            LEFT JOIN r_realty_bill_item r2 ON b.id = r2.bill_id AND r2.fee_id = 2
            LEFT JOIN r_realty_bill_item w ON b.id = w.bill_id AND w.fee_id = 4
            LEFT JOIN r_realty_bill_item e ON b.id = e.bill_id AND e.fee_id = 6
        </if>
        WHERE b.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND b.is_disabled = 0
    </select>

    <select id="listBillByRemoteOrderId" parameterType="java.lang.Long" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id
            , b.bill_year
            , b.bill_month
            , b.contract_no
            , r.serial_no AS realty_serial
            , r.name      AS realty_name
            , r.region1   as realty_region1
            , r.region2   as realty_region2
            , b.amount
            , b.penalty_amount
            , b.penalty_date
            , b.penalty_ignore
            , b.penalty_ignore_amount
            , b.total_amount
            , b.paid_amount
            , b.refund_amount
            , b.penalty_paid_amount
            , b.paid_time
            , b.remote_order_id
            , b.status
        FROM r_realty_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
        WHERE b.remote_order_id = #{orderId}
            AND b.is_disabled = 0
    </select>

    <select id="countPaidBills" parameterType="java.util.List" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM r_realty_bill
        WHERE id IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND status = 1
            AND is_disabled = 0
    </select>

    <select id="listBillByRefundOrderId" parameterType="java.lang.Long" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id
            , b.bill_year
            , b.bill_month
            , b.contract_no
            , r.serial_no AS realty_serial
            , r.name      AS realty_name
            , r.region1   as realty_region1
            , r.region2   as realty_region2
            , b.amount
            , b.penalty_amount
            , b.penalty_date
            , b.penalty_ignore
            , b.penalty_ignore_amount
            , b.total_amount
            , b.paid_amount
            , b.refund_amount
            , b.penalty_paid_amount
            , b.paid_time
            , b.remote_order_id
            , b.status
        FROM r_realty_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
        WHERE b.refund_order_id = #{orderId}
            AND b.is_disabled = 0
    </select>

    <!-- 统计物业账单 -->
    <select id="countRealtyBill" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_realty_bill b
            INNER JOIN r_realty r ON r.id = b.realty_id
            INNER JOIN r_contract c ON b.contract_no = c.contract_no
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= ${billStartYearMonth}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> ${billEndYearMonth}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtyRegion != null">
                AND r.region_id = #{realtyRegion}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="penaltyIgnore != null">
                AND b.penalty_ignore = #{penaltyIgnore}
            </if>
            <if test="send != null">
                AND b.send = #{send}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND b.receipt = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND b.receipt_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="wxOpenid != null and wxOpenid != ''">
                AND b.realty_id IN (
                SELECT ur.realty_id
                FROM wx_user_realty ur
                INNER JOIN wx_user u ON ur.user_id = u.id AND u.openid = #{wxOpenid} AND ur.start_date <![CDATA[<=]]> b.penalty_date AND ur.end_date >= b.penalty_date
                )
            </if>
            <if test="contractTypes != null and contractTypes.size > 0">
                <foreach collection="contractTypes" item="item">
                    AND EXISTS(
                        SELECT bc.id FROM r_contract bc INNER JOIN r_contract c2 ON c2.realty_id = bc.realty_id AND c2.contract_no = b.contract_no
                        WHERE bc.realty_id = b.realty_id
                            AND bc.`type` = #{item}
                            AND bc.status in (0, 2)
                            AND bc.start_date <![CDATA[<=]]> c2.end_date
                            AND bc.end_date >= c2.end_date
                        LIMIT 1
                    )
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="receiptRemark != null and receiptRemark != ''">
                AND b.receipt_remark LIKE CONCAT('%', #{receiptRemark} ,'%')
            </if>
            AND b.is_disabled = 0
        </where>
    </select>

    <!-- 物业账单列表 -->
    <select id="listBill" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id
            , b.bill_year
            , b.bill_month
            , b.contract_no
            , r.serial_no AS realty_serial
            , r.name AS realty_name
            , r.region1 as realty_region1
            , r.region2 as realty_region2
            , c.customer_name AS realty_owner_name
            , b.amount
            , b.penalty_amount
            , b.penalty_date
            , b.penalty_ignore
            , b.penalty_ignore_amount
            , b.total_amount
            , b.paid_amount
            , b.paid_still_amount
            , b.refund_amount
            , b.penalty_paid_amount
            , b.paid_time
            , b.status
            , b.send
            , b.send_time
            , b.bill_serial
            , b.create_time
        FROM r_realty_bill b
            INNER JOIN r_realty r ON r.id = b.realty_id
            INNER JOIN r_contract c ON b.contract_no = c.contract_no
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="send != null">
                AND b.send = #{send}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND b.receipt = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND b.receipt_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="wxOpenid != null and wxOpenid != ''">
                AND b.realty_id IN (SELECT ur.realty_id FROM wx_user_realty ur INNER JOIN wx_user u ON ur.user_id = u.id
                AND u.openid = #{wxOpenid} AND ur.start_date <![CDATA[<=]]> b.penalty_date AND ur.end_date >= b.penalty_date)
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR
                c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            AND b.is_disabled = 0
        </where>
        <if test="orderStr != null and orderStr != ''">
            ORDER BY ${orderStr}
        </if>
        <if test="pageSize>0">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="listReceiptBill" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id
            , b.bill_year
            , b.bill_month
            , r.serial_no AS realty_serial
            , r.name AS realty_name
            , r.region1 as realty_region1
            , r.region2 as realty_region2
            , c.customer_name AS realty_owner_name
            , b.contract_no
            , c.`type` AS contract_type
            , IFNULL((SELECT m.amount FROM r_realty_bill_item m INNER JOIN r_realty_receipt_item ri ON ri.receipt_serial_no = m.receipt_serial_no WHERE m.fee_id = 1 AND m.bill_id = b.id AND m.receipt_status = 2 AND ri.realty_receipt_id = br.receipt_apply_id), 0) AS manage_amount
            , IFNULL((SELECT r2.amount FROM r_realty_bill_item r2 INNER JOIN r_realty_receipt_item ri ON ri.receipt_serial_no = r2.receipt_serial_no WHERE r2.fee_id = 2 AND r2.bill_id = b.id AND r2.receipt_status = 2 AND ri.realty_receipt_id = br.receipt_apply_id), 0) AS rent_amount
            , IFNULL((SELECT w.amount FROM r_realty_bill_item w INNER JOIN r_realty_receipt_item ri ON ri.receipt_serial_no = w.receipt_serial_no WHERE w.fee_id = 4 AND w.bill_id = b.id AND w.receipt_status = 2 AND ri.realty_receipt_id = br.receipt_apply_id), 0) AS water_amount
            , IFNULL((SELECT e.amount FROM r_realty_bill_item e INNER JOIN r_realty_receipt_item ri ON ri.receipt_serial_no = e.receipt_serial_no WHERE e.fee_id = 6 AND e.bill_id = b.id AND e.receipt_status = 2 AND ri.realty_receipt_id = br.receipt_apply_id), 0) AS electric_amount
            , b.paid_amount
            , b.paid_time
            , b.receipt
            , uc.name AS receipt_man
            , b.receipt_remark
            , rr.audit_time AS receipt_time
        <if test="containContractType">
            , CASE WHEN (SELECT lb.id FROM r_contract lb WHERE lb.realty_id = b.realty_id AND lb.`type` = 3 AND lb.status IN (0, 2) AND lb.start_date <![CDATA[<=]]> STR_TO_DATE(CONCAT(b.bill_year_month, '01'), '%Y%m%d') AND lb.end_date >= STR_TO_DATE(CONCAT(b.bill_year_month, '01'), '%Y%m%d') LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS back_lease
            , CASE WHEN (SELECT rp.id FROM r_contract rp WHERE rp.realty_id = b.realty_id AND rp.`type` = 4 AND rp.status IN (0, 2) AND rp.start_date <![CDATA[<=]]> STR_TO_DATE(CONCAT(b.bill_year_month, '01'), '%Y%m%d') AND rp.end_date >= STR_TO_DATE(CONCAT(b.bill_year_month, '01'), '%Y%m%d') LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS replace_lease
            , CASE WHEN (SELECT rcp.id FROM r_contract rcp WHERE rcp.realty_id = b.realty_id AND rcp.`type` = 5 AND rcp.status IN (0, 2) AND rcp.start_date <![CDATA[<=]]> STR_TO_DATE(CONCAT(b.bill_year_month, '01'), '%Y%m%d') AND rcp.end_date >= STR_TO_DATE(CONCAT(b.bill_year_month, '01'), '%Y%m%d') LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS collection_lease
        </if>
        FROM r_realty_bill_receipt br
            INNER JOIN r_realty_bill b on b.id = br.bill_id
            INNER JOIN r_realty r on b.realty_id = r.id
            INNER JOIN r_contract c on b.contract_no = c.contract_no
            INNER JOIN r_realty_receipt rr on rr.id = br.receipt_apply_id
            LEFT JOIN u_customer uc on uc.id = b.receipt_man
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND rr.apply_status = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND rr.audit_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND rr.audit_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="contractTypes != null and contractTypes.size > 0">
                <foreach collection="contractTypes" item="item">
                    AND EXISTS(
                        SELECT bc.id FROM r_contract bc
                        WHERE bc.realty_id = b.realty_id
                            AND bc.`type` = #{item}
                            AND bc.status in (0, 2)
                            AND bc.start_date <![CDATA[<=]]> str_to_date(concat(b.bill_year_month, '01'), '%Y%m%d')
                            AND bc.end_date >= str_to_date(concat(b.bill_year_month, '01'), '%Y%m%d')
                        LIMIT 1
                    )
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="receiptRemark != null and receiptRemark != ''">
                AND b.receipt_remark LIKE CONCAT('%', #{receiptRemark} ,'%')
            </if>
            AND b.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY rr.audit_time DESC
            </otherwise>
        </choose>
        <if test="pageSize>0">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumReceiptBillAmount" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT SUM(IFNULL((SELECT m.amount FROM r_realty_bill_item m INNER JOIN r_realty_receipt_item ri ON ri.receipt_serial_no = m.receipt_serial_no WHERE m.fee_id = 1 AND m.bill_id = b.id AND m.receipt_status = 2 AND ri.realty_receipt_id = br.receipt_apply_id), 0)) AS manage_amount
            , SUM(IFNULL((SELECT r2.amount FROM r_realty_bill_item r2 INNER JOIN r_realty_receipt_item ri ON ri.receipt_serial_no = r2.receipt_serial_no WHERE r2.fee_id = 2 AND r2.bill_id = b.id AND r2.receipt_status = 2 AND ri.realty_receipt_id = br.receipt_apply_id), 0)) AS rent_amount
            , SUM(IFNULL((SELECT w.amount FROM r_realty_bill_item w INNER JOIN r_realty_receipt_item ri ON ri.receipt_serial_no = w.receipt_serial_no WHERE w.fee_id = 4 AND w.bill_id = b.id AND w.receipt_status = 2 AND ri.realty_receipt_id = br.receipt_apply_id), 0)) AS water_amount
            , SUM(IFNULL((SELECT e.amount FROM r_realty_bill_item e INNER JOIN r_realty_receipt_item ri ON ri.receipt_serial_no = e.receipt_serial_no WHERE e.fee_id = 6 AND e.bill_id = b.id AND e.receipt_status = 2 AND ri.realty_receipt_id = br.receipt_apply_id), 0)) AS electric_amount
        FROM r_realty_bill_receipt br
            INNER JOIN r_realty_bill b ON b.id = br.bill_id
            INNER JOIN r_realty r ON b.realty_id = r.id
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND b.receipt = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND b.receipt_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="receiptRemark != null and receiptRemark != ''">
                AND b.receipt_remark LIKE CONCAT('%', #{receiptRemark} ,'%')
            </if>
            AND b.is_disabled = 0
        </where>
    </select>

    <!-- 物业账单合计 -->
    <select id="sumBill" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT SUM(b.total_amount) AS total_amount
            , SUM(b.paid_amount) AS paid_amount
            , SUM(b.paid_still_amount) AS paid_still_amount
            , SUM(b.refund_amount) AS refund_amount
        FROM r_realty_bill b
            INNER JOIN r_realty r ON r.id = b.realty_id
            INNER JOIN r_contract c ON b.contract_no = c.contract_no
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="send != null">
                AND b.send = #{send}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND b.receipt = #{receipt}
            </if>
            <if test="wxOpenid != null and wxOpenid != ''">
                AND b.realty_id IN (SELECT ur.realty_id FROM wx_user_realty ur INNER JOIN wx_user u ON ur.user_id = u.id
                AND u.openid = #{wxOpenid} AND ur.start_date <![CDATA[<=]]> b.penalty_date AND ur.end_date >=
                b.penalty_date)
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR
                c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="contractTypes != null and contractTypes.size > 0">
                <foreach collection="contractTypes" item="item">
                    AND EXISTS(
                        SELECT bc.id FROM r_contract bc INNER JOIN r_contract c2 ON c2.realty_id = bc.realty_id AND c2.contract_no = b.contract_no
                        WHERE bc.realty_id = b.realty_id
                            AND bc.`type` = #{item}
                            AND bc.status in (0, 2)
                            AND bc.start_date <![CDATA[<=]]> c2.end_date
                            AND bc.end_date >= c2.end_date
                        LIMIT 1
                    )
                </foreach>
            </if>
            AND b.is_disabled = 0
        </where>
    </select>

    <!-- 物业账单带滞纳金费率列表 -->
    <select id="listBillWithPenaltyRate" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id, b.bill_year, b.bill_month, b.contract_no, r.serial_no AS realty_serial, r.name AS realty_name
            , r.region1 as realty_region1
            , r.region2 as realty_region2
            , b.amount, b.penalty_amount, b.penalty_date, b.penalty_ignore, b.penalty_ignore_amount, b.total_amount
            , b.paid_amount, b.paid_still_amount, b.refund_amount, b.penalty_paid_amount, b.paid_time, b.status, e.penalty_rate
            , IFNULL(wh.is_offer, 0) AS offer, IFNULL(bwh.is_back, 0) AS back
        FROM r_realty_bill b
            INNER JOIN r_realty r ON r.id = b.realty_id
            INNER JOIN r_contract c ON b.contract_no = c.contract_no
            LEFT JOIN r_contract_ext e ON e.contract_id = c.id
            LEFT JOIN r_realty_bill_withhold wh on wh.bill_id = b.id
            LEFT JOIN r_bank_withhold bwh ON bwh.bill_year = b.bill_year AND bwh.bill_month = b.bill_month
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="send != null">
                AND b.send = #{send}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="penaltyDateStart != null">
                AND b.penalty_date > #{penaltyDateStart}
            </if>
            <if test="penaltyDateEnd != null">
                AND b.penalty_date <![CDATA[<]]> #{penaltyDateEnd}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND b.receipt = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND b.receipt_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="wxOpenid != null and wxOpenid != ''">
                AND b.realty_id IN (SELECT ur.realty_id FROM wx_user_realty ur INNER JOIN wx_user u ON ur.user_id = u.id AND u.openid = #{wxOpenid} AND ur.start_date <![CDATA[<=]]> b.penalty_date AND ur.end_date >= b.penalty_date)
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            AND b.is_disabled = 0
        </where>
        <if test="orderStr != null and orderStr != ''">
            ORDER BY ${orderStr}
        </if>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="sumBillWithDetail" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT SUM(IFNULL(m.amount, 0)) AS manage_amount
            , SUM(IFNULL(r2.amount, 0)) AS rent_amount
            , SUM(IFNULL(w.amount, 0)) AS water_amount
            , SUM(IFNULL(e.amount, 0)) AS electric_amount
            , SUM(b.penalty_amount) AS penalty_amount
            , SUM(b.penalty_paid_amount) AS penalty_paid_amount
            , SUM(CASE b.status WHEN 1 THEN b.paid_amount ELSE b.amount END) AS receivable_amount
            , SUM(b.total_amount) AS total_amount
            , SUM(b.paid_amount) AS paid_amount
            , SUM(b.paid_still_amount) AS paid_still_amount
            , SUM(b.refund_amount) AS refund_amount
            , SUM(b.amount) AS total_amount_ignore_penalty
        FROM r_realty_bill b
            INNER JOIN r_realty r ON b.realty_id = r.id
            INNER JOIN r_contract c ON b.contract_no = c.contract_no
            LEFT JOIN r_realty_bill_item m ON b.id = m.bill_id AND m.fee_id = 1
            LEFT JOIN r_realty_bill_item r2 ON b.id = r2.bill_id AND r2.fee_id = 2
            LEFT JOIN r_realty_bill_item w ON b.id = w.bill_id AND w.fee_id = 4
            LEFT JOIN r_realty_bill_item e ON b.id = e.bill_id AND e.fee_id = 6
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth, jdbcType=NVARCHAR}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth, jdbcType=NVARCHAR}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtyRegion != null">
                AND r.region_id = #{realtyRegion}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="penaltyIgnore != null">
                <choose>
                    <when test="penaltyIgnore"> AND b.penalty_ignore_amount > 0</when>
                    <otherwise> AND b.penalty_ignore_amount = 0</otherwise>
                </choose>
            </if>
            <if test="send != null">
                AND b.send = #{send}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND b.receipt = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND b.receipt_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="wxOpenid != null and wxOpenid != ''">
                AND b.realty_id IN (
                    SELECT ur.realty_id
                    FROM wx_user_realty ur
                        INNER JOIN wx_user u ON ur.user_id = u.id AND u.openid = #{wxOpenid} AND ur.start_date <![CDATA[<=]]> b.penalty_date AND ur.end_date >= b.penalty_date
                )
            </if>
            <if test="contractTypes != null and contractTypes.size > 0">
                <foreach collection="contractTypes" item="item">
                    AND EXISTS(
                        SELECT bc.id FROM r_contract bc INNER JOIN r_contract c2 ON c2.realty_id = bc.realty_id AND c2.contract_no = b.contract_no
                        WHERE bc.realty_id = b.realty_id
                            AND bc.`type` = #{item}
                            AND bc.status in (0, 2)
                            AND bc.start_date <![CDATA[<=]]> c2.end_date
                            AND bc.end_date >= c2.end_date
                        LIMIT 1
                    )
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="receiptRemark != null and receiptRemark != ''">
                AND b.receipt_remark LIKE CONCAT('%', #{receiptRemark} ,'%')
            </if>
            AND b.is_disabled = 0
        </where>
    </select>

    <!-- 物业明细账单列表 -->
    <select id="listBillWithDetail" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id, b.contract_no, b.bill_year, b.bill_month, r.region_name AS realty_region, r.serial_no AS realty_serial,r.region1 as realty_region1,r.region2 as realty_region2, r.name AS realty_name
            , c.customer_name AS realty_owner_name, IFNULL(m.amount, 0) AS manage_amount, IFNULL(r2.amount, 0) AS rent_amount
            , IFNULL(w.amount, 0) AS water_amount, IFNULL(e.amount, 0) AS electric_amount, b.total_amount, b.paid_amount, b.paid_still_amount
            , b.penalty_amount, b.penalty_paid_amount, b.refund_amount, b.amount AS total_amount_ignore_penalty, CASE b.status WHEN 1 THEN b.paid_amount ELSE b.amount END AS receivable_amount
            , b.penalty_ignore, b.penalty_ignore_amount, b.bill_serial, b.status, b.paid_time, b.send, b.receipt
            , m.receipt_status  as manage_receipt_status
            , r2.receipt_status as rent_receipt_status
            , w.receipt_status  as water_receipt_status
            , e.receipt_status  as electric_receipt_status
        <if test="containContractType">
            , CASE WHEN (SELECT lb.id FROM r_contract lb INNER JOIN r_contract c1 ON lb.realty_id = c1.realty_id AND c1.contract_no = b.contract_no WHERE lb.`type` = 3 AND lb.status IN (0, 2) AND lb.start_date <![CDATA[<=]]> c1.start_date AND lb.end_date >= c1.end_date LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS back_lease
            , CASE WHEN (SELECT rp.id FROM r_contract rp INNER JOIN r_contract c1 ON rp.realty_id = c1.realty_id AND c1.contract_no = b.contract_no WHERE rp.`type` = 4 AND rp.status IN (0, 2) AND rp.start_date <![CDATA[<=]]> c1.end_date AND rp.end_date >= c1.end_date LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS replace_lease
            , CASE WHEN (SELECT rcp.id FROM r_contract rcp INNER JOIN r_contract c1 ON rcp.realty_id = c1.realty_id AND c1.contract_no = b.contract_no WHERE rcp.`type` = 5 AND rcp.status IN (0, 2) AND rcp.start_date <![CDATA[<=]]> c1.start_date AND rcp.end_date >= c1.end_date LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS collection_lease
        </if>
        FROM r_realty_bill b
            INNER JOIN r_realty r ON b.realty_id = r.id
            INNER JOIN r_contract c ON b.contract_no = c.contract_no
            LEFT JOIN r_realty_bill_item m ON b.id = m.bill_id AND m.fee_id = 1
            LEFT JOIN r_realty_bill_item r2 ON b.id = r2.bill_id AND r2.fee_id = 2
            LEFT JOIN r_realty_bill_item w ON b.id = w.bill_id AND w.fee_id = 4
            LEFT JOIN r_realty_bill_item e ON b.id = e.bill_id AND e.fee_id = 6
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth, jdbcType=NVARCHAR}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth, jdbcType=NVARCHAR}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtyRegion != null">
                AND r.region_id = #{realtyRegion}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="penaltyIgnore != null">
                <choose>
                    <when test="penaltyIgnore"> AND b.penalty_ignore_amount > 0</when>
                    <otherwise> AND b.penalty_ignore_amount = 0</otherwise>
                </choose>
            </if>
            <if test="send != null">
                AND b.send = #{send}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND b.receipt = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND b.receipt_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="wxOpenid != null and wxOpenid != ''">
                AND b.realty_id IN (
                SELECT ur.realty_id
                FROM wx_user_realty ur
                INNER JOIN wx_user u ON ur.user_id = u.id AND u.openid = #{wxOpenid} AND ur.start_date <![CDATA[<=]]>
                b.penalty_date AND ur.end_date >= b.penalty_date
                )
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR
                c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="contractTypes != null and contractTypes.size > 0">
                <foreach collection="contractTypes" item="item">
                    AND EXISTS(
                        SELECT bc.id FROM r_contract bc INNER JOIN r_contract c2 ON c2.realty_id = bc.realty_id AND c2.contract_no = b.contract_no
                        WHERE bc.realty_id = b.realty_id
                            AND bc.`type` = #{item}
                            AND bc.status in (0, 2)
                            AND bc.start_date <![CDATA[<=]]> c2.end_date
                            AND bc.end_date >= c2.end_date
                        LIMIT 1
                    )
                </foreach>
            </if>
            AND b.is_disabled = 0
        </where>
        <if test="orderStr != null and orderStr != ''">
            ORDER BY ${orderStr}
        </if>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="listBillWithDetailInfo" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="com.senox.realty.vo.RealtyBillVo">
        SELECT b.id, b.contract_no, b.bill_year, b.bill_month, r.region_name AS realty_region, r.serial_no AS realty_serial,r.region1 as realty_region1,r.region2 as realty_region2, r.name AS realty_name
        , c.customer_name AS realty_owner_name, IFNULL(m.amount, 0) AS manage_amount, IFNULL(r2.amount, 0) AS rent_amount
        , IFNULL(w.amount, 0) AS water_amount, IFNULL(e.amount, 0) AS electric_amount, b.total_amount, b.paid_amount, b.paid_still_amount
        , b.penalty_amount, b.penalty_paid_amount, b.refund_amount, b.amount AS total_amount_ignore_penalty, CASE b.status WHEN 1 THEN b.paid_amount ELSE b.amount END AS receivable_amount
        , b.penalty_ignore, b.penalty_ignore_amount, b.bill_serial, b.status, b.paid_time, b.send, b.receipt
        , m.receipt_status  as manage_receipt_status
        , r2.receipt_status as rent_receipt_status
        , w.receipt_status  as water_receipt_status
        , e.receipt_status  as electric_receipt_status
        <if test="containContractType">
            , CASE WHEN (SELECT lb.id FROM r_contract lb INNER JOIN r_contract c1 ON lb.realty_id = c1.realty_id AND c1.contract_no = b.contract_no WHERE lb.`type` = 3 AND lb.status IN (0, 2) AND lb.start_date <![CDATA[<=]]> c1.start_date AND lb.end_date >= c1.end_date LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS back_lease
            , CASE WHEN (SELECT rp.id FROM r_contract rp INNER JOIN r_contract c1 ON rp.realty_id = c1.realty_id AND c1.contract_no = b.contract_no WHERE rp.`type` = 4 AND rp.status IN (0, 2) AND rp.start_date <![CDATA[<=]]> c1.end_date AND rp.end_date >= c1.end_date LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS replace_lease
            , CASE WHEN (SELECT rcp.id FROM r_contract rcp INNER JOIN r_contract c1 ON rcp.realty_id = c1.realty_id AND c1.contract_no = b.contract_no WHERE rcp.`type` = 5 AND rcp.status IN (0, 2) AND rcp.start_date <![CDATA[<=]]> c1.start_date AND rcp.end_date >= c1.end_date LIMIT 1) IS NOT NULL THEN 1 ELSE 0 END AS collection_lease
        </if>
        <if test="containCustomerDetail">
            , uc.name as customer_name
            , CONCAT_WS(',', NULLIF(uc.telephone, ''), NULLIF(uc.telephone2, ''), NULLIF(uc.telephone3, '')) AS customer_contact
            , uce.remark as customer_address
            , uc.enterprise_code as customer_enterprise_code
            , uc.legal_person as customer_legal_person
            , uc.idcard as customer_id_card
            , oc.name as owner_name
            , CONCAT_WS(',', NULLIF(oc.telephone, ''), NULLIF(oc.telephone2, ''), NULLIF(oc.telephone3, '')) AS owner_contact
            , oc.enterprise_code as owner_enterprise_code
            , oc.legal_person as owner_legal_person
            , oc.idcard as owner_id_card
            , CASE WHEN re.guarantee_start_date IS NULL THEN ''
              WHEN re.guarantee_end_date IS NULL THEN CONCAT(DATE_FORMAT(re.guarantee_start_date, '%Y-%m-%d'), '-无确定时间')
              ELSE CONCAT(DATE_FORMAT(re.guarantee_start_date, '%Y-%m-%d'), '-', DATE_FORMAT(re.guarantee_end_date, '%Y-%m-%d')) END AS guarantee_realty
        </if>
        FROM r_realty_bill b
        INNER JOIN r_realty r ON b.realty_id = r.id
        INNER JOIN r_contract c ON b.contract_no = c.contract_no
        INNER JOIN r_business_region br on br.id = r.region_id
        LEFT JOIN r_street s on r.street_id = s.id
        <if test="containCustomerDetail">
            LEFT JOIN u_customer uc ON uc.id = c.customer_id
            LEFT JOIN u_customer oc ON oc.id = r.owner_id
            LEFT JOIN r_realty_ext re ON re.realty_id = r.id
            LEFT JOIN u_customer_ext uce ON uce.customer_id = c.customer_id
        </if>
        LEFT JOIN r_realty_bill_item m ON b.id = m.bill_id AND m.fee_id = 1
        LEFT JOIN r_realty_bill_item r2 ON b.id = r2.bill_id AND r2.fee_id = 2
        LEFT JOIN r_realty_bill_item w ON b.id = w.bill_id AND w.fee_id = 4
        LEFT JOIN r_realty_bill_item e ON b.id = e.bill_id AND e.fee_id = 6
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth, jdbcType=NVARCHAR}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth, jdbcType=NVARCHAR}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtyRegion != null">
                AND r.region_id = #{realtyRegion}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="penaltyIgnore != null">
                <choose>
                    <when test="penaltyIgnore"> AND b.penalty_ignore_amount > 0</when>
                    <otherwise> AND b.penalty_ignore_amount = 0</otherwise>
                </choose>
            </if>
            <if test="send != null">
                AND b.send = #{send}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND b.receipt = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND b.receipt_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="wxOpenid != null and wxOpenid != ''">
                AND b.realty_id IN (
                SELECT ur.realty_id
                FROM wx_user_realty ur
                INNER JOIN wx_user u ON ur.user_id = u.id AND u.openid = #{wxOpenid} AND ur.start_date <![CDATA[<=]]>
                b.penalty_date AND ur.end_date >= b.penalty_date
                )
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR
                c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="contractTypes != null and contractTypes.size > 0">
                <foreach collection="contractTypes" item="item">
                    AND EXISTS(
                    SELECT bc.id FROM r_contract bc INNER JOIN r_contract c2 ON c2.realty_id = bc.realty_id AND c2.contract_no = b.contract_no
                    WHERE bc.realty_id = b.realty_id
                    AND bc.`type` = #{item}
                    AND bc.status in (0, 2)
                    AND bc.start_date <![CDATA[<=]]> c2.end_date
                    AND bc.end_date >= c2.end_date
                    LIMIT 1
                    )
                </foreach>
            </if>
            AND b.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>ORDER BY br.order_num asc, s.order_num asc, r.serial_no asc</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 批量添加物业账单费项 -->
    <insert id="batchAddBillItems" parameterType="java.util.List">
        INSERT INTO r_realty_bill_item(
            bill_id, fee_id, fee_name, amount, attr1, attr2, status, paid_time, creator_id, creator_name, create_time
            , modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="billItems" item="item" separator=",">
        (
            #{item.billId}, #{item.feeId}, #{item.feeName}, #{item.amount}, #{item.attr1}, #{item.attr2}, #{item.status}
            , #{item.paidTime}, #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 批量更新物业账单费项 -->
    <update id="batchUpdateBillItems" parameterType="java.util.List">
        UPDATE r_realty_bill_item
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="amount = CASE" suffix="END,">
                <foreach collection="billItems" item="item">
                    <if test="item.amount != null">
                        WHEN id = #{item.id} THEN #{item.amount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="attr1 = CASE" suffix="END,">
                <foreach collection="billItems" item="item">
                    <if test="item.attr1 != null">
                        WHEN id = #{item.id} THEN #{item.attr1}
                    </if>
                </foreach>
            </trim>
            <trim prefix="attr2 = CASE" suffix="END,">
                <foreach collection="billItems" item="item">
                    <if test="item.attr2 != null">
                        WHEN id = #{item.id} THEN #{item.attr2}
                    </if>
                </foreach>
            </trim>
            <trim prefix="status = CASE" suffix="END,">
                <foreach collection="billItems" item="item">
                    <if test="item.status != null">
                        WHEN id = #{item.id} THEN #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="paid_time = CASE" suffix="END,">
                <foreach collection="billItems" item="item">
                    <if test="item.paidTime != null">
                        WHEN id = #{item.id} THEN #{item.paidTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = CASE" suffix="END,">
                <foreach collection="billItems" item="item">
                    <if test="item.modifierId != null">
                        WHEN id = #{item.id} THEN #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = CASE" suffix="END,">
                <foreach collection="billItems" item="item">
                    <if test="item.modifierName != null">
                        WHEN id = #{item.id} THEN #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE id IN <foreach collection="billItems" item="item" open="(" close=")" separator=",">#{item.id}</foreach>
            AND status = 0
    </update>

    <update id="changeBillFee" parameterType="com.senox.realty.vo.BillFeeChangeVo">
        UPDATE r_realty_bill_item
        SET amount = CASE WHEN amount + #{changeAmount} <![CDATA[<]]> 0 THEN 0 ELSE amount + #{changeAmount} END
            , modifier_id = #{operatorId}
            , modifier_name = #{operatorName}
            , modified_time = NOW()
        WHERE bill_id = #{billId} AND fee_id = #{feeId} AND status = 0
    </update>

    <update id="updateBillItemStatus">
        UPDATE r_realty_bill_item SET status = 1, paid_time = #{paidTime}, modified_time = NOW()
        WHERE bill_id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND status = 0
    </update>

    <update id="refundBillItem">
        UPDATE r_realty_bill_item i
            INNER JOIN r_realty_bill b ON b.id = i.bill_id
            INNER JOIN p_order_item oi ON oi.order_id = b.refund_order_id
            INNER JOIN p_order_item_detail oid ON oid.order_id = oi.order_id AND oid.fee_id = i.fee_id
        set i.status = case when oid.total_amount + i.amount <![CDATA[<=]]> 0 then 0 else i.status end
            , i.modified_time = NOW()
        WHERE i.bill_id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND i.status = 1
    </update>

    <update id="cancelBillItemPaid" parameterType="com.senox.common.vo.BillCancelVo">
        UPDATE r_realty_bill_item
        SET status = 0
            , paid_time = null
            , modifier_id = #{operatorId}
            , modifier_name = #{operatorName}
            , modified_time = NOW()
        WHERE bill_id = #{billId} AND status = 1
    </update>

    <!-- 批量删除物业账单费项 -->
    <update id="batchDelBillItems">
        DELETE FROM r_realty_bill_item
        WHERE bill_id = #{billId}
            AND id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <update id="deleteBillItems" parameterType="java.lang.Long">
        DELETE FROM r_realty_bill_item
        WHERE bill_id = #{billId}
            AND status = 0
    </update>

    <update id="deleteBillItemsByYearMonth">
        DELETE i FROM r_realty_bill_item i
            INNER JOIN r_realty_bill b ON b.id = i.bill_id
        WHERE b.bill_year = #{year}
            AND b.bill_month = #{month}
            AND b.status = 0
            AND b.paid_amount = 0
    </update>

    <!-- 查找账单明细 -->
    <select id="listBillItems" parameterType="java.lang.Long" resultType="com.senox.realty.domain.RealtyBillItem">
        SELECT id, bill_id, fee_id, fee_name,amount, attr1, attr2, status,receipt_status, paid_time
        FROM r_realty_bill_item
        WHERE bill_id = #{billId}
            AND is_disabled = 0
    </select>

    <!-- 查找账单明细 -->
    <select id="listBillItemByBillIds"  resultType="com.senox.realty.domain.RealtyBillItem">
        select id, bill_id, fee_id, fee_name,amount, attr1, attr2, status,receipt_status, paid_time
        from r_realty_bill_item
        where bill_id in <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
          and is_disabled = 0
    </select>

    <update id="updateBillItemReceipt" parameterType="com.senox.realty.domain.RealtyBillItem">
        UPDATE r_realty_bill_item SET receipt_serial_no = #{receiptSerialNo} ,receipt_status = #{receiptStatus}, modified_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="batchUpdateBillItemReceipt">
        update r_realty_bill_item
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="receipt_serial_no = case" suffix="end,">
                <foreach collection="billItemList" item="item">
                    <if test="null != item.receiptSerialNo and item.receiptSerialNo != ''">
                        when id = #{item.id} then #{item.receiptSerialNo}
                    </if>
                </foreach>
            </trim>
            <trim prefix="receipt_status = case" suffix="end,">
                <foreach collection="billItemList" item="item">
                    <if test="null != item.receiptStatus">
                        when id = #{item.id} then #{item.receiptStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = case" suffix="end,">
                <foreach collection="billItemList" item="item">
                    <if test="null != item.modifierId">
                        when id = #{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = case" suffix="end,">
                <foreach collection="billItemList" item="item">
                    <if test="null != item.modifierName">
                        when id = #{item.id} then #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = now()
        </trim>
        <where>
            AND id IN <foreach collection="billItemList" item="item" open="(" close=")" separator=",">#{item.id}</foreach>
        </where>
    </update>

    <select id="listBillItemByReceiptSerial" parameterType="string" resultType="com.senox.realty.domain.RealtyBillItem">
        SELECT id, bill_id, fee_id, fee_name,amount, attr1, attr2, status, paid_time, receipt_serial_no, receipt_status
        FROM r_realty_bill_item
        WHERE receipt_serial_no IN <foreach collection="receiptSerialNoList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND is_disabled = 0
    </select>

    <select id="countReceiptRealtyBill" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="integer">
        SELECT COUNT(b.id)
        FROM r_realty_bill_receipt br
            INNER JOIN r_realty_bill b ON b.id = br.bill_id
            INNER JOIN r_realty r ON b.realty_id = r.id
            INNER JOIN r_realty_receipt rr on rr.id = br.receipt_apply_id
            INNER JOIN r_contract c ON b.contract_no = c.contract_no
        <where>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billStartYearMonth != null">
                AND b.bill_year_month >= #{billStartYearMonth}
            </if>
            <if test="billEndYearMonth != null">
                AND b.bill_year_month <![CDATA[<=]]> #{billEndYearMonth}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no = #{contractNo}
            </if>
            <if test="contractType != null">
                AND c.`type` = #{contractType}
            </if>
            <if test="realtyId != null">
                AND b.realty_id = #{realtyId}
            </if>
            <if test="realtyRegion != null">
                AND r.region_id = #{realtyRegion}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial} ,'%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="penaltyIgnore != null">
                <choose>
                    <when test="penaltyIgnore"> AND b.penalty_ignore_amount > 0</when>
                    <otherwise> AND b.penalty_ignore_amount = 0</otherwise>
                </choose>
            </if>
            <if test="send != null">
                AND b.send = #{send}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt">
                AND rr.apply_status = #{receipt}
            </if>
            <if test="receiptTimeStart != null">
                AND rr.audit_time >= #{receiptTimeStart}
            </if>
            <if test="receiptTimeEnd != null">
                AND rr.audit_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="wxOpenid != null and wxOpenid != ''">
                AND b.realty_id IN (
                SELECT ur.realty_id
                FROM wx_user_realty ur
                INNER JOIN wx_user u ON ur.user_id = u.id AND u.openid = #{wxOpenid} AND ur.start_date <![CDATA[<=]]> b.penalty_date AND ur.end_date >= b.penalty_date
                )
            </if>
            <if test="contractTypes != null and contractTypes.size > 0">
                <foreach collection="contractTypes" item="item">
                    AND EXISTS(
                        SELECT bc.id FROM r_contract bc INNER JOIN r_contract c2 ON c2.realty_id = bc.realty_id AND c2.contract_no = b.contract_no
                        WHERE bc.realty_id = b.realty_id
                            AND bc.`type` = #{item}
                            AND bc.status in (0, 2)
                            AND bc.start_date <![CDATA[<=]]> c2.end_date
                            AND bc.end_date >= c2.end_date
                        LIMIT 1
                    )
                </foreach>
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword} ,'%') OR r.name LIKE CONCAT('%', #{keyword} ,'%') OR c.customer_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="receiptRemark != null and receiptRemark != ''">
                AND b.receipt_remark LIKE CONCAT('%', #{receiptRemark} ,'%')
            </if>
            AND b.is_disabled = 0
        </where>
    </select>

    <!--应收金额统计-->
    <select id="realtyBillStatistics" parameterType="com.senox.realty.vo.StatisticsGenerateVo" resultType="com.senox.realty.domain.RealtyStatistics">
        SELECT
            ifnull(sum(CASE WHEN rb.status = 1 THEN rb.total_amount - rb.penalty_ignore_amount ELSE 0 END), 0) as rent_collect_amount,
            ifnull(sum(CASE WHEN rb.status = 1 THEN 1 ELSE 0 END), 0) as rent_collect_num,
            ifnull(sum(CASE WHEN rb.status = 0 THEN rb.total_amount ELSE 0 END), 0) as un_rent_collect_amount,
            ifnull(sum(CASE WHEN rb.status = 0 THEN 1 ELSE 0 END), 0) as un_rent_collect_num,
            ifnull(sum(penalty_amount), 0) as penalty_amount
        from r_realty_bill rb
        <where>
            <if test="year != null">
                and rb.bill_year = #{year}
            </if>
            <if test="month != null">
                and rb.bill_month = #{month}
            </if>
        </where>
    </select>
</mapper>
