<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingFileMapper">

    <!-- 店铺消防档案合计 -->
    <select id="countFile" parameterType="com.senox.realty.vo.FirefightingFileSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_file f
        <where>
            <if test="inspectResult != null">
                AND f.inspect_result = #{inspectResult}
            </if>
            <if test="inspectResults != null and inspectResults.size() > 0">
                AND f.inspect_result IN <foreach collection="inspectResults" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="inspectDateStart != null">
                AND f.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND f.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="rectificationDeadlineStart != null">
                AND f.rectification_deadline >= #{rectificationDeadlineStart}
            </if>
            <if test="rectificationDeadlineEnd != null">
                AND f.rectification_deadline <![CDATA[<=]]> #{rectificationDeadlineEnd}
            </if>
            <if test="reinspectDateStart != null">
                AND f.reinspect_date >= #{reinspectDateStart}
            </if>
            <if test="reinspectDateEnd != null">
                AND f.reinspect_date <![CDATA[<=]]> #{reinspectDateEnd}
            </if>
            <if test="businessLicenseIssued != null">
                AND f.is_business_license_issued = #{businessLicenseIssued}
            </if>
            <if test="accommodated != null and accommodated != ''">
                AND f.accommodated = CONCAT('%', #{accommodated}, '%')
            </if>
            <if test="flameCooking != null and flameCooking != ''">
                AND f.flame_cooking = CONCAT('%', #{flameCooking}, '%')
            </if>
            <if test="heaterEquiped != null and heaterEquiped != ''">
                AND f.heater_equiped = CONCAT('%', #{heaterEquiped}, '%')
            </if>
            <if test="store != null and store != ''">
                AND f.store LIKE CONCAT('%', #{store}, '%')
            </if>
            <if test="storeKeyman != null and storeKeyman != ''">
                AND f.store_keyman LIKE CONCAT('%', #{storeKeyman}, '%')
            </if>
            <if test="storeContact != null and storeContact != ''">
                AND f.store_contact LIKE CONCAT('%', #{storeContact}, '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ir.id
                    FROM r_firefighting_inspect_realty ir
                    <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                        INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                    </if>
                    WHERE ir.inspect_id = f.id
                        AND ir.inspect_type = 'FILE'
                    <if test="regionId != null and regionId > 0">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND f.is_disabled = 0
        </where>
    </select>

    <!-- 店铺消防档案列表 -->
    <select id="listFile" parameterType="com.senox.realty.vo.FirefightingFileSearchVo"
            resultType="com.senox.realty.vo.FirefightingFileBriefVo">
        SELECT f.id, f.store, f.store_keyman, f.store_contact, f.store_address, f.is_business_license_issued AS business_license_issued
            , f.accommodated, f.flame_cooking, f.fire_extinguisher, f.is_fire_alarms_disposed AS fire_alarms_disposed
            , f.is_fire_reels_disposed AS fire_reels_disposed, f.is_simple_sprinklers_disposed AS simple_sprinklers_disposed
            , f.is_emergency_lights_disposed AS emergency_lights_disposed, f.evacuration_stairs, f.is_room_separated AS room_separated
            , f.is_escape_hatch_disposed AS escape_hatch_disposed, f.distribution_lines, f.emergency_stairs, f.expire_fire_extinguisher, f.gas_pipe
            , f.valve, f.re_unauthorized_residence, f.re_fire_extinguisher, f.re_fire_extinguisher_num, f.re_emergency_lights
            , f.re_thoroughfare, f.re_wire, f.re_heater_equiped, f.re_emergency_stairs, f.re_smoke_detector, f.re_gas_pipe, f.re_valve, f.create_time
            , f.inspect_result, f.inspect_date, f.rectification_deadline
        FROM r_firefighting_file f
        <where>
            <if test="inspectResult != null">
                AND f.inspect_result = #{inspectResult}
            </if>
            <if test="inspectResults != null and inspectResults.size() > 0">
                AND f.inspect_result IN <foreach collection="inspectResults" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="inspectDateStart != null">
                AND f.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND f.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="rectificationDeadlineStart != null">
                AND f.rectification_deadline >= #{rectificationDeadlineStart}
            </if>
            <if test="rectificationDeadlineEnd != null">
                AND f.rectification_deadline <![CDATA[<=]]> #{rectificationDeadlineEnd}
            </if>
            <if test="reinspectDateStart != null">
                AND f.reinspect_date >= #{reinspectDateStart}
            </if>
            <if test="reinspectDateEnd != null">
                AND f.reinspect_date <![CDATA[<=]]> #{reinspectDateEnd}
            </if>
            <if test="businessLicenseIssued != null">
                AND f.is_business_license_issued = #{businessLicenseIssued}
            </if>
            <if test="accommodated != null and accommodated != ''">
                AND f.accommodated = CONCAT('%', #{accommodated}, '%')
            </if>
            <if test="flameCooking != null and flameCooking != ''">
                AND f.flame_cooking = CONCAT('%', #{flameCooking}, '%')
            </if>
            <if test="heaterEquiped != null and heaterEquiped != ''">
                AND f.heater_equiped = CONCAT('%', #{heaterEquiped}, '%')
            </if>
            <if test="store != null and store != ''">
                AND f.store LIKE CONCAT('%', #{store}, '%')
            </if>
            <if test="storeKeyman != null and storeKeyman != ''">
                AND f.store_keyman LIKE CONCAT('%', #{storeKeyman}, '%')
            </if>
            <if test="storeContact != null and storeContact != ''">
                AND f.store_contact LIKE CONCAT('%', #{storeContact}, '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ir.id
                    FROM r_firefighting_inspect_realty ir
                    <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                        INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                    </if>
                    WHERE ir.inspect_id = f.id
                        AND ir.inspect_type = 'FILE'
                    <if test="regionId != null and regionId > 0">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND f.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY f.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>
