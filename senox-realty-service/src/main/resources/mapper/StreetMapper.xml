<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.StreetMapper">

    <resultMap id="Result_Street" type="com.senox.realty.domain.Street">
        <id property="id" jdbcType="BIGINT" column="id"/>
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="regionId" jdbcType="BIGINT" column="region_id" />
        <result property="orderNum" column="order_num"/>
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加费项 -->
    <insert id="addStreet" parameterType="com.senox.realty.domain.Street" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_street(
            name, region_id, order_num, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{name}, #{regionId}, #{orderNum}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新费项 -->
    <update id="updateStreet" parameterType="com.senox.realty.domain.Street">
        UPDATE r_street
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="regionId != null">
                , region_id = #{regionId}
            </if>
            <if test="orderNum != null">
                , order_num = #{orderNum}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 查找费项 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_Street">
        SELECT id, name, region_id, order_num FROM r_street WHERE id = #{id}
    </select>

    <select id="listRegionStreet" parameterType="java.lang.Long" resultMap="Result_Street">
        SELECT id, name, region_id, order_num FROM r_street WHERE region_id = #{regionId} AND is_disabled = 0
    </select>

    <!-- 费项列表 -->
    <select id="listAll" resultMap="Result_Street">
        SELECT id, name, region_id, order_num FROM r_street WHERE is_disabled = 0
    </select>

</mapper>
