<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyMapper">

    <resultMap id="Result_RealtyVo" type="com.senox.realty.vo.RealtyVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="serialNo" jdbcType="VARCHAR" column="serial_no" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="nature" jdbcType="TINYINT" column="nature" />
        <result property="area" jdbcType="DECIMAL" column="area" />
        <result property="regionId" jdbcType="BIGINT"  column="region_id" />
        <result property="regionName" jdbcType="VARCHAR" column="region_name" />
        <result property="region1" jdbcType="VARCHAR" column="region1" />
        <result property="region2" jdbcType="VARCHAR" column="region2" />
        <result property="streetId" jdbcType="BIGINT" column="street_id" />
        <result property="streetName" jdbcType="VARCHAR" column="street_name" />
        <result property="address" jdbcType="VARCHAR" column="address" />
        <result property="professionId" jdbcType="BIGINT" column="profession_id" />
        <result property="professionName" jdbcType="BIGINT" column="profession_name" />
        <result property="ownerId" jdbcType="BIGINT" column="owner_id" />
        <result property="ownerName" jdbcType="VARCHAR" column="owner_name" />
        <result property="ownerSerial" jdbcType="VARCHAR" column="owner_serial" />
        <result property="waterReadings" jdbcType="INTEGER" column="water_readings" />
        <result property="electricReadings" jdbcType="INTEGER" column="electric_readings" />
        <result property="rentTaxCode" column="rent_tax_code" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
    </resultMap>

    <!-- 查找物业及业主信息 -->
    <select id="findWithOwnerById" parameterType="java.lang.Long" resultMap="Result_RealtyVo">
        SELECT r.id, r.serial_no, r.name, r.nature, r.area, r.region_id, r.region_name, r.region1, region2, r.street_id, r.street_name, r.address
            , r.profession_id, r.profession_name, r.owner_id, r.owner_name, c.serial_no AS owner_serial, r.is_disabled
        FROM r_realty r
            LEFT JOIN u_customer c ON r.owner_id = c.id
        WHERE r.id = #{id}
    </select>

    <!-- 查找物业id -->
    <select id="findIdBySerialNo" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT id FROM r_realty WHERE serial_no = #{serialNo} AND is_disabled = 0
    </select>

    <!-- 物业数 -->
    <select id="countRealty" parameterType="com.senox.realty.vo.RealtySearchVo" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM r_realty r
        <if test="isGuarantee != null">
            LEFT JOIN r_realty_ext e ON e.realty_id = r.id
        </if>
        <where>
            <if test="serialNo != null and serialNo != ''">
                AND r.serial_no LIKE CONCAT('%', #{serialNo}, '%')
            </if>
            <if test="null != serialNos and serialNos.size() > 0">
                AND r.serial_no IN <foreach collection="serialNos" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="regionId != null">
                AND r.region_id = #{regionId}
            </if>
            <if test="streetId != null">
                AND r.street_id = #{streetId}
            </if>
            <if test="ownerId != null">
                AND r.owner_id = #{ownerId}
            </if>
            <if test="address != null and address != ''">
                AND r.address LIKE CONCAT('%', #{address}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword}, '%') OR r.name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="isGuarantee != null">
                <choose>
                    <when test="isGuarantee">AND e.guarantee_start_date IS NOT NULL</when>
                    <otherwise>AND e.guarantee_start_date IS NULL</otherwise>
                </choose>
            </if>
            AND r.is_disabled = 0
        </where>
    </select>

    <!-- 查询物业列表 -->
    <select id="listRealty" parameterType="com.senox.realty.vo.RealtySearchVo" resultType="com.senox.realty.domain.Realty">
        SELECT r.id, r.serial_no, r.name, r.nature, r.area, r.region_name, r.region1, r.region2, r.street_name, r.address, r.profession_name, r.owner_id, r.owner_name
            , r.modified_time
        FROM r_realty r
        LEFT JOIN r_business_region br on br.id = r.region_id
        LEFT JOIN r_street s on r.street_id = s.id
        <if test="isGuarantee != null">
            LEFT JOIN r_realty_ext e ON e.realty_id = r.id
        </if>
        <where>
            <if test="serialNo != null and serialNo != ''">
                AND r.serial_no LIKE CONCAT('%', #{serialNo}, '%')
            </if>
            <if test="null != serialNos and serialNos.size() > 0">
                AND r.serial_no IN <foreach collection="serialNos" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="regionId != null">
                AND r.region_id = #{regionId}
            </if>
            <if test="streetId != null">
                AND r.street_id = #{streetId}
            </if>
            <if test="ownerId != null">
                AND r.owner_id = #{ownerId}
            </if>
            <if test="address != null and address != ''">
                AND r.address LIKE CONCAT('%', #{address}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (r.serial_no LIKE CONCAT('%', #{keyword}, '%') OR r.name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="isGuarantee != null">
                <choose>
                    <when test="isGuarantee">AND e.guarantee_start_date IS NOT NULL</when>
                    <otherwise>AND e.guarantee_start_date IS NULL</otherwise>
                </choose>
            </if>
            AND r.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>ORDER BY br.order_num asc, s.order_num asc, r.serial_no asc</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 根据物业号获取物业id -->
    <select id="listReadings" parameterType="java.util.List" resultType="com.senox.realty.vo.RealtyReadingsVo">
        SELECT r.id, r.serial_no AS realty_serial, IFNULL(e.water_readings, 0) AS water_readings, IFNULL(e.electric_readings, 0) AS electric_readings, 0 AS alias
        FROM r_realty r
            LEFT JOIN r_realty_ext e on r.id = e.realty_id
        WHERE r.serial_no IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND r.is_disabled = 0
    </select>

    <!--未租赁公司物业数-->
    <select id="unRentCompanyRealtyNum" resultType="int">
        SELECT
            ifnull(un_rent_record_company_realty_num +
        (
                SELECT
                    SUM( CASE WHEN r.nature = 1 THEN 1 ELSE 0 END ) AS un_rent_record_company_realty_num
                FROM
                    r_realty r
                LEFT JOIN r_contract c ON r.id = c.realty_id
                WHERE c.id IS NULL
        ), 0) AS un_rent_company_realty_num
        FROM
        (
            SELECT
                sum( CASE WHEN r.nature = 1 THEN 1 ELSE 0 END ) AS un_rent_record_company_realty_num
            FROM
            (
                SELECT
                    r.nature
                FROM
                r_realty r
                INNER JOIN r_contract c ON r.id = c.realty_id
                GROUP BY
                    r.nature,
                    c.realty_id
                HAVING
                    max( `status` ) <![CDATA[<]]> 2
            ) AS r
        ) AS t
    </select>

    <!--已租赁公司物业数-->
    <select id="rentCompanyRealtyNum" resultType="int">
        SELECT
            ifnull(sum( CASE WHEN r.nature = 1 THEN 1 ELSE 0 END ), 0) AS rent_company_realty_num
        from (
                 SELECT
                     r.nature, c.realty_id
                 from r_realty r INNER JOIN r_contract c on r.id = c.realty_id
                 where status = 2
                 GROUP BY r.nature, c.realty_id
        ) as r
    </select>

    <select id="countListRealtyTaxRate" resultType="int">
        select count(*)
        from r_realty r
        inner join r_realty_ext re on re.realty_id = r.id and re.rent_tax_code is not null
        <where>
            <if test="null != serialNos and serialNos.size() > 0">
                and r.serial_no in
                <foreach collection="serialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listRealtyTaxRate" resultMap="Result_RealtyVo">
        select id,
               serial_no,
               street_name,
               name,
               address,
               area,
               nature,
               owner_id,
               owner_name,
               profession_name,
               region_name,
               re.rent_tax_code,
               r.modified_time
        from r_realty r
                 inner join r_realty_ext re on re.realty_id = r.id and re.rent_tax_code is not null
        <where>
            <if test="null != serialNos and serialNos.size() > 0">
                and r.serial_no in
                <foreach collection="serialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
