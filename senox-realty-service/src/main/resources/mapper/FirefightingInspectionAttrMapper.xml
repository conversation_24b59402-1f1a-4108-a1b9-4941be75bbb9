<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingInspectionAttrMapper">

    <!-- 更新巡检变量 -->
    <update id="updateInspectionAttrs">
        UPDATE r_firefighting_inspection_attr
        <set>
            <trim prefix="attr_type = CASE" suffix="ELSE attr_type END, ">
                <foreach collection="attrs" item="item">
                    <if test="item.attrType != null and item.attrType != ''">
                        WHEN attr_name = #{item.attrName} THEN #{item.attrType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="attr_value = CASE" suffix="ELSE attr_value END, ">
                <foreach collection="attrs" item="item">
                    <if test="item.attrValue != null and item.attrValue != ''">
                        WHEN attr_name = #{item.attrName} THEN #{item.attrValue}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </set>
        WHERE inspection_type = #{inspectionType}
            AND inspection_id = #{inspectionId}
            AND template_code = #{code}
            AND template_version = #{version}
            AND attr_name IN <foreach collection="attrs" item="item" open="(" close=")" separator=",">#{item.attrName}</foreach>
    </update>

    <!-- 删除巡检变量 -->
    <delete id="deleteInspectionAttrs">
        DELETE FROM r_firefighting_inspection_attr
        WHERE inspection_type = #{inspectionType}
            AND inspection_id = #{inspectionId}
            AND template_code = #{code}
            AND template_version = #{version}
            AND attr_name IN <foreach collection="attrs" item="item" open="(" close=")" separator=",">#{item.attrName}</foreach>
    </delete>
</mapper>
