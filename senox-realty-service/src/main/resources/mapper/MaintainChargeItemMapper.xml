<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainChargeItemMapper">

    <resultMap id="ChargeItem_Result" type="com.senox.realty.vo.MaintainChargeVo">
        <result property="id" column="id"/>
        <result property="chargeNo" column="charge_no"/>
        <result property="orderId" column="order_id"/>
        <result property="jobId" column="job_id"/>
        <result property="chargeYear" column="charge_year"/>
        <result property="chargeMonth" column="charge_month"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="status" column="status"/>
        <result property="paidTime" column="paid_time"/>
        <result property="remoteOrderId" column="remote_order_id"/>
        <result property="remark" column="remark"/>
        <collection property="chargeItemVos" ofType="com.senox.realty.vo.MaintainChargeItemVo">
            <result property="id" column="item_id"/>
            <result property="jobId" column="job_id"/>
            <result property="orderId" column="order_id"/>
            <result property="chargeId" column="charge_id"/>
            <result property="feeId" column="fee_id"/>
            <result property="feeTitle" column="fee_title"/>
            <result property="feeItemCode" column="fee_item_code"/>
            <result property="feeItemName" column="fee_item_name"/>
            <result property="price" column="price"/>
            <result property="quantity" column="quantity"/>
            <result property="amount" column="amount"/>
        </collection>
    </resultMap>

    <select id="listMaintainChargeByIds" resultMap="ChargeItem_Result">
        select c.id, c.charge_no, c.order_id, c.job_id, c.charge_year, c.charge_month, c.total_amount, c.status, c.paid_time,
               c.remote_order_id, c.remark, ci.id as item_id, ci.charge_id, ci.fee_id, ci.fee_title, ci.fee_item_code,
               ci.fee_item_name, ci.price, ci.quantity, ci.amount, c.order_id, c.job_id
        from r_maintain_charge c
        INNER JOIN r_maintain_charge_item ci ON c.id = ci.charge_id
        WHERE c.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="listChargeItemByJobId" parameterType="long" resultType="com.senox.realty.vo.MaintainChargeItemVo">
        select ci.id, j.job_no, ci.charge_id, ci.fee_id, ci.fee_title, ci.fee_item_code, ci.fee_item_name,
               ci.price, ci.quantity, ci.amount, c.status, c.order_id, c.job_id
        from r_maintain_charge c
                 INNER JOIN r_maintain_charge_item ci ON c.id = ci.charge_id
                 inner join r_maintain_job j on j.id = c.job_id
        where c.job_id = #{jobId}
        ORDER BY c.job_id asc
    </select>

    <select id="listChargeItemByOrderId" parameterType="long" resultType="com.senox.realty.vo.MaintainChargeItemVo">
        select ci.id, j.job_no, ci.charge_id, ci.fee_id, ci.fee_title, ci.fee_item_code, ci.fee_item_name,
               ci.price, ci.quantity, ci.amount, c.status, c.order_id, c.job_id
        from r_maintain_charge c
                INNER JOIN r_maintain_charge_item ci ON c.id = ci.charge_id
                inner join r_maintain_job j on j.id = c.job_id
        where c.order_id = #{orderId}
        ORDER BY c.job_id asc
    </select>

    <select id="listChargeItemByChargeId" parameterType="long" resultType="com.senox.realty.vo.MaintainChargeItemVo">
        select ci.id, j.job_no, ci.charge_id, ci.fee_id, ci.fee_title, ci.fee_item_code, ci.fee_item_name,
               ci.price, ci.quantity, ci.amount, c.status, c.order_id, c.job_id
        from r_maintain_charge c
                 INNER JOIN r_maintain_charge_item ci ON c.id = ci.charge_id
                 inner join r_maintain_job j on j.id = c.job_id
        where c.id = #{chargeId}
        ORDER BY c.job_id asc, ci.fee_id asc
    </select>

</mapper>
