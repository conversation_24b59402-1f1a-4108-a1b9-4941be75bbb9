<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingAccommodateInspectionMapper">

    <!-- 违规住人巡检统计 -->
    <select id="countInspection" parameterType="com.senox.realty.vo.FirefightingAccommodateInspectionSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_accommodate_inspection i
        <where>
            <if test="accommodated != null">
                AND i.accommodated > 0
            </if>
            <if test="inspectResult != null">
                AND i.inspect_result = #{inspectResult}
            </if>
            <if test="inspectResults != null and inspectResults.size() > 0">
                AND i.inspect_result IN <foreach collection="inspectResults" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="inspectDateStart != null">
                AND i.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND i.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="rectificationDeadlineStart != null">
                AND i.rectification_deadline >= #{rectificationDeadlineStart}
            </if>
            <if test="rectificationDeadlineEnd != null">
                AND i.rectification_deadline <![CDATA[<=]]> #{rectificationDeadlineEnd}
            </if>
            <if test="reinspectDateStart != null">
                AND i.reinspect_date >= #{reinspectDateStart}
            </if>
            <if test="reinspectDateEnd != null">
                AND i.reinspect_date <![CDATA[<=]]> #{reinspectDateEnd}
            </if>
            <if test="address != null and address != ''">
                AND i.address LIKE  CONCAT('%', #{address} , '%'))
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ir.id
                    FROM r_firefighting_inspect_realty ir
                    <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                        INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                    </if>
                    WHERE ir.inspect_id = i.id
                        AND ir.inspect_type = 'ACCOMMODATE'
                    <if test="regionId != null and regionId > 0">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND i.is_disabled = 0
        </where>
    </select>


    <select id="listInspection" parameterType="com.senox.realty.vo.FirefightingAccommodateInspectionSearchVo"
            resultType="com.senox.realty.vo.FirefightingAccommodateInspectionBriefVo">
        SELECT i.id, i.address, i.business_type, i.accommodated, i.accommodated_count, i.runner, i.runner_contact
            , i.inspect_result , i.inspector, i.inspect_date, i.rectification_deadline
        FROM r_firefighting_accommodate_inspection i
        <where>
            <if test="accommodated != null">
                AND i.accommodated > 0
            </if>
            <if test="inspectResult != null">
                AND i.inspect_result = #{inspectResult}
            </if>
            <if test="inspectResults != null and inspectResults.size() > 0">
                AND i.inspect_result IN <foreach collection="inspectResults" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="inspectDateStart != null">
                AND i.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND i.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="reinspectDateStart != null">
                AND i.reinspect_date >= #{reinspectDateStart}
            </if>
            <if test="rectificationDeadlineStart != null">
                AND i.rectification_deadline >= #{rectificationDeadlineStart}
            </if>
            <if test="rectificationDeadlineEnd != null">
                AND i.rectification_deadline <![CDATA[<=]]> #{rectificationDeadlineEnd}
            </if>
            <if test="reinspectDateEnd != null">
                AND i.reinspect_date <![CDATA[<=]]> #{reinspectDateEnd}
            </if>
            <if test="address != null and address != ''">
                AND i.address LIKE  CONCAT('%', #{address} , '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ir.id
                    FROM r_firefighting_inspect_realty ir
                    <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                        INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                    </if>
                    WHERE ir.inspect_id = i.id
                        AND ir.inspect_type = 'ACCOMMODATE'
                    <if test="regionId != null and regionId > 0">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND i.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY i.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>