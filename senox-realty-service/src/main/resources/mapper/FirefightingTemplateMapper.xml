<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingTemplateMapper">

    <!-- 消防告知单模板统计 -->
    <select id="countTemplate" parameterType="com.senox.realty.vo.FireFightingTemplateSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_template t
        <where>
            <if test="code != null and code != ''">
                AND t.code = #{code}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="versionGe != null">
                AND t.version >= #{versionGe}
            </if>
            <if test="versionLe != null">
                AND t.version <![CDATA[<=]]> #{versionLe}
            </if>
            <if test="title != null and title != ''">
                AND t.title LIKE CONCAT('%', #{title}, '%')
            </if>
            AND t.is_disabled = 0
        </where>
    </select>

    <select id="listTemplate" parameterType="com.senox.realty.vo.FireFightingTemplateSearchVo"
            resultType="com.senox.realty.domain.FirefightingTemplate">
        SELECT t.id, t.code, t.version, t.status, t.valid_date, t.invalid_date, t.title
        FROM r_firefighting_template t
        <where>
            <if test="code != null and code != ''">
                AND t.code = #{code}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="versionGe != null">
                AND t.version >= #{versionGe}
            </if>
            <if test="versionLe != null">
                AND t.version <![CDATA[<=]]> #{versionLe}
            </if>
            <if test="title != null and title != ''">
                AND t.title LIKE CONCAT('%', #{title}, '%')
            </if>
            AND t.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY t.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>