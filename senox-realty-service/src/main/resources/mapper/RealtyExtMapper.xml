<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyExtMapper">

    <!-- 添加物业扩展信息 -->
    <insert id="addRealtyExt" parameterType="com.senox.realty.domain.RealtyExt">
        INSERT INTO r_realty_ext(
            realty_id, water_readings, electric_readings, water_price, electric_price, water_consume_unit, electric_consume_unit, guarantee_start_date, guarantee_end_date
            , remark, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{realtyId}, #{waterReadings}, #{electricReadings}, #{waterPrice}, #{electricPrice}, #{waterConsumeUnit}, #{electricConsumeUnit}, #{guaranteeStartDate}, #{guaranteeEndDate}
            , #{remark}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新物业扩展信息 -->
    <update id="updateRealtyExt" parameterType="com.senox.realty.domain.RealtyExt">
        UPDATE r_realty_ext
        <set>
            <if test="waterReadings != null">
                , water_readings = #{waterReadings}
            </if>
            <if test="electricReadings != null">
                , electric_readings = #{electricReadings}
            </if>
            <if test="waterPrice != null">
                , water_price = #{waterPrice}
            </if>
            <if test="electricPrice != null">
                , electric_price = #{electricPrice}
            </if>
            <if test="waterConsumeUnit != null">
                , water_consume_unit = #{waterConsumeUnit}
            </if>
            <if test="electricConsumeUnit != null">
                , electric_consume_unit = #{electricConsumeUnit}
            </if>
            <if test="guaranteeStartDate != null">
                , guarantee_start_date = #{guaranteeStartDate}
            </if>
            <if test="guaranteeEndDate != null">
                , guarantee_end_date = #{guaranteeEndDate}
            </if>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE realty_id = #{realtyId}
    </update>

    <!-- 更新物业担保 -->
    <update id="updateRealtyGuarantee" parameterType="com.senox.realty.vo.RealtyGuaranteeVo">
        UPDATE r_realty_ext
        <set>
            , guarantee_start_date = #{guaranteeStartDate}
            , guarantee_end_date = #{guaranteeEndDate}
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE realty_id = #{realtyId}
    </update>

    <update id="updateRealtyExtTaxRate">
        update r_realty_ext
        <set>
              rent_tax_code = #{taxCode}
            , modifier_id = #{user.userId}
            , modifier_name = #{user.username}
            , modified_time = now()
        </set>
        <where>
            and realty_id in
            <foreach collection="realtyIds" item="item" open="(" close=")" separator=",">
             #{item}
            </foreach>
        </where>
    </update>

    <update id="updateEnergyPriceByContractId">
        UPDATE r_realty_ext re
            INNER JOIN r_realty r ON r.id = re.realty_id
            INNER JOIN r_contract c ON c.realty_id  = r.id
        <set>
            <if test="ext.waterPrice != null">
                , re.water_price = #{ext.waterPrice}
            </if>
            <if test="ext.electricPrice != null">
                , re.electric_price = #{ext.electricPrice}
            </if>
            <if test="ext.modifierId != null">
                , re.modifier_id = #{ext.modifierId}
            </if>
            <if test="ext.modifierName != null">
                , re.modifier_name = #{ext.modifierName}
            </if>
            , re.modified_time = NOW()
        </set>
        WHERE c.id = #{contractId}
    </update>

    <!-- 批量更新水电读数 -->
    <update id="batchUpdateRealtyReadings" parameterType="com.senox.realty.vo.RealtyReadingsVo">
        update r_realty_ext re
            inner join r_realty r ON r.id = re.realty_id
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="re.water_readings = CASE" suffix=" ELSE re.water_readings END,">
                <foreach collection="list" item="item">
                    <if test="item.waterReadings != null">
                        WHEN r.serial_no = #{item.realtySerial} THEN #{item.waterReadings}
                    </if>
                </foreach>
            </trim>
            <trim prefix="re.electric_readings = CASE" suffix=" ELSE re.electric_readings END,">
                <foreach collection="list" item="item">
                    <if test="item.electricReadings != null">
                        WHEN r.serial_no = #{item.realtySerial} THEN #{item.electricReadings}
                    </if>
                </foreach>
            </trim>
            <trim prefix="re.modifier_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierId != null">
                        WHEN r.serial_no = #{item.realtySerial} THEN #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="re.modifier_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierName != null">
                        WHEN r.serial_no = #{item.realtySerial} THEN #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            re.modified_time = NOW()
        </trim>
        WHERE r.serial_no IN <foreach collection="list" item="item" separator="," open="(" close=")">#{item.realtySerial}</foreach>
            AND r.is_disabled = 0
    </update>

    <!-- 根据物业id获取扩展信息 -->
    <select id="findByRealtyId" parameterType="java.lang.Long" resultType="com.senox.realty.domain.RealtyExt">
        SELECT realty_id, water_readings, electric_readings, water_price, electric_price, water_consume_unit, electric_consume_unit
            , guarantee_start_date, guarantee_end_date, rent_tax_code, remark
        FROM r_realty_ext
        WHERE realty_id = #{realtyId}
    </select>

    <select id="listEnergyBySerials" parameterType="java.util.List" resultType="com.senox.realty.vo.RealtyEnergyVo">
        SELECT e.realty_id, r.serial_no AS realty_serial, e.water_readings, e.electric_readings, e.water_price, e.electric_price
            , e.water_consume_unit, e.electric_consume_unit, e.guarantee_start_date, e.guarantee_end_date
        FROM r_realty_ext e
            INNER JOIN r_realty r ON e.realty_id = r.id
        WHERE r.serial_no IN <foreach collection="realtySerials" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="listEnergyBySubSerials" parameterType="java.util.List" resultType="com.senox.realty.vo.RealtyEnergyVo">
        SELECT e.realty_id, a.serial_no AS realty_serial, e.water_readings, e.electric_readings, e.water_price, e.electric_price
             , e.water_consume_unit, e.electric_consume_unit
        FROM r_realty_ext e
            INNER JOIN r_realty_alias a ON e.realty_id = a.realty_id
        WHERE a.serial_no IN <foreach collection="realtySerials" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

</mapper>
