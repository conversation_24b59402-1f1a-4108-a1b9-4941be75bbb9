<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingStoreInspectionMapper">

    <!-- 商铺消防巡检记录统计 -->
    <select id="countStoreInspection" parameterType="com.senox.realty.vo.FirefightingStoreInspectionSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_store_inspection si
        <where>
            <if test="storeAccommodated != null">
                AND si.is_store_accommodated = #{storeAccommodated}
            </if>
            <if test="firefightingFacilitiesSatisfied != null">
                AND si.is_firefighting_facilities_satisfied = #{firefightingFacilitiesSatisfied}
            </if>
            <if test="firefightingWallSatisfied != null">
                AND si.is_firefighting_wall_satisfied = #{firefightingWallSatisfied}
            </if>
            <if test="firefightingStairsSatisfied != null">
                AND si.is_firefighting_stairs_satisfied = #{firefightingStairsSatisfied}
            </if>
            <if test="firefightingLinesSatisfied != null">
                AND si.is_firefighting_lines_satisfied = #{firefightingLinesSatisfied}
            </if>
            <if test="firefightingAlarmSatisfied != null">
                AND si.is_firefighting_alarm_satisfied = #{firefightingAlarmSatisfied}
            </if>
            <if test="firefightingExitSatisfied != null">
                AND si.is_firefighting_exit_satisfied = #{firefightingExitSatisfied}
            </if>
            <if test="inspectResult != null">
                AND si.inspect_result = #{inspectResult}
            </if>
            <if test="inspectResults != null and inspectResults.size() > 0">
                AND si.inspect_result IN <foreach collection="inspectResults" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="inspectDateStart != null">
                AND si.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND si.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="rectificationDeadlineStart != null">
                AND si.rectification_deadline >= #{rectificationDeadlineStart}
            </if>
            <if test="rectificationDeadlineEnd != null">
                AND si.rectification_deadline <![CDATA[<=]]> #{rectificationDeadlineEnd}
            </if>
            <if test="reInspectDateStart != null">
                AND si.reinspect_date >= #{reInspectDateStart}
            </if>
            <if test="reInspectDateEnd != null">
                AND si.reinspect_date <![CDATA[<=]]> #{reInspectDateEnd}
            </if>
            <if test="store != null and store != ''">
                AND si.store LIKE CONCAT('%', #{store}, '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ir.id
                    FROM r_firefighting_inspect_realty ir
                    <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                        INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                    </if>
                    WHERE ir.inspect_id = si.id
                        AND ir.inspect_type = 'STORE'
                    <if test="regionId != null and regionId > 0">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND si.is_disabled = 0
        </where>
    </select>

    <!-- 商铺消防巡检记录查询列表 -->
    <select id="listStoreInspection" parameterType="com.senox.realty.vo.FirefightingStoreInspectionSearchVo"
            resultType="com.senox.realty.domain.FirefightingStoreInspection">
        SELECT si.id, si.inspect_agency, si.store, si.store_business_type, si.store_owner, si.store_owner_contact
            , si.store_runner, si.store_runner_contact, si.is_store_accommodated, si.is_firefighting_facilities_satisfied
            , si.is_firefighting_wall_satisfied, si.is_firefighting_stairs_satisfied, si.is_firefighting_lines_satisfied
            , si.is_firefighting_alarm_satisfied, si.is_firefighting_exit_satisfied, si.inspect_result, si.inspect_date
            , si.rectification_deadline, si.reinspect_date
        FROM r_firefighting_store_inspection si
        <where>
            <if test="storeAccommodated != null">
                AND si.is_store_accommodated = #{storeAccommodated}
            </if>
            <if test="firefightingFacilitiesSatisfied != null">
                AND si.is_firefighting_facilities_satisfied = #{firefightingFacilitiesSatisfied}
            </if>
            <if test="firefightingWallSatisfied != null">
                AND si.is_firefighting_wall_satisfied = #{firefightingWallSatisfied}
            </if>
            <if test="firefightingStairsSatisfied != null">
                AND si.is_firefighting_stairs_satisfied = #{firefightingStairsSatisfied}
            </if>
            <if test="firefightingLinesSatisfied != null">
                AND si.is_firefighting_lines_satisfied = #{firefightingLinesSatisfied}
            </if>
            <if test="firefightingAlarmSatisfied != null">
                AND si.is_firefighting_alarm_satisfied = #{firefightingAlarmSatisfied}
            </if>
            <if test="firefightingExitSatisfied != null">
                AND si.is_firefighting_exit_satisfied = #{firefightingExitSatisfied}
            </if>
            <if test="inspectResult != null">
                AND si.inspect_result = #{inspectResult}
            </if>
            <if test="inspectResults != null and inspectResults.size() > 0">
                AND si.inspect_result IN <foreach collection="inspectResults" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="inspectDateStart != null">
                AND si.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND si.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="rectificationDeadlineStart != null">
                AND si.rectification_deadline >= #{rectificationDeadlineStart}
            </if>
            <if test="rectificationDeadlineEnd != null">
                AND si.rectification_deadline <![CDATA[<=]]> #{rectificationDeadlineEnd}
            </if>
            <if test="reInspectDateStart != null">
                AND si.reinspect_date >= #{reInspectDateStart}
            </if>
            <if test="reInspectDateEnd != null">
                AND si.reinspect_date <![CDATA[<=]]> #{reInspectDateEnd}
            </if>
            <if test="store != null and store != ''">
                AND si.store LIKE CONCAT('%', #{store}, '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ir.id
                    FROM r_firefighting_inspect_realty ir
                    <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                        INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                    </if>
                    WHERE ir.inspect_id = si.id
                        AND ir.inspect_type = 'STORE'
                    <if test="regionId != null and regionId > 0">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND si.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY si.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>