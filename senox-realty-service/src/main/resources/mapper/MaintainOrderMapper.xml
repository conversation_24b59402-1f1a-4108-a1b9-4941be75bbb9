<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainOrderMapper">

    <resultMap id="Result_MaintainOrder" type="com.senox.realty.vo.MaintainOrderVo">
        <result property="id" jdbcType="BIGINT" column="id"/>
        <result property="orderNo" jdbcType="VARCHAR" column="order_no"/>
        <result property="maintainType" jdbcType="TINYINT" column="maintain_type"/>
        <result property="customerName" jdbcType="VARCHAR" column="customer_name"/>
        <result property="contact" jdbcType="VARCHAR" column="contact"/>
        <result property="regionName" jdbcType="VARCHAR" column="region_name"/>
        <result property="streetName" jdbcType="VARCHAR" column="street_name"/>
        <result property="address" jdbcType="VARCHAR" column="address"/>
        <result property="problem" jdbcType="VARCHAR" column="problem"/>
        <result property="status" jdbcType="SMALLINT" column="status"/>
        <result property="handlerName" jdbcType="VARCHAR" column="handler_name"/>
        <result property="handlerDeptId" column="handler_dept_id"/>
        <result property="handlerDeptName" column="handler_dept_name"/>
        <result property="createOpenid" jdbcType="VARCHAR" column="create_openid"/>
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time"/>
    </resultMap>

    <insert id="addMaintainOrder" parameterType="com.senox.realty.domain.MaintainOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_maintain_order(
            order_no, maintain_type, customer_name, contact, region_name, street_name, address, problem, create_openid, handler_dept_id, handler_dept_name, management_dept_id, management_dept_name, creator_id, creator_name, create_time
            , modifier_id, modifier_name, modified_time
        ) VALUES (
            #{orderNo}, #{maintainType}, #{customerName}, #{contact}, #{regionName}, #{streetName}, #{address}, #{problem}, #{createOpenid}, #{handlerDeptId}, #{handlerDeptName}, #{managementDeptId}, #{managementDeptName}, #{creatorId}, #{creatorName}, NOW()
            , #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 获取最大的订单号 -->
    <select id="findMaxOrderNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(order_no) FROM r_maintain_order WHERE order_no LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="count" parameterType="com.senox.realty.vo.MaintainOrderSearchVo" resultType="int">
        select count(1)
        from r_maintain_order
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
            <if test="regionName != null and regionName != ''">
                AND region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND street_name = #{streetName}
            </if>
            <if test="maintainType != null ">
                AND maintain_type = #{maintainType}
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name = #{customerName}
            </if>
            <if test="status != null and status.size > 0">
                AND status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND create_openid = #{createOpenid}
            </if>
            <if test="dateStart != null">
                AND create_time >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND create_time <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="finishStart != null">
                AND finish_time >= #{finishStart}
            </if>
            <if test="finishEnd != null">
                AND finish_time <![CDATA[<=]]> #{finishEnd}
            </if>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="evaluated != null and evaluated == true">
                AND evaluate_rating > 0
            </if>
            <if test="evaluated != null and evaluated == false">
                AND evaluate_rating = 0
            </if>
            <if test="evaluateRatingList != null and evaluateRatingList.size() > 0">
                AND evaluate_rating IN <foreach collection="evaluateRatingList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listMaintainOrder" parameterType="com.senox.realty.vo.MaintainOrderSearchVo" resultMap="Result_MaintainOrder">
        select id, order_no, maintain_type, customer_name, contact, region_name, street_name, address, status, problem, handler_name
            , handler_dept_id, handler_dept_name, create_openid, evaluate_rating, evaluate_time, create_time, modified_time
        from r_maintain_order
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
            <if test="regionName != null and regionName != ''">
                AND region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND street_name = #{streetName}
            </if>
            <if test="maintainType != null ">
                AND maintain_type = #{maintainType}
            </if>
            <if test="customerName != null and customerName != ''">
                AND customer_name = #{customerName}
            </if>
            <if test="status != null and status.size() > 0">
                AND status IN <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND create_openid = #{createOpenid}
            </if>
            <if test="dateStart != null">
                AND create_time >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND create_time <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="finishStart != null">
                AND finish_time >= #{finishStart}
            </if>
            <if test="finishEnd != null">
                AND finish_time <![CDATA[<=]]> #{finishEnd}
            </if>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="evaluated != null and evaluated == true">
                AND evaluate_rating > 0
            </if>
            <if test="evaluated != null and evaluated == false">
                AND evaluate_rating = 0
            </if>
            <if test="evaluateRatingList != null and evaluateRatingList.size() > 0">
                AND evaluate_rating IN <foreach collection="evaluateRatingList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}, id
            </when>
            <otherwise>
                ORDER BY modified_time DESC, id
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="exportListMaintainOrder" parameterType="com.senox.realty.vo.MaintainOrderSearchVo" resultMap="Result_MaintainOrder">
        select o.id, o.order_no, o.maintain_type, o.customer_name, o.contact, o.region_name, o.street_name, o.address, o.status, o.problem
                , IFNULL(GROUP_CONCAT( DISTINCT ji.handler_name ), o.handler_name) as handler_name, o.handler_dept_id
                , o.handler_dept_name, o.create_openid, o.evaluate_rating, o.evaluate_time, o.evaluate, o.create_time, o.modified_time
        from r_maintain_order o
            left join r_maintain_job j on o.id = j.order_id AND j.dispatch_type = 0
            left join r_maintain_job_item ji on j.id = ji.job_id
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND o.order_no = #{orderNo}
            </if>
            <if test="regionName != null and regionName != ''">
                AND o.region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND o.street_name = #{streetName}
            </if>
            <if test="maintainType != null ">
                AND o.maintain_type = #{maintainType}
            </if>
            <if test="customerName != null and customerName != ''">
                AND o.customer_name = #{customerName}
            </if>
            <if test="status != null and status.size() > 0">
                AND o.status IN <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND o.create_openid = #{createOpenid}
            </if>
            <if test="dateStart != null">
                AND o.create_time >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND o.create_time <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="finishStart != null">
                AND o.finish_time >= #{finishStart}
            </if>
            <if test="finishEnd != null">
                AND o.finish_time <![CDATA[<=]]> #{finishEnd}
            </if>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="evaluated != null and evaluated == true">
                AND o.evaluate_rating > 0
            </if>
            <if test="evaluated != null and evaluated == false">
                AND o.evaluate_rating = 0
            </if>
            <if test="evaluateRatingList != null and evaluateRatingList.size() > 0">
                AND o.evaluate_rating IN <foreach collection="evaluateRatingList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            AND o.is_disabled = 0
        </where>
        GROUP BY o.id
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}, o.id
            </when>
            <otherwise>
                ORDER BY o.status asc, o.modified_time DESC
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="countOrderStatistic" parameterType="com.senox.realty.vo.MaintainOrderSearchVo" resultType="int">
        SELECT
            count( 1 )
        FROM
            (
                SELECT
                    count( 1 )
                FROM
                    r_maintain_order o
                        INNER JOIN r_maintain_material m
                        INNER JOIN ( SELECT mi.material_id FROM r_maintain_material_item mi GROUP BY mi.material_id ) AS mi ON mi.material_id = m.id ON m.order_id = o.id
                        LEFT JOIN r_maintain_charge c ON c.order_id = o.id
                        LEFT JOIN ( SELECT ci.charge_id FROM r_maintain_charge_item ci GROUP BY ci.charge_id ) AS ci ON ci.charge_id = c.id
                <where>
                    <if test="orderNo != null and orderNo != ''">
                        AND o.order_no = #{orderNo}
                    </if>
                    <if test="regionName != null and regionName != ''">
                        AND o.region_name = #{regionName}
                    </if>
                    <if test="streetName != null and streetName != ''">
                        AND o.street_name = #{streetName}
                    </if>
                    <if test="contact != null and contact != ''">
                        AND o.contact  LIKE CONCAT('%', #{contact}, '%')
                    </if>
                    <if test="maintainType != null ">
                        AND o.maintain_type = #{maintainType}
                    </if>
                    <if test="customerName != null and customerName != ''">
                        AND o.customer_name = #{customerName}
                    </if>
                    <if test="status != null and status.size() > 0">
                        AND o.status IN <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </if>
                    <if test="createOpenid != null and createOpenid != ''">
                        AND o.create_openid = #{createOpenid}
                    </if>
                    <if test="dateStart != null">
                        AND o.create_time >= #{dateStart}
                    </if>
                    <if test="dateEnd != null">
                        AND o.create_time <![CDATA[<=]]> #{dateEnd}
                    </if>
                    <if test="finishStart != null">
                        AND o.finish_time >= #{finishStart}
                    </if>
                    <if test="finishEnd != null">
                        AND o.finish_time <![CDATA[<=]]> #{finishEnd}
                    </if>
                    <if test="managementDeptList != null and managementDeptList.size() > 0">
                        AND o.management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </if>
                    <if test="evaluated != null and evaluated == true">
                        AND o.evaluate_rating > 0
                    </if>
                    <if test="evaluated != null and evaluated == false">
                        AND o.evaluate_rating = 0
                    </if>
                    <if test="evaluateRatingList != null and evaluateRatingList.size() > 0">
                        AND o.evaluate_rating IN <foreach collection="evaluateRatingList" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </if>
                    AND o.is_disabled = 0
                </where>
                GROUP BY o.id
                <if test="payStatus != null">
                    HAVING MIN( CASE WHEN c.`status` IS NULL THEN 1 ELSE c.`status` END ) = #{payStatus}
                </if>
            ) as a
    </select>

    <select id="listOrderStatistic" parameterType="com.senox.realty.vo.MaintainOrderSearchVo" resultType="com.senox.realty.vo.MaintainOrderStatisticVo">
        SELECT
            o.id,
            o.order_no,
            o.contact,
            o.maintain_type,
            o.STATUS,
            o.customer_name,
            o.problem,
            o.create_time,
            MIN( CASE WHEN c.`status` IS NULL THEN 1 ELSE c.`status` END ) AS pay_status,
            IFNULL( sum( DISTINCT mi.cost_price ), 0 ) AS cost_price,
            IFNULL( sum( DISTINCT ci.labor_amount ), 0 ) AS labor_amount,
            IFNULL( sum( DISTINCT ci.income_price ), 0 ) AS income_amount,
            IFNULL( sum( DISTINCT ci.labor_amount ) + sum( DISTINCT ci.income_price ), 0 ) AS total_amount
        FROM r_maintain_order o
        LEFT JOIN r_maintain_material m
        LEFT JOIN (
            SELECT mi.material_id,
                   IFNULL( SUM( mi.amount ), 0 ) AS cost_price
            FROM r_maintain_material_item mi
            GROUP BY mi.material_id
        ) AS mi ON mi.material_id = m.id ON m.order_id = o.id
        LEFT JOIN r_maintain_charge c ON c.order_id = o.id
        LEFT JOIN (
            SELECT
                ci.charge_id,
                IFNULL( sum( CASE fee_id WHEN 18 THEN amount ELSE 0 END ), 0 ) AS labor_amount,
                IFNULL( sum( CASE fee_id WHEN 19 THEN amount ELSE 0 END ), 0 ) AS income_price
            FROM r_maintain_charge_item ci
            GROUP BY ci.charge_id
        ) AS ci ON ci.charge_id = c.id
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND o.order_no = #{orderNo}
            </if>
            <if test="regionName != null and regionName != ''">
                AND o.region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND o.street_name = #{streetName}
            </if>
            <if test="contact != null and contact != ''">
                AND o.contact  LIKE CONCAT('%', #{contact}, '%')
            </if>
            <if test="maintainType != null ">
                AND o.maintain_type = #{maintainType}
            </if>
            <if test="customerName != null and customerName != ''">
                AND o.customer_name = #{customerName}
            </if>
            <if test="status != null and status.size() > 0">
                AND o.status IN <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND o.create_openid = #{createOpenid}
            </if>
            <if test="dateStart != null">
                AND o.create_time >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND o.create_time <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="finishStart != null">
                AND o.finish_time >= #{finishStart}
            </if>
            <if test="finishEnd != null">
                AND o.finish_time <![CDATA[<=]]> #{finishEnd}
            </if>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND o.management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="evaluated != null and evaluated == true">
                AND o.evaluate_rating > 0
            </if>
            <if test="evaluated != null and evaluated == false">
                AND o.evaluate_rating = 0
            </if>
            <if test="evaluateRatingList != null and evaluateRatingList.size() > 0">
                AND o.evaluate_rating IN <foreach collection="evaluateRatingList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            AND o.is_disabled = 0 AND (m.id IS NOT NULL OR c.id IS NOT NULL)
        </where>
        GROUP BY o.id
        <if test="payStatus != null">
            having pay_status = #{payStatus}
        </if>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}, o.id
            </when>
            <otherwise>
                ORDER BY o.modified_time DESC, o.id
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="sumOrderStatistic" parameterType="com.senox.realty.vo.MaintainOrderSearchVo" resultType="com.senox.realty.vo.MaintainOrderStatisticVo">
        SELECT
            IFNULL( sum( DISTINCT mi.cost_price ), 0 ) AS cost_price,
            IFNULL( sum( DISTINCT ci.labor_amount ), 0 ) AS labor_amount,
            IFNULL( sum( DISTINCT ci.income_price ), 0 ) AS income_amount,
            IFNULL( sum( DISTINCT ci.labor_amount ) + sum( DISTINCT ci.income_price ), 0 ) AS total_amount
        FROM r_maintain_order o
        LEFT JOIN r_maintain_material m
        LEFT JOIN (
            SELECT mi.material_id,
                   IFNULL( SUM( mi.amount ), 0 ) AS cost_price
            FROM r_maintain_material_item mi
            GROUP BY mi.material_id
        ) AS mi ON mi.material_id = m.id ON m.order_id = o.id
        LEFT JOIN r_maintain_charge c ON c.order_id = o.id
        LEFT JOIN (
            SELECT
                ci.charge_id,
                IFNULL( sum( CASE fee_id WHEN 18 THEN amount ELSE 0 END ), 0 ) AS labor_amount,
                IFNULL( sum( CASE fee_id WHEN 19 THEN amount ELSE 0 END ), 0 ) AS income_price
            FROM r_maintain_charge_item ci
            GROUP BY ci.charge_id
        ) AS ci ON ci.charge_id = c.id
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND o.order_no = #{orderNo}
            </if>
            <if test="regionName != null and regionName != ''">
                AND o.region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND o.street_name = #{streetName}
            </if>
            <if test="contact != null and contact != ''">
                AND o.contact  LIKE CONCAT('%', #{contact}, '%')
            </if>
            <if test="maintainType != null ">
                AND o.maintain_type = #{maintainType}
            </if>
            <if test="customerName != null and customerName != ''">
                AND o.customer_name = #{customerName}
            </if>
            <if test="status != null and status.size() > 0">
                AND o.status IN <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND o.create_openid = #{createOpenid}
            </if>
            <if test="dateStart != null">
                AND o.create_time >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND o.create_time <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="finishStart != null">
                AND o.finish_time >= #{finishStart}
            </if>
            <if test="finishEnd != null">
                AND o.finish_time <![CDATA[<=]]> #{finishEnd}
            </if>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND o.management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="evaluated != null and evaluated == true">
                AND o.evaluate_rating > 0
            </if>
            <if test="evaluated != null and evaluated == false">
                AND o.evaluate_rating = 0
            </if>
            <if test="evaluateRatingList != null and evaluateRatingList.size() > 0">
                AND o.evaluate_rating IN <foreach collection="evaluateRatingList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            AND o.is_disabled = 0 AND (m.id IS NOT NULL OR c.id IS NOT NULL)
        </where>
        <if test="payStatus != null">
            having pay_status = #{payStatus}
        </if>
    </select>


    <select id="evaluateOrderCount" parameterType="com.senox.realty.vo.MaintainOrderEvaluateSearchVo" resultType="int">
        select count(*) from r_maintain_order o
        <where>
            <if test="evaluateOpenid != null and evaluateOpenid != ''">
                and o.evaluate_openid = #{evaluateOpenid}
            </if>
            <if test="evaluateTimeStart != null">
                AND o.evaluate_time >= #{evaluateTimeStart}
            </if>
            <if test="evaluateTimeEnd != null">
                AND o.evaluate_time <![CDATA[<=]]> #{evaluateTimeEnd}
            </if>
        </where>
    </select>

    <select id="evaluateOrderList" parameterType="com.senox.realty.vo.MaintainOrderEvaluateSearchVo" resultType="com.senox.realty.vo.MaintainOrderVo">
        select id
            , order_no
            , maintain_type
            , customer_name
            , contact
            , region_name
            , street_name
            , address
            , problem
            , handler_name
            , status
            , evaluate_rating
            , create_time
            , modified_time
            , evaluate_time
        from r_maintain_order o
        <where>
            <if test="evaluateOpenid != null and evaluateOpenid != ''">
                and o.evaluate_openid = #{evaluateOpenid}
            </if>
            <if test="evaluateTimeStart != null">
                AND o.evaluate_time >= #{evaluateTimeStart}
            </if>
            <if test="evaluateTimeEnd != null">
                AND o.evaluate_time <![CDATA[<=]]> #{evaluateTimeEnd}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY o.id desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
