<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyEnergyPointMapper">
    <select id="checkPointDeviceBind" resultType="integer">
        select count(*)
        from r_realty_energy_point
        <where>
            and point_type = #{energyPointType.value}
            and realty_serial_no = #{realtyBindPoint.realtySerialNo}
            and point_code = #{realtyBindPoint.pointCode}
        </where>
    </select>

    <insert id="bindPointDevice" parameterType="com.senox.realty.vo.RealtyBindEnergyMeteringPointVo">
        insert into r_realty_energy_point(point_code, point_type, realty_serial_no)
        values (#{realtyBindPoint.pointCode}, #{energyPointType.value}, #{realtyBindPoint.realtySerialNo});
    </insert>

    <delete id="unBindPointDevice">
        delete
        from r_realty_energy_point
        where point_code = #{pointCode}
    </delete>

    <select id="meteringPointToRealtyCountList" resultType="int">
        select count(*)
        from d_energy_metering_point dp
        left join r_realty_energy_point rep on rep.point_code = dp.code
        left join r_realty r on r.serial_no = rep.realty_serial_no
        <where>
            and dp.is_disabled = false
            <if test="null != code and code != ''">
                and dp.code like concat('%',#{code},'%')
            </if>
            <if test="null != codes and codes.size() > 0">
                and dp.code in
                <foreach collection="codes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != name and name != ''">
                and dp.name like concat('%',#{name},'%')
            </if>
            <if test="null != energyType">
                and dp.`type` = #{energyType.value}
            </if>
            <if test="null != rtuCode and rtuCode != ''">
                and dp.rtu_code like concat('%',#{rtuCode},'%')
            </if>
            <if test="null != existRealty">
                <choose>
                    <when test="existRealty">and rep.realty_serial_no is not null</when>
                    <otherwise>and rep.realty_serial_no is null</otherwise>
                </choose>
            </if>
            <if test="null != state">
                and dp.status = #{state.state}
            </if>
            <if test="null != powerState">
                and dp.power_status = #{powerState.state}
            </if>
            <if test="null != keyword and keyword != ''">
                and (dp.code like concat('%', #{keyword} ,'%') or dp.name like concat('%', #{keyword} ,'%'))
            </if>
            <if test="null != realtySerialNo and realtySerialNo != ''">
                and r.serial_no like concat('%',#{realtySerialNo})
            </if>
            <if test="null != realtyName and realtyName != ''">
                and r.name like concat('%',#{realtyName},'%')
            </if>
        </where>
    </select>

    <select id="meteringPointToRealtyList" resultType="com.senox.realty.vo.RealtyToEnergyMeteringPointVo">
        select dp.id,
        dp.code,
        dp.name,
        dp.`type` as energy_type,
        dp.rtu_code,
        dp.rate,
        dp.status as state,
        dp.power_status as power_state,
        r.name as realty_name,
        r.serial_no as realty_serial_no
        from d_energy_metering_point dp
        left join r_realty_energy_point rep on rep.point_code = dp.code
        left join r_realty r on r.serial_no = rep.realty_serial_no
        <where>
            and dp.is_disabled = false
            <if test="null != code and code != ''">
                and dp.code like concat('%',#{code},'%')
            </if>
            <if test="null != codes and codes.size() > 0">
                and dp.code in
                <foreach collection="codes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != name and name != ''">
                and dp.name like concat('%',#{name},'%')
            </if>
            <if test="null != energyType">
                and dp.`type` = #{energyType.value}
            </if>
            <if test="null != rtuCode and rtuCode != ''">
                and dp.rtu_code like concat('%',#{rtuCode},'%')
            </if>
            <if test="null != existRealty">
                <choose>
                    <when test="existRealty">and rep.realty_serial_no is not null</when>
                    <otherwise>and rep.realty_serial_no is null</otherwise>
                </choose>
            </if>
            <if test="null != state">
                and dp.status = #{state.state}
            </if>
            <if test="null != powerState">
                and dp.power_status = #{powerState.state}
            </if>
            <if test="null != keyword and keyword != ''">
                and (dp.code like concat('%', #{keyword} ,'%') or dp.name like concat('%', #{keyword} ,'%'))
            </if>
            <if test="null != realtySerialNo and realtySerialNo != ''">
                and r.serial_no like concat('%',#{realtySerialNo})
            </if>
            <if test="null != realtyName and realtyName != ''">
                and r.name like concat('%',#{realtyName},'%')
            </if>
        </where>
        <choose>
            <when test="null != orderStr and orderStr != ''">
                order by ${orderStr}
            </when>
            <otherwise>
                order by dp.id
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="meteringPointReadingsToRealtyCountList" resultType="int">
        select count(*)
        from
        <choose>
            <when test="realTime">
                d_energy_metering_point_readings_new epb
            </when>
            <otherwise>
                d_energy_metering_point_readings epb
            </otherwise>
        </choose>
        <where>
            <if test="null != pointCode and pointCode != ''">
                and epb.point_code like concat('%',#{pointCode},'%')
            </if>
            <if test="null != pointType">
                and epb.point_type = #{pointType}
            </if>
            <if test="null != startTime">
                and epb.data_time >= #{startTime}
            </if>
            <if test="null != endTime">
                and epb.data_time &lt;= #{endTime}
            </if>
            <if test="null != realtySerialNo and realtySerialNo != ''">
                and epb.realty_serial_no like concat('%',#{realtySerialNo})
            </if>
            <if test="null != existRealty">
                <choose>
                    <when test="existRealty">and epb.realty_serial_no is not null</when>
                    <otherwise>and epb.realty_serial_no is null</otherwise>
                </choose>
            </if>
            <if test="null != realtyName and realtyName != ''">
                and epb.realty_name like concat('%',#{realtyName},'%')
            </if>
        </where>
    </select>

    <select id="meteringPointReadingsToRealtyList" resultType="com.senox.realty.vo.RealtyToEnergyMeteringPointReadingsVo">
        select epb.id,
        epb.rtu_code,
        epb.point_code,
        epb.readings,
        epb.data_time,
        epb.grab_time,
        epb.realty_serial_no,
        epb.realty_name,
        epb.point_rate,
        epb.point_type
        from
        <choose>
            <when test="realTime">
                d_energy_metering_point_readings_new epb
            </when>
            <otherwise>
                d_energy_metering_point_readings epb
            </otherwise>
        </choose>
        <where>
            <if test="null != pointCode and pointCode != ''">
                and epb.point_code like concat('%',#{pointCode},'%')
            </if>
            <if test="null != pointType">
                and epb.point_type = #{pointType}
            </if>
            <if test="null != startTime">
                and epb.data_time >= #{startTime}
            </if>
            <if test="null != endTime">
                and epb.data_time &lt;= #{endTime}
            </if>
            <if test="null != realtySerialNo and realtySerialNo != ''">
                and epb.realty_serial_no like concat('%',#{realtySerialNo})
            </if>
            <if test="null != existRealty">
                <choose>
                    <when test="existRealty">and epb.realty_serial_no is not null</when>
                    <otherwise>and epb.realty_serial_no is null</otherwise>
                </choose>
            </if>
            <if test="null != realtyName and realtyName != ''">
                and epb.realty_name like concat('%',#{realtyName},'%')
            </if>
        </where>
        <choose>
            <when test="null != orderStr and orderStr != ''">
                order by ${orderStr}
            </when>
            <otherwise>
                order by epb.data_time desc
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
