<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.OneTimeFeeMapper">

    <resultMap id="Result_OneTimeFeeVo" type="com.senox.realty.vo.OneTimeFeeVo">
        <id property="id" column="id" jdbcType="BIGINT" />
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="refrigeration" column="is_refrigeration" jdbcType="TINYINT"/>
        <result property="mobileEditable" column="mobile_editable" jdbcType="TINYINT"/>
        <result property="departments" column="departments" jdbcType="VARCHAR" />
        <result property="departmentIdStr" column="department_id_str" jdbcType="VARCHAR" />

    </resultMap>

    <!-- 添加一次性费项 -->
    <insert id="addOneTimeFee" parameterType="com.senox.realty.domain.OneTimeFee" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_one_time_fee(
            name, is_refrigeration, mobile_editable, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{name}, #{refrigeration}, #{mobileEditable}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新一次性费项 -->
    <update id="updateOneTimeFee" parameterType="com.senox.realty.domain.OneTimeFee">
        UPDATE r_one_time_fee
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="refrigeration != null">
                , is_refrigeration = #{refrigeration}
            </if>
            <if test="mobileEditable != null">
                , mobile_editable = #{mobileEditable}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null and modifierName != ''">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 添加一次性费项部门 -->
    <insert id="batchAddOneTimeFeeDepartment">
        INSERT INTO r_one_time_fee_department(
            fee_id, department_id, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="departments" item="item" separator=",">
        (
            #{fee.id}, #{item}, #{fee.modifierId}, #{fee.modifierName}, NOW(), #{fee.modifierId}, #{fee.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 批量删除一次性费用项目部门 -->
    <delete id="batchDelOneTimeFeeDepartment">
        DELETE FROM r_one_time_fee_department
        WHERE fee_id = #{feeId}
            AND department_id IN <foreach collection="departments" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </delete>

    <!-- 根据id查找一次性费项 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_OneTimeFeeVo">
        SELECT id, name, is_refrigeration, mobile_editable FROM r_one_time_fee WHERE id = #{id} AND is_disabled = 0
    </select>

    <!-- 根据名词查找一次性费项 -->
    <select id="findByName" parameterType="java.lang.String" resultMap="Result_OneTimeFeeVo">
        SELECT id, name, is_refrigeration, mobile_editable FROM r_one_time_fee WHERE name = #{name} AND is_disabled = 0
    </select>

    <!-- 根据id查找收费项目详情 -->
    <select id="findWithDepartmentById" parameterType="java.lang.Long" resultMap="Result_OneTimeFeeVo">
        SELECT f.id, f.name, is_refrigeration, mobile_editable, GROUP_CONCAT(d.id) AS department_id_str, GROUP_CONCAT(d.name) AS departments
        FROM r_one_time_fee f
            LEFT JOIN r_one_time_fee_department fd ON fd.fee_id = f.id
            LEFT JOIN u_department d ON d.id = fd.department_id
        WHERE f.id = #{id} AND f.is_disabled = 0
        GROUP BY f.id, f.name
    </select>

    <!-- 根据名字查找收费项目详情 -->
    <select id="findWithDepartmentByName" parameterType="java.lang.String" resultMap="Result_OneTimeFeeVo">
        SELECT f.id, f.name, is_refrigeration, mobile_editable, GROUP_CONCAT(d.id) AS department_id_str, GROUP_CONCAT(d.name) AS departments
        FROM r_one_time_fee f
            LEFT JOIN r_one_time_fee_department fd ON fd.fee_id = f.id
            LEFT JOIN u_department d ON d.id = fd.department_id
        WHERE f.name = #{name} AND f.is_disabled = 0
    </select>

    <!-- 一次性收费项目部门-->
    <select id="listOneTimeFeeDepartments" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT department_id FROM r_one_time_fee_department WHERE fee_id = #{feeId} AND is_disabled = 0
    </select>

    <!-- 统计一次性收费项目 -->
    <select id="countOneTimeFee" parameterType="com.senox.realty.vo.OneTimeFeeSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(f.id)
        FROM r_one_time_fee f
        <if test="departmentId != null or (departmentIds != null and departmentIds.size() > 0)">
            INNER JOIN r_one_time_fee_department fd ON fd.fee_id = f.id
        </if>
        <where>
            <if test="departmentId != null">
                AND fd.department_id = #{departmentId}
            </if>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND fd.department_id IN <foreach collection="departmentIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            AND f.is_disabled = 0
        </where>
    </select>

    <!-- 一次性收费项目列表 -->
    <select id="listOneTimeFee" parameterType="com.senox.realty.vo.OneTimeFeeSearchVo" resultMap="Result_OneTimeFeeVo">
        SELECT f.id, f.name, is_refrigeration , mobile_editable, GROUP_CONCAT(d.full_name) AS departments
        FROM r_one_time_fee f
            LEFT JOIN r_one_time_fee_department fd ON fd.fee_id = f.id
            LEFT JOIN u_department d ON d.id = fd.department_id
        <where>
            <if test="departmentId != null">
                AND fd.department_id = #{departmentId}
            </if>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND fd.department_id IN <foreach collection="departmentIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            AND f.is_disabled = 0
        </where>
        GROUP BY f.id, f.name
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY f.id
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>
</mapper>
