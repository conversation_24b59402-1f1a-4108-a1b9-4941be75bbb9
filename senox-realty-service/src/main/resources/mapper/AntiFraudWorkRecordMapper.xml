<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.AntiFraudWorkRecordMapper">

    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        insert into r_anti_fraud_work_record(region_name, street_name, full_Address, completed_people,
                                             remaining_people, total_people, description, is_cooperative,
                                             cooperative_notes, registrant,
                                             registration_time, openid, creator_id, creator_name, create_time,
                                             modifier_id, modifier_name, modified_time)
        values ( #{regionName}, #{streetName}, #{fullAddress}, #{completedPeople}
               , #{remainingPeople}, #{totalPeople}, #{description}
               , #{cooperative}, #{cooperativeNotes}, #{registrant}, #{registrationTime}, #{openid}, #{creatorId}
               , #{creatorName}, #{createTime}, #{modifierId}
               , #{modifierName}, #{modifiedTime})
    </insert>

    <update id="updateById">
        update r_anti_fraud_work_record
        <set>
            <if test="null != regionName">
                , region_name = #{regionName}
            </if>
            <if test="null != streetName">
                , street_name = #{streetName}
            </if>
            <if test="null != fullAddress">
                , full_address = #{fullAddress}
            </if>
            <if test="null != completedPeople">
                , completed_people = #{completedPeople}
            </if>
            <if test="null != remainingPeople">
                , remaining_people = #{remainingPeople}
            </if>
            <if test="null != totalPeople">
                , total_people = #{totalPeople}
            </if>
            <if test="null != description and description != ''">
                , description = #{description}
            </if>
            <if test="null != cooperative">
                , is_cooperative = #{cooperative}
            </if>
            <if test="null != cooperativeNotes and cooperativeNotes != ''">
                , cooperative_notes = #{cooperativeNotes}
            </if>
            <if test="null != registrationTime">
                , registration_time = #{registrationTime}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = #{modifiedTime}
        </set>
        <where>
            and id = #{id}
        </where>
    </update>

    <delete id="deleteByIds">
        delete from r_anti_fraud_work_record
        <where>
            and id in
            <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </where>
    </delete>

    <select id="countList" resultType="int">
        select count(*)
        from r_anti_fraud_work_record
        <where>
            <if test="null != openid and openid != ''">
                and openid = #{openid}
            </if>
            <if test="null != regionName and regionName != ''">
                and region_name like concat('%',#{regionName},'%')
            </if>
            <if test="null != streetName and streetName != ''">
                and street_name like concat('%',#{streetName},'%')
            </if>
            <if test="null != fullAddress and fullAddress != ''">
                and full_address like concat('%',#{fullAddress},'%')
            </if>
            <if test="null != registrationTimeStart">
                and registration_time >= #{registrationTimeStart}
            </if>
            <if test="null != registrationTimeEnd">
                and registration_time &lt;= #{registrationTimeEnd}
            </if>
            <if test="null != registrant and registrant != ''">
                and registrant like concat('%',#{registrant},'%')
            </if>
        </where>
    </select>

    <select id="list" resultType="com.senox.realty.vo.AntiFraudWorkRecordVo">
        select id,
               region_name,
               street_name,
               full_Address,
               completed_people,
               remaining_people,
               total_people,
               description,
               is_cooperative as cooperative,
               cooperative_notes,
               registrant,
               registration_time,
               creator_id,
               creator_name,
               create_time,
               modifier_id,
               modifier_name,
               modified_time
        from r_anti_fraud_work_record
        <where>
            <if test="null != id">
                and id = #{id}
            </if>
            <if test="null != openid and openid != ''">
                and openid = #{openid}
            </if>
            <if test="null != regionName and regionName != ''">
                 and region_name like concat('%',#{regionName},'%')
            </if>
            <if test="null != streetName and streetName != ''">
                and street_name like concat('%',#{streetName},'%')
            </if>
            <if test="null != fullAddress and fullAddress != ''">
                and full_address like concat('%',#{fullAddress},'%')
            </if>
            <if test="null != registrationTimeStart">
                and registration_time >= #{registrationTimeStart}
            </if>
            <if test="null != registrationTimeEnd">
                and registration_time &lt;= #{registrationTimeEnd}
            </if>
            <if test="null != registrant and registrant != ''">
                and registrant like concat('%',#{registrant},'%')
            </if>
        </where>
        order by id desc
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="listStatistics" resultType="com.senox.realty.vo.AntiFraudWorkRecordStatistics">
        select sum(completed_people) as total_completed_people, sum(remaining_people) as total_remaining_people, sum(total_people) as total_people
        from r_anti_fraud_work_record
        <where>
            <if test="null != openid and openid != ''">
                and openid = #{openid}
            </if>
            <if test="null != regionName and regionName != ''">
                and region_name like concat('%',#{regionName},'%')
            </if>
            <if test="null != streetName and streetName != ''">
                and street_name like concat('%',#{streetName},'%')
            </if>
            <if test="null != fullAddress and fullAddress != ''">
                and full_address like concat('%',#{fullAddress},'%')
            </if>
            <if test="null != registrationTimeStart">
                and registration_time >= #{registrationTimeStart}
            </if>
            <if test="null != registrationTimeEnd">
                and registration_time &lt;= #{registrationTimeEnd}
            </if>
            <if test="null != registrant and registrant != ''">
                and registrant like concat('%',#{registrant},'%')
            </if>
        </where>
    </select>

</mapper>
