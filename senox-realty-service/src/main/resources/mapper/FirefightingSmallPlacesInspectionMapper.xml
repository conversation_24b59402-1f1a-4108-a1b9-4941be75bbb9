<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingSmallPlacesInspectionMapper">

    <!-- 三小场所、出租屋消防巡检简要记录合计 -->
    <select id="countSmallPlacesInspection" parameterType="com.senox.realty.vo.FirefightingSmallPlacesInspectionSearchVo"
            resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_small_places_inspection i
        <where>
            <if test="inspectResult != null">
                AND i.inspect_result = #{inspectResult}
            </if>
            <if test="inspectResults != null and inspectResults.size() > 0">
                AND i.inspect_result IN <foreach collection="inspectResults" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="inspectDateStart != null">
                AND i.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND i.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="rectificationDeadlineStart != null">
                AND i.rectification_deadline >= #{rectificationDeadlineStart}
            </if>
            <if test="rectificationDeadlineEnd != null">
                AND i.rectification_deadline <![CDATA[<=]]> #{rectificationDeadlineEnd}
            </if>
            <if test="reinspectDateStart != null">
                AND i.reinspect_date >= #{reinspectDateStart}
            </if>
            <if test="reinspectDateEnd != null">
                AND i.reinspect_date <![CDATA[<=]]> #{reinspectDateEnd}
            </if>
            <if test="placeType != null and placeType != ''">
                AND i.place_type = #{placeType}
            </if>
            <if test="inspectedPlace != null and inspectedPlace != ''">
                AND i.inspected_place LIKE CONCAT('%', #{inspectedPlace}, '%')
            </if>
            <if test="keyman != null and keyman != ''">
                AND i.keyman LIKE CONCAT('%', #{keyman}, '%')
            </if>
            <if test="contact != null and contact != ''">
                AND i.contact LIKE CONCAT('%', #{contact}, '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ir.id
                    FROM r_firefighting_inspect_realty ir
                    <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                        INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                    </if>
                    WHERE ir.inspect_id = i.id
                        AND ir.inspect_type = 'SMALL_PLACES'
                    <if test="regionId != null and regionId > 0">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND i.is_disabled = 0
        </where>
    </select>

    <!-- 三小场所、出租屋消防巡检简要记录列表 -->
    <select id="listSmallPlacesInspection" parameterType="com.senox.realty.vo.FirefightingSmallPlacesInspectionSearchVo"
            resultType="com.senox.realty.vo.FirefightingSmallPlacesInspectionBriefVo">
        SELECT i.id, i.inspected_place, i.keyman, i.address, i.place_type, i.business_license, i.building_safety
            , i.business_scope, i.inspect_result, i.inspector, i.inspect_date, i.rectification_deadline, i.reinspector
            , i.reinspect_date, i.expire_fire_extinguisher, i.unauthorized_residence, i.re_fire_extinguisher
            , i.re_fire_extinguisher_num, i.re_emergency_lights, i.re_fire_alarms_disposed, i.re_wire, i.re_unauthorized_residence
        FROM r_firefighting_small_places_inspection i
        <where>
            <if test="inspectResult != null">
                AND i.inspect_result = #{inspectResult}
            </if>
            <if test="inspectResults != null and inspectResults.size() > 0">
                AND i.inspect_result IN <foreach collection="inspectResults" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="inspectDateStart != null">
                AND i.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND i.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="rectificationDeadlineStart != null">
                AND i.rectification_deadline >= #{rectificationDeadlineStart}
            </if>
            <if test="rectificationDeadlineEnd != null">
                AND i.rectification_deadline <![CDATA[<=]]> #{rectificationDeadlineEnd}
            </if>
            <if test="reinspectDateStart != null">
                AND i.reinspect_date >= #{reinspectDateStart}
            </if>
            <if test="reinspectDateEnd != null">
                AND i.reinspect_date <![CDATA[<=]]> #{reinspectDateEnd}
            </if>
            <if test="placeType != null and placeType != ''">
                AND i.place_type = #{placeType}
            </if>
            <if test="inspectedPlace != null and inspectedPlace != ''">
                AND i.inspected_place LIKE CONCAT('%', #{inspectedPlace}, '%')
            </if>
            <if test="keyman != null and keyman != ''">
                AND i.keyman LIKE CONCAT('%', #{keyman}, '%')
            </if>
            <if test="contact != null and contact != ''">
                AND i.contact LIKE CONCAT('%', #{contact}, '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                SELECT ir.id
                FROM r_firefighting_inspect_realty ir
                <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                    INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                </if>
                WHERE ir.inspect_id = i.id
                    AND ir.inspect_type = 'SMALL_PLACES'
                <if test="regionId != null and regionId > 0">
                    AND r.region_id = #{regionId}
                </if>
                <if test="streetId != null and streetId > 0">
                    AND r.street_id = #{streetId}
                </if>
                <if test="realtySerial != null and realtySerial != ''">
                    AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                </if>
                )
            </if>
            AND i.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY i.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>
