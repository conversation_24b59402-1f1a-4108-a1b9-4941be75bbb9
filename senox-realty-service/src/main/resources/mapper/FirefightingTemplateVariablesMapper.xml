<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingTemplateVariablesMapper">

    <!-- 更新模板变量 -->
    <update id="updateVariables" parameterType="com.senox.realty.domain.FirefightingTemplateVariables">
        UPDATE r_firefighting_template_variables
        <set>
            <trim prefix="attr_type = CASE" suffix="ELSE attr_type END, ">
                <foreach collection="list" item="item">
                    <if test="item.attrType != null and item.attrType != ''">
                        WHEN attr_name = #{item.attrName} THEN #{item.attrType}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </set>
        WHERE code = #{code}
            AND version = #{version}
            AND attr_name IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item.attrName}</foreach>
    </update>

    <!-- 删除模板变量 -->
    <delete id="deleteVariables" parameterType="com.senox.realty.domain.FirefightingTemplateVariables">
        DELETE FROM r_firefighting_template_variables
        WHERE code = #{code}
            AND version = #{version}
            AND attr_name IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item.attrName}</foreach>
    </delete>

    <!-- 根据模板id删除模板变量 -->
    <delete id="deleteVariablesByTemplateId" parameterType="java.lang.Long">
        DELETE v
        FROM r_firefighting_template t
            INNER JOIN r_firefighting_template_variables v ON t.code = v.code AND t.version = v.version
        where t.id = #{id}
    </delete>

</mapper>