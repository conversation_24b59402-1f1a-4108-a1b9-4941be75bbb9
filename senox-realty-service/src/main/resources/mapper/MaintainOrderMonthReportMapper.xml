<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainOrderMonthReportMapper">


    <select id="count" parameterType="com.senox.realty.vo.MaintainOrderMonthReportSearchVo" resultType="int">
        select count(*) from r_maintain_order_month_report
        <where>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="reportYearMonthStart != null and reportYearMonthEnd != ''">
                AND report_year_month >= #{reportYearMonthStart}
            </if>
            <if test="reportYearMonthEnd != null and reportYearMonthEnd != ''">
                AND report_year_month <![CDATA[<=]]> #{reportYearMonthEnd}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="list" parameterType="com.senox.realty.vo.MaintainOrderMonthReportSearchVo" resultType="com.senox.realty.vo.MaintainOrderMonthReportVo">
        select id, report_year_month, management_dept_id, management_dept_name, add_singular_numbers, complete_singular_numbers
            from r_maintain_order_month_report
        <where>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="reportYearMonthStart != null and reportYearMonthEnd != ''">
                AND report_year_month >= #{reportYearMonthStart}
            </if>
            <if test="reportYearMonthEnd != null and reportYearMonthEnd != ''">
                AND report_year_month <![CDATA[<=]]> #{reportYearMonthEnd}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY report_year_month desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sum" parameterType="com.senox.realty.vo.MaintainOrderMonthReportSearchVo" resultType="com.senox.realty.vo.MaintainOrderMonthReportVo">
        select sum(add_singular_numbers) as add_singular_numbers
            , sum(complete_singular_numbers) as complete_singular_numbers
        from r_maintain_order_month_report
        <where>
            <if test="managementDeptList != null and managementDeptList.size() > 0">
                AND management_dept_id IN <foreach collection="managementDeptList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="reportYearMonthStart != null and reportYearMonthEnd != ''">
                AND report_year_month >= #{reportYearMonthStart}
            </if>
            <if test="reportYearMonthEnd != null and reportYearMonthEnd != ''">
                AND report_year_month <![CDATA[<=]]> #{reportYearMonthEnd}
            </if>
            AND is_disabled = 0
        </where>
    </select>

</mapper>
