<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.senox.realty.mapper.ContractRentsMapper">
    <select id="contractRentsList" parameterType="com.senox.realty.vo.ContractRentsSearchVo" resultType="com.senox.realty.domain.ContractRents">
        SELECT
            c.contract_no,
            f.amount AS rent_amount,
            mf.amount AS manage_amount,
            IFNULL((SELECT MIN(rd.STATUS) FROM r_realty_deposit rd WHERE rd.contract_id = c.id), 99) AS deposit_status,
            IFNULL((SELECT SUM(amount) FROM r_realty_deposit rd WHERE rd.contract_id = c.id), 0) AS deposit_amount,
            rp.contract_no AS rent_proxy_contract_no,
            oc.name AS owner_name,
            CONCAT_WS(
                    ',',
                    NULLIF(oc.telephone, ''),
                    NULLIF(oc.telephone2, ''),
                    NULLIF(oc.telephone3, '')
                ) AS owner_contact
        FROM r_contract c
                 LEFT JOIN r_contract_fee f ON f.contract_id = c.id AND f.fee_id = 2 AND f.category = 0
                 LEFT JOIN r_contract_fee mf ON mf.contract_id = c.id AND mf.fee_id = 1 AND mf.category = 0
                 LEFT JOIN r_contract rp ON rp.realty_id = c.realty_id AND rp.type = 4 AND rp.start_date &lt;= c.end_date AND rp.end_date &gt;= c.end_date AND rp.STATUS = c.STATUS
                 LEFT JOIN u_customer oc ON oc.id = rp.customer_id
        <where>
            AND c.type = 1
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no = #{contractNo}
            </if>
            <if test="contractNoList != null and contractNoList.size > 0">
                AND c.contract_no in <foreach collection="contractNoList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="startTime != null">
                AND c.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND c.create_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>

    </select>
</mapper>
