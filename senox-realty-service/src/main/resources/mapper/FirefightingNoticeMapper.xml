<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingNoticeMapper">

    <!-- 告知单统计 -->
    <select id="countNotice" parameterType="com.senox.realty.vo.FirefightingNoticeSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_notice n
        <where>
            <if test="templateCode != null and templateCode != ''">
                AND n.template_code = #{templateCode}
            </if>
            <if test="templateVersionGe != null">
                AND n.template_version >= #{templateVersionGe}
            </if>
            <if test="templateVersionLe != null">
                AND n.template_version <![CDATA[<=]]> #{templateVersionLe}
            </if>
            <if test="notifyDateStart != null">
                AND n.notify_date >= #{notifyDateStart}
            </if>
            <if test="notifyDateEnd != null">
                AND n.notify_date <![CDATA[<=]]> #{notifyDateEnd}
            </if>
            <if test="store != null and store != ''">
                AND n.store LIKE CONCAT('%', #{store}, '%')
            </if>
            <if test="storeKeyman != null and storeKeyman != ''">
                AND n.store_keyman LIKE CONCAT('%', #{storeKeyman}, '%')
            </if>
            <if test="storeContact != null and storeContact != ''">
                AND n.store_contact LIKE CONCAT('%', #{storeContact}, '%')
            </if>
            <if test="inspector != null and inspector != ''">
                AND n.inspector LIKE CONCAT('%', #{inspector}, '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ir.id
                    FROM r_firefighting_inspect_realty ir
                    <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                        INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                    </if>
                    WHERE ir.inspect_id = n.id
                        AND ir.inspect_type = 'NOTICE'
                    <if test="regionId != null and regionId > 0">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND n.is_disabled = 0
        </where>
    </select>

    <!-- 告知单列表 -->
    <select id="listNotice" parameterType="com.senox.realty.vo.FirefightingNoticeSearchVo"
            resultType="com.senox.realty.vo.FirefightingNoticeVo">
        SELECT n.id, n.template_code, n.template_version, n.store, n.store_keyman, n.store_contact, n.inspector, n.notify_date
            , t.title, n.create_time
        FROM r_firefighting_notice n
            INNER JOIN r_firefighting_template t ON t.code = n.template_code AND t.version = n.template_version
        <where>
            <if test="templateCode != null and templateCode != ''">
                AND n.template_code = #{templateCode}
            </if>
            <if test="templateVersionGe != null">
                AND n.template_version >= #{templateVersionGe}
            </if>
            <if test="templateVersionLe != null">
                AND n.template_version <![CDATA[<=]]> #{templateVersionLe}
            </if>
            <if test="notifyDateStart != null">
                AND n.notify_date >= #{notifyDateStart}
            </if>
            <if test="notifyDateEnd != null">
                AND n.notify_date <![CDATA[<=]]> #{notifyDateEnd}
            </if>
            <if test="store != null and store != ''">
                AND n.store LIKE CONCAT('%', #{store}, '%')
            </if>
            <if test="storeKeyman != null and storeKeyman != ''">
                AND n.store_keyman LIKE CONCAT('%', #{storeKeyman}, '%')
            </if>
            <if test="storeContact != null and storeContact != ''">
                AND n.store_contact LIKE CONCAT('%', #{storeContact}, '%')
            </if>
            <if test="inspector != null and inspector != ''">
                AND n.inspector LIKE CONCAT('%', #{inspector}, '%')
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0) or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                SELECT ir.id
                FROM r_firefighting_inspect_realty ir
                <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                    INNER JOIN r_realty r ON r.serial_no = ir.realty_serial
                </if>
                WHERE ir.inspect_id = n.id
                    AND ir.inspect_type = 'NOTICE'
                <if test="regionId != null and regionId > 0">
                    AND r.region_id = #{regionId}
                </if>
                <if test="streetId != null and streetId > 0">
                    AND r.street_id = #{streetId}
                </if>
                <if test="realtySerial != null and realtySerial != ''">
                    AND ir.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                </if>
                )
            </if>
            AND n.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY n.notify_date DESC, n.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>