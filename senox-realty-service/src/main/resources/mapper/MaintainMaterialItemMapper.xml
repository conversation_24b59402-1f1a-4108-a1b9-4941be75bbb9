<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainMaterialItemMapper">

    <select id="findByOrderIdAndJobId" resultType="com.senox.realty.vo.MaintainMaterialItemVo" parameterType="long">
        select mi.id, mi.material_id, mi.material_code, mi.material_name, mi.price, mi.quantity, mi.amount, mi.remark
             , m.out_no, j.job_no, m.order_id, m.job_id, CASE WHEN m.out_no IS NOT NULL THEN 1 ELSE 0 END AS out_status
        from r_maintain_material_item mi
            LEFT JOIN r_maintain_material m ON mi.material_id = m.id
            LEFT JOIN r_maintain_job j ON j.id = m.job_id
        where m.order_id = #{orderId} and m.job_id = #{jobId}
    </select>

    <select id="findByOrderId" resultType="com.senox.realty.vo.MaintainMaterialItemVo" parameterType="long">
        select mi.id, mi.material_id, mi.material_code, mi.material_name, mi.price, mi.quantity, mi.amount, mi.remark
             , m.out_no, j.job_no, m.order_id, m.job_id, CASE WHEN m.out_no IS NOT NULL THEN 1 ELSE 0 END AS out_status
        from r_maintain_material_item mi
                 LEFT JOIN r_maintain_material m ON mi.material_id = m.id
                 LEFT JOIN r_maintain_job j ON j.id = m.job_id
        where m.order_id = #{orderId}
    </select>

</mapper>
