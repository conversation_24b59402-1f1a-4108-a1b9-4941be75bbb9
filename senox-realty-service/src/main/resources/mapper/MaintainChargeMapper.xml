<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainChargeMapper">

    <resultMap id="MaintainCharge_Result" type="com.senox.realty.vo.MaintainChargeDataVo">
        <result property="id" column="id"/>
        <result property="chargeNo" column="charge_no"/>
        <result property="orderId" column="order_id"/>
        <result property="jobId" column="job_id"/>
        <result property="chargeYear" column="charge_year"/>
        <result property="chargeMonth" column="charge_month"/>
        <result property="customerName" column="customer_name"/>
        <result property="maintainType" column="maintain_type"/>
        <result property="regionName" column="region_name"/>
        <result property="streetName" column="street_name"/>
        <result property="address" column="address"/>
        <result property="jobNo" column="job_no"/>
        <result property="remark" column="remark"/>
        <result property="receivableAmount" column="receivable_amount"/>
        <result property="paidAmount" column="paid_amount"/>
        <result property="laborAmount" column="labor_amount"/>
        <result property="materialAmount" column="material_amount"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="modifiedTime" column="modified_time"/>
        <result property="paidTime" column="paid_time"/>
        <result property="payWay" column="pay_way"/>
    </resultMap>

    <!-- 获取最大的收费单号 -->
    <select id="findMaxChargeNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(charge_no) FROM r_maintain_charge WHERE charge_no LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="countMaintainCharge" parameterType="com.senox.realty.vo.MaintainChargeSearchVo" resultType="int">
        select  COUNT(c.id) from r_maintain_charge c
                LEFT JOIN r_maintain_order o ON c.order_id = o.id
                LEFT JOIN r_maintain_job j ON c.job_id = j.id
                LEFT JOIN p_order po on po.id = c.remote_order_id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (o.customer_name LIKE CONCAT('%', #{keyword}, '%') OR o.address LIKE CONCAT('%', #{keyword}, '%') OR o.order_no LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="customerName != null and customerName != ''">
                AND o.customer_name  LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="regionName != null and regionName != ''">
                AND o.region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND o.street_name = #{streetName}
            </if>
            <if test="contact != null and contact != ''">
                AND o.contact  LIKE CONCAT('%', #{contact}, '%')
            </if>
            <if test="chargeNo != null and chargeNo != ''">
                AND c.charge_no  = #{chargeNo}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="chargeYear != null">
                AND c.charge_year = #{chargeYear}
            </if>
            <if test="chargeMonth != null">
                AND c.charge_month = #{chargeMonth}
            </if>
            <if test="maintainType != null">
                AND j.maintain_type = #{maintainType}
            </if>
            <if test="startTime != null">
                AND c.paid_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND c.paid_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="createTime != null">
                AND c.create_time >= #{createTime}
            </if>
            <if test="createTime != null">
                AND c.create_time <![CDATA[<=]]> #{createTime}
            </if>
            <if test="payWay != null">
                AND po.pay_way = #{payWay}
            </if>
            AND c.is_disabled = 0
        </where>
    </select>

    <select id="listMaintainCharge" parameterType="com.senox.realty.vo.MaintainChargeSearchVo" resultMap="MaintainCharge_Result">
        select c.id, c.charge_no, c.order_id, c.job_id, c.charge_year, c.charge_month, o.customer_name, j.maintain_type
               , o.region_name, o.street_name, o.address, j.job_no, o.problem as remark, c.create_time, c.modified_time, c.paid_time, po.pay_way, o.order_no, o.contact,
            IFNULL(c.total_amount, 0)  AS receivable_amount, IFNULL(i.labor_amount, 0) AS labor_amount,
            IFNULL(i.material_amount, 0) AS material_amount, IFNULL(CASE c.status WHEN 1 THEN c.total_amount ELSE 0 END ,0) as paid_amount, c.status
        from r_maintain_charge c
        INNER JOIN (
            select charge_id, IFNULL(sum(CASE fee_id WHEN 18 THEN amount ELSE 0 END), 0) AS labor_amount,
                   IFNULL(sum(CASE fee_id WHEN 19 THEN amount ELSE 0 END), 0) AS material_amount
            from r_maintain_charge_item
            GROUP BY charge_id
            ) AS i ON c.id = i.charge_id
        LEFT JOIN r_maintain_order o ON c.order_id = o.id
        LEFT JOIN r_maintain_job j ON c.job_id = j.id
        LEFT JOIN p_order po on po.id = c.remote_order_id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (o.customer_name LIKE CONCAT('%', #{keyword}, '%') OR o.address LIKE CONCAT('%', #{keyword}, '%') OR o.order_no LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="customerName != null and customerName != ''">
                AND o.customer_name  LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="regionName != null and regionName != ''">
                AND o.region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND o.street_name = #{streetName}
            </if>
            <if test="contact != null and contact != ''">
                AND o.contact  LIKE CONCAT('%', #{contact}, '%')
            </if>
            <if test="chargeNo != null and chargeNo != ''">
                AND c.charge_no  = #{chargeNo}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="chargeYear != null">
                AND c.charge_year = #{chargeYear}
            </if>
            <if test="chargeMonth != null">
                AND c.charge_month = #{chargeMonth}
            </if>
            <if test="maintainType != null">
                AND j.maintain_type = #{maintainType}
            </if>
            <if test="startTime != null">
                AND c.paid_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND c.paid_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="createTime != null">
                AND c.create_time >= #{createTime}
            </if>
            <if test="createTime != null">
                AND c.create_time <![CDATA[<=]]> #{createTime}
            </if>
            <if test="payWay != null">
                AND po.pay_way = #{payWay}
            </if>
        </where>
        <if test="orderStr != null and orderStr != ''">
            ORDER BY ${orderStr}
        </if>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="chargeDataVoById" parameterType="long" resultMap="MaintainCharge_Result">
        select c.id, c.charge_no, c.order_id, c.job_id, c.charge_year, c.charge_month, o.customer_name, j.maintain_type
               , o.region_name, o.street_name, o.address, j.job_no, o.problem as remark, c.create_time, c.modified_time, c.paid_time, po.pay_way,
            IFNULL(c.total_amount, 0)  AS receivable_amount ,
            IFNULL(i.labor_amount, 0) AS labor_amount ,
            IFNULL(i.material_amount, 0) AS material_amount,
            IFNULL(CASE c.status WHEN 1 THEN c.total_amount ELSE 0 END ,0) as paid_amount ,c.status
        from r_maintain_charge c
                 INNER JOIN (
            select charge_id, IFNULL(sum(CASE fee_id WHEN 18 THEN amount ELSE 0 END), 0) AS labor_amount,
                   IFNULL(sum(CASE fee_id WHEN 19 THEN amount ELSE 0 END), 0) AS material_amount
            from r_maintain_charge_item
            GROUP BY charge_id
        ) AS i ON c.id = i.charge_id
                 LEFT JOIN r_maintain_order o ON c.order_id = o.id
                 LEFT JOIN r_maintain_job j ON c.job_id = j.id
                 LEFT JOIN p_order po on po.id = c.remote_order_id
        where c.id = #{id}
    </select>

    <!-- 根据派工id获取账单信息 -->
    <select id="chargeDataVoByJobId" parameterType="long" resultMap="MaintainCharge_Result">
        select c.id, c.charge_no, c.order_id, c.job_id, c.charge_year, c.charge_month, o.customer_name, j.maintain_type
               , o.region_name, o.street_name , o.address, j.job_no, o.problem as remark, c.create_time, c.modified_time, c.paid_time, po.pay_way,
            IFNULL(c.total_amount, 0)  AS receivable_amount ,
            IFNULL(i.labor_amount, 0) AS labor_amount ,
            IFNULL(i.material_amount, 0) AS material_amount,
            IFNULL(CASE c.status WHEN 1 THEN c.total_amount ELSE 0 END ,0) as paid_amount ,c.status
        from r_maintain_charge c
                 INNER JOIN (
            select charge_id, IFNULL(sum(CASE fee_id WHEN 18 THEN amount ELSE 0 END), 0) AS labor_amount,
                   IFNULL(sum(CASE fee_id WHEN 19 THEN amount ELSE 0 END), 0) AS material_amount
            from r_maintain_charge_item
            GROUP BY charge_id
        ) AS i ON c.id = i.charge_id
                 LEFT JOIN r_maintain_order o ON c.order_id = o.id
                 LEFT JOIN r_maintain_job j ON c.job_id = j.id
                 LEFT JOIN p_order po on po.id = c.remote_order_id
        where j.id = #{jobId}
    </select>

    <select id="sumMaintainCharge" parameterType="com.senox.realty.vo.MaintainChargeSearchVo" resultMap="MaintainCharge_Result">
        select SUM(IFNULL(c.total_amount, 0))  AS receivable_amount,
               SUM(IFNULL(CASE WHEN c.status = 1 THEN c.total_amount ELSE 0 END , 0)) AS paid_amount,
               SUM(IFNULL(i.labor_amount, 0)) AS labor_amount,
               SUM(IFNULL(i.material_amount, 0)) AS material_amount
        from r_maintain_charge c
                INNER JOIN (
            select charge_id, IFNULL(sum(CASE fee_id WHEN 18 THEN amount ELSE 0 END), 0) AS labor_amount,
                   IFNULL(sum(CASE fee_id WHEN 19 THEN amount ELSE 0 END), 0) AS material_amount
            from r_maintain_charge_item
            GROUP BY charge_id
        ) AS i ON c.id = i.charge_id
                LEFT JOIN r_maintain_order o ON c.order_id = o.id
                LEFT JOIN r_maintain_job j ON c.job_id = j.id
                LEFT JOIN p_order po on po.id = c.remote_order_id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (o.customer_name LIKE CONCAT('%', #{keyword}, '%') OR o.address LIKE CONCAT('%', #{keyword}, '%') OR o.order_no LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="customerName != null and customerName != ''">
                AND o.customer_name  LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="regionName != null and regionName != ''">
                AND o.region_name = #{regionName}
            </if>
            <if test="streetName != null and streetName != ''">
                AND o.street_name = #{streetName}
            </if>
            <if test="contact != null and contact != ''">
                AND o.contact  LIKE CONCAT('%', #{contact}, '%')
            </if>
            <if test="chargeNo != null and chargeNo != ''">
                AND c.charge_no  = #{chargeNo}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="chargeYear != null">
                AND c.charge_year = #{chargeYear}
            </if>
            <if test="chargeMonth != null">
                AND c.charge_month = #{chargeMonth}
            </if>
            <if test="maintainType != null">
                AND o.maintain_type = #{maintainType}
            </if>
            <if test="startTime != null">
                AND c.paid_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND c.paid_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="createTime != null">
                AND c.create_time >= #{createTime}
            </if>
            <if test="createTime != null">
                AND c.create_time <![CDATA[<=]]> #{createTime}
            </if>
            <if test="payWay != null">
                AND po.pay_way = #{payWay}
            </if>
        </where>
    </select>

    <update id="updateChargeStatusById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_maintain_charge c
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id  = c.id
        <set>
            <if test="orderId != null">
                , c.remote_order_id = #{orderId}
            </if>
            <if test="paid">
                , c.status = 1
                , c.paid_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , c.toll_man_id = #{tollMan}
            </if>
            , c.modified_time = NOW()
        </set>
        <where>
            AND c.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND c.status = 0
            AND c.is_disabled = 0
        </where>
    </update>

    <update id="updateChargeStatusByOrderId" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_maintain_charge c
        INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id  = c.id
        <set>
            <if test="paid">
                , c.status = 1
                , c.paid_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , c.toll_man_id = #{tollMan}
            </if>
            , c.modified_time = NOW()
        </set>
        <where>
            AND c.remote_order_id = #{orderId}
            AND c.status = 0
            AND c.is_disabled = 0
        </where>
    </update>

    <!-- 更新票据号 -->
    <update id="updateChargeSerial" parameterType="com.senox.realty.vo.MaintainChargeSerialVo">
        UPDATE r_maintain_charge
        <set>
            <if test="chargeSerial != null and chargeSerial != ''">
                , toll_serial = #{chargeSerial}
            </if>
            <if test="operatorId != null">
                , modifier_id = #{operatorId}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{chargeId} AND status = 1
    </update>

    <select id="findChargeByJobId" parameterType="java.lang.Long" resultType="com.senox.realty.domain.MaintainCharge">
        select id, charge_no, order_id, job_id, charge_year, charge_month, total_amount, status,
               paid_time, remote_order_id, toll_serial, toll_man_id, remark
        from r_maintain_charge
        where job_id = #{jobId}
    </select>

    <select id="listChargeDataVoByOrderId" parameterType="long" resultMap="MaintainCharge_Result">
        select c.id, c.charge_no, c.order_id, c.job_id, c.charge_year, c.charge_month, o.customer_name, j.maintain_type
               , o.region_name, o.street_name, o.address, j.job_no, o.problem as remark, c.create_time, c.modified_time, c.paid_time, po.pay_way,
            IFNULL(c.total_amount, 0)  AS receivable_amount ,
            IFNULL(i.labor_amount, 0) AS labor_amount ,
            IFNULL(i.material_amount, 0) AS material_amount,
            IFNULL(CASE c.status WHEN 1 THEN c.total_amount ELSE 0 END ,0) as paid_amount ,c.status
        from r_maintain_charge c
                 INNER JOIN (
            select charge_id, IFNULL(sum(CASE fee_id WHEN 18 THEN amount ELSE 0 END), 0) AS labor_amount,
                   IFNULL(sum(CASE fee_id WHEN 19 THEN amount ELSE 0 END), 0) AS material_amount
            from r_maintain_charge_item
            GROUP BY charge_id
        ) AS i ON c.id = i.charge_id
                 LEFT JOIN r_maintain_order o ON c.order_id = o.id
                 LEFT JOIN r_maintain_job j ON c.job_id = j.id
                 LEFT JOIN p_order po on po.id = c.remote_order_id
        where c.order_id = #{orderId}
    </select>

    <!-- 更新账单备注 -->
    <update id="updateChargeRemark" parameterType="com.senox.realty.vo.MaintainChargeRemarkVo">
        UPDATE r_maintain_charge
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="remark = CASE" suffix="ELSE remark END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN TRIM(CONCAT(IFNULL(remark, ''), ' ', #{item.remark}))
                </foreach>
            </trim>
            <trim prefix="modifier_id = CASE" suffix="ELSE modifier_id END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.operatorId}
                </foreach>
            </trim>
            <trim prefix="modifier_name = CASE" suffix="ELSE modifier_name END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.operatorName}
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE id IN <foreach collection="list" item="item" separator="," open="(" close=")">#{item.id}</foreach>
    </update>
</mapper>
