<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingInspectTaskMapper">

    <!-- 获取消防巡检任务 -->
    <select id="findVoById" parameterType="java.lang.Long" resultType="com.senox.realty.vo.FirefightingInspectTaskVo">
        SELECT t.id, t.name, t.category, t.start_date, t.end_date
            , IFNULL((SELECT COUNT(*) FROM r_firefighting_inspect_task_item ti WHERE ti.task_id = t.id), 0) AS task_count
            , IFNULL((SELECT COUNT(*) FROM r_firefighting_inspect_task_item ti WHERE ti.task_id = t.id AND ti.inspect_id > 0), 0) AS task_done_count
            , IFNULL((SELECT COUNT(*) FROM (SELECT task_id, utility_id, enterprise_id FROM r_firefighting_inspect_task_item ti3 where ti3.task_id = t.id GROUP BY task_id, utility_id, enterprise_id) t1), 0) AS task_entity_count
            , IFNULL((SELECT COUNT(*) FROM (SELECT task_id, utility_id, enterprise_id FROM r_firefighting_inspect_task_item ti4 where ti4.task_id = t.id GROUP BY task_id, utility_id, enterprise_id having MIN(ti4.inspect_id) > 0) t2), 0) AS task_done_entity_count
            , IFNULL(u.real_name, u.username) AS creator, t.create_time
        FROM r_firefighting_inspect_task t
            LEFT JOIN u_admin_user u ON u.id = t.creator_id
        WHERE t.id = #{id}
    </select>

    <!-- 消防巡检任务统计 -->
    <select id="countTask" parameterType="com.senox.realty.vo.FirefightingInspectTaskSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_inspect_task t
        <where>
            <if test="category != null and category > 0">
                AND t.category = #{category}
            </if>
            <if test="taskDateStart != null">
                AND t.end_date >= #{taskDateStart}
            </if>
            <if test="taskDateEnd != null">
                AND t.start_date <![CDATA[<=]]> #{taskDateEnd}
            </if>
            <if test="taskDone != null">
                <choose>
                    <when test="taskDone">AND NOT EXISTS (SELECT ti.id FROM r_firefighting_inspect_task_item ti WHERE ti.task_id = t.id AND ti.inspect_id = 0)</when>
                    <otherwise>AND EXISTS (SELECT ti.id FROM r_firefighting_inspect_task_item ti WHERE ti.task_id = t.id AND ti.inspect_id = 0)</otherwise>
                </choose>
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                AND EXISTS (
                    SELECT ti.id
                    FROM r_firefighting_inspect_task_item ti
                        LEFT JOIN r_firefighting_utility u ON ti.utility_id = u.id
                        LEFT JOIN r_firefighting_inspect_task_realty tr ON tr.task_item_id = ti.id
                        LEFT JOIN r_realty r ON r.serial_no = tr.realty_serial
                    WHERE ti.task_id = t.id
                    <if test="regionId != null and regionId > 0">
                        AND (u.region_id = #{regionId} OR r.region_id = #{regionId})
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND (u.street_id = #{streetId} OR r.street_id = #{streetId})
                    </if>
                )
            </if>
        </where>
    </select>

    <!-- 消防巡检任务列表 -->
    <select id="listTask" parameterType="com.senox.realty.vo.FirefightingInspectTaskSearchVo"
            resultType="com.senox.realty.vo.FirefightingInspectTaskVo">
        SELECT t.id, t.name, t.category, t.start_date, t.end_date
            , IFNULL((SELECT COUNT(*) FROM r_firefighting_inspect_task_item ti1 WHERE ti1.task_id = t.id), 0) AS task_count
            , IFNULL((SELECT COUNT(*) FROM r_firefighting_inspect_task_item ti2 WHERE ti2.task_id = t.id AND ti2.inspect_id > 0), 0) AS task_done_count
            , IFNULL((SELECT COUNT(*) FROM (SELECT task_id, utility_id, enterprise_id FROM r_firefighting_inspect_task_item ti3 where ti3.task_id = t.id GROUP BY task_id, utility_id, enterprise_id) t1), 0) AS task_entity_count
            , IFNULL((SELECT COUNT(*) FROM (SELECT task_id, utility_id, enterprise_id FROM r_firefighting_inspect_task_item ti4 where ti4.task_id = t.id GROUP BY task_id, utility_id, enterprise_id having MIN(ti4.inspect_id) > 0) t2), 0) AS task_done_entity_count
            , IFNULL(u.real_name, u.username) AS creator, t.create_time
        FROM r_firefighting_inspect_task t
            LEFT JOIN u_admin_user u ON u.id = t.creator_id
        <where>
            <if test="category != null and category > 0">
                AND t.category = #{category}
            </if>
            <if test="taskDateStart != null">
                AND t.end_date >= #{taskDateStart}
            </if>
            <if test="taskDateEnd != null">
                AND t.start_date <![CDATA[<=]]> #{taskDateEnd}
            </if>
            <if test="taskDone != null">
                <choose>
                    <when test="taskDone">AND NOT EXISTS (SELECT ti.id FROM r_firefighting_inspect_task_item ti WHERE ti.task_id = t.id AND ti.inspect_id = 0)</when>
                    <otherwise>AND EXISTS (SELECT ti.id FROM r_firefighting_inspect_task_item ti WHERE ti.task_id = t.id AND ti.inspect_id = 0)</otherwise>
                </choose>
            </if>
            <if test="(regionId != null and regionId > 0) or (streetId != null and streetId > 0)">
                AND EXISTS (
                    SELECT ti.id
                    FROM r_firefighting_inspect_task_item ti
                        LEFT JOIN r_firefighting_utility u ON ti.utility_id = u.id
                        LEFT JOIN r_firefighting_inspect_task_realty tr ON tr.task_item_id = ti.id
                        LEFT JOIN r_realty r ON r.serial_no = tr.realty_serial
                    WHERE ti.task_id = t.id
                    <if test="regionId != null and regionId > 0">
                        AND (u.region_id = #{regionId} OR r.region_id = #{regionId})
                    </if>
                    <if test="streetId != null and streetId > 0">
                        AND (u.street_id = #{streetId} OR r.street_id = #{streetId})
                    </if>
                )
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY t.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>