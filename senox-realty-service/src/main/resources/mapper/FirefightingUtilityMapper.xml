<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingUtilityMapper">

    <!-- 根据位置查找公共消防 -->
    <select id="listByLocation" parameterType="java.util.List" resultType="com.senox.realty.domain.FirefightingUtility">
        SELECT id, region_id, region_name, street_id, street_name, location
        FROM r_firefighting_utility
        WHERE (region_name, street_name, location) IN <foreach collection="list" item="item" open="(" close=")" separator=",">(#{item.regionName}, #{item.streetName}, #{item.location})</foreach>
            AND is_disabled = 0
    </select>

    <!-- 公共消防设施统计 -->
    <select id="countUtility" parameterType="com.senox.realty.vo.FirefightingUtilitySearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_utility
        <where>
            <if test="regionId != null">
                AND region_id = #{regionId}
            </if>
            <if test="streetId != null">
                AND street_id = #{streetId}
            </if>
            <if test="streetName != null and streetName != ''">
                AND street_name LIKE CONCAT('%', ${streetName}, '%')
            </if>
            <if test="location != null and location != ''">
                AND location LIKE CONCAT('%', #{location}, '%')
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <!-- 公共消防设施列表 -->
    <select id="listUtility" parameterType="com.senox.realty.vo.FirefightingUtilitySearchVo" resultType="com.senox.realty.domain.FirefightingUtility">
        SELECT id, region_id, region_name, street_id, street_name, location
        FROM r_firefighting_utility
        <where>
            <if test="regionId != null">
                AND region_id = #{regionId}
            </if>
            <if test="streetId != null">
                AND street_id = #{streetId}
            </if>
            <if test="streetName != null and streetName != ''">
                AND street_name LIKE CONCAT('%', ${streetName}, '%')
            </if>
            <if test="location != null and location != ''">
                AND location LIKE CONCAT('%', #{location}, '%')
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>