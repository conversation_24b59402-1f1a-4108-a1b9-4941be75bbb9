<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.WaterElectricPriceTypeMapper">
    
    <resultMap id="Result_PriceType" type="com.senox.realty.domain.WaterElectricPriceType">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="type" jdbcType="INTEGER" column="type" />
        <result property="price" jdbcType="DECIMAL" column="price" />
        <result property="remark" jdbcType="VARCHAR" column="remark" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加价格类别 -->
    <insert id="addPriceType" parameterType="com.senox.realty.domain.WaterElectricPriceType" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_water_electric_price_type(
            name, type, price, remark, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{name}, #{type}, #{price}, #{remark}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新价格类别 -->
    <update id="updatePriceType" parameterType="com.senox.realty.domain.WaterElectricPriceType">
        UPDATE r_water_electric_price_type
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="type != null">
                , type = #{type}
            </if>
            <if test="price != null">
                , price = #{price}
            </if>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据id查找价格类别 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_PriceType">
        SELECT id, name, type, price, remark, is_disabled FROM r_water_electric_price_type WHERE id = #{id}
    </select>

    <!-- 价格类别列表 -->
    <select id="listAll" resultMap="Result_PriceType">
        SELECT id, name, type, price, is_disabled, modified_time FROM r_water_electric_price_type WHERE is_disabled = 0
    </select>

</mapper>