<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.EnergyProfitMapper">

    <!-- 能源损益合计 -->
    <select id="countProfit" parameterType="com.senox.realty.vo.EnergyProfitSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(p.*)
        FROM r_energy_profit p
        <where>
            <if test="billTimeStart != null">
                AND p.bill_time >= #{billTimeStart}
            </if>
            <if test="billTimeEnd != null">
                AND p.bill_time <![CDATA[<=]]> #{billTimeEnd}
            </if>
            <if test="energyType != null">
                AND p.energy_type = #{energyType.value}
            </if>
        </where>
    </select>

    <!-- 能源损益列表 -->
    <select id="listProfit" parameterType="com.senox.realty.vo.EnergyProfitSearchVo" resultType="com.senox.realty.domain.EnergyProfit">
        SELECT p.id, p.bill_time, p.energy_type, p.recorded_start_date, p.recorded_end_date, p.recorded_days, p.recorded_cost
            , p.balance_start_date, p.balance_end_date, p.balance_days, p.balance_cost, p.diff_days, p.diff_cost
            , p.profits_cost, p.profits_rate
        FROM r_energy_profit
        <where>
            <if test="billTimeStart != null">
                AND p.bill_time >= #{billTimeStart}
            </if>
            <if test="billTimeEnd != null">
                AND p.bill_time <![CDATA[<=]]> #{billTimeEnd}
            </if>
            <if test="energyType != null">
                AND p.energy_type = #{energyType.value}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY p.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>