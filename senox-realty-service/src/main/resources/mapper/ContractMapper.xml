<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.ContractMapper">

    <resultMap id="Result_Contract" type="com.senox.realty.domain.Contract">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="contractNo" jdbcType="VARCHAR" column="contract_no" />
        <result property="orderNo" jdbcType="VARCHAR" column="order_no" />
        <result property="type" jdbcType="TINYINT" column="type" />
        <result property="category" column="category"/>
        <result property="realtyId" jdbcType="BIGINT" column="realty_id" />
        <result property="realtyName" jdbcType="VARCHAR" column="realty_name" />
        <result property="customerId" jdbcType="BIGINT" column="customer_id" />
        <result property="customerName" jdbcType="VARCHAR" column="customer_name" />
        <result property="signDate" jdbcType="TIMESTAMP" column="sign_date" />
        <result property="startDate" jdbcType="TIMESTAMP" column="start_date" />
        <result property="endDate" jdbcType="TIMESTAMP" column="end_date" />
        <result property="status" jdbcType="TINYINT" column="status" />
        <result property="stopBy" jdbcType="BIGINT" column="stop_by" />
        <result property="stopTime" jdbcType="TIMESTAMP" column="stop_time" />
        <result property="icCardNo" jdbcType="VARCHAR" column="ic_card_no" />
        <result property="temporaryRent" jdbcType="TINYINT" column="is_temporary_rent" />
        <result property="renamed" jdbcType="TINYINT" column="is_renamed" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <resultMap id="Result_ContractVo" type="com.senox.realty.vo.ContractVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="contractNo" jdbcType="VARCHAR" column="contract_no" />
        <result property="orderNo" jdbcType="VARCHAR" column="order_no" />
        <result property="type" jdbcType="TINYINT" column="type" />
        <result property="realtyId" jdbcType="BIGINT" column="realty_id" />
        <result property="realtySerial" jdbcType="VARCHAR" column="realty_serial" />
        <result property="realtyName" jdbcType="VARCHAR" column="realty_name" />
        <result property="customerId" jdbcType="BIGINT" column="customer_id" />
        <result property="customerSerial" jdbcType="VARCHAR" column="customer_serial" />
        <result property="customerName" jdbcType="VARCHAR" column="customer_name" />
        <result property="signDate" jdbcType="TIMESTAMP" column="sign_date" />
        <result property="startDate" jdbcType="TIMESTAMP" column="start_date" />
        <result property="endDate" jdbcType="TIMESTAMP" column="end_date" />
        <result property="status" jdbcType="TINYINT" column="status" />
        <result property="temporaryRent" jdbcType="TINYINT" column="is_temporary_rent" />
        <result property="renamed" jdbcType="TINYINT" column="is_renamed" />
        <result property="penaltyStartDate" jdbcType="INTEGER" column="penalty_start_date" />
        <result property="waterPriceType" jdbcType="BIGINT" column="water_price_type" />
        <result property="electricPriceType" jdbcType="BIGINT" column="electric_price_type" />
        <result property="firstRate" jdbcType="DECIMAL" column="first_rate" />
        <result property="monthlyRate" jdbcType="DECIMAL" column="monthly_rate" />
        <result property="archiveUrl" jdbcType="VARCHAR" column="archive_url" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
        <result property="category" column="category"/>
    </resultMap>

    <resultMap id="Result_ContractBank" type="com.senox.realty.vo.ContractBankVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="contractNo" jdbcType="VARCHAR" column="contract_no" />
        <result property="contractType" jdbcType="TINYINT" column="type" />
        <result property="startDate" jdbcType="TIMESTAMP" column="start_date" />
        <result property="endDate" jdbcType="TIMESTAMP" column="end_date" />
        <result property="realtySerial" jdbcType="VARCHAR" column="realty_serial" />
        <result property="realtyRegion" jdbcType="VARCHAR" column="realty_region" />
        <result property="realtyName" jdbcType="VARCHAR" column="realty_name" />
        <result property="customerName" jdbcType="VARCHAR" column="customer_name" />
        <result property="bankName" jdbcType="VARCHAR" column="bank_name" />
        <result property="bankAccountNo" jdbcType="VARCHAR" column="bank_account_no" />
        <result property="bankAccountName" jdbcType="VARCHAR" column="bank_account_name" />
        <result property="bankAccountIdcard" jdbcType="VARCHAR" column="bank_account_idcard" />
        <result property="bankDelegate" jdbcType="TINYINT" column="bank_delegate" />
    </resultMap>

    <!-- 新建合同 -->
    <insert id="addContract" parameterType="com.senox.realty.domain.Contract" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_contract(
            contract_no, order_no, type,category, realty_id, realty_name, customer_id, customer_name, sign_date, start_date, end_date
            , status, is_temporary_rent, is_renamed,creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{contractNo}, #{orderNo}, #{type},#{category},#{realtyId}, #{realtyName}, #{customerId}, #{customerName}, #{signDate}, #{startDate}
            , #{endDate}, #{status}, #{temporaryRent}, #{renamed}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新合同 -->
    <update id="updateContract" parameterType="com.senox.realty.domain.Contract">
        UPDATE r_contract
        <set>
            <if test="orderNo != null">
                , order_no = #{orderNo}
            </if>
            <if test="type != null">
                , type = #{type}
            </if>
            <if test="null != category">
                , category = #{category}
            </if>
            <if test="realtyId != null">
                , realty_id = #{realtyId}
            </if>
            <if test="realtyName != null">
                , realty_name = #{realtyName}
            </if>
            <if test="customerId != null">
                , customer_id = #{customerId}
            </if>
            <if test="customerName != null">
                , customer_name = #{customerName}
            </if>
            <if test="signDate != null">
                , sign_date = #{signDate}
            </if>
            <if test="startDate != null">
                , start_date = #{startDate}
            </if>
            <if test="endDate != null">
                , end_date = #{endDate}
            </if>
            <if test="status != null">
                , status = #{status}
            </if>
            <if test="stopBy != null">
                , stop_by = #{stopBy}
            </if>
            <if test="stopTime != null">
                , stop_time = #{stopTime}
            </if>
            <if test="icCardNo != null">
                , ic_card_no = #{icCardNo}
            </if>
            <if test="temporaryRent != null">
                , is_temporary_rent = #{temporaryRent}
            </if>
            <if test="renamed != null">
                , is_renamed = #{renamed}
            </if>
            <if test="null != businessLicenseName">
                , business_license_name = #{businessLicenseName}
            </if>
            <if test="null != taxSerial">
                , tax_serial = #{taxSerial}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="enableContract" parameterType="com.senox.realty.vo.ContractEnableDto">
        UPDATE r_contract
        SET status = 2
            , modifier_id = #{operator.userId}
            , modifier_name = #{operator.username}
            , modified_time = NOW()
        WHERE contract_no = #{contractNo} AND status IN (0, 1)
    </update>

    <update id="suspendByContractNo" parameterType="com.senox.realty.vo.RealtyContractSuspendDto">
        UPDATE r_contract
        SET end_date = #{suspendDate}
            , status = 0
            , stop_by = #{operator.userId}
            , stop_time = NOW()
            , modifier_id = #{operator.userId}
            , modifier_name = #{operator.username}
            , modified_time = NOW()
        WHERE contract_no = #{contractNo} AND status = 2
    </update>

    <update id="suspendByRealtyAndType" parameterType="com.senox.realty.vo.RealtyContractSuspendDto">
        UPDATE r_contract
        SET end_date = #{suspendDate}
            , status = 0
            , stop_by = #{operator.userId}
            , stop_time = NOW()
            , modifier_id = #{operator.userId}
            , modifier_name = #{operator.username}
            , modified_time = NOW()
        WHERE realty_id = #{realtyId}
            AND type = #{contractType}
            AND status = 2
            <if test="contractStartDate != null">
                AND start_date = #{contractStartDate}
            </if>
    </update>

    <!-- 根据id获取合同 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_Contract">
        SELECT id, contract_no, order_no, type, category, realty_id, realty_name, customer_id, customer_name, sign_date, start_date
            , end_date, status, stop_by, stop_time, ic_card_no, is_temporary_rent, is_renamed, is_disabled
        FROM r_contract
        WHERE id = #{id}
    </select>

    <!-- 根据合同号获取合同 -->
    <select id="findByContractNo" parameterType="java.lang.String" resultMap="Result_Contract">
        SELECT id, contract_no, order_no, type, category, realty_id, realty_name, customer_id, customer_name, sign_date, start_date
            , end_date, status, stop_by, stop_time, ic_card_no, is_temporary_rent, is_renamed, is_disabled
        FROM r_contract
        WHERE contract_no = #{contractNo}
    </select>

    <select id="listByContractNos"  resultMap="Result_Contract">
        SELECT id, contract_no, order_no, type, category, realty_id, realty_name, customer_id, customer_name, sign_date, start_date
             , end_date, status, stop_by, stop_time, ic_card_no, is_temporary_rent, is_disabled
        FROM r_contract
        WHERE contract_no IN
        <foreach collection="contractNos" item="item" separator="," open="(" close=")">
        #{item}
        </foreach>
    </select>

    <select id="findRealtyLastContract" resultMap="Result_Contract">
        SELECT id, contract_no, order_no, `type`, realty_id, realty_name, customer_id, customer_name, sign_date, start_date
             , end_date, status, stop_by, stop_time, ic_card_no, is_temporary_rent, is_disabled
        FROM r_contract
        <where>
            AND realty_id = #{realtyId}
            AND `type` = #{type}
            AND status = 2
            <if test="id != null and id > 0">
                AND id != #{id}
            </if>
            AND is_disabled = 0
        </where>
        ORDER BY end_date DESC
        LIMIT 1
    </select>

    <select id="findWithExtByContractNo" parameterType="java.lang.String" resultMap="Result_ContractVo">
        SELECT c.id, c.contract_no, c.type, c.realty_id, r.serial_no AS realty_serial, c.customer_id, u.serial_no AS customer_serial, u.name AS customer_name
            , c.start_date, c.end_date, c.status, e.penalty_start_date, e.water_price_type, e.electric_price_type, e.first_rate, e.monthly_rate
        FROM r_contract c
            INNER JOIN r_realty r ON r.id = c.realty_id
            LEFT JOIN r_contract_ext e ON e.contract_id = c.id
            LEFT JOIN u_customer u ON c.customer_id = u.id
        WHERE c.contract_no = #{contractNo}
            AND c.is_disabled = 0
    </select>

    <select id="findRealtyMaxRentableDate" parameterType="java.lang.Long" resultType="java.time.LocalDate">
        SELECT MAX(end_date)
        FROM r_contract
        WHERE realty_id = #{realtyId} AND `type` IN (3, 4, 5) AND status = 2
    </select>

    <select id="listAvailableContractByRealtyId" resultMap="Result_Contract">
        SELECT id, contract_no, order_no, type,category, realty_id, realty_name, customer_id, customer_name, sign_date, start_date
             , end_date, status, stop_by, stop_time, ic_card_no, is_temporary_rent, is_renamed, is_disabled
        FROM r_contract
        WHERE realty_id = #{realtyId}
            AND status = 2
            AND end_date >= #{date}
        ORDER BY type, sign_date DESC
    </select>

    <select id="findWithRealtyAndCustomerById" parameterType="java.lang.Long" resultMap="Result_ContractVo">
        SELECT c.id, c.contract_no, c.order_no, c.type,c.category, c.realty_id, c.realty_name, r.serial_no AS realty_serial, c.customer_id, c.customer_name, cu.serial_no AS customer_serial
            , c.sign_date, c.start_date, c.end_date, c.status, c.stop_by, c.stop_time, c.ic_card_no, c.is_temporary_rent, c.is_renamed, c.is_disabled
        FROM r_contract c
            LEFT JOIN r_realty r ON r.id = c.realty_id
            LEFT JOIN u_customer cu ON cu.id = c.customer_id
        WHERE c.id = #{id}
    </select>

    <select id="findRenewFrom" parameterType="java.lang.Long" resultMap="Result_ContractVo">
        SELECT c.id, c.contract_no, c.order_no, c.type,c.category, c.realty_id, c.realty_name, r.serial_no AS realty_serial, c.customer_id, c.customer_name, cu.serial_no AS customer_serial
            , c.sign_date, c.start_date, c.end_date, c.status, c.stop_by, c.stop_time, c.ic_card_no, c.is_temporary_rent, c.is_renamed,c.is_disabled
        FROM r_contract c
            INNER JOIN r_contract f ON f.realty_id = c.realty_id AND f.`type` = c.`type` AND f.customer_id = c.customer_id AND f.id != c.id
            INNER JOIN r_realty r ON r.id = c.realty_id
            INNER JOIN u_customer cu ON cu.id = c.customer_id
        WHERE f.id = #{id}
            AND DATE_SUB(f.start_date , INTERVAL 1 DAY) = c.end_date
    </select>

    <select id="findMaxContractNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT contract_no FROM r_contract WHERE contract_no LIKE CONCAT(#{prefix}, '%') ORDER BY id DESC LIMIT 1
    </select>

    <!-- 合同统计 -->
    <select id="countContract" parameterType="com.senox.realty.vo.ContractSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(c.id)
        FROM r_contract c
        <if test="(realtySerial != null and realtySerial != '') or (realtyName != null and realtyName != '')">
            INNER JOIN r_realty r ON r.id = c.realty_id
        </if>
        <if test="customerSerial != null and customerSerial != ''">
            INNER JOIN u_customer cu ON cu.id = c.customer_id
        </if>
        <if test="archived != null">
            INNER JOIN r_contract_ext e ON e.contract_id = c.id
        </if>
        <where>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no = #{contractNo}
            </if>
            <if test="realtyId != null">
                AND c.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="realtyName != null and realtyName != ''">
                AND r.name LIKE CONCAT('%', #{realtyName}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND cu.serial_no = #{customerSerial}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="type != null">
                AND c.type = #{type}
            </if>
            <if test="types != null and types.size() > 0">
                AND c.type IN <foreach collection="types" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="signDateBegin != null">
                AND c.sign_date >= #{signDateBegin}
            </if>
            <if test="signDateEnd != null">
                AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="null != category">
                and c.category = #{category}
            </if>
            <if test="archived != null">
                <choose>
                    <when test="archived">AND LENGTH(e.archive_url) > 0</when>
                    <otherwise>AND (e.archive_url IS NULL OR e.archive_url = '')</otherwise>
                </choose>
            </if>
            AND c.is_disabled = 0
        </where>
    </select>

    <!-- 合同列表 -->
    <select id="listContract" parameterType="com.senox.realty.vo.ContractSearchVo" resultMap="Result_ContractVo">
        SELECT c.id, c.contract_no, c.realty_id, r.serial_no AS realty_serial, c.realty_name,c.category, c.customer_id, cu.serial_no AS customer_serial
            , c.customer_name, c.sign_date, c.start_date, c.end_date, c.status, c.ic_card_no, c.is_temporary_rent, c.is_renamed, e.archive_url, c.modified_time
            , cu.telephone as customer_contact
        FROM r_contract c
            LEFT JOIN r_realty r ON r.id = c.realty_id
            LEFT JOIN r_business_region br on br.id = r.region_id
            LEFT JOIN r_street s on r.street_id = s.id
            LEFT JOIN u_customer cu ON cu.id = c.customer_id
            LEFT JOIN r_contract_ext e ON e.contract_id = c.id
        <where>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no = #{contractNo}
            </if>
            <if test="realtyId != null">
                AND c.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="realtyName != null and realtyName != ''">
                AND r.name LIKE CONCAT('%', #{realtyName}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND cu.serial_no = #{customerSerial}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="type != null">
                AND c.type = #{type}
            </if>
            <if test="types != null and types.size() > 0">
                AND c.type IN <foreach collection="types" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="signDateBegin != null">
                AND c.sign_date >= #{signDateBegin}
            </if>
            <if test="signDateEnd != null">
                AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="archived != null">
                <choose>
                    <when test="archived">AND LENGTH(e.archive_url) > 0</when>
                    <otherwise>AND (e.archive_url IS NULL OR e.archive_url = '')</otherwise>
                </choose>
            </if>
             <if test="null != category">
                and c.category = #{category}
             </if>
            AND c.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY c.type, c.status desc, c.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 账单合同 -->
    <select id="listBillContract" parameterType="com.senox.realty.vo.ContractBillSearchVo" resultMap="Result_ContractVo">
        SELECT c.id, c.contract_no, c.realty_id, r.serial_no AS realty_serial, c.realty_name, c.type, c.start_date, c.end_date
            , c.status, c.is_temporary_rent, c.is_renamed,ce.penalty_start_date, ce.water_price_type, ce.electric_price_type
            , ce.first_rate, ce.monthly_rate
        FROM r_contract c
            INNER JOIN r_realty r ON r.id = c.realty_id
            LEFT JOIN r_contract_ext ce ON ce.contract_id = c.id
        <if test="billGenerated != null">
            LEFT JOIN r_realty_bill b ON b.contract_no = c.contract_no AND b.bill_year = #{billYear} AND b.bill_month = #{billMonth}
        </if>
        <if test="payoffGenerated != null">
            LEFT JOIN r_realty_payoff p ON p.contract_no = c.contract_no AND p.bill_year = #{billYear} AND p.bill_month = #{billMonth}
        </if>
        <where>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no = #{contractNo}
            </if>
            <if test="types != null and types.size() > 0">
                AND c.type IN <foreach collection="types" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="status != null">
                <choose>
                    <when test="statelessEndDateBegin != null and statelessEndDateEnd != null">
                        AND (c.status = #{status} OR (c.end_date >= #{statelessEndDateBegin} AND c.end_date <![CDATA[<=]]> #{statelessEndDateEnd}))
                    </when>
                    <otherwise>
                        AND c.status = #{status}
                    </otherwise>
                </choose>
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="billGenerated != null">
                <choose>
                    <when test="billGenerated">
                        AND b.id IS NOT NULL
                    </when>
                    <otherwise>
                        AND b.id IS NULL
                    </otherwise>
                </choose>
            </if>
            <if test="payoffGenerated != null">
                <choose>
                    <when test="payoffGenerated">
                        AND p.id IS NOT NULL
                    </when>
                    <otherwise>
                        AND p.id IS NULL
                    </otherwise>
                </choose>
            </if>
            AND c.is_disabled = 0
        </where>
        ORDER BY r.id, c.id DESC
    </select>


    <select id="listContractBank" parameterType="com.senox.realty.vo.ContractSearchVo" resultMap="Result_ContractBank">
        SELECT c.id, c.contract_no, c.`type`, c.start_date, c.end_date, r.serial_no AS realty_serial, r.region_name AS realty_region
            , r.name AS realty_name, c.customer_name, ce.bank_name, ce.bank_account_no, ce.bank_account_name, ce.bank_account_idcard
            , CASE ce.cost_type WHEN 2 THEN 1 ELSE 0 END AS bank_delegate
        FROM r_contract c
            INNER JOIN r_realty r ON r.id = c.realty_id
            LEFT JOIN r_contract_ext ce ON ce.contract_id = c.id
        <if test="customerSerial != null and customerSerial != ''">
            INNER JOIN u_customer cu ON cu.id = c.customer_id
        </if>
        <where>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no = #{contractNo}
            </if>
            <if test="realtyId != null">
                AND c.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND cu.serial_no = #{customerSerial}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="type != null">
                AND c.type = #{type}
            </if>
            <if test="types != null and types.size() > 0">
                AND c.type IN <foreach collection="types" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="signDateBegin != null">
                AND c.sign_date >= #{signDateBegin}
            </if>
            <if test="signDateEnd != null">
                AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            AND c.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY c.contract_no, r.serial_no</otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <!-- 租赁合同统计 -->
    <select id="countLeaseContract" parameterType="com.senox.realty.vo.ContractSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(c.id)
        FROM r_contract c
            INNER JOIN r_realty r ON r.id = c.realty_id
        <if test="customerSerial != null and customerSerial != ''">
            INNER JOIN u_customer cu ON cu.id = c.customer_id
        </if>
        <where>
        AND c.type = 1
        <if test="contractNo != null and contractNo != ''">
            AND c.contract_no = #{contractNo}
        </if>
        <if test="realtyId != null">
            AND c.realty_id = #{realtyId}
        </if>
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
        </if>
        <if test="realtyName != null and realtyName != ''">
            AND r.name LIKE CONCAT('%', #{realtyName}, '%')
        </if>
        <if test="customerSerial != null and customerSerial != ''">
            AND cu.serial_no = #{customerSerial}
        </if>
        <if test="customerName != null and customerName != ''">
            AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="status != null">
            AND c.status = #{status}
        </if>
        <if test="signDateBegin != null">
            AND c.sign_date >= #{signDateBegin}
        </if>
        <if test="signDateEnd != null">
            AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
        </if>
        <if test="startDateBegin != null">
            AND c.start_date >= #{startDateBegin}
        </if>
        <if test="startDateEnd != null">
            AND c.start_date <![CDATA[<=]]> #{startDateEnd}
        </if>
        <if test="endDateBegin != null">
            AND c.end_date >= #{endDateBegin}
        </if>
        <if test="endDateEnd != null">
            AND c.end_date <![CDATA[<=]]> #{endDateEnd}
        </if>
        AND c.is_disabled = 0
        </where>
    </select>

    <!-- 租赁合同列表 -->
    <select id="listLeaseContract" parameterType="com.senox.realty.vo.ContractSearchVo"
            resultType="com.senox.realty.vo.LeaseContractListVo">
        SELECT c.id, c.contract_no, c.realty_id, r.serial_no AS realty_serial, c.realty_name, c.category, c.customer_id, cu.serial_no AS customer_serial
            , c.customer_name, c.sign_date, c.start_date, c.end_date, c.status, f.amount AS rent_amount, rp.contract_no AS rent_proxy_contract_no
            , cu.telephone as customer_contact, ifnull((select min(rd.status) from r_realty_deposit rd where rd.contract_id = c.id), 99) as deposit_status
            , r.area, mf.amount as manage_amount, ifnull((select sum(amount) FROM r_realty_deposit rd WHERE rd.contract_id = c.id), 0) as deposit_amount, oc.name as owner_name
            , CONCAT_WS(',', NULLIF(oc.telephone, ''), NULLIF(oc.telephone2, ''), NULLIF(oc.telephone3, '')) AS owner_contact, au.real_name as creator_real_name
        FROM r_contract c
            INNER JOIN r_realty r ON r.id = c.realty_id
            INNER JOIN r_business_region br on br.id = r.region_id
            LEFT JOIN r_street s on r.street_id = s.id
            LEFT JOIN u_customer cu ON cu.id = c.customer_id
            LEFT JOIN r_contract_fee f ON f.contract_id = c.id AND f.fee_id = 2 AND f.category = 0
            LEFT JOIN r_contract_fee mf ON mf.contract_id = c.id AND mf.fee_id = 1 AND mf.category = 0
            LEFT JOIN r_contract rp on rp.realty_id = c.realty_id AND rp.type = 4 AND rp.start_date <![CDATA[<=]]> c.end_date AND rp.end_date >= c.end_date and rp.status = c.status
            LEFT JOIN u_customer oc ON oc.id = rp.customer_id
            LEFT JOIN u_admin_user au on au.id = c.creator_id
        <where>
        AND c.type = 1
        <if test="contractNo != null and contractNo != ''">
            AND c.contract_no = #{contractNo}
        </if>
        <if test="realtyId != null">
            AND c.realty_id = #{realtyId}
        </if>
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
        </if>
        <if test="realtyName != null and realtyName != ''">
            AND r.name LIKE CONCAT('%', #{realtyName}, '%')
        </if>
        <if test="customerSerial != null and customerSerial != ''">
            AND cu.serial_no = #{customerSerial}
        </if>
        <if test="customerName != null and customerName != ''">
            AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="status != null">
            AND c.status = #{status}
        </if>
        <if test="signDateBegin != null">
            AND c.sign_date >= #{signDateBegin}
        </if>
        <if test="signDateEnd != null">
            AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
        </if>
        <if test="startDateBegin != null">
            AND c.start_date >= #{startDateBegin}
        </if>
        <if test="startDateEnd != null">
            AND c.start_date <![CDATA[<=]]> #{startDateEnd}
        </if>
        <if test="endDateBegin != null">
            AND c.end_date >= #{endDateBegin}
        </if>
        <if test="endDateEnd != null">
            AND c.end_date <![CDATA[<=]]> #{endDateEnd}
        </if>
        AND c.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY c.status desc, c.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>


    <!-- 租赁合同列表 -->
    <select id="listLeaseContractRevision" parameterType="com.senox.realty.vo.ContractSearchVo"
            resultType="com.senox.realty.vo.LeaseContractListVo">
        select
            c.id,
            c.contract_no,
            c.realty_id,
            r.serial_no AS realty_serial,
            c.realty_name,
            c.category,
            c.customer_id,
            cu.serial_no AS customer_serial,
            c.customer_name,
            c.sign_date,
            c.start_date,
            c.end_date,
            c.status,
            cu.telephone as customer_contact,
            rcj.rent_amount AS rent_amount,
            rcj.rent_proxy_contract_no AS rent_proxy_contract_no,
            rcj.deposit_status as deposit_status,
            r.area,
            rcj.manage_amount,
            rcj.deposit_amount as deposit_amount,
            rcj.owner_name as owner_name,
            rcj.owner_contact AS owner_contact,
            au.real_name as creator_real_name
        FROM r_contract c
            INNER JOIN r_realty r ON r.id = c.realty_id
            INNER JOIN r_business_region br on br.id = r.region_id
            LEFT JOIN r_street s on r.street_id = s.id
            LEFT JOIN u_customer cu ON cu.id = c.customer_id
            LEFT JOIN r_contract_rents rcj ON rcj.contract_no = c.contract_no
            LEFT JOIN u_admin_user au on au.id = c.creator_id
        <where>
            AND c.type = 1
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no = #{contractNo}
            </if>
            <if test="realtyId != null">
                AND c.realty_id = #{realtyId}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="realtyName != null and realtyName != ''">
                AND r.name LIKE CONCAT('%', #{realtyName}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND cu.serial_no = #{customerSerial}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="signDateBegin != null">
                AND c.sign_date >= #{signDateBegin}
            </if>
            <if test="signDateEnd != null">
                AND c.sign_date <![CDATA[<=]]> #{signDateEnd}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            AND c.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY c.status desc, c.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
