<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.OneTimeFeeBillMapper">

    <resultMap id="Result_OneTimeFeeBillVo" type="com.senox.realty.vo.OneTimeFeeBillVo">
        <id property="id" column="id" jdbcType="BIGINT" />
        <result property="billNo" column="bill_no" jdbcType="VARCHAR" />
        <result property="billYear" column="bill_year" jdbcType="INTEGER" />
        <result property="billMonth" column="bill_month" jdbcType="INTEGER" />
        <result property="feeId" column="fee_id" jdbcType="BIGINT" />
        <result property="feeName" column="fee_name" jdbcType="VARCHAR" />
        <result property="amount" column="amount" jdbcType="DECIMAL" />
        <result property="departmentId" column="department_id" jdbcType="BIGINT" />
        <result property="departmentName" column="department_name" jdbcType="VARCHAR" />
        <result property="customerSerial" column="customer_serial" jdbcType="VARCHAR" />
        <result property="customer" column="customer" jdbcType="VARCHAR" />
        <result property="realtyId" column="realty_id" jdbcType="BIGINT" />
        <result property="realtySerial" column="realty_serial" jdbcType="VARCHAR" />
        <result property="realtyName" column="realty_name" jdbcType="VARCHAR" />
        <result property="remark" column="remark" jdbcType="VARCHAR" />
        <result property="status" column="status" jdbcType="INTEGER" />
        <result property="remoteOrderId" column="remote_order_id" jdbcType="BIGINT" />
        <result property="refundOrderId" column="refund_order_id" jdbcType="BIGINT" />
        <result property="operateBy" column="operate_by" jdbcType="BIGINT" />
        <result property="operator" column="operator" jdbcType="VARCHAR" />
        <result property="operateDate" column="operate_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="Result_OneTimeFeeBillSummary" type="com.senox.realty.vo.OneTimeFeeBillSummaryVo">
        <result property="amount" jdbcType="DECIMAL" column="amount" />
        <result property="refundAmount" jdbcType="DECIMAL" column="refund_amount" />
        <result property="totalAmount" jdbcType="DECIMAL" column="total_amount" />
    </resultMap>

    <!-- 添加一次性收费账单 -->
    <insert id="addOneTimeFeeBill" parameterType="com.senox.realty.domain.OneTimeFeeBill" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_one_time_fee_bill(
            bill_no, bill_year, bill_month, fee_id, fee_name, amount, department_id, department_name, customer_serial, customer
            , realty_id, remark, operate_by, operate_date, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
            , openid, wechat_creator_openid
        ) VALUES (
            #{billNo}, #{billYear}, #{billMonth}, #{feeId}, #{feeName}, #{amount}, #{departmentId}, #{departmentName}, #{customerSerial}, #{customer}
            , #{realtyId}, #{remark}, #{operateBy}, #{operateDate}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
            , #{openid}, #{wechatCreatorOpenid}
        )
    </insert>

    <!-- 更新一次性收费账单 -->
    <update id="updateOneTimeFeeBill" parameterType="com.senox.realty.domain.OneTimeFeeBill">
        UPDATE r_one_time_fee_bill
        <set>
            <if test="feeId != null">
                , fee_id = #{feeId}
            </if>
            <if test="feeName != null and feeName != ''">
                , fee_name = #{feeName}
            </if>
            <if test="amount != null">
                , amount = #{amount}
            </if>
            <if test="departmentId != null">
                , department_id = #{departmentId}
            </if>
            <if test="departmentName != null and departmentName != ''">
                , department_name = #{departmentName}
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                , customer_serial = #{customerSerial}
            </if>
            <if test="customer != null and customer != ''">
                , customer = #{customer}
            </if>
            <if test="realtyId != null">
                , realty_id = #{realtyId}
            </if>
            <if test="remark != null and remark != ''">
                , remark = #{remark}
            </if>
            <if test="operateBy != null">
                , operate_by = #{operateBy}
            </if>
            <if test="operateDate != null">
                , operate_date = #{operateDate}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null and modifierName != ''">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新一次性收费账单票据号 -->
    <update id="updateOneTimeFeeBillSerial" parameterType="com.senox.realty.vo.TollSerialVo">
        UPDATE r_one_time_fee_bill
        <set>
            <if test="serial != null">
                <choose>
                    <when test="refund">, refund_serial = #{serial}</when>
                    <otherwise>, toll_serial = #{serial}</otherwise>
                </choose>
            </if>
            <if test="operatorId != null">
                , modifier_id = #{operatorId}
            </if>
            <if test="operatorName != null and operatorName != ''">
                , modifier_name = #{operatorName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 支付一次性收费账单 -->
    <update id="updateOneTimeFeeBillPaidStatus" parameterType="com.senox.realty.vo.BillTollVo">
        UPDATE r_one_time_fee_bill
        <set>
            , toll_serial = #{serial}
            , toll_by = #{operator}
            <choose>
                <when test="status == 0">
                    , toll_time = NULL
                </when>
                <otherwise>, toll_time = NOW()</otherwise>
            </choose>
            , status = #{status}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="revokeOneTimeFeeBillPaid" parameterType="com.senox.realty.vo.BillTollVo">
        UPDATE r_one_time_fee_bill
        <set>
            , status = 0
            , toll_by = 0
            , toll_time = NULL
            , modifier_id = #{operator}
            , modifier_name = #{operatorName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id} AND status = 1
    </update>

    <!-- 一次性收费退费 -->
    <update id="refundOneTimeFeeBill" parameterType="com.senox.realty.vo.BillTollVo">
        UPDATE r_one_time_fee_bill
        <set>
            , status = #{status}
            , refund_amount = #{amount}
            , refund_serial = #{serial}
            , refund_by = #{operator}
            <choose>
                <when test="status == 10">
                    , refund_time = NOW()
                </when>
                <otherwise>, refund_time = NULL</otherwise>
            </choose>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 撤销一次性收费退费 -->
    <update id="revokeOneTimeFeeRefund" parameterType="com.senox.realty.vo.BillTollVo">
        UPDATE r_one_time_fee_bill
        <set>
            , status = 1
            , refund_amount = 0
            , refund_by = 0
            , refund_time = NULL
            , modifier_id = #{operator}
            , modifier_name = #{operatorName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id} AND status = 10
    </update>

    <!-- 获取最大的账单号 -->
    <select id="findMaxBillNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT bill_no FROM r_one_time_fee_bill WHERE bill_no LIKE CONCAT(#{prefix}, '%') ORDER BY bill_no DESC LIMIT 1
    </select>

    <!-- 根据id查找一次性收费账单 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_OneTimeFeeBillVo">
        SELECT b.id, b.bill_no, b.bill_year, b.bill_month, b.fee_id, b.fee_name, b.amount, b.department_id, b.department_name
            , b.customer_serial, b.customer, b.realty_id, r.serial_no AS realty_serial, r.name AS realty_name, b.remark, b.status
            , b.operate_by, b.operate_date, o.real_name AS operator, b.remote_order_id, b.refund_order_id
        FROM r_one_time_fee_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
            LEFT JOIN u_admin_user o ON o.id = b.operate_by
        WHERE b.id = #{id}
    </select>
    
    <select id="listByIds" parameterType="java.lang.Long" resultMap="Result_OneTimeFeeBillVo">
        SELECT b.id, b.bill_no, b.bill_year, b.bill_month, b.fee_id, b.fee_name, b.amount, b.department_id, b.department_name
            , b.customer_serial, b.customer, b.realty_id, r.serial_no AS realty_serial, r.name AS realty_name, b.remark, b.status
            , b.operate_by, b.operate_date, o.real_name AS operator
        FROM r_one_time_fee_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
            LEFT JOIN u_admin_user o ON o.id = b.operate_by
        WHERE b.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <!-- 根据票据号查找一次性收费账单 -->
    <select id="findByBillNo" parameterType="java.lang.String" resultMap="Result_OneTimeFeeBillVo">
        SELECT b.id, b.bill_no, b.bill_year, b.bill_month, b.fee_id, b.fee_name, b.amount, b.department_id, b.department_name
            , b.customer_serial, b.customer, b.realty_id, r.serial_no AS realty_serial, r.name AS realty_name, b.remark, b.status
            , b.operate_by, b.operate_date, o.real_name AS operator
        FROM r_one_time_fee_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
            LEFT JOIN u_admin_user o ON o.id = b.operate_by
        WHERE b.bill_no = #{billNo}
    </select>

    <!-- 根据id查找一次性收费账单明细 -->
    <select id="findWithDetailById" parameterType="java.lang.Long" resultType="com.senox.realty.vo.OneTimeFeeBillTradeVo">
        SELECT b.id, b.bill_no, b.bill_year, b.bill_month, b.fee_name AS fee, b.amount, b.department_name AS department, b.customer_serial, b.customer
            , r.serial_no AS realty_serial, r.name AS realty_name, b.remark, b.status, o.real_name AS operator, b.operate_date
            , c.real_name AS creator, b.toll_serial, t.real_name AS toll_man, b.toll_time, b.refund_amount
            , rf.real_name AS refund_man, b.refund_time
        FROM r_one_time_fee_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
            LEFT JOIN u_admin_user o ON o.id = b.operate_by
            LEFT JOIN u_admin_user c ON c.id = b.creator_id
            LEFT JOIN u_admin_user t ON t.id = b.toll_by
            LEFT JOIN u_admin_user rf ON rf.id = b.refund_by
        WHERE b.id = #{id}
    </select>

    <select id="sumOneTimeFeeBill" parameterType="com.senox.realty.vo.OneTimeFeeBillSearchVo" resultMap="Result_OneTimeFeeBillSummary">
        SELECT SUM(b.amount) AS amount, SUM(b.refund_amount) AS refund_amount, SUM(b.amount + b.refund_amount) AS total_amount
        FROM r_one_time_fee_bill b
        <if test="realtySerial != null and realtySerial != ''">
            INNER JOIN r_realty r ON r.id = b.realty_id
        </if>
        <where>
            <if test="billNo != null and billNo != ''">
                AND b.bill_no = #{billNo}
            </if>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billSerial != null and billSerial != ''">
                AND (b.toll_serial = #{billSerial} OR b.refund_serial = #{billSerial})
            </if>
            <if test="feeId != null">
                AND b.fee_id = #{feeId}
            </if>
            <if test="fees != null and fees.size() > 0">
                AND b.fee_id IN <foreach collection="fees" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="departmentId != null">
                AND b.department_id = #{departmentId}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND b.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="operateDateStart != null">
                AND b.operate_date >= #{operateDateStart}
            </if>
            <if test="operateDateEnd != null">
                AND b.operate_date <![CDATA[<=]]> #{operateDateEnd}
            </if>
            <if test="tollTimeStart != null">
                AND b.toll_time >= #{tollTimeStart}
            </if>
            <if test="tollTimeEnd != null">
                AND b.toll_time <![CDATA[<=]]> #{tollTimeEnd}
            </if>
            <if test="refundTimeStart != null">
                AND b.refund_time >= #{refundTimeStart}
            </if>
            <if test="refundTimeEnd != null">
                AND b.refund_time <![CDATA[<=]]> #{refundTimeEnd}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND b.customer_serial LIKE CONCAT('%', #{customerSerial}, '%')
            </if>
            <if test="customer != null and customer != ''">
                AND b.customer LIKE CONCAT('%', #{customer}, '%')
            </if>
            AND b.is_disabled = 0
        </where>
    </select>

    <!-- 统计一次性收费账单数 -->
    <select id="countOneTimeFeeBill" parameterType="com.senox.realty.vo.OneTimeFeeBillSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(b.id)
        FROM r_one_time_fee_bill b
        <if test="realtySerial != null and realtySerial != ''">
            INNER JOIN r_realty r ON r.id = b.realty_id
        </if>
        <where>
            <if test="billNo != null and billNo != ''">
                AND b.bill_no = #{billNo}
            </if>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billSerial != null and billSerial != ''">
                AND (b.toll_serial = #{billSerial} OR b.refund_serial = #{billSerial})
            </if>
            <if test="feeId != null">
                AND b.fee_id = #{feeId}
            </if>
            <if test="fees != null and fees.size() > 0">
                AND b.fee_id IN <foreach collection="fees" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="departmentId != null">
                AND b.department_id = #{departmentId}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND b.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="operateDateStart != null">
                AND b.operate_date >= #{operateDateStart}
            </if>
            <if test="operateDateEnd != null">
                AND b.operate_date <![CDATA[<=]]> #{operateDateEnd}
            </if>
            <if test="tollTimeStart != null">
                AND b.toll_time >= #{tollTimeStart}
            </if>
            <if test="tollTimeEnd != null">
                AND b.toll_time <![CDATA[<=]]> #{tollTimeEnd}
            </if>
            <if test="refundTimeStart != null">
                AND b.refund_time >= #{refundTimeStart}
            </if>
            <if test="refundTimeEnd != null">
                AND b.refund_time <![CDATA[<=]]> #{refundTimeEnd}
            </if>
            <if test="null != openid">
                and b.openid = #{openid}
            </if>
            <if test="null != wechatCreatorOpenid">
                and b.wechat_creator_openid = #{wechatCreatorOpenid}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND b.customer_serial LIKE CONCAT('%', #{customerSerial}, '%')
            </if>
            <if test="customer != null and customer != ''">
                AND b.customer LIKE CONCAT('%', #{customer}, '%')
            </if>
            AND b.is_disabled = 0
        </where>
    </select>

    <!-- 一次性收费账单列表 -->
    <select id="listOneTimeFeeBill" parameterType="com.senox.realty.vo.OneTimeFeeBillSearchVo" resultType="com.senox.realty.vo.OneTimeFeeBillTradeVo">
        SELECT b.id, b.bill_no, b.bill_year, b.bill_month, b.fee_name AS fee, b.amount, b.department_name AS department, b.customer_serial, b.customer
            , r.serial_no AS realty_serial, r.name AS realty_name, b.remark, b.status, o.real_name AS operator, b.operate_date
            , c.real_name AS creator, b.toll_serial, t.real_name AS toll_man, b.toll_time, b.refund_serial, b.refund_amount
             , rf.real_name AS refund_man, b.refund_time, b.openid as openid
        FROM r_one_time_fee_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
            LEFT JOIN u_admin_user o ON o.id = b.operate_by
            LEFT JOIN u_admin_user c ON c.id = b.creator_id
            LEFT JOIN u_admin_user t ON t.id = b.toll_by
            LEFT JOIN u_admin_user rf ON rf.id = b.refund_by
        <where>
            <if test="billNo != null and billNo != ''">
                AND b.bill_no = #{billNo}
            </if>
            <if test="billYear != null">
                AND b.bill_year = #{billYear}
            </if>
            <if test="billMonth != null">
                AND b.bill_month = #{billMonth}
            </if>
            <if test="billSerial != null and billSerial != ''">
                AND (b.toll_serial = #{billSerial} OR b.refund_serial = #{billSerial})
            </if>
            <if test="feeId != null">
                AND b.fee_id = #{feeId}
            </if>
            <if test="fees != null and fees.size() > 0">
                AND b.fee_id IN <foreach collection="fees" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="departmentId != null">
                AND b.department_id = #{departmentId}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND b.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="operateDateStart != null">
                AND b.operate_date >= #{operateDateStart}
            </if>
            <if test="operateDateEnd != null">
                AND b.operate_date <![CDATA[<=]]> #{operateDateEnd}
            </if>
            <if test="tollTimeStart != null">
                AND b.toll_time >= #{tollTimeStart}
            </if>
            <if test="tollTimeEnd != null">
                AND b.toll_time <![CDATA[<=]]> #{tollTimeEnd}
            </if>
            <if test="refundTimeStart != null">
                AND b.refund_time >= #{refundTimeStart}
            </if>
            <if test="refundTimeEnd != null">
                AND b.refund_time <![CDATA[<=]]> #{refundTimeEnd}
            </if>
            <if test="null != openid">
                and b.openid = #{openid}
            </if>
            <if test="null != wechatCreatorOpenid">
                and b.wechat_creator_openid = #{wechatCreatorOpenid}
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND b.customer_serial LIKE CONCAT('%', #{customerSerial}, '%')
            </if>
            <if test="customer != null and customer != ''">
                AND b.customer LIKE CONCAT('%', #{customer}, '%')
            </if>
            AND b.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY b.status, b.operate_date DESC, b.id DESC
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <!-- 一次性收费账单交易合计 -->
    <select id="sumOneTimeFeeBillTrade" parameterType="com.senox.realty.vo.OneTimeFeeBillTradeSearchVo" resultMap="Result_OneTimeFeeBillSummary">
        SELECT SUM(b.amount) AS amount, SUM(b.refund_amount) AS refund_amount, SUM(b.amount + b.refund_amount) AS total_amount
        FROM r_one_time_fee_bill b
        <if test="payWay !=null or payWays != null">
            LEFT JOIN p_order po on po.id = b.remote_order_id AND po.status = 10
        </if>
        <where>
            <if test="fee != null">
                AND b.fee_id = #{fee}
            </if>
            <if test="department != null">
                AND b.department_id = #{department}
            </if>
            <if test="operator != null">
                AND b.operate_by = #{operator}
            </if>
            <if test="creator != null">
                AND b.creator_id = #{creator}
            </if>
            <if test="customer != null and customer != ''">
                AND b.customer LIKE CONCAT('%', #{customer}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND b.customer_serial LIKE CONCAT('%', #{customerSerial}, '%')
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND b.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <choose>
                <when test="payWay == 1">
                    AND IFNULL(po.pay_way, 1) = 1
                </when>
                <when test="payWay != null">
                    AND po.pay_way = #{payWay}
                </when>
            </choose>
            <if test="payWays != null">
                <choose>
                    <when test="payWays.contains(1)">
                        AND (po.pay_way IN <foreach collection="payWays" item="item" open="(" close=")" separator=",">#{item}</foreach> OR po.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND po.pay_way IN <foreach collection="payWays" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </otherwise>
                </choose>
            </if>
            <choose>
                <when test="tollMan != null and tradeTimeStart != null and tradeTimeEnd != null">
                    AND ((b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.toll_by = #{tollMan})
                    OR (b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.refund_by = #{tollMan}))
                </when>
                <when test="tollMan != null and tradeTimeStart != null">
                    AND b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.toll_by = #{tollMan}
                </when>
                <when test="tollMan != null and tradeTimeEnd != null">
                    AND b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.refund_by = #{tollMan}
                </when>
                <when test="tollMan != null">
                    AND (b.toll_by = #{tollMan} OR b.refund_by = #{tollMan})
                </when>
                <when test="tradeTimeStart != null and tradeTimeEnd != null">
                    AND ((b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd})
                    OR (b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd}))
                </when>
                <when test="tradeTimeStart != null">
                    AND (b.toll_time >= #{tradeTimeStart} OR b.refund_time >= #{tradeTimeStart})
                </when>
                <when test="tradeTimeEnd != null">
                    AND (b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} OR b.refund_time <![CDATA[<=]]> #{tradeTimeEnd})
                </when>
            </choose>
            AND b.is_disabled = 0
        </where>
    </select>

    <!-- 统计一次性收费账单交易明细记录数 -->
    <select id="countOneTimeFeeBillTrade" parameterType="com.senox.realty.vo.OneTimeFeeBillTradeSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(b.id)
        FROM r_one_time_fee_bill b
        <if test="payWay !=null or payWays != null">
            LEFT JOIN p_order po on po.id = b.remote_order_id AND po.status = 10
        </if>
        <where>
            <if test="fee != null">
                AND b.fee_id = #{fee}
            </if>
            <if test="department != null">
                AND b.department_id = #{department}
            </if>
            <if test="operator != null">
                AND b.operate_by = #{operator}
            </if>
            <if test="creator != null">
                AND b.creator_id = #{creator}
            </if>
            <if test="customer != null and customer != ''">
                AND b.customer LIKE CONCAT('%', #{customer}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND b.customer_serial LIKE CONCAT('%', #{customerSerial}, '%')
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND b.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <choose>
                <when test="payWay == 1">
                    AND IFNULL(po.pay_way, 1) = 1
                </when>
                <when test="payWay != null">
                    AND po.pay_way = #{payWay}
                </when>
            </choose>
            <if test="payWays != null">
                <choose>
                    <when test="payWays.contains(1)">
                        AND (po.pay_way IN <foreach collection="payWays" item="item" open="(" close=")" separator=",">#{item}</foreach> OR po.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND po.pay_way IN <foreach collection="payWays" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </otherwise>
                </choose>
            </if>
            <choose>
                <when test="tollMan != null and tradeTimeStart != null and tradeTimeEnd != null">
                    AND ((b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.toll_by = #{tollMan})
                    OR (b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.refund_by = #{tollMan}))
                </when>
                <when test="tollMan != null and tradeTimeStart != null">
                    AND b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.toll_by = #{tollMan}
                </when>
                <when test="tollMan != null and tradeTimeEnd != null">
                    AND b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.refund_by = #{tollMan}
                </when>
                <when test="tollMan != null">
                    AND (b.toll_by = #{tollMan} OR b.refund_by = #{tollMan})
                </when>
                <when test="tradeTimeStart != null and tradeTimeEnd != null">
                    AND ((b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd})
                    OR (b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd}))
                </when>
                <when test="tradeTimeStart != null">
                    AND (b.toll_time >= #{tradeTimeStart} OR b.refund_time >= #{tradeTimeStart})
                </when>
                <when test="tradeTimeEnd != null">
                    AND (b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} OR b.refund_time <![CDATA[<=]]> #{tradeTimeEnd})
                </when>
            </choose>
            AND b.is_disabled = 0
        </where>
    </select>

    <!-- 一次性收费账单交易明细 -->
    <select id="listOneTimeFeeBillTrade" parameterType="com.senox.realty.vo.OneTimeFeeBillTradeSearchVo" resultType="com.senox.realty.vo.OneTimeFeeBillTradeVo">
        SELECT b.id, b.bill_no, b.bill_year, b.bill_month, b.fee_name AS fee, b.amount, b.department_name AS department, b.customer_serial, b.customer
            , r.serial_no AS realty_serial, r.name AS realty_name, b.remark, b.status, o.real_name AS operator, b.operate_date
            , c.real_name AS creator, b.toll_serial, t.real_name AS toll_man, b.toll_time, b.refund_serial, b.refund_amount
            , rf.real_name AS refund_man, b.refund_time, (b.amount + b.refund_amount) AS total_amount, IFNULL(po.pay_way, 1) as pay_way
        FROM r_one_time_fee_bill b
            LEFT JOIN r_realty r ON r.id = b.realty_id
            LEFT JOIN p_order po ON po.id = b.remote_order_id AND po.status = 10
            LEFT JOIN u_admin_user o ON o.id = b.operate_by
            LEFT JOIN u_admin_user c ON c.id = b.creator_id
            LEFT JOIN u_admin_user t ON t.id = b.toll_by
            LEFT JOIN u_admin_user rf ON rf.id = b.refund_by
        <where>
            <if test="fee != null">
                AND b.fee_id = #{fee}
            </if>
            <if test="department != null">
                AND b.department_id = #{department}
            </if>
            <if test="operator != null">
                AND b.operate_by = #{operator}
            </if>
            <if test="creator != null">
                AND b.creator_id = #{creator}
            </if>
            <if test="customer != null and customer != ''">
                AND b.customer LIKE CONCAT('%', #{customer}, '%')
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND b.customer_serial LIKE CONCAT('%', #{customerSerial}, '%')
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND b.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <choose>
                <when test="payWay == 1">
                    AND IFNULL(po.pay_way, 1) = 1
                </when>
                <when test="payWay != null">
                    AND po.pay_way = #{payWay}
                </when>
            </choose>
            <if test="payWays != null">
                <choose>
                    <when test="payWays.contains(1)">
                        AND (po.pay_way IN <foreach collection="payWays" item="item" open="(" close=")" separator=",">#{item}</foreach> OR po.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND po.pay_way IN <foreach collection="payWays" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </otherwise>
                </choose>
            </if>
            <choose>
                <when test="tollMan != null and tradeTimeStart != null and tradeTimeEnd != null">
                    AND ((b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.toll_by = #{tollMan})
                    OR (b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.refund_by = #{tollMan}))
                </when>
                <when test="tollMan != null and tradeTimeStart != null">
                    AND b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.toll_by = #{tollMan}
                </when>
                <when test="tollMan != null and tradeTimeEnd != null">
                    AND b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd} AND b.refund_by = #{tollMan}
                </when>
                <when test="tollMan != null">
                    AND (b.toll_by = #{tollMan} OR b.refund_by = #{tollMan})
                </when>
                <when test="tradeTimeStart != null and tradeTimeEnd != null">
                    AND ((b.toll_time >= #{tradeTimeStart} AND b.toll_time <![CDATA[<=]]> #{tradeTimeEnd})
                    OR (b.refund_time >= #{tradeTimeStart} AND b.refund_time <![CDATA[<=]]> #{tradeTimeEnd}))
                </when>
                <when test="tradeTimeStart != null">
                    AND (b.toll_time >= #{tradeTimeStart} OR b.refund_time >= #{tradeTimeStart})
                </when>
                <when test="tradeTimeEnd != null">
                    AND (b.toll_time <![CDATA[<=]]> #{tradeTimeEnd} OR b.refund_time <![CDATA[<=]]> #{tradeTimeEnd})
                </when>
            </choose>
            AND b.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY b.status, b.operate_date DESC, b.id DESC
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <update id="updateBillPaidById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_one_time_fee_bill bill
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = bill.id
        <set>
            <if test="null!=orderId">
                , bill.remote_order_id = #{orderId}
            </if>
            <choose>
                <when test="paid">
                    , bill.status = 1
                    , bill.toll_time = #{paidTime}
                </when>
                <otherwise>,bill.status = 0</otherwise>
            </choose>
            <if test="tollMan != null">
                , bill.toll_by = #{tollMan}
            </if>
            ,bill.modified_time = now()
        </set>
        <where>
            and bill.id in
            <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            and bill.status = 0 and bill.amount > 0
        </where>
    </update>

    <update id = "updateBillPaidByRemoteOrder" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_one_time_fee_bill bill
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = bill.id
        <set>
            <choose>
                <when test="paid">
                    , bill.status = 1
                    , bill.toll_time = #{paidTime}
                </when>
                <otherwise>,bill.status = 0</otherwise>
            </choose>
            <if test="tollMan != null">
                , bill.toll_by = #{tollMan}
            </if>
            , bill.modified_time = now()
        </set>
        <where>
            AND bill.remote_order_id = #{orderId}
            AND bill.status = 0
            AND bill.amount > 0
        </where>
    </update>

    <update id="updateBillRefundById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_one_time_fee_bill b
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = b.id
        <set>
            <if test="orderId != null">
                , b.refund_order_id = #{orderId}
            </if>
            <if test="paid">
                , b.status = 10
                , b.refund_amount = b.refund_amount + oi.total_amount
                , b.refund_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , b.refund_by = #{tollMan}
            </if>
            , b.modified_time = NOW()
        </set>
        WHERE b.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND b.status = 1 AND b.is_disabled = 0
    </update>

    <!-- 通过支付订单id一次性收费退费 -->
    <update id="updateBillRefundByRemoteOrder" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_one_time_fee_bill b
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = b.id
        <set>
            <if test="paid">
                , b.status = 10
                , b.refund_amount = b.refund_amount + oi.total_amount
                , b.refund_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , b.refund_by = #{tollMan}
            </if>
            , b.modified_time = NOW()
        </set>
        WHERE b.refund_order_id = #{orderId} AND b.status = 1 AND b.is_disabled = 0
    </update>

    <!-- 冷藏押金 -->
    <select id="listOneTimeFeeDeposit" parameterType="com.senox.realty.vo.OneTimeFeeDepositSearchVo"
            resultType="com.senox.realty.vo.OneTimeFeeDepositVo">
        SELECT customer_serial, SUM(amount - refund_amount) as deposit_amount
        FROM r_one_time_fee_bill
        <where>
            AND fee_id = #{depositFee}
            AND status = 1
            <if test="customers != null and customers.size() > 0">
                AND customer_serial IN <foreach collection="customers" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
        GROUP BY customer_serial
    </select>
</mapper>
