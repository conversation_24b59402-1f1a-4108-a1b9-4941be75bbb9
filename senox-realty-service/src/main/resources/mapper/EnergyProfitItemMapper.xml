<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.EnergyProfitItemMapper">

    <!-- 批量更新能源损益明细 -->
    <update id="batchUpdateProfitItem">
        UPDATE r_energy_profit_item
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="cost = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.cost != null">
                        WHEN unit_key = #{item.unitKey} and unit_name = #{item.unitName} THEN #{item.cost}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierId != null">
                        WHEN id = #{item.id} THEN #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierName != null">
                        WHEN id = #{item.id} THEN #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        <where>
            AND profitId = #{profitId}
            AND (unit_key, unit_name) IN <foreach collection="list" item="item" open="(" close=")" separator=",">(#{item.unitKey}, #{item.unitName})</foreach>
        </where>
    </update>
</mapper>