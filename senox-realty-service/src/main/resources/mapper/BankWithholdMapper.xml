<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.BankWithholdMapper">

    <resultMap id="Result_BankWithhold" type="com.senox.realty.domain.BankWithhold">
        <result property="billYear" column="bill_year" jdbcType="INTEGER" />
        <result property="billMonth" column="bill_month" jdbcType="INTEGER" />
        <result property="offer" column="is_offer" jdbcType="TINYINT" />
        <result property="offerTime" column="offer_time" jdbcType="TIMESTAMP" />
        <result property="back" column="is_back" jdbcType="TINYINT" />
        <result property="backTime" column="back_time" jdbcType="TIMESTAMP" />
    </resultMap>

    <!-- 添加物业账单银行托收记录 -->
    <insert id="addWithhold" parameterType="com.senox.realty.domain.BankWithhold">
        INSERT INTO r_bank_withhold(
            bill_year, bill_month, is_offer, offer_time, offer_user, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{billYear}, #{billMonth}, #{offer}, #{offerTime}, #{modifierId}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新物业账单银行托收报盘 -->
    <update id="updateWithholdOffer" parameterType="com.senox.realty.domain.BankWithhold">
        UPDATE r_bank_withhold
        <set>
            <if test="offer != null">
                , is_offer = #{offer}
            </if>
            <if test="offerTime != null">
                , offer_time = #{offerTime}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
                , offer_user = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE bill_year = #{billYear} AND bill_month = #{billMonth}
            AND is_back = 0 AND is_disabled = 0
    </update>

    <!-- 更新物业账单银行托收回盘 -->
    <update id="updateWithholdBack" parameterType="com.senox.realty.domain.BankWithhold">
        UPDATE r_bank_withhold
        <set>
            <if test="back != null">
                , is_back = #{back}
            </if>
            <if test="backTime != null">
                , back_time = #{backTime}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
                , back_user = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE bill_year = #{billYear} AND bill_month = #{billMonth}
            AND is_offer = 1 AND is_disabled = 0
    </update>

    <!-- 根据账单年月查找报盘信息 -->
    <select id="findByYearMonth" resultMap="Result_BankWithhold">
        SELECT bill_year, bill_month, is_offer, offer_time, is_back, back_time
        FROM r_bank_withhold
        WHERE bill_year = #{year} AND bill_month = #{month} AND is_disabled = 0
    </select>
</mapper>