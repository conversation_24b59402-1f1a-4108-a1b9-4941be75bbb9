<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.AdvertisingBillMapper">

    <!-- 添加广告应收账单 -->
    <insert id="addBill" parameterType="com.senox.realty.domain.AdvertisingBill" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_advertising_bill(
            bill_year, bill_month, space_id, contract_no, amount, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{billYear}, #{billMonth}, #{spaceId}, #{contractNo}, #{amount}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 根据账单id更新订单支付结果 -->
    <update id="updateBillPaidById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_advertising_bill b
            INNER JOIN r_advertising_contract c ON c.contract_no = b.contract_no
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id  = b.id
        <set>
            <if test="orderId != null">
                , b.remote_order_id = #{orderId}
            </if>
            <if test="paid">
                , b.status = 1
                , b.paid_amount = b.amount
                , b.paid_time = #{paidTime}
                , c.is_paid = 1
            </if>
            <if test="tollMan != null">
                , b.toll_man_id = #{tollMan}
            </if>
            , b.modified_time = NOW()
            , c.modified_time = NOW()
        </set>
        <where>
            AND b.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND b.status = 0
            AND b.is_disabled = 0
        </where>
    </update>

    <!-- 根据支付订单id更新账单支付结果 -->
    <update id="updateBillPaidByRemoteOrder" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_advertising_bill b
            INNER JOIN r_advertising_contract c ON c.contract_no = b.contract_no
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = b.id
        <set>
            <if test="orderId != null">
                , b.remote_order_id = #{orderId}
            </if>
            <if test="paid">
                , b.status = 1
                , b.paid_amount = b.amount
                , b.paid_time = #{paidTime}
                , c.is_paid = 1
            </if>
            <if test="tollMan != null">
                , b.toll_man_id = #{tollMan}
            </if>
            , b.modified_time = NOW()
            , c.modified_time = NOW()
        </set>
        <where>
            AND b.remote_order_id = #{orderId}
            AND b.status = 0
            AND b.is_disabled = 0
        </where>
    </update>

    <!-- 更新开票信息 -->
    <update id="updateBillReceipt" parameterType="com.senox.common.vo.BillReceiptBriefVo">
        UPDATE r_advertising_bill
        <set>
            is_receipt = #{receipt}
            <choose>
                <when test="receipt">
                    , receipt_title = #{taxHeader}
                    , receipt_time = NOW()
                </when>
                <otherwise>
                    , receipt_title = ''
                    , receipt_time = null
                </otherwise>
            </choose>
            , modifier_id = #{operatorId}
            , modifier_name = #{operatorName}
            , modified_time = NOW()
        </set>
        <where>
            AND id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND status = 1
            AND is_disabled = 0
        </where>
    </update>

    <!-- 根据id查找应收账单 -->
    <select id="listByIds" parameterType="java.util.List" resultType="com.senox.realty.vo.AdvertisingBillVo">
        SELECT b.id, c.id AS contract_id, b.contract_no, s.serial_no AS space_serial, s.name AS space_name, s.region AS space_region
            , s.street AS space_street, c.customer_name, c.customer_user, c.customer_contact, c.present_months
            , c.start_date, c.end_date, b.amount, b.paid_amount, b.toll_serial, IFNULL(u.real_name, u.username) AS toll_man
            , b.paid_time, b.remark, b.status
        FROM r_advertising_bill b
            INNER JOIN r_advertising_contract c ON c.contract_no = b.contract_no
            INNER JOIN r_advertising_space s ON s.id = b.space_id
            LEFT JOIN u_admin_user u ON u.id = b.toll_man_id
        WHERE b.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <!-- 广告应收账单合计 -->
    <select id="sumBill" parameterType="com.senox.realty.vo.AdvertisingBillSearchVo" resultType="com.senox.realty.domain.AdvertisingBill">
        SELECT SUM(b.amount) AS amount, SUM(b.paid_amount) AS paid_amount
        FROM r_advertising_bill b
            INNER JOIN r_advertising_contract c ON c.contract_no = b.contract_no
            INNER JOIN r_advertising_space s ON s.id = b.space_id
        <where>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="paidTimeBegin != null">
                AND b.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt != null">
                AND b.is_receipt = #{receipt}
            </if>
            <if test="receiptTimeBegin != null">
                AND b.receipt_time >= #{receiptTimeBegin}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="spaceName != null and spaceName != ''">
                AND s.name LIKE CONCAT('%', #{spaceName}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
        </where>
    </select>

    <!-- 广告应收账单统计 -->
    <select id="countBill" parameterType="com.senox.realty.vo.AdvertisingBillSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(b.id)
        FROM r_advertising_bill b
            INNER JOIN r_advertising_contract c ON c.contract_no = b.contract_no
            INNER JOIN r_advertising_space s ON s.id = b.space_id
        <where>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="paidTimeBegin != null">
                AND b.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt != null">
                AND b.is_receipt = #{receipt}
            </if>
            <if test="receiptTimeBegin != null">
                AND b.receipt_time >= #{receiptTimeBegin}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="spaceName != null and spaceName != ''">
                AND s.name LIKE CONCAT('%', #{spaceName}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
        </where>
    </select>

    <!-- 广告应收账单列表 -->
    <select id="listBill" parameterType="com.senox.realty.vo.AdvertisingBillSearchVo" resultType="com.senox.realty.vo.AdvertisingBillVo">
        SELECT b.id, c.id AS contract_id, b.contract_no, s.serial_no AS space_serial, s.name AS space_name, s.region AS space_region
            , s.street AS space_street, c.customer_name, c.customer_user, c.customer_contact, c.present_months
            , c.start_date, c.end_date, b.amount, b.paid_amount, b.toll_serial, b.paid_time, b.remark, b.status
        FROM r_advertising_bill b
            INNER JOIN r_advertising_contract c ON c.contract_no = b.contract_no
            INNER JOIN r_advertising_space s ON s.id = b.space_id
        <where>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="paidTimeBegin != null">
                AND b.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="receipt != null">
                AND b.is_receipt = #{receipt}
            </if>
            <if test="receiptTimeBegin != null">
                AND b.receipt_time >= #{receiptTimeBegin}
            </if>
            <if test="receiptTimeEnd != null">
                AND b.receipt_time <![CDATA[<=]]> #{receiptTimeEnd}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="spaceName != null and spaceName != ''">
                AND s.name LIKE CONCAT('%', #{spaceName}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND b.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY b.status, b.paid_time DESC, b.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="advertisingBillStatistics" parameterType="com.senox.realty.vo.StatisticsGenerateVo" resultType="com.senox.realty.domain.AdvertisingStatistics">
        SELECT
            sum( CASE WHEN rb.STATUS = 1 THEN rb.paid_amount ELSE 0 END ) rent_collect_amount,
            sum( CASE WHEN rb.STATUS = 1 THEN 1 ELSE 0 END ) rent_collect_num,
            sum( CASE WHEN rb.STATUS = 0 THEN rb.amount ELSE 0 END ) un_rent_collect_amount,
            sum( CASE WHEN rb.STATUS = 0 THEN 1 ELSE 0 END ) un_rent_collect_num
        FROM r_advertising_bill rb
        <where>
            <if test="year != null">
                and rb.bill_year = #{year}
            </if>
            <if test="month != null">
                and rb.bill_month = #{month}
            </if>
        </where>
    </select>
</mapper>