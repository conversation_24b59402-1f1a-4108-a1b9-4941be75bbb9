<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.PowerOutageNoticeMapper">
    <resultMap id="resultMap" type="com.senox.realty.vo.PowerOutageNoticeVo">
        <id property="id" column="id"/>
        <result property="regionId" column="region_id"/>
        <result property="regionName" column="region_name"/>
        <result property="streetId" column="street_id"/>
        <result property="streetName" column="street_name"/>
        <result property="stallAddress" column="stall_address"/>
        <result property="poserOutageReason" column="poser_outage_reason"/>
        <result property="preTime" column="pre_time"/>
        <result property="preInspectorSign" column="pre_inspector_sign"/>
        <result property="preOwnerSign" column="pre_owner_sign"/>
        <result property="postTime" column="post_time"/>
        <result property="postInspectorSign" column="post_inspector_sign"/>
        <result property="postOwnerSign" column="post_owner_sign"/>
        <result property="reviewStatus" column="review_status"/>
        <collection property="preMedias" ofType="com.senox.realty.vo.PowerOutageNoticeMediaVo">
            <result property="fileName" column="pre_media_file_name"/>
            <result property="fileUrl" column="pre_media_file_url"/>
        </collection>
        <collection property="postMedias" ofType="com.senox.realty.vo.PowerOutageNoticeMediaVo">
            <result property="fileName" column="post_media_file_name"/>
            <result property="fileUrl" column="post_media_file_url"/>
        </collection>
    </resultMap>

    <select id="findById" resultMap="resultMap">
        select pon.id,
               pon.region_id,
               pon.region_name,
               pon.street_id,
               pon.street_name,
               pon.stall_address,
               pon.poser_outage_reason,
               pon.pre_time,
               pon.pre_inspector_sign,
               pon.pre_owner_sign,
               pon.post_time,
               pon.post_inspector_sign,
               pon.post_owner_sign,
               pon.review_status,
               pre_media.file_name  as pre_media_file_name,
               pre_media.file_url   as pre_media_file_url,
               post_media.file_name as post_media_file_name,
               post_media.file_url  as post_media_file_url
        from r_power_outage_notice pon
                 left join r_power_outage_notice_media pre_media
                           on pre_media.power_outage_notice_id = pon.id and pre_media.type = 1
                 left join r_power_outage_notice_media post_media
                           on post_media.power_outage_notice_id = pon.id and post_media.type = 2
        <where>
            pon.id = #{id}
        </where>
    </select>

    <select id="countList" resultType="int">
        select count(*)
        from r_power_outage_notice pon
    </select>

    <select id="list" resultMap="resultMap">
        select pon.id,
               pon.region_id,
               pon.region_name,
               pon.street_id,
               pon.street_name,
               pon.stall_address,
               pon.poser_outage_reason,
               pon.pre_time,
               pon.pre_inspector_sign,
               pon.pre_owner_sign,
               pon.post_time,
               pon.post_inspector_sign,
               pon.post_owner_sign,
               pon.review_status,
               pre_media.file_name  as pre_media_file_name,
               pre_media.file_url   as pre_media_file_url,
               post_media.file_name as post_media_file_name,
               post_media.file_url  as post_media_file_url
        from r_power_outage_notice pon
                 left join r_power_outage_notice_media pre_media
                           on pre_media.power_outage_notice_id = pon.id and pre_media.type = 1
                 left join r_power_outage_notice_media post_media
                           on post_media.power_outage_notice_id = pon.id and post_media.type = 2
        <where>
            <if test="null != reviewStatus">
                pon.review_status = #{reviewStatus}
            </if>
        </where>
    </select>
</mapper>
