<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.SecurityJournalMapper">

    <!-- 安保日志合计 -->
    <select id="countJournal" parameterType="com.senox.realty.vo.SecurityJournalSearchVo"
            resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM d_security_journal
        <where>
            <if test="journalDateStart != null">
                AND journal_date >= #{journalDateStart}
            </if>
            <if test="journalDateEnd != null">
                AND journal_date <![CDATA[<=]]> #{journalDateEnd}
            </if>
            <if test="eventTypes != null and eventTypes.size() > 0">
                AND event_type IN <foreach collection="eventTypes" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="keyword != null and keyword.length() > 0">
                AND description LIKE CONCAT('%', #{keyword}, '%')
            </if>
        </where>
    </select>

    <!-- 安保日志列表 -->
    <select id="listJournal" parameterType="com.senox.realty.vo.SecurityJournalSearchVo"
            resultType="com.senox.realty.domain.SecurityJournal">
        SELECT id, journal_date, event_type, event_time, description, expense, recorder, creator_id
        FROM d_security_journal
        <where>
            <if test="journalDateStart != null">
                AND journal_date >= #{journalDateStart}
            </if>
            <if test="journalDateEnd != null">
                AND journal_date <![CDATA[<=]]> #{journalDateEnd}
            </if>
            <if test="eventTypes != null and eventTypes.size() > 0">
                AND event_type IN <foreach collection="eventTypes" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="keyword != null and keyword.length() > 0">
                AND description LIKE CONCAT('%', #{keyword}, '%')
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>