<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingUtilityInspectionMapper">

    <!-- 公共消防设施巡检合计 -->
    <select id="countInspection" parameterType="com.senox.realty.vo.FirefightingUtilityInspectionSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM r_firefighting_utility_inspection i
            INNER JOIN r_firefighting_utility u ON u.id = i.utility_id
        <where>
            <if test="regionId != null">
                AND u.region_id = #{regionId}
            </if>
            <if test="streetId != null">
                AND u.street_id = #{streetId}
            </if>
            <if test="fireHydrant != null and fireHydrant != ''">
                AND i.fire_hydrant = #{fireHydrant}
            </if>
            <if test="fireHose != null and fireHose != ''">
                AND i.fire_hose = #{fireHose}
            </if>
            <if test="fireExtinguisher != null and fireExtinguisher != ''">
                AND i.fire_extinguisher = #{fireExtinguisher}
            </if>
            <if test="waterGun != null and waterGun != ''">
                AND i.water_gun = #{waterGun}
            </if>
            <if test="valve != null and valve != ''">
                AND i.valve = #{valve}
            </if>
            <if test="fireExits != null and fireExits != ''">
                AND i.fire_exits = #{fireExits}
            </if>
            <if test="inspectDateStart != null">
                AND i.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND i.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (u.street_name LIKE CONCAT('%', #{keyword}, '%') OR u.lcation LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
    </select>

    <!-- 公共消防设施巡检列表 -->
    <select id="listInspection" parameterType="com.senox.realty.vo.FirefightingUtilityInspectionSearchVo"
            resultType="com.senox.realty.vo.FirefightingUtilityInspectionVo">
        SELECT i.id, i.utility_id, u.region_name AS utility_region, u.street_name AS utility_street, u.location AS utility_location
            , i.fire_hydrant, i.fire_hose, i.fire_extinguisher, i.water_gun, i.valve, i.fire_exits, i.fire_extinguisher_num
            , i.expire_fire_extinguisher, i.remark
        FROM r_firefighting_utility_inspection i
            INNER JOIN r_firefighting_utility u ON u.id = i.utility_id
        <where>
            <if test="regionId != null">
                AND u.region_id = #{regionId}
            </if>
            <if test="streetId != null">
                AND u.street_id = #{streetId}
            </if>
            <if test="fireHydrant != null and fireHydrant != ''">
                AND i.fire_hydrant = #{fireHydrant}
            </if>
            <if test="fireHose != null and fireHose != ''">
                AND i.fire_hose = #{fireHose}
            </if>
            <if test="fireExtinguisher != null and fireExtinguisher != ''">
                AND i.fire_extinguisher = #{fireExtinguisher}
            </if>
            <if test="waterGun != null and waterGun != ''">
                AND i.water_gun = #{waterGun}
            </if>
            <if test="valve != null and valve != ''">
                AND i.valve = #{valve}
            </if>
            <if test="fireExits != null and fireExits != ''">
                AND i.fire_exits = #{fireExits}
            </if>
            <if test="inspectDateStart != null">
                AND i.inspect_date >= #{inspectDateStart}
            </if>
            <if test="inspectDateEnd != null">
                AND i.inspect_date <![CDATA[<=]]> #{inspectDateEnd}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (u.street_name LIKE CONCAT('%', #{keyword}, '%') OR u.lcation LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY i.inspect_date DESC, i.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
