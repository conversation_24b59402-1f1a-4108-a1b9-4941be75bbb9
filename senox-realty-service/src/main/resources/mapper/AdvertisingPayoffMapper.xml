<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.AdvertisingPayoffMapper">

    <!-- 合计应付账单 -->
    <select id="sumPayoff" parameterType="com.senox.realty.vo.AdvertisingPayoffSearchVo"
            resultType="com.senox.realty.vo.AdvertisingPayoffDetailVo">
        SELECT SUM(c.amount) AS amount, SUM(po.amount) AS share_amount
        FROM r_advertising_payoff po
            INNER JOIN r_advertising_contract c ON c.contract_no = po.contract_no
            INNER JOIN r_advertising_space s ON s.id = c.space_id
        <where>
            <if test="status != null">
                AND po.status = #{status}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="payway != null">
                AND po.payway = #{payway}
            </if>
            <if test="paidTimeBegin != null">
                AND po.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND po.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="tollManId != null">
                AND po.toll_man_id = #{tollManId}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="spaceName != null and spaceName != ''">
                AND s.name LIKE CONCAT('%', #{spaceName}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND po.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="realtyName != null and realtyName != ''">
                AND po.realty_name LIKE CONCAT('%', #{realtyName}, '%')
            </if>
            <if test="realtyOwner != null and realtyOwner != ''">
                AND po.customer_name LIKE CONCAT('%', #{realtyOwner}, '%')
            </if>
        </where>
    </select>

    <!-- 统计应付账单数 -->
    <select id="countPayoff" parameterType="com.senox.realty.vo.AdvertisingPayoffSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(po.id)
        FROM r_advertising_payoff po
            INNER JOIN r_advertising_contract c ON c.contract_no = po.contract_no
            INNER JOIN r_advertising_space s ON s.id = c.space_id
        <where>
            <if test="status != null">
                AND po.status = #{status}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="payway != null">
                AND po.payway = #{payway}
            </if>
            <if test="paidTimeBegin != null">
                AND po.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND po.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="tollManId != null">
                AND po.toll_man_id = #{tollManId}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="spaceName != null and spaceName != ''">
                AND s.name LIKE CONCAT('%', #{spaceName}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND po.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="realtyName != null and realtyName != ''">
                AND po.realty_name LIKE CONCAT('%', #{realtyName}, '%')
            </if>
            <if test="realtyOwner != null and realtyOwner != ''">
                AND po.customer_name LIKE CONCAT('%', #{realtyOwner}, '%')
            </if>
        </where>
    </select>

    <select id="listPayoff" parameterType="com.senox.realty.vo.AdvertisingPayoffSearchVo"
            resultType="com.senox.realty.vo.AdvertisingPayoffDetailVo">
        SELECT po.id, c.id AS contract_id, c.contract_no, s.serial_no AS space_serial, s.name AS space_name, s.region AS space_region
            , s.street AS space_street, c.customer_name AS customer, c.start_date, c.end_date, po.realty_serial, po.realty_name
            , po.customer_name AS realty_owner, c.amount, po.amount AS share_amount, po.status, po.payway, po.paid_time
            , IFNULL(t.real_name , t.username) AS toll_man
        FROM r_advertising_payoff po
            INNER JOIN r_advertising_contract c ON c.contract_no = po.contract_no
            INNER JOIN r_advertising_space s ON s.id = c.space_id
            LEFT JOIN u_admin_user t on t.id = po.toll_man_id
        <where>
            <if test="status != null">
                AND po.status = #{status}
            </if>
            <if test="startDateBegin != null">
                AND c.start_date >= #{startDateBegin}
            </if>
            <if test="startDateEnd != null">
                AND c.start_date <![CDATA[<=]]> #{startDateEnd}
            </if>
            <if test="endDateBegin != null">
                AND c.end_date >= #{endDateBegin}
            </if>
            <if test="endDateEnd != null">
                AND c.end_date <![CDATA[<=]]> #{endDateEnd}
            </if>
            <if test="payway != null">
                AND po.payway = #{payway}
            </if>
            <if test="paidTimeBegin != null">
                AND po.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND po.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="tollManId != null">
                AND po.toll_man_id = #{tollManId}
            </if>
            <if test="spaceRegionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{spaceRegionId})
            </if>
            <if test="spaceStreetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{spaceStreetId})
            </if>
            <if test="spaceSerial != null and spaceSerial != ''">
                AND s.serial_no LIKE CONCAT('%', #{spaceSerial}, '%')
            </if>
            <if test="spaceName != null and spaceName != ''">
                AND s.name LIKE CONCAT('%', #{spaceName}, '%')
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no LIKE CONCAT('%', #{contractNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND po.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="realtyName != null and realtyName != ''">
                AND po.realty_name LIKE CONCAT('%', #{realtyName}, '%')
            </if>
            <if test="realtyOwner != null and realtyOwner != ''">
                AND po.customer_name LIKE CONCAT('%', #{realtyOwner}, '%')
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY po.status, po.paid_time DESC, po.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>