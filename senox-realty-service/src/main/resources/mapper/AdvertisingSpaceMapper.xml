<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.AdvertisingSpaceMapper">

    <!-- 最大编号 -->
    <select id="findMaxSerial" resultType="java.lang.String">
        SELECT serial_no FROM r_advertising_space ORDER BY serial_no DESC LIMIT 1
    </select>

    <!-- 统计广告位 -->
    <select id="countSpace" parameterType="com.senox.realty.vo.AdvertisingSpaceSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(s.id)
        FROM r_advertising_space s
        <if test="rent != null">
            LEFT JOIN r_advertising_contract c ON c.space_id = s.id AND c.status = 2
        </if>
        <where>
            <if test="serialNo != null and serialNo != ''">
                AND s.serial_no = #{serialNo}
            </if>
            <if test="name != null and name != ''">
                AND s.name = #{name}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (s.serial_no LIKE CONCAT('%', #{keyword}, '%') OR s.name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="rent != null">
                <choose>
                    <when test="rent"> AND c.id IS NOT NULL</when>
                    <otherwise> AND c.id IS NULL</otherwise>
                </choose>
            </if>
            <if test="regionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{regionId})
            </if>
            <if test="streetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{streetId})
            </if>
            AND s.is_disabled = 0
        </where>
    </select>

    <!-- 广告位列表 -->
    <select id="listSpace" parameterType="com.senox.realty.vo.AdvertisingSpaceSearchVo"
            resultType="com.senox.realty.vo.AdvertisingSpaceListVo">
        SELECT s.id, s.serial_no, s.name, s.address, s.`length`, s.width, s.`size`, s.region, s.street
            , (select MAX(c.contract_no) from r_advertising_contract c where c.space_id = s.id and c.status = 2) AS rent_contract
            , (select count(1) from r_advertising_media m
                               inner join r_advertising_contract c on c.id = m.contract_id
                               where c.space_id = s.id and c.status = 2 and c.is_disabled = false limit 1) as exist_resources
        <if test="null != orderStr and orderStr != ''">
            , (select sp.region_id from r_advertising_space_position sp where sp.space_id = s.id limit 1) as region_id
        </if>
        FROM r_advertising_space s
        <if test="rent != null">
            LEFT JOIN r_advertising_contract c ON c.space_id = s.id AND c.status = 2
        </if>
        <where>
            <if test="serialNo != null and serialNo != ''">
                AND s.serial_no = #{serialNo}
            </if>
            <if test="name != null and name != ''">
                AND s.name = #{name}
            </if>
            <if test="rent != null">
                <choose>
                    <when test="rent"> AND c.id IS NOT NULL</when>
                    <otherwise> AND c.id IS NULL</otherwise>
                </choose>
            </if>
            <if test="regionId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position pr WHERE pr.space_id = s.id AND pr.region_id = #{regionId})
            </if>
            <if test="streetId != null">
                AND EXISTS (SELECT 1 FROM r_advertising_space_position ps WHERE ps.space_id = s.id AND ps.street_id = #{streetId})
            </if>
            <if test="keyword != null and keyword != ''">
                AND (s.serial_no LIKE CONCAT('%', #{keyword}, '%') OR s.name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            AND s.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">ORDER BY ${orderStr}</when>
            <otherwise>ORDER BY s.id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="advertisingStatistics" resultType="com.senox.realty.domain.AdvertisingStatistics">
        SELECT having_rent_record_advertising_num + (
            SELECT count(a.id) as un_rent_advertising_num
            from r_advertising_space a
                LEFT JOIN r_advertising_contract c on c.space_id = a.id
                where c.id is null
            ) as un_rent_advertising_num, rent_advertising_num from (
                SELECT
                    sum( CASE WHEN r.`status` <![CDATA[<]]> 2 THEN 1 ELSE 0 END ) AS having_rent_record_advertising_num,
                    sum( CASE WHEN r.`status` = 2 THEN 1 ELSE 0 END ) AS rent_advertising_num
                FROM (
                    SELECT a.id, count(1), MAX(`status`) as `status`
                    from  r_advertising_space a
                        INNER JOIN r_advertising_contract c on c.space_id = a.id
                        GROUP BY a.id
                ) as r
        ) as t
    </select>
</mapper>
