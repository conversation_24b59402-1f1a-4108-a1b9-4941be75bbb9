<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyWeMapper">

    <!-- 更新读数账单id -->
    <update id="batchUpdateBillById">
        UPDATE r_realty_we
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="we_bill_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.weBillId != null">
                        WHEN id = #{item.id} THEN #{item.weBillId}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE id IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item.id}</foreach>
    </update>

    <!-- 更新水电账单读数 -->
    <update id="batchUpdateBillBySerial">
        UPDATE r_realty_we
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="we_bill_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.weBillId != null">
                        WHEN realty_serial = #{item.realtySerial} THEN #{item.weBillId}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE bill_year = #{year}
            AND bill_month = #{month}
            AND `type` = #{type}
            AND realty_serial IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item.realtySerial}</foreach>
    </update>

    <update id="unlinkWeBill" parameterType="java.util.List">
        UPDATE r_realty_we SET we_bill_id = 0, modified_time = NOW() where we_bill_id IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <update id="deleteByYearMonth">
        DELETE w
        FROM r_realty_we w
            LEFT JOIN r_realty_bill_we bw ON bw.id = w.we_bill_id
            LEFT JOIN r_realty_bill b ON b.id = bw.bill_id
        WHERE w.bill_year = #{year}
            AND w.bill_month = #{month}
            AND w.`type` = 1
            AND (b.id IS NULL OR b.status = 0)
    </update>

    <select id="listByIds" resultType="com.senox.realty.vo.RealtyWeVo">
        SELECT w.id, w.bill_year, w.bill_month, w.`type`, w.realty_serial, IFNULL(r.name, a.name) AS realty_name
            , w.last_water_readings, w.water_readings
            , IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0) AS water_cost
            , w.water_base
            , w.last_electric_readings, w.electric_readings
            , IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0) AS electric_cost
            , w.electric_base
            , w.we_bill_id
            , IFNULL(b.status, 0) AS we_bill_status
        FROM r_realty_we w
            LEFT JOIN r_realty r on r.serial_no = w.realty_serial
            LEFT JOIN r_realty_alias a on a.serial_no = w.realty_serial
            LEFT JOIN r_realty_bill_we bw ON bw.id = w.we_bill_id
            LEFT JOIN r_realty_bill b ON b.id = bw.bill_id
        <where>
            AND w.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </where>
    </select>

    <!-- 取最新的水电数据 -->
    <select id="listWeLatestData" parameterType="java.lang.String" resultType="com.senox.realty.domain.RealtyWe">
        SELECT realty_serial, MAX(water_readings) AS water_readings, MAX(electric_readings) AS electric_readings
            , MAX(record_date) AS record_date
        FROM r_realty_we
        <where>
            <if test="realtySerials != null and realtySerials.size > 0">
                AND realty_serial IN <foreach collection="realtySerials" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="excludeYear != null and excludeMonth != null and excludeWeType != null">
                AND (bill_year, bill_month, `type`) not in ((#{excludeYear}, #{excludeMonth}, #{excludeWeType.value}))
            </if>
        </where>
        GROUP BY realty_serial
    </select>


    <!-- 物业水电读数 -->
    <select id="listMonthReadings" resultType="com.senox.realty.vo.RealtyWeVo">
        SELECT w.id, w.realty_serial, w.last_water_readings, w.water_readings, w.last_electric_readings, w.electric_readings
            , w.we_bill_id, b.status AS we_bill_status
        FROM r_realty_we w
            LEFT JOIN r_realty_bill_we bw ON bw.id = w.we_bill_id
            LEFT JOIN r_realty_bill b ON b.id = bw.bill_id
        WHERE w.bill_year = #{year}
            AND w.bill_month = #{month}
            <if test="type != null">
                AND w.`type` = #{type}
            </if>
    </select>

    <select id="countReadings" parameterType="com.senox.realty.vo.RealtyWeSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(w.id)
        FROM r_realty_we w
            LEFT JOIN r_realty_bill_we bw ON bw.id = w.we_bill_id
            LEFT JOIN r_realty_bill b ON b.id = bw.bill_id
        <where>
            <if test="year != null">
                AND w.bill_year = #{year}
            </if>
            <if test="month != null">
                AND w.bill_month = #{month}
            </if>
            <if test="weType != null">
                AND w.`type` = #{weType.value}
            </if>
            <if test="paid">
                AND b.status = 1
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND w.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="null != manMade">
                and w.is_man_made =#{manMade}
            </if>
        </where>
    </select>

    <select id="sumReadings" parameterType="com.senox.realty.vo.RealtyWeSearchVo" resultType="com.senox.realty.vo.RealtyWeVo">
        SELECT SUM(w.last_water_readings) AS last_water_readings
            , SUM(w.water_readings) AS water_readings
            , SUM(IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0)) AS water_cost
            , SUM(w.last_electric_readings) AS last_electric_readings
            , SUM(w.electric_readings) AS electric_readings
            , SUM(IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0)) AS electric_cost
        FROM r_realty_we w
        <if test="paid">
            LEFT JOIN r_realty_bill_we bw ON bw.id = w.we_bill_id
            LEFT JOIN r_realty_bill b ON b.id = bw.bill_id
        </if>
        <where>
            <if test="year != null">
                AND w.bill_year = #{year}
            </if>
            <if test="month != null">
                AND w.bill_month = #{month}
            </if>
            <if test="weType != null">
                AND w.`type` = #{weType.value}
            </if>
            <if test="paid">
                AND b.status = 1
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND w.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
        </where>
    </select>

    <select id="listReadings"  parameterType="com.senox.realty.vo.RealtyWeSearchVo" resultType="com.senox.realty.vo.RealtyWeVo">
        SELECT w.id, w.bill_year, w.bill_month, w.`type`, w.realty_serial, IFNULL(r.name, a.name) AS realty_name
            , w.last_water_readings, w.water_readings
            , IFNULL(w.water_readings, 0) - IFNULL(w.last_water_readings, 0) AS water_cost
            , w.water_base
            , w.last_electric_readings, w.electric_readings
            , IFNULL(w.electric_readings, 0) - IFNULL(w.last_electric_readings, 0) AS electric_cost
            , w.electric_base
            , w.is_man_made as man_made
            , b.status AS we_bill_status
        FROM r_realty_we w
            LEFT JOIN r_realty r on r.serial_no = w.realty_serial
            LEFT JOIN r_realty_alias a on a.serial_no = w.realty_serial
            LEFT JOIN r_realty_bill_we bw ON bw.id = w.we_bill_id
            LEFT JOIN r_realty_bill b ON b.id = bw.bill_id
        <where>
            <if test="year != null">
                AND w.bill_year = #{year}
            </if>
            <if test="month != null">
                AND w.bill_month = #{month}
            </if>
            <if test="weType != null">
                AND w.`type` = #{weType.value}
            </if>
            <if test="paid">
                AND b.status = 1
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND w.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="null != manMade">
                and w.is_man_made =#{manMade}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>ORDER BY id DESC</otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <!-- 水消费统计 -->
    <select id="listWaterProfit" parameterType="com.senox.common.vo.BillTimeVo" resultType="com.senox.realty.domain.EnergyProfitItem">
        SELECT w.bill_year, w.bill_month
            , u.unit AS unit_key, u.name AS unit_name
            , min(w.last_record_date) as last_record_date
            , max(w.record_date) as record_date
            , 1 AS source
            , sum(w.water_readings - w.last_water_readings) as cost
        FROM r_realty_we w
            LEFT JOIN r_energy_consume_unit u ON w.water_consume_unit = u.id AND u.`type` = 2
        WHERE w.bill_year = #{year} and w.bill_month = #{month}
        GROUP BY u.unit, u.name, w.bill_year, w.bill_month
    </select>

    <!-- 电消费统计 -->
    <select id="lisElectricProfit" parameterType="com.senox.common.vo.BillTimeVo" resultType="com.senox.realty.domain.EnergyProfitItem">
        SELECT w.bill_year, w.bill_month
             , u.unit AS unit_key, u.name AS unit_name
             , min(w.last_record_date) as last_record_date
             , max(w.record_date) as record_date
             , 1 AS source
             , sum(w.water_readings - w.last_water_readings) as cost
        FROM r_realty_we w
                 LEFT JOIN r_energy_consume_unit u ON w.water_consume_unit = u.id AND u.`type` = 2
        WHERE w.bill_year = #{year} and w.bill_month = #{month}
        GROUP BY u.unit, u.name, w.bill_year, w.bill_month
    </select>

</mapper>
