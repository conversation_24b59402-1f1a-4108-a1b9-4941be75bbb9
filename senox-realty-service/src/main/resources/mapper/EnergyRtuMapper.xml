<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.EnergyRtuMapper">

    <resultMap id="energyRtuTreeResultMap" type="com.senox.dm.vo.EnergyRtuVo">
        <result column="rtu_id" property="id"/>
        <result column="rtu_code" property="code"/>
        <result column="rtu_name" property="name"/>
        <result column="rtu_status" property="status" typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
        <result column="rtu_on_line_time" property="onLineTime"/>
        <collection property="energyPointList" ofType="com.senox.dm.vo.EnergyPointVo">
            <result column="point_id" property="id"/>
            <result column="point_code" property="code"/>
            <result column="point_name" property="name"/>
            <result column="point_type" property="energyType"
                    typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
            <result column="point_rate" property="rate"/>
        </collection>
    </resultMap>


    <!-- 添加集中器 -->
    <insert id="add" parameterType="com.senox.realty.domain.EnergyRtu" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO d_energy_rtu(code, name, creator_id, creator_name, create_time, modifier_id, modifier_name,
                                 modified_time)
        VALUES (#{code}, #{name}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW())
    </insert>

    <!-- 批量添加集中器 -->
    <insert id="addBatch" parameterType="com.senox.realty.domain.EnergyRtu" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO d_energy_rtu(
        code, name, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="rtuList" item="item" separator=",">
            (
            #{item.code}, #{item.name}, #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId},
            #{item.modifierName}, NOW()
            )
        </foreach>
    </insert>

    <!-- 根据id更新华立集抄集中器 -->
    <update id="updateById" parameterType="com.senox.realty.domain.EnergyRtu">
        UPDATE d_energy_rtu
        <set>
            <if test="name != null">
                , name = #{name}
            </if>
            <if test="status != null">
                , status = #{status}
            </if>
            <if test="code != null">
                , code = #{code}
            </if>
            <if test="onLineTime != null">
                , on_line_time = #{onLineTime}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据编码更新华立集抄集中器 -->
    <update id="updateByCode" parameterType="com.senox.realty.domain.EnergyRtu">
        UPDATE d_energy_rtu
        <set>
            <if test="name != null">
                , name = #{name}
            </if>
            <if test="status != null">
                , status = #{status}
            </if>
            <if test="onLineTime != null">
                , on_line_time = #{onLineTime}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE code = #{code}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultType="com.senox.realty.domain.EnergyRtu">
        SELECT id,
               code,
               name,
               status,
               on_line_time,
               modifier_id,
               modifier_name,
               modified_time
        FROM d_energy_rtu
        WHERE id = #{id}
    </select>

    <select id="findByCode" parameterType="java.lang.String" resultType="com.senox.realty.domain.EnergyRtu">
        SELECT id,
               code,
               name,
               status,
               on_line_time,
               modifier_id,
               modifier_name,
               modified_time
        FROM d_energy_rtu
        WHERE code = #{code}
    </select>

    <select id="countList" parameterType="com.senox.dm.vo.EnergyRtuSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM d_energy_rtu
        <where>
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="state != null">
                AND status = #{state}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (code LIKE CONCAT('%', #{keyword} ,'%') OR name LIKE CONCAT('%', #{keyword} ,'%'))
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="list" parameterType="com.senox.dm.vo.EnergyRtuSearchVo"
            resultType="com.senox.realty.domain.EnergyRtu">
        SELECT id, code, name, status, on_line_time
        FROM d_energy_rtu
        <where>
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="state != null">
                AND status = #{state}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (code LIKE CONCAT('%', #{keyword} ,'%') OR name LIKE CONCAT('%', #{keyword} ,'%'))
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
