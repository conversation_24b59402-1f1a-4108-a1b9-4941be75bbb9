<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.AdvertisingStatisticsMapper">

    <select id="countAdvertisingStatistics" parameterType="com.senox.realty.vo.StatisticsSearchVo" resultType="int">
        select count(id) from r_advertising_statistics
        <where>
            <if test="startDate != null">
                and statistics_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND statistics_date <![CDATA[<=]]> #{endDate}
            </if>
        </where>
    </select>

    <select id="listAdvertisingStatistics" parameterType="com.senox.realty.vo.StatisticsSearchVo" resultType="com.senox.realty.domain.AdvertisingStatistics">
        select id
            , statistics_date
            , advertising_num
            , un_rent_advertising_num
            , rent_advertising_num
            , rent_collect_num
            , rent_collect_amount
            , un_rent_collect_num
            , un_rent_collect_amount
        from r_advertising_statistics
        <where>
            <if test="startDate != null">
                and statistics_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND statistics_date <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY statistics_date asc
            </otherwise>
        </choose>
    </select>

</mapper>
