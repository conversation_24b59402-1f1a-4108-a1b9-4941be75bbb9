<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyBillWithholdMapper">

    <!-- 物业账单银行报盘对象 -->
    <resultMap id="Result_BankOfferBillVo" type="com.senox.realty.vo.BankOfferRealtyBillVo">
        <result property="billYear" column="bill_year" jdbcType="INTEGER" />
        <result property="billMonth" column="bill_month" jdbcType="INTEGER" />
        <result property="billId" column="bill_id" jdbcType="BIGINT" />
        <result property="realtySerial" column="realty_serial" jdbcType="VARCHAR" />
        <result property="realtyName" column="realty_name" jdbcType="VARCHAR" />
        <result property="customerName" column="customer_name" jdbcType="VARCHAR" />
        <result property="contractNo" column="contract_no" jdbcType="VARCHAR" />
        <result property="amount" column="amount" jdbcType="DECIMAL" />
        <result property="bankName" column="bank_name" jdbcType="VARCHAR" />
        <result property="bankAccountNo" column="bank_account_no" jdbcType="VARCHAR" />
        <result property="bankAccountName" column="bank_account_name" jdbcType="VARCHAR" />
        <result property="offer" column="is_offer" jdbcType="TINYINT" />
        <result property="back" column="is_back" jdbcType="TINYINT" />
        <result property="status" column="status" jdbcType="INTEGER" />
    </resultMap>

    <resultMap id="Result_WithholdSum" type="com.senox.realty.vo.WithholdSumVo">
        <result property="offerCount" column="offer_count" jdbcType="INTEGER" />
        <result property="backCount" column="back_count" jdbcType="INTEGER" />
        <result property="offerAmount" column="offer_amount" jdbcType="DECIMAL" />
        <result property="backAmount" column="back_amount" jdbcType="DECIMAL" />
    </resultMap>

    <!-- 物业月账单银行托收报盘 -->
    <insert id="addBillBankWithhold" parameterType="com.senox.realty.domain.BankWithhold">
        INSERT INTO r_realty_bill_withhold (
            bill_id, is_offer, offer_amount, offer_bank, offer_account_no, offer_account_name, offer_account_idcard, offer_time
        )
        SELECT b.id, #{offer}, (b.paid_still_amount - b.penalty_amount), ce.bank_name, ce.bank_account_no , ce.bank_account_name, ce.bank_account_idcard, #{offerTime}
        FROM r_realty_bill b
            INNER JOIN r_contract c ON c.contract_no  = b.contract_no AND c.`type` IN (1, 2)
            INNER JOIN r_contract_ext ce ON ce.contract_id  = c.id AND ce.cost_type = 2
        WHERE b.bill_year = #{billYear}
            AND b.bill_month = #{billMonth}
            AND b.status = 0
            AND b.paid_still_amount > 0
            AND b.is_disabled = 0
            AND c.status = 2
            AND c.end_date >= #{billDate}
            AND ce.bank_account_no is not null
            AND ce.bank_account_no != ''
    </insert>

    <update id="updateBillBankWithholdIgnorePenalty" parameterType="com.senox.realty.domain.BankWithhold">
        update r_realty_bill b
            INNER JOIN r_contract c ON c.contract_no  = b.contract_no AND c.`type` IN (1, 2)
            INNER JOIN r_contract_ext ce ON ce.contract_id  = c.id AND ce.cost_type = 2
        SET b.paid_still_amount = b.amount - b.paid_amount, b.total_amount = b.amount, b.penalty_amount = 0
        WHERE b.bill_year = #{billYear}
            AND b.bill_month = #{billMonth}
            AND b.status = 0
            AND b.paid_still_amount > 0
            AND b.is_disabled = 0
            AND c.status = 2
            AND c.end_date >= #{billDate}
            AND ce.bank_account_no is not null
            AND ce.bank_account_no != ''
    </update>

    <!-- 删除物业月账单银行托收报盘记录 -->
    <update id="deleteBillBankWithhold">
        DELETE wh
        FROM r_realty_bill_withhold wh
            INNER JOIN r_realty_bill b ON wh.bill_id  = b.id
        where b.bill_year = #{year}
            AND b.bill_month = #{month}
            AND b.status = 0
            AND b.is_disabled = 0
            AND wh.is_offer = 1
            AND wh.is_back = 0
    </update>

    <!-- 物业月账单银行托收回盘 -->
    <update id="updateBillBankWithholdBack" parameterType="com.senox.realty.vo.WithholdPaidVo">
        UPDATE r_realty_bill_withhold wh
            INNER JOIN r_realty_bill b ON b.id = wh.bill_id
        <if test="paid">
            INNER JOIN r_realty_bill_item i ON i.bill_id = wh.bill_id AND i.status = 0
        </if>
        <set>
            <if test="remoteOrderId != null">
                , b.remote_order_id  = #{remoteOrderId}
            </if>
            <if test="paid">
                , b.status = 1
                , b.paid_amount = b.paid_amount + #{amount}
                , b.penalty_paid_amount = CASE b.penalty_ignore WHEN 0 THEN b.penalty_amount ELSE 0 END
                , b.paid_still_amount = b.paid_still_amount - #{amount}
                , b.paid_time = #{paidTime}
                , wh.is_back = 1
                , wh.back_amount = #{amount}
                , wh.back_account_no = #{accountNo}
                , wh.back_account_name = #{accountName}
                , wh.back_time = #{paidTime}
                , i.status = 1
                , i.paid_time = #{paidTime}
                , i.modified_time = NOW()
            </if>
            <if test="tollMan != null">
                , b.toll_man_id = #{tollMan}
            </if>
            , b.modified_time = NOW()
        </set>
        WHERE wh.bill_id = #{billId} AND wh.offer_amount = #{amount} AND wh.is_offer = 1 AND wh.is_back = 0
    </update>

    <select id="listRealtyBankOfferBill" resultMap="Result_BankOfferBillVo">
        SELECT b.bill_year, b.bill_month, b.id AS bill_id, r.serial_no AS realty_serial, r.name AS realty_name, b.contract_no
            , wh.offer_amount AS amount, wh.offer_bank AS bank_name, wh.offer_account_no AS bank_account_no
            , wh.offer_account_name AS bank_account_name, IFNULL(wh.is_offer, 0) AS is_offer, IFNULL(wh.is_back, 0) AS is_back
            , b.status
        FROM r_realty_bill b
            INNER JOIN r_realty_bill_withhold wh on wh.bill_id = b.id
            INNER JOIN r_realty r ON r.id = b.realty_id
        WHERE b.bill_year = #{year}
            AND b.bill_month = #{month}
            AND r.serial_no = #{realtySerial}
            AND b.is_disabled = 0
    </select>

    <!-- 银行托收物业账单数量 -->
    <select id="countBankOfferBillApplying" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(b.id)
        FROM r_realty_bill b
            INNER JOIN r_contract c ON c.contract_no  = b.contract_no AND c.`type` IN (1, 2)
            INNER JOIN r_contract_ext ce ON ce.contract_id  = c.id AND ce.cost_type = 2
        <if test="realtySerial != null and realtySerial != ''">
            INNER JOIN r_realty r ON b.realty_id = r.id
        </if>
        WHERE b.bill_year = #{billYear}
            AND b.bill_month = #{billMonth}
            AND b.status = 0
            AND b.is_disabled = 0
            AND b.paid_still_amount > 0
            AND c.status = 2
            AND c.end_date >= #{billDate}
            AND ce.bank_account_no is not null
            AND ce.bank_account_no <![CDATA[!=]]> ''
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT('%',  #{realtySerial} ,'%')
        </if>
    </select>

    <!-- 银行托收物业账单列表 -->
    <select id="listBankOfferBillApplying" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultMap="Result_BankOfferBillVo">
        SELECT b.bill_year, b.bill_month, b.id AS bill_id, r.serial_no AS realty_serial, r.name AS realty_name, c.contract_no
            , c.customer_name, (b.paid_still_amount - b.penalty_amount) AS amount, ce.bank_name, ce.bank_account_no, ce.bank_account_name, 0 AS is_offer
            , 0 AS is_back, 0 AS status
        FROM r_realty_bill b
            INNER JOIN r_contract c ON c.contract_no  = b.contract_no AND c.`type` IN (1, 2)
            INNER JOIN r_contract_ext ce ON ce.contract_id  = c.id AND ce.cost_type = 2
            INNER JOIN r_realty r ON r.id = b.realty_id
        WHERE b.bill_year = #{billYear}
            AND b.bill_month = #{billMonth}
            AND b.status = 0
            AND b.paid_still_amount > 0
            AND b.is_disabled = 0
            AND c.status = 2
            AND c.end_date >= #{billDate}
            AND ce.bank_account_no is not null
            AND ce.bank_account_no <![CDATA[!=]]> ''
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%',  #{realtySerial} ,'%')
            </if>
        <if test="page">
            <choose>
                <when test="orderStr != null and orderStr != ''">
                    ORDER BY ${orderStr}
                </when>
                <otherwise>
                    ORDER BY r.serial_no
                </otherwise>
            </choose>
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 合计银行托收物业账单金额 -->
    <select id="sumBankOfferBillApplying" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultMap="Result_WithholdSum">
        SELECT IFNULL(SUM(b.paid_still_amount - b.penalty_amount), 0) AS offer_amount, 0 AS back_amount
            , IFNULL(COUNT(b.id), 0) AS offer_count, 0 AS back_count
        FROM r_realty_bill b
            INNER JOIN r_contract c ON c.contract_no  = b.contract_no AND c.`type` IN (1, 2)
            INNER JOIN r_contract_ext ce ON ce.contract_id  = c.id AND ce.cost_type = 2
        <if test="realtySerial != null and realtySerial != ''">
            INNER JOIN r_realty r ON b.realty_id = r.id
        </if>
        WHERE b.bill_year = #{billYear}
            AND b.bill_month = #{billMonth}
            AND b.status = 0
            AND b.is_disabled = 0
            AND b.paid_still_amount > 0
            AND c.status = 2
            AND c.end_date >= #{billDate}
            AND ce.bank_account_no is not null
            AND ce.bank_account_no <![CDATA[!=]]> ''
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT('%',  #{realtySerial} ,'%')
        </if>
    </select>

    <!-- 银行托收物业账单数量（已提交） -->
    <select id="countBankOfferBillSubmitted" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(b.id)
        FROM r_realty_bill b
            INNER JOIN r_realty_bill_withhold wh on wh.bill_id = b.id
        <if test="realtySerial != null and realtySerial != ''">
            INNER JOIN r_realty r ON b.realty_id = r.id
        </if>
        WHERE b.bill_year = #{billYear}
            AND b.bill_month = #{billMonth}
            AND b.is_disabled = 0
            AND wh.is_offer = 1
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT('%',  #{realtySerial} ,'%')
        </if>
    </select>

    <!-- 银行托收物业账单列表（已提交） -->
    <select id="listBankOfferBillSubmitted" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultMap="Result_BankOfferBillVo">
        SELECT b.bill_year, b.bill_month, b.id AS bill_id, r.serial_no AS realty_serial, r.name AS realty_name, c.customer_name
            , wh.offer_amount AS amount, wh.offer_bank AS bank_name, wh.offer_account_no AS bank_account_no
            , wh.offer_account_name AS bank_account_name, IFNULL(wh.is_offer, 0) AS is_offer
            , IFNULL(wh.is_back, 0) AS is_back, b.status
        FROM r_realty_bill b
            INNER JOIN r_realty_bill_withhold wh on wh.bill_id = b.id
            INNER JOIN r_realty r ON r.id = b.realty_id
            INNER JOIN r_contract c ON c.contract_no  = b.contract_no
        WHERE b.bill_year = #{billYear}
            AND b.bill_month = #{billMonth}
            AND b.is_disabled = 0
            AND wh.is_offer = 1
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT('%',  #{realtySerial} ,'%')
        </if>
        <if test="page">
            <choose>
                <when test="orderStr != null and orderStr != ''">
                    ORDER BY ${orderStr}
                </when>
                <otherwise>
                    ORDER BY r.serial_no
                </otherwise>
            </choose>
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 合计银行托收物业账单金额（已提交） -->
    <select id="sumBankOfferBillSubmitted" parameterType="com.senox.realty.vo.RealtyBillSearchVo" resultMap="Result_WithholdSum">
        SELECT IFNULL(SUM(wh.offer_amount), 0) AS offer_amount, IFNULL(SUM(wh.back_amount), 0) AS back_amount
             , IFNULL(SUM(wh.is_offer), 0) AS offer_count, IFNULL(SUM(wh.is_back), 0) AS back_count
        FROM r_realty_bill b
            INNER JOIN r_realty_bill_withhold wh on wh.bill_id = b.id
        <if test="realtySerial != null and realtySerial != ''">
            INNER JOIN r_realty r ON b.realty_id = r.id
        </if>
        WHERE b.bill_year = #{billYear}
            AND b.bill_month = #{billMonth}
            AND b.is_disabled = 0
            AND wh.is_offer = 1
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT('%',  #{realtySerial} ,'%')
        </if>
    </select>
</mapper>