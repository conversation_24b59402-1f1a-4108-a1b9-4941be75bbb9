<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.EnergyMeteringPointReadingsMapper">

    <insert id="addBatch">
        insert into
        <choose>
            <when test="isNew">d_energy_metering_point_readings_new</when>
            <otherwise>d_energy_metering_point_readings</otherwise>
        </choose>
        (rtu_code, point_code, point_rate, point_type, realty_serial_no, realty_name, readings, data_time, grab_time)
        values
        <foreach collection="meteringPointReadingsList" item="item" separator=",">
            (#{item.rtuCode},#{item.pointCode},#{item.pointRate}, #{item.pointType}, #{item.realtySerialNo}, #{item.realtyName}, #{item.readings}, #{item.dataTime}, #{item.grabTime})
        </foreach>
    </insert>

    <select id="countList" resultType="integer">
        select count(pm.id)
        from
        <choose>
            <when test="realTime">
                d_energy_metering_point_readings_new pm
            </when>
            <otherwise>
                d_energy_metering_point_readings pm
            </otherwise>
        </choose>
        <where>
            <if test="null != pointCode and pointCode != ''">
                and pm.point_code like concat('%',#{pointCode},'%')
            </if>
            <if test="null != pointType">
                and pm.point_type = #{pointType}
            </if>
            <if test="null != startTime">
                and pm.data_time >= #{startTime}
            </if>
            <if test="null != endTime">
                and pm.data_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="list" resultType="com.senox.realty.domain.EnergyMeteringPointReadings">
        select
        pm.id,
        pm.rtu_code,
        pm.point_code,
        pm.point_rate,
        pm.point_type,
        pm.realty_serial_no,
        pm.realty_name,
        pm.readings,
        pm.data_time,
        pm.grab_time
        from<choose>
            <when test="realTime">
                d_energy_metering_point_readings_new pm
            </when>
            <otherwise>
                d_energy_metering_point_readings pm
            </otherwise>
        </choose>
        <where>
            <if test="null != pointCode and pointCode != ''">
                and pm.point_code like concat('%',#{pointCode},'%')
            </if>
            <if test="null != pointType">
                and pm.point_type = #{pointType}
            </if>
            <if test="null != startTime">
                and pm.data_time >= #{startTime}
            </if>
            <if test="null != endTime">
                and pm.data_time &lt;= #{endTime}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                order by ${orderStr}
            </when>
            <otherwise>
                order by pm.data_time desc
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>

    </select>

    <update id="updateBatch">
        update
        <choose>
            <when test="isNew">d_energy_metering_point_readings_new</when>
            <otherwise>d_energy_metering_point_readings</otherwise>
        </choose>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="rtu_code = case" suffix="else rtu_code end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.rtuCode and item.rtuCode != ''">
                        when id = #{item.id} then #{item.rtuCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="point_code = case" suffix="else point_code end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.pointCode and item.pointCode != ''">
                        when id = #{item.id} then #{item.pointCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="point_rate = case" suffix="else point_rate end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.pointRate">
                        when id = #{item.id} then #{item.pointRate}
                    </if>
                </foreach>
            </trim>
            <trim prefix="point_type = case" suffix="else point_type end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.pointType">
                        when id = #{item.id} then #{item.pointType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="realty_serial_no = case" suffix="else realty_serial_no end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.realtySerialNo and item.realtySerialNo != ''">
                        when id = #{item.id} then #{item.realtySerialNo}
                    </if>
                </foreach>
            </trim>
            <trim prefix="realty_name = case" suffix="else realty_name end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.realtyName and item.realtyName != ''">
                        when id = #{item.id} then #{item.realtyName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="readings = case" suffix="else readings end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.readings">
                        when id = #{item.id} then #{item.readings}
                    </if>
                </foreach>
            </trim>
            <trim prefix="data_time = case" suffix="else data_time end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.dataTime">
                        when id = #{item.id} then #{item.dataTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="grab_time = case" suffix="else grab_time end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.grabTime">
                        when id = #{item.id} then #{item.grabTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="realty_serial_no = case" suffix="end,">
                <foreach collection="meteringPointReadingsList" item="item">
                    <if test="null != item.realtySerialNo and item.realtySerialNo != ''">
                        when id = #{item.id} then #{item.realtySerialNo}
                    </if>
                </foreach>
            </trim>
        </trim>
        <where>
            and id in
            <foreach collection="meteringPointReadingsList" item="item" separator="," open="(" close=")">#{item.id}</foreach>
        </where>
    </update>
</mapper>
