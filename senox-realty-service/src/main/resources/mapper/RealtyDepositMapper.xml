<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.RealtyDepositMapper">

    <resultMap id="Result_RealtyDepositVo" type="com.senox.realty.vo.RealtyDepositVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="contractId" jdbcType="BIGINT" column="contract_id" />
        <result property="contractNo" jdbcType="VARCHAR" column="contract_no" />
        <result property="customerId" jdbcType="BIGINT" column="customer_id" />
        <result property="customerName" jdbcType="VARCHAR" column="customer_name" />
        <result property="realtyId" jdbcType="BIGINT" column="realty_id" />
        <result property="realtySerial" jdbcType="VARCHAR" column="realty_serial" />
        <result property="realtyName" jdbcType="VARCHAR" column="realty_name" />
        <result property="feeId" jdbcType="BIGINT" column="fee_id" />
        <result property="feeName" jdbcType="VARCHAR" column="fee_name" />
        <result property="amount" jdbcType="DECIMAL" column="amount" />
        <result property="remark" jdbcType="VARCHAR" column="remark" />
        <result property="status" jdbcType="INTEGER" column="status" />
        <result property="operateDate" jdbcType="TIMESTAMP" column="operate_date" />
        <result property="tollSerial" jdbcType="VARCHAR" column="toll_serial" />
        <result property="tollMan" jdbcType="VARCHAR" column="toll_man" />
        <result property="tollTime" jdbcType="TIMESTAMP" column="toll_time" />
        <result property="tollPayWay" jdbcType="INTEGER" column="toll_pay_way" />
        <result property="remoteOrderId" jdbcType="BIGINT" column="remote_order_id" />
        <result property="refundAmount" jdbcType="DECIMAL" column="refund_amount" />
        <result property="refundSerial" jdbcType="VARCHAR" column="refund_serial" />
        <result property="refundMan" jdbcType="VARCHAR" column="refund_man" />
        <result property="refundTime" jdbcType="TIMESTAMP" column="refund_time" />
        <result property="refundPayWay" jdbcType="INTEGER" column="refund_pay_way" />
        <result property="refundOrderId" jdbcType="BIGINT" column="refund_order_id" />
        <result property="totalAmount" jdbcType="DECIMAL" column="total_amount" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
    </resultMap>

    <!-- 添加物业押金 -->
    <insert id="addDeposit" parameterType="com.senox.realty.domain.RealtyDeposit" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_realty_deposit (
            contract_id, customer_id, customer_name, realty_id, fee_id, fee_name, amount, remark, operate_date, creator_id, creator_name, create_time
            , modifier_id, modifier_name, modified_time
        ) VALUES (
            #{contractId}, #{customerId}, #{customerName}, #{realtyId}, #{feeId}, #{feeName}, #{amount}, #{remark}, #{operateDate}, #{creatorId}, #{creatorName}, NOW()
            , #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <insert id="batchAddDeposit" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_realty_deposit (
           contract_id, customer_id, customer_name, realty_id, fee_id, fee_name, amount, remark, operate_date, creator_id, creator_name, create_time
           , modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.contractId}, #{item.customerId}, #{item.customerName}, #{item.realtyId}, #{item.feeId}, #{item.feeName}, #{item.amount}, #{item.remark}
            , #{item.operateDate}, #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 更新物业押金 -->
    <update id="updateDeposit" parameterType="com.senox.realty.domain.RealtyDeposit">
        UPDATE r_realty_deposit
        <set>
            <if test="contractId != null">
                , contract_id = #{contractId}
            </if>
            <if test="customerId != null">
                , customer_id = #{customerId}
            </if>
            <if test="customerName != null">
                , customer_name = #{customerName}
            </if>
            <if test="realtyId != null">
                , realty_id = #{realtyId}
            </if>
            <if test="feeId != null">
                , fee_id = #{feeId}
            </if>
            <if test="feeName != null and feeName != ''">
                , fee_name = #{feeName}
            </if>
            <if test="amount != null">
                , amount = #{amount}
            </if>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            <if test="operateDate != null">
                , operate_date = #{operateDate}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null and modifierName != ''">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id} AND status = 0
    </update>

    <update id="updateDepositPaidById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_realty_deposit d
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = d.id
        <set>
            <if test="orderId != null">
                , d.remote_order_id = #{orderId}
            </if>
            <if test="paid">
                , d.status = 1
                , d.toll_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , d.toll_by = #{tollMan}
            </if>
            , d.modified_time = NOW()
        </set>
        <where>
            AND d.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND d.status = 0
            AND d.is_disabled = 0
        </where>
    </update>

    <update id="updateDepositPaidByRemoteOrder" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_realty_deposit d
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id  = d.id
        <set>
            <if test="paid">
                , d.status = 1
                , d.toll_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , d.toll_by = #{tollMan}
            </if>
            , d.modified_time = NOW()
        </set>
        <where>
            AND d.remote_order_id = #{orderId}
            AND d.status = 0
            AND d.is_disabled = 0
        </where>
    </update>

    <update id="updateDepositRefundById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_realty_deposit d
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id = d.id
        <set>
            <if test="orderId != null">
                , d.refund_order_id = #{orderId}
            </if>
            <if test="paid">
                , d.status = 10
                , d.refund_amount = d.refund_amount + oi.total_amount
                , d.refund_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , d.refund_by = #{tollMan}
            </if>
            , d.modified_time = NOW()
        </set>
        <where>
            AND d.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND d.status = 1
            AND d.is_disabled = 0
        </where>
    </update>

    <update id="updateDepositRefundByRemoteOrder" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE r_realty_deposit d
            INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id  = d.id
        <set>
            <if test="paid">
                , d.status = 10
                , d.refund_amount = d.refund_amount + oi.total_amount
                , d.refund_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , d.refund_by = #{tollMan}
            </if>
            , d.modified_time = NOW()
        </set>
        <where>
            AND d.refund_order_id = #{orderId}
            AND d.status = 1
            AND d.is_disabled = 0
        </where>
    </update>

    <update id="revokeDepositPaid" parameterType="com.senox.realty.vo.BillTollVo">
        UPDATE r_realty_deposit
        <set>
            , status = 0
            , toll_by = 0
            , toll_time = NULL
            , modifier_id = #{operator}
            , modifier_name = #{operatorName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id} AND status = 1
    </update>

    <!-- 更新物业押金票据号 -->
    <update id="updateDepositTollSerial" parameterType="com.senox.realty.vo.TollSerialVo">
        UPDATE r_realty_deposit
        <set>
            <if test="serial != null">
                <choose>
                    <when test="refund">, refund_serial = #{serial}</when>
                    <otherwise>, toll_serial = #{serial}</otherwise>
                </choose>
            </if>
            <if test="operatorId != null">
                , modifier_id = #{operatorId}
            </if>
            <if test="operatorName != null and operatorName != ''">
                , modifier_name = #{operatorName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 支付物业押金 -->
    <update id="payDeposit" parameterType="com.senox.realty.vo.BillTollVo">
        UPDATE r_realty_deposit
        <set>
            , toll_serial = #{serial}
            , toll_by = #{operator}
            <choose>
                <when test="status == 0">
                    , toll_time = NULL
                </when>
                <otherwise>, toll_time = NOW()</otherwise>
            </choose>
            , status = #{status}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 物业押金退费 -->
    <update id="refundDeposit" parameterType="com.senox.realty.vo.BillTollVo">
        UPDATE r_realty_deposit
        <set>
            , status = #{status}
            , refund_amount = #{amount}
            , refund_serial = #{serial}
            , refund_by = #{operator}
            <choose>
                <when test="status == 10">
                    , refund_time = NOW()
                </when>
                <otherwise>, refund_time = NULL</otherwise>
            </choose>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="revokeDepositRefund" parameterType="com.senox.realty.vo.BillTollVo">
        UPDATE r_realty_deposit
        <set>
            , status = 1
            , refund_amount = 0
            , refund_by = 0
            , refund_time = NULL
            , modifier_id = #{operator}
            , modifier_name = #{operatorName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id} AND status = 10
    </update>

    <!-- 根据id获取物业押金记录 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_RealtyDepositVo">
        SELECT d.id
            , d.contract_id
            , c.contract_no
            , d.customer_id
            , d.customer_name
            , d.realty_id
            , r.serial_no AS realty_serial
            , r.name AS realty_name
            , d.fee_id
            , d.fee_name
            , d.amount
            , d.remark
            , d.status
            , d.operate_date
            , d.toll_serial
            , d.toll_time
            , d.remote_order_id
            , d.refund_amount
            , d.refund_serial
            , d.refund_time
            , d.refund_order_id
        FROM r_realty_deposit d
            LEFT JOIN r_contract c ON c.id = d.contract_id
            LEFT JOIN r_realty r ON r.id = d.realty_id
        WHERE d.id = #{id}
    </select>

    <select id="listByIds" parameterType="java.lang.Long" resultMap="Result_RealtyDepositVo">
        SELECT d.id
            , d.contract_id
            , c.contract_no
            , d.customer_id
            , d.customer_name
            , d.realty_id
            , r.serial_no AS realty_serial
            , r.name AS realty_name
            , d.fee_id
            , d.fee_name
            , d.amount
            , d.remark
            , d.status
            , d.operate_date
            , d.toll_serial
            , d.toll_time
            , d.refund_amount
            , d.refund_serial
            , d.refund_time
        FROM r_realty_deposit d
            LEFT JOIN r_contract c ON c.id = d.contract_id
            LEFT JOIN r_realty r ON r.id = d.realty_id
        WHERE d.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="listContractDeposit" parameterType="java.lang.Long" resultMap="Result_RealtyDepositVo">
        SELECT d.id
            , d.contract_id
            , c.contract_no
            , d.customer_id
            , d.customer_name
            , d.realty_id
            , r.serial_no AS realty_serial
            , r.name AS realty_name
            , d.fee_id
            , d.fee_name
            , d.amount
            , d.remark
            , d.status
            , d.operate_date
            , d.toll_serial
            , d.toll_time
            , d.refund_amount
            , d.refund_serial
            , d.refund_time
        FROM r_realty_deposit d
            LEFT JOIN r_contract c ON c.id = d.contract_id
            LEFT JOIN r_realty r ON r.id = d.realty_id
        WHERE d.contract_id = #{contractId} AND d.is_disabled = 0
    </select>

    <select id="sumDeposit" parameterType="com.senox.realty.vo.RealtyDepositSearchVo" resultMap="Result_RealtyDepositVo">
        SELECT SUM(d.amount) AS amount, SUM(d.refund_amount) AS refund_amount, SUM(d.amount + d.refund_amount) AS total_amount
        FROM r_realty_deposit d
        <if test="customerName != null and customerName != ''">
            LEFT JOIN u_customer u ON u.id = d.customer_id
        </if>
        <if test="realtySerial != null and realtySerial != ''">
            LEFT JOIN r_realty r ON r.id = d.realty_id
        </if>
        <if test="tollPayWay != null">
            LEFT JOIN p_order po ON po.id = d.remote_order_id AND po.status = 10
        </if>
        <if test="refundPayWay != null">
            LEFT JOIN p_order fo ON fo.id = d.refund_order_id AND fo.order_type = 8 and fo.status = 10
        </if>
        <where>
            <if test="statusList != null and statusList.size() > 0">
                AND d.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="operateDateStart != null">
                AND d.operate_date >= #{operateDateStart}
            </if>
            <if test="operateDateEnd != null">
                AND d.operate_date <![CDATA[<=]]> #{operateDateEnd}
            </if>
            <if test="tollTimeStart != null">
                AND d.toll_time >= #{tollTimeStart}
            </if>
            <if test="tollTimeEnd != null">
                AND d.toll_time <![CDATA[<=]]> #{tollTimeEnd}
            </if>
            <if test="tollMan != null">
                AND d.toll_by = #{tollMan}
            </if>
            <if test="tollPayWay != null">
                <choose>
                    <when test="tollPayWay == 1">
                        AND d.status IN (1, 10)
                        AND (po.pay_way = 1 OR po.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND po.pay_way = #{tollPayWay}
                    </otherwise>
                </choose>
            </if>
            <if test="refundTimeStart != null">
                AND d.refund_time >= #{refundTimeStart}
            </if>
            <if test="refundTimeEnd != null">
                AND d.refund_time <![CDATA[<=]]> #{refundTimeEnd}
            </if>
            <if test="refundMan != null">
                AND d.refund_by = #{refundMan}
            </if>
            <if test="refundPayWay != null">
                <choose>
                    <when test="refundPayWay == 1">
                        AND d.status = 10
                        AND (fo.pay_way = 1 OR fo.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND fo.pay_way = #{refundPayWay}
                    </otherwise>
                </choose>
            </if>
            <if test="billSerial != null and billSerial != ''">
                AND (d.toll_serial = #{billSerial} OR d.refund_serial = #{billSerial})
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND d.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND d.is_disabled = 0
        </where>
    </select>

    <!-- 合计物业押金记录 -->
    <select id="countDeposit" parameterType="com.senox.realty.vo.RealtyDepositSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(d.id)
        FROM r_realty_deposit d
        <if test="customerName != null and customerName != ''">
            LEFT JOIN u_customer u ON u.id = d.customer_id
        </if>
        <if test="realtySerial != null and realtySerial != ''">
            LEFT JOIN r_realty r ON r.id = d.realty_id
        </if>
        <if test="tollPayWay != null">
            LEFT JOIN p_order po ON po.id = d.remote_order_id AND po.status = 10
        </if>
        <if test="refundPayWay != null">
            LEFT JOIN p_order fo ON fo.id = d.refund_order_id AND fo.order_type = 8 and fo.status = 10
        </if>
        <where>
            <if test="statusList != null and statusList.size() > 0">
                AND d.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="operateDateStart != null">
                AND d.operate_date >= #{operateDateStart}
            </if>
            <if test="operateDateEnd != null">
                AND d.operate_date <![CDATA[<=]]> #{operateDateEnd}
            </if>
            <if test="tollTimeStart != null">
                AND d.toll_time >= #{tollTimeStart}
            </if>
            <if test="tollTimeEnd != null">
                AND d.toll_time <![CDATA[<=]]> #{tollTimeEnd}
            </if>
            <if test="tollMan != null">
                AND d.toll_by = #{tollMan}
            </if>
            <if test="tollPayWay != null">
                <choose>
                    <when test="tollPayWay == 1">
                        AND d.status IN (1, 10)
                        AND (po.pay_way = 1 OR po.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND po.pay_way = #{tollPayWay}
                    </otherwise>
                </choose>
            </if>
            <if test="refundTimeStart != null">
                AND d.refund_time >= #{refundTimeStart}
            </if>
            <if test="refundTimeEnd != null">
                AND d.refund_time <![CDATA[<=]]> #{refundTimeEnd}
            </if>
            <if test="refundMan != null">
                AND d.refund_by = #{refundMan}
            </if>
            <if test="refundPayWay != null">
                <choose>
                    <when test="refundPayWay == 1">
                        AND d.status = 10
                        AND (fo.pay_way = 1 OR fo.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND fo.pay_way = #{refundPayWay}
                    </otherwise>
                </choose>
            </if>
            <if test="billSerial != null and billSerial != ''">
                AND (d.toll_serial = #{billSerial} OR d.refund_serial = #{billSerial})
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND d.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND d.is_disabled = 0
        </where>
    </select>

    <!-- 物业押金记录列表 -->
    <select id="listDeposit" parameterType="com.senox.realty.vo.RealtyDepositSearchVo" resultMap="Result_RealtyDepositVo">
        SELECT d.id
            , c.contract_no
            , d.customer_name
            , r.serial_no AS realty_serial
            , r.name AS realty_name
            , d.fee_name
            , d.amount
            , d.status
            , d.operate_date
            , d.toll_serial
            , o.real_name AS toll_man
            , d.toll_time
            , IFNULL(po.pay_way, CASE WHEN d.status > 0 THEN 1 ELSE 0 END) AS toll_pay_way
            , d.refund_amount
            , d.refund_serial
            , f.real_name AS refund_man
            , d.refund_time
            , IFNULL(fo.pay_way, CASE WHEN d.status = 10 THEN 1 ELSE 0 END) AS refund_pay_way
            , (d.amount + d.refund_amount) AS total_amount
            , ct.real_name AS creator_name
            , d.remark
            , d.create_time
        FROM r_realty_deposit d
            LEFT JOIN r_contract c ON c.id = d.contract_id
            LEFT JOIN r_realty r ON r.id = d.realty_id
            LEFT JOIN p_order po ON po.id = d.remote_order_id AND po.order_type = 8 and po.status = 10
            LEFT JOIN p_order fo ON fo.id = d.refund_order_id AND fo.order_type = 8 and fo.status = 10
            LEFT JOIN u_admin_user o ON o.id = d.toll_by
            LEFT JOIN u_admin_user f ON f.id = d.refund_by
            LEFT JOIN u_admin_user ct ON ct.id = d.creator_id
        <where>
            <if test="statusList != null and statusList.size() > 0">
                AND d.status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="operateDateStart != null">
                AND d.operate_date >= #{operateDateStart}
            </if>
            <if test="operateDateEnd != null">
                AND d.operate_date <![CDATA[<=]]> #{operateDateEnd}
            </if>
            <if test="tollTimeStart != null">
                AND d.toll_time >= #{tollTimeStart}
            </if>
            <if test="tollTimeEnd != null">
                AND d.toll_time <![CDATA[<=]]> #{tollTimeEnd}
            </if>
            <if test="tollMan != null">
                AND d.toll_by = #{tollMan}
            </if>
            <if test="tollPayWay != null">
                <choose>
                    <when test="tollPayWay == 1">
                        AND d.status IN (1, 10)
                        AND (po.pay_way = 1 OR po.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND po.pay_way = #{tollPayWay}
                    </otherwise>
                </choose>
            </if>
            <if test="refundTimeStart != null">
                AND d.refund_time >= #{refundTimeStart}
            </if>
            <if test="refundTimeEnd != null">
                AND d.refund_time <![CDATA[<=]]> #{refundTimeEnd}
            </if>
            <if test="refundMan != null">
                AND d.refund_by = #{refundMan}
            </if>
            <if test="refundPayWay != null">
                <choose>
                    <when test="refundPayWay == 1">
                        AND d.status = 10
                        AND (fo.pay_way = 1 OR fo.pay_way IS NULL)
                    </when>
                    <otherwise>
                        AND fo.pay_way = #{refundPayWay}
                    </otherwise>
                </choose>
            </if>
            <if test="billSerial != null and billSerial != ''">
                AND (d.toll_serial = #{billSerial} OR d.refund_serial = #{billSerial})
            </if>
            <if test="realtySerial != null and realtySerial != ''">
                AND r.serial_no LIKE CONCAT('%', #{realtySerial}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND d.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            AND d.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY d.operate_date DESC, d.customer_id, d.id DESC
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="getContractNoList" parameterType="com.senox.common.vo.BillPaidVo" resultType="java.lang.String">
        SELECT
        c.contract_no
        FROM
        r_realty_deposit d
        INNER JOIN r_contract c ON d.contract_id = c.id
        <where>
            <if test="billIds != null and billIds.size() > 0">
                AND d.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="orderId != null">
                AND (d.refund_order_id = #{orderId} OR d.remote_order_id = #{orderId})
            </if>
        </where>
    </select>


</mapper>
