<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.FirefightingInspectTaskRealtyMapper">

    <!-- 添加巡检任务物业 -->
    <insert id="addInspectTaskRealty">
        INSERT INTO r_firefighting_inspect_task_realty(task_id, task_item_id, realty_serial, modified_time) VALUES
        <foreach collection="realtySerials" item="realtySerial" separator=",">
        (#{taskId}, #{taskItemId}, #{realtySerial}, NOW())
        </foreach>
    </insert>

    <!-- 添加巡检任务物业 -->
    <insert id="batchAddInspectTaskRealty" parameterType="com.senox.realty.domain.FirefightingInspectRealty">
        INSERT INTO r_firefighting_inspect_task_realty(task_id, task_item_id, realty_serial, modified_time) VALUES
        <foreach collection="list" item="item" separator=",">
        (#{item.taskId}, #{item.taskItemId}, #{item.realtySerial}, NOW())
        </foreach>
    </insert>

    <!-- 删除巡检任务物业 -->
    <delete id="deleteInspectTaskRealtyByTask">
        DELETE FROM r_firefighting_inspect_task_realty
        WHERE task_id = #{taskId}
            AND task_item_id IN <foreach collection="taskItemIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </delete>

    <!-- 删除巡检任务物业 -->
    <delete id="deleteInspectTaskRealtyBySerial">
        DELETE FROM r_firefighting_inspect_task_realty
        WHERE task_id = #{taskId}
            AND task_item_id = #{taskItemId}
            AND realty_serial IN <foreach collection="realtySerials" item="realtySerial" open="(" close=")" separator=",">#{realtySerial}</foreach>
    </delete>

    <!-- 巡检任务物业 -->
    <select id="listInspectTaskRealtyByTask" resultType="java.lang.String">
        SELECT realty_serial
        FROM r_firefighting_inspect_task_realty
        WHERE task_id = #{taskId}
            AND task_item_id = #{taskItemId}
    </select>
</mapper>