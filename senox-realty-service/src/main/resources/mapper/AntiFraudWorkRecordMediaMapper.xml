<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.AntiFraudWorkRecordMediaMapper">

    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
        insert into r_anti_fraud_work_record_media(product_id, source, file_name, file_url, creator_id, creator_name,
        create_time)
        values
        <foreach collection="medias" item="item" separator=",">
            (#{item.productId},#{item.source},#{item.fileName}
            ,#{item.fileUrl},#{item.creatorId},#{item.creatorName},#{item.createTime})
        </foreach>
    </insert>

    <delete id="deleteByIds">
        delete from r_anti_fraud_work_record_media
        <where>
            and id in
            <foreach collection="mediaIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </where>
    </delete>

    <delete id="deleteByProductIds">
        delete from r_anti_fraud_work_record_media
        <where>
            and product_id in
            <foreach collection="productIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            and source in
            <foreach collection="sources" item="item" open="(" close=")" separator=",">#{item.number}</foreach>
        </where>
    </delete>

    <select id="countList" resultType="int">
        select count(*)
        from r_anti_fraud_work_record_media
        <where>
            <if test="null != productIds and productIds.size() > 0">
                and product_id in <foreach collection="productIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="null != sources and sources.size() > 0">
                and source in <foreach collection="sources" item="item" open="(" close=")" separator=",">#{item.number}</foreach>
            </if>
        </where>
    </select>

    <select id="list" resultType="com.senox.realty.domain.AntiFraudWorkRecordMedia">
        select id,
        product_id,
        source,
        file_name,
        file_url,
        creator_id,
        creator_name,
        create_time
        from r_anti_fraud_work_record_media
        <where>
            <if test="null != productIds and productIds.size() > 0">
                and product_id in <foreach collection="productIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="null != sources and sources.size() > 0">
                and source in <foreach collection="sources" item="item" open="(" close=")" separator=",">#{item.number}</foreach>
            </if>
        </where>
    </select>
</mapper>
