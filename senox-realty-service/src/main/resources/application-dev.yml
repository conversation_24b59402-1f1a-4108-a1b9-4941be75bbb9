spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************
    username: root
    password: vvwo9YWDPDXB7m%G
  redis:
    host: **************
    port: 6379
    password: WT0UdHepNsSnYb81
    timeout: 10000
  zipkin:
    enabled: false

senox:
  realty:
    config:
      water-base: 2
      electric-base: 5
      record-day: 28
      water-price: 5.13
      electric-price: 0.978
xxl:
  job:
    admin:
      address: http://**************:8989/xxl-job-admin/
    accessToken: ''
    executor:
      appName: ${spring.application.name}-executor
      ip: **************
      port: 9988
      logPath: /data/logs/xxl-job/${xxl.job.executor.appName}
      logRetentionDays: 30

logging:
  level:
    com.senox.realty.mapper: debug
