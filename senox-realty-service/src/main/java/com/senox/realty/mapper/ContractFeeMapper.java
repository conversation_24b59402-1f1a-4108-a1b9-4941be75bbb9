package com.senox.realty.mapper;

import com.senox.realty.domain.ContractFee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/22 11:20
 */
@Mapper
@Repository
public interface ContractFeeMapper {

    /**
     * 批量添加合同费项
     * @param fees
     * @return
     */
    int batchAddContractFee(@Param("fees") List<ContractFee> fees);

    /**
     * 批量删除合同费项
     * @param contractId
     * @param ids
     * @return
     */
    int batchDeleteContractFee(@Param("contractId") Long contractId, @Param("ids") List<Long> ids);

    /**
     * 合同费项列表
     * @param contractId
     * @return
     */
    List<ContractFee> listContractFee(Long contractId);

    /**
     * 合同某费项列表
     * @param contractId
     * @param feeId
     * @return
     */
    List<ContractFee> listContractFeeByFeeId(@Param("contractId") Long contractId, @Param("feeId") Integer feeId);
}
