package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.MaintainMaterial;
import com.senox.realty.vo.MaintainMaterialDataVo;
import com.senox.realty.vo.MaintainMaterialOutNoVo;
import com.senox.realty.vo.MaintainMaterialSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 14:28
 */
@Mapper
@Repository
public interface MaintainMaterialMapper extends BaseMapper<MaintainMaterial> {


    /**
     * 维修物料合计
     * @param search
     * @return
     */
    int countMaintainMaterial(MaintainMaterialSearchVo search);

    /**
     * 维修物料列表
     * @param search
     * @return
     */
    List<MaintainMaterialDataVo> listMaintainMaterial(MaintainMaterialSearchVo search);

    /**
     * 更新出库单号
     * @param outNoVo
     * @return
     */
    int updateOutNo(MaintainMaterialOutNoVo outNoVo);

    /**
     * 撤销出库
     * @param outNo
     * @return
     */
    int cancelOutBound(@Param("outNo") String outNo);
}
