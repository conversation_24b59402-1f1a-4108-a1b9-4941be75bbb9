package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.AdvertisingSpace;
import com.senox.realty.domain.AdvertisingStatistics;
import com.senox.realty.vo.AdvertisingSpaceListVo;
import com.senox.realty.vo.AdvertisingSpaceSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18 9:11
 */
@Mapper
@Repository
public interface AdvertisingSpaceMapper extends BaseMapper<AdvertisingSpace> {

    /**
     * 最大编号
     * @return
     */
    String findMaxSerial();

    /**
     * 统计广告位
     * @param search
     * @return
     */
    int countSpace(AdvertisingSpaceSearchVo search);

    /**
     * 广告位列表
     * @param search
     * @return
     */
    List<AdvertisingSpaceListVo> listSpace(AdvertisingSpaceSearchVo search);

    /**
     * 广告位数量
     * @return
     */
    AdvertisingStatistics advertisingStatistics();
}
