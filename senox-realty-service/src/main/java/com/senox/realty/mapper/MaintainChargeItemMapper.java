package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.MaintainChargeItem;
import com.senox.realty.vo.MaintainChargeItemVo;
import com.senox.realty.vo.MaintainChargeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/13 8:49
 */
@Mapper
public interface MaintainChargeItemMapper extends BaseMapper<MaintainChargeItem> {

    /**
     * 根据id物维收费单集合
     * @param ids
     * @return
     */
    List<MaintainChargeVo> listMaintainChargeByIds(@Param("ids") List<Long> ids);

    /**
     * 根据派工id查询物维收费单集合
     * @param jobId
     * @return
     */
    List<MaintainChargeItemVo> listChargeItemByJobId(@Param("jobId") Long jobId);

    /**
     * 根据订单id查询物维收费单集合
     * @param orderId
     * @return
     */
    List<MaintainChargeItemVo> listChargeItemByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据物维收费id获取收费项
     * @param chargeId
     * @return
     */
    List<MaintainChargeItemVo> listChargeItemByChargeId(@Param("chargeId") Long chargeId);

}
