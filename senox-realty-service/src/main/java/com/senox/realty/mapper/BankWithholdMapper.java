package com.senox.realty.mapper;

import com.senox.realty.domain.BankWithhold;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/1/14 15:18
 */
@Mapper
@Repository
public interface BankWithholdMapper {


    /**
     * 添加物业账单银行托收记录
     * @param withhold
     * @return
     */
    int addWithhold(BankWithhold withhold);

    /**
     * 更新物业账单银行托收报盘
     * @param withhold
     * @return
     */
    int updateWithholdOffer(BankWithhold withhold);

    /**
     * 更新物业账单银行托收回盘
     * @param withhold
     * @return
     */
    int updateWithholdBack(BankWithhold withhold);

    /**
     * 根据账单年月查找报盘信息
     * @param year
     * @param month
     * @return
     */
    BankWithhold findByYearMonth(@Param("year") Integer year, @Param("month") Integer month);
}
