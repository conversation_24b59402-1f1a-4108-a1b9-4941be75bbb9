package com.senox.realty.mapper;


import com.senox.realty.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-11-2
 */
public interface RealtyEnergyPointMapper {

    /**
     * 检查是否已绑定计量点设备
     *
     * @param realtyBindPoint 绑定vo
     * @param energyPointType 计量点类型
     * @return >0:包含
     */
    int checkPointDeviceBind(RealtyBindEnergyMeteringPointVo realtyBindPoint, Integer energyPointType);

    /**
     * 绑定计量点设备
     *
     * @param realtyBindPoint 绑定vo
     * @param energyPointType 计量点类型
     */
    void bindPointDevice(RealtyBindEnergyMeteringPointVo realtyBindPoint, Integer energyPointType);

    /**
     * 解绑计量点设备
     *
     * @param pointCode 计量点编码
     * @return true:解绑成功,false:解绑失败
     */
    boolean unBindPointDevice(String pointCode);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int meteringPointToRealtyCountList(RealtyToEnergyMeteringPointSearchVo search);

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    List<RealtyToEnergyMeteringPointVo> meteringPointToRealtyList(RealtyToEnergyMeteringPointSearchVo search);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int meteringPointReadingsToRealtyCountList(RealtyToEnergyMeteringPointReadingsSearchVo search);

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    List<RealtyToEnergyMeteringPointReadingsVo> meteringPointReadingsToRealtyList(RealtyToEnergyMeteringPointReadingsSearchVo search);

}
