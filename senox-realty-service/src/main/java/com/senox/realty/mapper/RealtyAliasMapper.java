package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.RealtyAlias;
import com.senox.realty.vo.RealtyReadingsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-8-12
 */
@Mapper
public interface RealtyAliasMapper extends BaseMapper<RealtyAlias> {

    /**
     * 批量添加物业别名
     * @param list
     * @return
     */
    int batchAddAlias(@Param("list") List<RealtyAlias> list);

    /**
     * 批量更新物业别名
     * @param list
     * @return
     */
    int batchUpdateAlias(@Param("list") List<RealtyAlias> list);

    /**
     * 批量更新物业读数
     * @param list
     * @return
     */
    int batchUpdateAliasReadings(@Param("list") List<RealtyReadingsVo> list);
}
