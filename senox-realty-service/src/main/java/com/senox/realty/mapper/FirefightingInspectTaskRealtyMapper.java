package com.senox.realty.mapper;

import com.senox.realty.domain.FirefightingInspectTaskRealty;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/17 15:31
 */
@Mapper
@Repository
public interface FirefightingInspectTaskRealtyMapper {

    /**
     * 添加巡检任务物业
     * @param taskId
     * @param taskItemId
     * @param realtySerials
     * @return
     */
    int addInspectTaskRealty(@Param("taskId") Long taskId,
                             @Param("taskItemId") Long taskItemId,
                             @Param("realtySerials") List<String> realtySerials);

    /**
     * 添加巡检任务物业
     * @param list
     * @return
     */
    int batchAddInspectTaskRealty(@Param("list") List<FirefightingInspectTaskRealty> list);

    /**
     * 删除巡检任务物业
     * @param taskId
     * @param taskItemIds
     * @return
     */
    int deleteInspectTaskRealtyByTask(@Param("taskId") Long taskId,
                                      @Param("taskItemIds") List<Long> taskItemIds);

    /**
     * 删除巡检任务物业
     * @param taskId
     * @param taskItemId
     * @param realtySerials
     * @return
     */
    int deleteInspectTaskRealtyBySerial(@Param("taskId") Long taskId,
                                        @Param("taskItemId") Long taskItemId,
                                        @Param("realtySerials") List<String> realtySerials);

    /**
     * 巡检任务物业
     * @param taskId
     * @param taskItemId
     * @return
     */
    List<String> listInspectTaskRealtyByTask(@Param("taskId") Long taskId,
                                             @Param("taskItemId") Long taskItemId);
}
