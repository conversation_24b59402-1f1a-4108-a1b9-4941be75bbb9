package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.AntiFraudWorkRecord;
import com.senox.realty.vo.AntiFraudWorkRecordSearchVo;
import com.senox.realty.vo.AntiFraudWorkRecordStatistics;
import com.senox.realty.vo.AntiFraudWorkRecordVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-27
 **/
public interface AntiFraudWorkRecordMapper extends BaseMapper<AntiFraudWorkRecord> {

    /**
     * 添加
     * @param workRecord 工作记录参数
     */
    void add(AntiFraudWorkRecord workRecord);

    /**
     * 根据id更新
     * @param workRecord 工作记录参数
     */
    @Override
    int updateById(AntiFraudWorkRecord workRecord);

    /**
     * 根据id批量删除
     * @param ids id集
     */
    void deleteByIds(List<Long> ids);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(AntiFraudWorkRecordSearchVo search);

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    List<AntiFraudWorkRecordVo> list(AntiFraudWorkRecordSearchVo search);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计
     */
    AntiFraudWorkRecordStatistics listStatistics(AntiFraudWorkRecordSearchVo search);
}
