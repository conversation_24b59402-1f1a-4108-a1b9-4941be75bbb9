package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingUtility;
import com.senox.realty.vo.FirefightingUtilitySearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13 9:24
 */
@Mapper
@Repository
public interface FirefightingUtilityMapper extends BaseMapper<FirefightingUtility> {

    /**
     * 根据位置查找公共消防设施
     * @param list
     * @return
     */
    List<FirefightingUtility> listByLocation(List<FirefightingUtility> list);

    /**
     * 公共消防设施统计
     * @param search
     * @return
     */
    int countUtility(FirefightingUtilitySearchVo search);

    /**
     * 公共消防设施列表
     * @param search
     * @return
     */
    List<FirefightingUtility> listUtility(FirefightingUtilitySearchVo search);
}
