package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.MaintainJob;
import com.senox.realty.vo.MaintainDispatchJobVo;
import com.senox.realty.vo.MaintainJobSearchVo;
import com.senox.realty.vo.MaintainJobVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/3 8:58
 */
@Mapper
@Repository
public interface MaintainJobMapper extends BaseMapper<MaintainJob> {
    /**
     * 添加维修订单
     *
     * @param job
     * @return
     */
    int addMaintainJob(MaintainJob job);

    /**
     * 获取最大的工单号
     *
     * @param prefix
     * @return
     */
    String findMaxJobNo(String prefix);

    /**
     * 查询派发流转
     *
     * @param orderId
     * @return
     */
    List<MaintainJobVo> listDispatchByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据id查询派工单及派发流转
     * @param id
     * @return
     */
    MaintainJobVo findDispatchById(@Param("id") Long id);

    /**
     * 查询派单列表
     *
     * @param searchVo
     * @return
     */
    List<MaintainDispatchJobVo> listDispatchJob(MaintainJobSearchVo searchVo);

    /**
     * 查询派单数量
     *
     * @param searchVo
     * @return
     */
    int count(MaintainJobSearchVo searchVo);

    /**
     * 根据派工子单查询
     * @param itemId
     * @return
     */
    MaintainDispatchJobVo findDispatchByJobItemId(@Param("itemId") Long itemId);

    /**
     * 查询所有未完成的审核节点派工单
     * @return
     */
    List<MaintainJob> findExamineList();

    /**
     * 根据维修单id查询最近一张派工单
     * @param orderId
     * @return
     */
    MaintainJob findLastJobByOrderId(@Param("orderId") Long orderId);

}
