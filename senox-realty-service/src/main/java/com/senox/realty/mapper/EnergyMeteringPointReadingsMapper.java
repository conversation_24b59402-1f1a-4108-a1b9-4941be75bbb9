package com.senox.realty.mapper;

import com.senox.realty.domain.EnergyMeteringPointReadings;
import com.senox.realty.vo.EnergyMeteringPointReadingsSearchVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-08
 **/
public interface EnergyMeteringPointReadingsMapper {

    /**
     * 批量添加表码
     *
     * @param meteringPointReadingsList 计量点读数集
     * @param isNew 是否最新
     */
    void addBatch(List<EnergyMeteringPointReadings> meteringPointReadingsList,boolean isNew);

    /**
     * 批量更新计量点读数
     * @param meteringPointReadingsList 计量点读数集
     * @param isNew 是否最新
     */
    void updateBatch(List<EnergyMeteringPointReadings> meteringPointReadingsList, boolean isNew);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(EnergyMeteringPointReadingsSearchVo search);

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    List<EnergyMeteringPointReadings> list(EnergyMeteringPointReadingsSearchVo search);
}
