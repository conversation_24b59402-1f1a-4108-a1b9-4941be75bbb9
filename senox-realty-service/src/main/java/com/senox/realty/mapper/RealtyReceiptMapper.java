package com.senox.realty.mapper;

import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.realty.domain.RealtyReceipt;
import com.senox.realty.vo.RealtyBillReceiptApplyInfoVo;
import com.senox.realty.vo.RealtyReceiptSearchVo;
import com.senox.realty.vo.RealtyReceiptVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-3-23
 */

public interface RealtyReceiptMapper {

    /**
     * 添加物业发票申请
     *
     * @param realtyReceipt 物业发票申请
     */
    void add(RealtyReceipt realtyReceipt);

    /**
     * 根据id获取物业发票申请
     * @param id 物业发票申请id
     * @return 物业发票申请
     */
    RealtyReceipt findById(Long id);

    /**
     * 绑定发票单据号
     *
     * @param realtyReceiptId     物业发票id
     * @param receiptSerialNoList 发票单据号列表
     */
    void bindReceipt(Long realtyReceiptId, List<String> receiptSerialNoList);

    /**
     * 物业发票申请count
     *
     * @param search 查询参数
     * @return count
     */
    int count(RealtyReceiptSearchVo search);

    /**
     * 物业发票申请列表
     * @param search 查询参数
     * @return 物业发票申请列表
     */
    List<RealtyReceiptVo> list(RealtyReceiptSearchVo search);

    /**
     *  批量更新物业发票申请
     * @param realtyReceiptList 物业发票申请列表
     */
    void updateBatch(List<RealtyReceipt> realtyReceiptList);

    /**
     * 根据物业发票申请id列表获取发票单据号列表
     * @param ids 物业发票申请id列表
     * @return 发票单据号列表
     */
    List<String> receiptSerialNoListById(List<Long> ids);

    /**
     * 发票申请列表
     * @param id 物业发票申请id
     * @param isDetail 是否详细
     * @return 申请列表
     */
    List<ReceiptApplyVo> receiptApplyListByRealtyReceiptId(Long id,Boolean isDetail);

    /**
     * 物业账单发票申请信息列表
     *
     * @param id 物业发票申请id
     * @return 账单发票申请信息列表
     */
    List<RealtyBillReceiptApplyInfoVo> applyBillInfoList(Long id);

    /**
     * 根据单据号列表获取发票申请
     * @param serialNoList 单据号列表
     * @return 发票申请列表
     */
    List<RealtyReceipt> getBySerialNoList(List<String> serialNoList);

    /**
     * 物业发票绑定物业账单
     * @param receiptId 发票申请id
     * @param realtyBillIds 账单id列表
     */
    void realtyReceiptBindRealtyBill(long receiptId, List<Long> realtyBillIds);

    /**
     * 物业发票解绑物业账单
     * @param receiptId 发票申请id
     * @param realtyBillIds 账单id列表
     */
    void realtyReceiptUnbindRealtyBill(long receiptId, List<Long> realtyBillIds);

    /**
     * 刷新物业账单发票状态
     * @param billIds 账单集
     */
    void refreshBillReceiptStatus(List<Long> billIds);
}
