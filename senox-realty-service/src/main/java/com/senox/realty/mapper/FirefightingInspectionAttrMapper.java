package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingInspectionAttr;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/29 15:23
 */
@Mapper
@Repository
public interface FirefightingInspectionAttrMapper extends BaseMapper<FirefightingInspectionAttr> {

    /**
     * 更新巡检变量
     * @param inspectionType
     * @param inspectionId
     * @param code
     * @param version
     * @param attrs
     * @return
     */
    int updateInspectionAttrs(@Param("inspectionType") String inspectionType,
                              @Param("inspectionId") Long inspectionId,
                              @Param("code") String code,
                              @Param("version") Integer version,
                              @Param("attrs") List<FirefightingInspectionAttr> attrs);

    /**
     * 删除巡检变量
     * @param inspectionType
     * @param inspectionId
     * @param code
     * @param version
     * @param attrs
     * @return
     */
    int deleteInspectionAttrs(@Param("inspectionType") String inspectionType,
                              @Param("inspectionId") Long inspectionId,
                              @Param("code") String code,
                              @Param("version") Integer version,
                              @Param("attrs") List<FirefightingInspectionAttr> attrs);
}
