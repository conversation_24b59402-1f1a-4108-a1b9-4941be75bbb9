package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.common.vo.BillTimeVo;
import com.senox.realty.domain.EnergyProfitItem;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.vo.RealtyWeSearchVo;
import com.senox.realty.vo.RealtyWeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-8-11
 */
@Mapper
public interface RealtyWeMapper extends BaseMapper<RealtyWe> {

    /**
     * 更新读数账单id
     * @param list
     */
    void batchUpdateBillById(@Param("list") List<RealtyWe> list);

    /**
     * 更新水电账单读数
     * @param year
     * @param month
     * @param type
     * @param list
     */
    void batchUpdateBillBySerial(@Param("year") Integer year,
                                 @Param("month") Integer month,
                                 @Param("type") Integer type,
                                 @Param("list") List<RealtyWe> list);

    /**
     * 取消关联水电账单
     * @param billIds
     */
    void unlinkWeBill(@Param("list") List<Long> billIds);

    /**
     * 按年月删除读数
     * @param year
     * @param month
     */
    void deleteByYearMonth(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 根据id获取物业水电读数
     * @param ids
     * @return
     */
    List<RealtyWeVo> listByIds(@Param("ids") List<Long> ids);

    /**
     * 最近一次水电读书
     * @param search
     * @return
     */
    List<RealtyWe> listWeLatestData(RealtyWeSearchVo search);

    /**
     * 物业水电读数
     * @param year
     * @param month
     * @param type
     * @return
     */
    List<RealtyWeVo> listMonthReadings(@Param("year") Integer year, @Param("month") Integer month, @Param("type") Integer type);

    /**
     * 统计读数
     * @param search
     * @return
     */
    int countReadings(RealtyWeSearchVo search);

    /**
     * 合计读数
     * @param search
     * @return
     */
    RealtyWeVo sumReadings(RealtyWeSearchVo search);

    /**
     * 读数列表
     * @param search
     * @return
     */
    List<RealtyWeVo> listReadings(RealtyWeSearchVo search);

    /**
     * 水消费统计
     * @param billTime
     * @return
     */
    List<EnergyProfitItem> listWaterProfit(BillTimeVo billTime);

    /**
     * 电消费统计
     * @param billTime
     * @return
     */
    List<EnergyProfitItem> lisElectricProfit(BillTimeVo billTime);
}
