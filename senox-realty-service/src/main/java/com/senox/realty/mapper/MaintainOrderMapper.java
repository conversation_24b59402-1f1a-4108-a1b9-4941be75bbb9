package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.MaintainOrder;
import com.senox.realty.vo.MaintainOrderEvaluateSearchVo;
import com.senox.realty.vo.MaintainOrderSearchVo;
import com.senox.realty.vo.MaintainOrderStatisticVo;
import com.senox.realty.vo.MaintainOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/21 16:50
 */
@Mapper
@Repository
public interface MaintainOrderMapper extends BaseMapper<MaintainOrder> {

    /**
     * 添加维修订单
     * @param order
     * @return
     */
    int addMaintainOrder(MaintainOrder order);

    /**
     * 获取最大的订单号
     * @param prefix
     * @return
     */
    String findMaxOrderNo(String prefix);

    /**
     * 总数
     * @param searchVo
     * @return
     */
    int count(MaintainOrderSearchVo searchVo);

    /**
     * 查询集合
     * @param searchVo
     * @return
     */
    List<MaintainOrderVo> listMaintainOrder(MaintainOrderSearchVo searchVo);

    /**
     * 维修单列表导出所有处理节点
     * @param searchVo
     * @return
     */
    List<MaintainOrderVo> exportListMaintainOrder(MaintainOrderSearchVo searchVo);

    /**
     * 维修单费用统计数量
     * @param searchVo
     * @return
     */
    int countOrderStatistic(MaintainOrderSearchVo searchVo);

    /**
     * 维修单费用统计列表
     * @param searchVo
     * @return
     */
    List<MaintainOrderStatisticVo> listOrderStatistic(MaintainOrderSearchVo searchVo);

    /**
     * 维修单费用合计
     * @param searchVo
     * @return
     */
    MaintainOrderStatisticVo sumOrderStatistic(MaintainOrderSearchVo searchVo);


    /**
     * 维修单评价总数
     * @param searchVo
     * @return
     */
    int evaluateOrderCount(MaintainOrderEvaluateSearchVo searchVo);

    /**
     * 维修单评价列表
     * @param searchVo
     * @return
     */
    List<MaintainOrderVo> evaluateOrderList(MaintainOrderEvaluateSearchVo searchVo);
}
