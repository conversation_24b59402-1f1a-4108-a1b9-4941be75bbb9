package com.senox.realty.mapper;

import com.senox.realty.domain.OneTimeFee;
import com.senox.realty.vo.OneTimeFeeSearchVo;
import com.senox.realty.vo.OneTimeFeeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/11 16:58
 */
@Mapper
@Repository
public interface OneTimeFeeMapper {

    /**
     * 添加一次性费用项目
     * @param fee
     * @return
     */
    int addOneTimeFee(OneTimeFee fee);

    /**
     * 更新一次性费用项目
     * @param fee
     * @return
     */
    int updateOneTimeFee(OneTimeFee fee);

    /**
     * 批量添加一次性费用项目部门
     * @param fee
     * @param departments
     * @return
     */
    int batchAddOneTimeFeeDepartment(@Param("fee") OneTimeFee fee, @Param("departments") List<Long> departments);

    /**
     * 批量删除一次性费用项目部门
     * @param feeId
     * @param departments
     * @return
     */
    int batchDelOneTimeFeeDepartment(@Param("feeId") Long feeId, @Param("departments") List<Long> departments);

    /**
     * 根据id查找收费项目
     * @param id
     * @return
     */
    OneTimeFeeVo findById(Long id);

    /**
     * 根据名字查找收费项目
     * @param name
     * @return
     */
    OneTimeFeeVo findByName(String name);

    /**
     * 根据id查找收费项目详情
     * @param id
     * @return
     */
    OneTimeFeeVo findWithDepartmentById(Long id);

    /**
     * 根据名字查找收费项目详情
     * @param name
     * @return
     */
    OneTimeFeeVo findWithDepartmentByName(String name);

    /**
     * 一次性收费项目部门
     * @param feeId
     * @return
     */
    List<Long> listOneTimeFeeDepartments(Long feeId);

    /**
     * 统计一次性收费项目
     * @return
     */
    int countOneTimeFee(OneTimeFeeSearchVo search);

    /**
     * 一次性收费项目列表
     * @param search
     * @return
     */
    List<OneTimeFeeVo> listOneTimeFee(OneTimeFeeSearchVo search);
}
