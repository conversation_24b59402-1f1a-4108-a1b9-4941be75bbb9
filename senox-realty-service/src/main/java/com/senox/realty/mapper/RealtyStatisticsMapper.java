package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.RealtyStatistics;
import com.senox.realty.vo.StatisticsSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 9:04
 */
@Mapper
public interface RealtyStatisticsMapper extends BaseMapper<RealtyStatistics> {

    /**
     * 物业统计数量
     * @param searchVo
     * @return
     */
    int countRealtyStatistics(StatisticsSearchVo searchVo);

    /**
     * 物业统计列表
     * @param searchVo
     * @return
     */
    List<RealtyStatistics> listRealtyStatistics(StatisticsSearchVo searchVo);
}
