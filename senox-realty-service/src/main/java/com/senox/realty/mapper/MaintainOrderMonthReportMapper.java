package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.MaintainOrderMonthReport;
import com.senox.realty.vo.MaintainOrderMonthReportSearchVo;
import com.senox.realty.vo.MaintainOrderMonthReportVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15 10:38
 */
@Mapper
public interface MaintainOrderMonthReportMapper extends BaseMapper<MaintainOrderMonthReport> {

    /**
     * 总数量
     * @param searchVo
     * @return
     */
    int count(MaintainOrderMonthReportSearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<MaintainOrderMonthReportVo> list(MaintainOrderMonthReportSearchVo searchVo);

    /**
     * 合计
     * @param searchVo
     * @return
     */
    MaintainOrderMonthReportVo sum(MaintainOrderMonthReportSearchVo searchVo);
}
