package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingSmallPlacesInspection;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionBriefVo;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/29 17:17
 */
@Mapper
@Repository
public interface FirefightingSmallPlacesInspectionMapper extends BaseMapper<FirefightingSmallPlacesInspection> {

    /**
     * 三小场所、出租屋消防巡检简要记录合计
     * @param search
     * @return
     */
    int countSmallPlacesInspection(FirefightingSmallPlacesInspectionSearchVo search);

    /**
     * 三小场所、出租屋消防巡检简要记录列表
     * @param search
     * @return
     */
    List<FirefightingSmallPlacesInspectionBriefVo> listSmallPlacesInspection(FirefightingSmallPlacesInspectionSearchVo search);
}
