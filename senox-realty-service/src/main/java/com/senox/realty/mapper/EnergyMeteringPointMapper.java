package com.senox.realty.mapper;

import com.senox.realty.domain.EnergyMeteringPoint;
import com.senox.realty.vo.EnergyMeterPointSearchVo;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-11-08
 **/
public interface EnergyMeteringPointMapper {

    /**
     * 批量添加计量点
     * @param pointList 计量点集
     */
    void addBatch(List<EnergyMeteringPoint> pointList);

    /**
     * 根据id更新计量点
     * @param point 计量点
     */
    void updateById(EnergyMeteringPoint point);

    /**
     * 根据编码批量更新计量点
     * @param pointList 计量点集
     */
    void updateBatchByCode(List<EnergyMeteringPoint> pointList);

    /**
     * 根据id查找计量点
     * @param id 计量点id
     * @return 返回查找到的计量点
     */
    EnergyMeteringPoint findById(Long id);

    /**
     * 根据编码查找计量点
     * @param code 计量点编码
     * @return 返回查找到的计量点
     */
    EnergyMeteringPoint findByCode(String code);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(EnergyMeterPointSearchVo search);

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    List<EnergyMeteringPoint> list(EnergyMeterPointSearchVo search);
}
