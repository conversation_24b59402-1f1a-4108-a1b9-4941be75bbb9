package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.Realty;
import com.senox.realty.vo.RealtyReadingsVo;
import com.senox.realty.vo.RealtySearchVo;
import com.senox.realty.vo.RealtyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/16 9:07
 */
@Mapper
@Repository
public interface RealtyMapper extends BaseMapper<Realty> {


    /**
     * 查找物业及业主信息
     * @param id
     * @return
     */
    RealtyVo findWithOwnerById(Long id);

    /**
     * 查找物业id
     * @param serialNo
     * @return
     */
    Long findIdBySerialNo(String serialNo);

    /**
     * 统计物业
     * @param searchVo
     * @return
     */
    int countRealty(RealtySearchVo searchVo);

    /**
     * 物业列表
     * @param searchVo
     * @return
     */
    List<Realty> listRealty(RealtySearchVo searchVo);

    /**
     * 根据物业号获取物业水电表读数
     * @param list
     * @return
     */
    List<RealtyReadingsVo> listReadings(@Param("list") List<String> list);

    /**
     * 未租赁物业数
     * @return
     */
    int unRentCompanyRealtyNum();

    /**
     * 已租赁物业数
     * @return
     */
    int rentCompanyRealtyNum();

    /**
     * 物业税率列表统计查询
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countListRealtyTaxRate(RealtySearchVo search);

    /**
     * 物业税率列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    List<RealtyVo> listRealtyTaxRate(RealtySearchVo search);
}
