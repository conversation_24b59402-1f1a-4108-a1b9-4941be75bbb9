package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingTemplateVariables;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26 16:46
 */
@Mapper
@Repository
public interface FirefightingTemplateVariablesMapper extends BaseMapper<FirefightingTemplateVariables> {


    /**
     * 更新模板变量
     * @param code
     * @param version
     * @param list
     */
    void updateVariables(@Param("code") String code,
                         @Param("version") Integer version,
                         @Param("list") List<FirefightingTemplateVariables> list);

    /**
     * 删除模板变量
     * @param code
     * @param version
     * @param list
     */
    void deleteVariables(@Param("code") String code,
                         @Param("version") Integer version,
                         @Param("list") List<FirefightingTemplateVariables> list);

    /**
     * 根据模板id删除模板变量
     * @param id
     */
    void deleteVariablesByTemplateId(Long id);
}
