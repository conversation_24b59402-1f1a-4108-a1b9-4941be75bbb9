package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.MaintainOrderDayReport;
import com.senox.realty.vo.MaintainOrderDayReportSearchVo;
import com.senox.realty.vo.MaintainOrderDayReportVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15 10:37
 */
@Mapper
public interface MaintainOrderDayReportMapper extends BaseMapper<MaintainOrderDayReport> {

    /**
     * 总数量
     * @param searchVo
     * @return
     */
    int count(MaintainOrderDayReportSearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<MaintainOrderDayReportVo>  list( MaintainOrderDayReportSearchVo searchVo);

    /**
     * 合计
     * @param searchVo
     * @return
     */
    MaintainOrderDayReportVo sum(MaintainOrderDayReportSearchVo searchVo);
}
