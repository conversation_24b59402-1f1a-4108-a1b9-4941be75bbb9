package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingFile;
import com.senox.realty.vo.FirefightingFileBriefVo;
import com.senox.realty.vo.FirefightingFileSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 9:57
 */
@Mapper
@Repository
public interface FirefightingFileMapper extends BaseMapper<FirefightingFile> {

    /**
     * 店铺消防档案合计
     * @param search
     * @return
     */
    int countFile(FirefightingFileSearchVo search);

    /**
     * 店铺消防档案列表
     * @param search
     * @return
     */
    List<FirefightingFileBriefVo> listFile(FirefightingFileSearchVo search);
}
