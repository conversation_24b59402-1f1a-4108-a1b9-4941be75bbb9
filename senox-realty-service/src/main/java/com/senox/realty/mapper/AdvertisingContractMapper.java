package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.vo.AdvertisingContractListVo;
import com.senox.realty.vo.AdvertisingContractSearchVo;
import com.senox.realty.vo.AdvertisingContractVo;
import com.senox.realty.vo.AdvertisingIncomeVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/19 14:05
 */
@Mapper
@Repository
public interface AdvertisingContractMapper extends BaseMapper<AdvertisingContract> {

    /**
     * 查找最大合同号
     * @param prefix
     * @return
     */
    String findMaxContractNo(String prefix);

    /**
     * 根据id获取合同明细
     * @param id
     * @return
     */
    AdvertisingContractVo findDetailById(Long id);

    /**
     * 广告收益合计
     * @param search
     * @return
     */
    AdvertisingIncomeVo sumContractIncome(AdvertisingContractSearchVo search);

    /**
     * 统计合同
     * @param search
     * @return
     */
    int countContract(AdvertisingContractSearchVo search);

    /**
     * 合同视图列表
     * @param search
     * @return
     */
    List<AdvertisingContractListVo> listContractView(AdvertisingContractSearchVo search);

    /**
     * 合同收益列表
     * @param search
     * @return
     */
    List<AdvertisingIncomeVo> listContractIncome(AdvertisingContractSearchVo search);

}
