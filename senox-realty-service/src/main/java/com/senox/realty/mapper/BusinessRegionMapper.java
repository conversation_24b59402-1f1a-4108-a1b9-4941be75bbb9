package com.senox.realty.mapper;

import com.senox.realty.domain.BusinessRegion;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/15 14:41
 */
@Mapper
@Repository
public interface BusinessRegionMapper {

    /**
     * 添加经营区域
     * @param region
     * @return
     */
    int addRegion(BusinessRegion region);

    /**
     * 修改经营区域
     * @param region
     * @return
     */
    int updateRegion(BusinessRegion region);

    /**
     * 查找经营区域
     * @param id
     * @return
     */
    BusinessRegion findById(Long id);

    /**
     * 经营区域列表
     * @return
     */
    List<BusinessRegion> listAll();
}
