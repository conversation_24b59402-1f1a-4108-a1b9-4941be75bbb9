package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.EnergyProfitItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 14:45
 */
@Mapper
@Repository
public interface EnergyProfitItemMapper extends BaseMapper<EnergyProfitItem> {


    /**
     * 批量更新能源损益明细
     * @param profitId
     * @param list
     * @return
     */
    int batchUpdateProfitItem(@Param("profitId") Long profitId, @Param("list") List<EnergyProfitItem> list);
}
