package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.MaintainMaterialItem;
import com.senox.realty.vo.MaintainMaterialItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/25 10:45
 */
@Mapper
public interface MaintainMaterialItemMapper extends BaseMapper<MaintainMaterialItem> {

    /**
     * 根据订单号和派单号查询维修物料
     * @param orderId
     * @param jobId
     * @return
     */
    List<MaintainMaterialItemVo> findByOrderIdAndJobId(@Param("orderId") Long orderId, @Param("jobId") Long jobId);

    /**
     * 根据订单号查询维修物料
     * @param orderId
     * @return
     */
    List<MaintainMaterialItemVo> findByOrderId(@Param("orderId") Long orderId);

}
