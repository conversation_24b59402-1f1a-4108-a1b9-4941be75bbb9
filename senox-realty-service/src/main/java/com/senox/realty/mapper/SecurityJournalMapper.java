package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.SecurityJournal;
import com.senox.realty.vo.SecurityJournalSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26 15:13
 */
@Mapper
@Repository
public interface SecurityJournalMapper extends BaseMapper<SecurityJournal> {


    /**
     * 安保日志合计
     * @param search
     * @return
     */
    int countJournal(SecurityJournalSearchVo search);

    /**
     * 安保日志列表
     * @param search
     * @return
     */
    List<SecurityJournal> listJournal(SecurityJournalSearchVo search);
}
