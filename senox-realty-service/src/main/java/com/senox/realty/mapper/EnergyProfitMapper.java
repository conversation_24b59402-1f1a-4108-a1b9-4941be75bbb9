package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.EnergyProfit;
import com.senox.realty.vo.EnergyProfitSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 14:45
 */
@Mapper
@Repository
public interface EnergyProfitMapper extends BaseMapper<EnergyProfit> {

    /**
     * 能源损益合计
     * @param search
     * @return
     */
    int countProfit(EnergyProfitSearchVo search);

    /**
     * 能源损益列表
     * @param search
     * @return
     */
    List<EnergyProfit> listProfit(EnergyProfitSearchVo search);
}
