package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.MaintainJobItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/8/28 16:26
 */
@Mapper
public interface MaintainJobItemMapper extends BaseMapper<MaintainJobItem> {

    /**
     * 查询最近一次为审核节点的维修员
     * @param orderId
     * @return
     */
    MaintainJobItem findLastExamineByOrderId(@Param("orderId") Long orderId);

}
