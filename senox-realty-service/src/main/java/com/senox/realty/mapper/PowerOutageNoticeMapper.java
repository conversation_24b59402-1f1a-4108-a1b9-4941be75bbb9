package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.PowerOutageNotice;
import com.senox.realty.vo.PowerOutageNoticeSearchVo;
import com.senox.realty.vo.PowerOutageNoticeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-08
 **/
public interface PowerOutageNoticeMapper extends BaseMapper<PowerOutageNotice> {

    /**
     * 根据id查找
     * @param id id
     * @return 返回查询到的数据
     */
    PowerOutageNoticeVo findById(Long id);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(PowerOutageNoticeSearchVo search);

    /**
     * 列表
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    List<PowerOutageNoticeVo> list(PowerOutageNoticeSearchVo search);
}
