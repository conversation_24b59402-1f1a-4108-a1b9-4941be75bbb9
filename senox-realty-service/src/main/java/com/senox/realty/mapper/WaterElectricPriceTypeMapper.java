package com.senox.realty.mapper;

import com.senox.realty.domain.WaterElectricPriceType;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/9 14:02
 */
@Mapper
@Repository
public interface WaterElectricPriceTypeMapper {

    /**
     * 添加价格类别
     * @param priceType
     * @return
     */
    int addPriceType(WaterElectricPriceType priceType);

    /**
     * 更新价格类别
     * @param priceType
     * @return
     */
    int updatePriceType(WaterElectricPriceType priceType);

    /**
     * 根据id查找价格类别
     * @param id
     * @return
     */
    WaterElectricPriceType findById(Long id);

    /**
     * 价格类别列表
     * @return
     */
    List<WaterElectricPriceType> listAll();

}
