package com.senox.realty.mapper;

import com.senox.realty.domain.Fee;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/15 11:49
 */
@Mapper
@Repository
public interface FeeMapper {

    /**
     * 添加费项
     * @param fee
     * @return
     */
    int addFee(Fee fee);

    /**
     * 更新费项
     * @param fee
     * @return
     */
    int updateFee(Fee fee);


    /**
     * 查找费项
     * @param id
     * @return
     */
    Fee findById(Long id);

    /**
     * 查找费项
     * @param name
     * @return
     */
    Fee findByName(String name);

    /**
     * 费项列表
     * @return
     */
    List<Fee> listAll();
}
