package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.RealtyBillWe;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyBillWeSearchVo;
import com.senox.realty.vo.RealtyBillWeVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-7-18
 */
public interface RealtyBillWeMapper extends BaseMapper<RealtyBillWe> {

    /**
     * 更新水电账单物业账单id
     * @param ids
     * @param billId
     * @return
     */
    int updateWeBillId(@Param("ids") List<Long> ids, @Param("billId") Long billId);

    /**
     * 按合同类型更新水电账单物业账单id
     * @param weTime
     * @param billTime
     * @param contractType
     * @return
     */
    int updateWeBillIdByContractType(@Param("weTime") BillMonthVo weTime,
                                     @Param("billTime") BillMonthVo billTime,
                                     @Param("contractType") Integer contractType);


    /**
     * 删除多余的水电账单
     * @param weTime
     * @return
     */
    int removeAbandonWeBill(BillMonthVo weTime);

    /**
     * 解除水电物业账单关联
     * @param billId
     * @return
     */
    int unlinkWeBill(Long billId);

    /**
     * 解除水电物业账单关联
     * @param year
     * @param month
     * @return
     */
    int unlinkWeBillByYearMonth(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 待生成水电账单数据
     * @param weTime
     * @return
     */
    List<RealtyBillWe> listGeneratingBill(BillMonthVo weTime);

    /**
     * 待生成副表水电账单数据
     * @param weTime
     * @return
     */
    List<RealtyBillWe> listGeneratingAliasBill(BillMonthVo weTime);

    /**
     * 待重新生成水电账单列表
     * @param weTime
     * @return
     */
    List<RealtyBillWe> listGeneratedBill(BillMonthVo weTime);

    /**
     * 根据 id 查找水电账单
     * @param id
     * @return
     */
    RealtyBillWeVo findById(Long id);

    /**
     * 查找最新的水电账单
     * @param realtySerial
     * @return
     */
    RealtyBillWe findLatestWeBill(@RequestParam String realtySerial);

    /**
     * 水电账单合计
     * @param search
     * @return
     */
    RealtyBillWeVo sumWeBill(RealtyBillWeSearchVo search);

    /**
     * 水电账单统计计
     * @param search
     * @return
     */
    int countWeBill(RealtyBillWeSearchVo search);

    /**
     * 水电账单列表
     * @param search 查询
     * @return
     */
    List<RealtyBillWeVo> listWeBill(RealtyBillWeSearchVo search);
}
