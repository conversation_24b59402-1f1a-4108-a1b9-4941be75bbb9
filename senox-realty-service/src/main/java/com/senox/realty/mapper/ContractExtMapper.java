package com.senox.realty.mapper;

import com.senox.realty.domain.ContractExt;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/2/19 10:53
 */
@Mapper
@Repository
public interface ContractExtMapper {

    /**
     * 添加合同扩展信息
     * @param contractExt
     * @return
     */
    int addContractExt(ContractExt contractExt);

    /**
     * 更新合同扩展信息
     * @param contractExt
     * @return
     */
    int updateContractExt(ContractExt contractExt);

    /**
     * 根据合同id获取合同扩展信息
     * @param contractId
     * @return
     */
    ContractExt findExtByContractId(Long contractId);
}
