package com.senox.realty.mapper;

import com.senox.dm.vo.EnergyRtuSearchVo;
import com.senox.realty.domain.EnergyRtu;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/24 11:10
 */
public interface EnergyRtuMapper {

    /**
     * 添加集中器
     * @param rtu 集中器
     * @return 返回集中器id
     */
    int add(EnergyRtu rtu);

    /**
     * 批量添加集中器
     * @param rtuList 集中器集
     */
    void addBatch(List<EnergyRtu> rtuList);

    /**
     * 根据id更新集中器
     * @param rtu 集中器
     */
    void updateById(EnergyRtu rtu);

    /**
     * 根据编码更新集中器
     * @param rtu 集中器
     */
    void updateByCode(EnergyRtu rtu);

    /**
     * 根据id查找
     * @param id 集中器id
     * @return 返回查找到的集中器
     */
    EnergyRtu findById(Long id);

    /**43443533rr454444444
     * 根据编码查找
     * @param code 集中器编码
     * @return 返回查找到的集中器
     */
    EnergyRtu findByCode(String code);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(EnergyRtuSearchVo search);

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    List<EnergyRtu> list(EnergyRtuSearchVo search);

}
