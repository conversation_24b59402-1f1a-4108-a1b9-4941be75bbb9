package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingInspectTask;
import com.senox.realty.vo.FirefightingInspectTaskSearchVo;
import com.senox.realty.vo.FirefightingInspectTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/14 11:37
 */
@Mapper
@Repository
public interface FirefightingInspectTaskMapper extends BaseMapper<FirefightingInspectTask> {

    /**
     * 获取消防巡检任务
     * @param id
     * @return
     */
    FirefightingInspectTaskVo findVoById(Long id);

    /**
     * 消防巡检任务统计
     * @param search
     * @return
     */
    int countTask(FirefightingInspectTaskSearchVo search);


    /**
     * 消防巡检任务列表
     * @param search
     * @return
     */
    List<FirefightingInspectTaskVo> listTask(FirefightingInspectTaskSearchVo search);
}
