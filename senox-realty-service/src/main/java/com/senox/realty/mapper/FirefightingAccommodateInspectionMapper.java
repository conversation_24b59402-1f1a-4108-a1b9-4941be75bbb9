package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingAccommodateInspection;
import com.senox.realty.vo.FirefightingAccommodateInspectionBriefVo;
import com.senox.realty.vo.FirefightingAccommodateInspectionSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/8 15:27
 */
@Mapper
@Repository
public interface FirefightingAccommodateInspectionMapper extends BaseMapper<FirefightingAccommodateInspection> {

    /**
     * 违规住人巡检统计
     * @param search
     * @return
     */
    int countInspection(FirefightingAccommodateInspectionSearchVo search);

    /**
     * 违规住人巡检列表
     * @param search
     * @return
     */
    List<FirefightingAccommodateInspectionBriefVo> listInspection(FirefightingAccommodateInspectionSearchVo search);
}
