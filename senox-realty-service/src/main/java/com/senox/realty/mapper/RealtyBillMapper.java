package com.senox.realty.mapper;

import com.senox.common.vo.BillCancelVo;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.realty.domain.RealtyBill;
import com.senox.realty.domain.RealtyBillItem;
import com.senox.realty.domain.RealtyStatistics;
import com.senox.realty.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/8 11:40
 */
@Mapper
@Repository
public interface RealtyBillMapper {

    /**
     * 添加物业应收账单
     *
     * @param bill
     * @return
     */
    int addBill(RealtyBill bill);

    /**
     * 更新物业账单
     *
     * @param bill
     * @return
     */
    int updateBill(RealtyBill bill);

    /**
     * 更新账单金额
     * @param billTime
     * @return
     */
    int updateBillAmount(BillMonthVo billTime);

    /**
     * 更新账单水电金额
     * @param billTime
     * @return
     */
    int updateBillWeAmount(BillMonthVo billTime);

    /**
     * 更新水电日期
     * @param billTime
     * @param attr1
     * @param attr2
     * @return
     */
    int updateBillWeAttr(@Param("billTime") BillMonthVo billTime, @Param("attr1") String attr1, @Param("attr2") String attr2);

    /**
     * 减免滞纳金
     *
     * @param penaltyIgnore
     * @return
     */
    int updateBillPenaltyIgnore(BillPenaltyIgnoreVo penaltyIgnore);

    /**
     * 发票
     * @param batchBills
     * @return
     */
    int updateBillReceipt(RealtyBillBatchVo batchBills);

    /**
     * 下发物业账单
     *
     * @param sendVo
     * @return
     */
    int sendBill(RealtyBillSendVo sendVo);

    /**
     * 停用账单
     * @param suspendDto
     * @return
     */
    int suspendBill(RealtyContractSuspendDto suspendDto);

    /**
     * 通过账单id更新物业账单状态
     *
     * @param billPaid
     * @return
     */
    int updateBillPaidById(BillPaidVo billPaid);

    /**
     * 通过支付订单id更新物业账单状态
     *
     * @param billPaid
     * @return
     */
    int updateBillPaidByRemoteOrder(BillPaidVo billPaid);

    /**
     * 通过账单id物业退费
     *
     * @param billPaid
     * @return
     */
    int updateBillRefundById(BillPaidVo billPaid);

    /**
     * 通过退费订单id物业退费
     *
     * @param billPaid
     * @return
     */
    int updateBillRefundByRemoteOrder(BillPaidVo billPaid);

    /**
     * 撤销支付
     * @param cancel
     * @return
     */
    int cancelBillPaid(BillCancelVo cancel);

    /**
     * 删除应收账单
     * @param id
     * @return
     */
    int deleteBillById(Long id);

    /**
     * 删除停用的账单
     * @param billMonth
     * @return
     */
    int deleteZeroBill(BillMonthVo billMonth);

    /**
     * 按年月删除应收账单
     * @param year
     * @param month
     * @return
     */
    int deleteBillByYearMonth(Integer year, Integer month);

    /**
     * 月账单是否已下发
     *
     * @param year
     * @param month
     * @return
     */
    Long findMonthlyBillSend(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 通过账单id查找物业账单
     *
     * @param billId
     * @param year
     * @param month
     * @return
     */
    RealtyBill findMonthlyBillById(@Param("billId") Long billId,
                                   @Param("year") Integer year,
                                   @Param("month") Integer month);

    /**
     * 根据合同查找物业月账单
     *
     * @param contractNo
     * @param year
     * @param month
     * @return
     */
    RealtyBill findMonthlyBillByContractNo(@Param("contractNo") String contractNo,
                                           @Param("year") Integer year,
                                           @Param("month") Integer month);

    /**
     * 根据合同查找物业月账单列表
     *
     * @param contractNo
     * @param year
     * @param month
     * @return
     */
    List<RealtyBillVo> findMonthlyBillListByContractNo(@Param("contractNo") String contractNo,
                                           @Param("year") Integer year,
                                           @Param("month") Integer month);

    /**
     * 根据id查找物业账单
     *
     * @param id
     * @return
     */
    RealtyBillVo findBillById(Long id);

    /**
     * 根据账单id列表查找物业账单列表
     *
     * @param ids
     * @param isWithDetail
     * @return
     */
    List<RealtyBillVo> listBillById(@Param("ids") List<Long> ids, @Param("isWithDetail") boolean isWithDetail);

    /**
     * 根据远程订单id查找物业账单
     *
     * @param orderId
     * @return
     */
    List<RealtyBillVo> listBillByRemoteOrderId(Long orderId);

    /**
     * 根据退费订单id查找物业账单
     *
     * @param orderId
     * @return
     */
    List<RealtyBillVo> listBillByRefundOrderId(Long orderId);

    /**
     * 已支付账单数
     * @param ids
     * @return
     */
    int countPaidBills(@Param("list") List<Long> ids);

    /**
     * 统计物业账单
     *
     * @param search
     * @return
     */
    int countRealtyBill(RealtyBillSearchVo search);

    /**
     * 物业账单列表
     *
     * @param search
     * @return
     */
    List<RealtyBillVo> listBill(RealtyBillSearchVo search);

    /**
     * 已开票账单列表
     *
     * @param search
     * @return
     */
    List<RealtyBillVo> listReceiptBill(RealtyBillSearchVo search);

    /**
     * 已开票账单列表金额合计
     * @param search 查询参数
     */
    RealtyBillVo sumReceiptBillAmount(RealtyBillSearchVo search);

    /**
     * 物业账单合计
     *
     * @param search
     * @return
     */
    RealtyBillVo sumBill(RealtyBillSearchVo search);

    /**
     * 物业账单带滞纳金费率列表
     *
     * @param search
     * @return
     */
    List<RealtyBillVo> listBillWithPenaltyRate(RealtyBillSearchVo search);

    /**
     * 物业账单明细合计
     *
     * @param search
     * @return
     */
    RealtyBillVo sumBillWithDetail(RealtyBillSearchVo search);

    /**
     * 物业账单明细列表
     *
     * @param search
     * @return
     */
    List<RealtyBillVo> listBillWithDetail(RealtyBillSearchVo search);

    /**
     * 物业账单明细列表信息
     * @param searchVo
     * @return
     */
    List<RealtyBillVo> listBillWithDetailInfo(RealtyBillSearchVo searchVo);

    /**
     * 批量添加物业账单费项
     *
     * @param billItems
     * @return
     */
    int batchAddBillItems(@Param("billItems") List<RealtyBillItem> billItems);

    /**
     * 批量更新物业账单费项
     *
     * @param billItems
     * @return
     */
    int batchUpdateBillItems(@Param("billItems") List<RealtyBillItem> billItems);

    /**
     * 修改应收账单费项
     * @param change
     * @return
     */
    int changeBillFee(BillFeeChangeVo change);

    /**
     * 更新明细支付状态
     *
     * @param billIds
     * @param paidTime
     * @return
     */
    int updateBillItemStatus(@Param("billIds") List<Long> billIds, @Param("paidTime") LocalDateTime paidTime);

    /**
     * 支付订单明细退费
     *
     * @param billIds
     * @return
     */
    int refundBillItem(@Param("billIds") List<Long> billIds);

    /**
     * 撤销支付
     * @param cancel
     * @return
     */
    int cancelBillItemPaid(BillCancelVo cancel);

    /**
     * 批量删除物业账单费项
     *
     * @param billId
     * @param ids
     * @return
     */
    int batchDelBillItems(@Param("billId") Long billId, @Param("ids") List<Long> ids);

    /**
     * 删除应收账单明细
     * @param billId
     * @return
     */
    int deleteBillItems(Long billId);

    /**
     * 按年月删除应收账单明细
     * @param year
     * @param month
     * @return
     */
    int deleteBillItemsByYearMonth(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查找账单明细
     *
     * @param billId
     * @return
     */
    List<RealtyBillItem> listBillItems(Long billId);

    /**
     * 查找账单明细
     *
     * @param billIds
     * @return
     */
    List<RealtyBillItem> listBillItemByBillIds(List<Long> billIds);

    /**
     * 根据发票单据编号查询账单明细
     * @param receiptSerialNoList 发票单据编号列表
     * @return 账单明细列表
     */
    List<RealtyBillItem> listBillItemByReceiptSerial(List<String> receiptSerialNoList);

    /**
     * 批量更新账单费项发票状态
     * @param billItemList 账单费项列表
     */
    void batchUpdateBillItemReceipt(List<RealtyBillItem> billItemList);

    void updateBillItemReceipt(RealtyBillItem billItem);

    /**
     * 已开票账单列表count
     *
     * @param search 查询参数
     * @return count
     */
    Integer countReceiptRealtyBill(RealtyBillSearchVo search);

    /**
     * 应收金额统计
     * @param generateVo
     * @return
     */
    RealtyStatistics realtyBillStatistics(StatisticsGenerateVo generateVo);
}
