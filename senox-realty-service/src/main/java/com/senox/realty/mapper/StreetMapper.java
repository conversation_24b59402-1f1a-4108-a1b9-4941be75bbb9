package com.senox.realty.mapper;

import com.senox.realty.domain.Street;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/19 17:20
 */
@Mapper
@Repository
public interface StreetMapper {

    /**
     * 添加街道
     * @param street
     * @return
     */
    int addStreet(Street street);

    /**
     * 更新街道
     * @param street
     * @return
     */
    int updateStreet(Street street);


    /**
     * 查找街道
     * @param id
     * @return
     */
    Street findById(Long id);

    /**
     * 街道列表
     * @return
     */
    List<Street> listAll();

    /**
     * 区域街道
     * @param regionId
     * @return
     */
    List<Street> listRegionStreet(Long regionId);
}
