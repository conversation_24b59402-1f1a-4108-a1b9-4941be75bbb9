package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.AdvertisingPayoff;
import com.senox.realty.vo.AdvertisingPayoffDetailVo;
import com.senox.realty.vo.AdvertisingPayoffSearchVo;
import com.senox.realty.vo.AdvertisingPayoffVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/3 8:43
 */
@Mapper
@Repository
public interface AdvertisingPayoffMapper extends BaseMapper<AdvertisingPayoff> {

    /**
     * 合计应付账单
     * @param search
     * @return
     */
    AdvertisingPayoffDetailVo sumPayoff(AdvertisingPayoffSearchVo search);

    /**
     * 统计应付账单数
     * @param search
     * @return
     */
    int countPayoff(AdvertisingPayoffSearchVo search);

    /**
     * 应付账单列表
     * @param search
     * @return
     */
    List<AdvertisingPayoffDetailVo> listPayoff(AdvertisingPayoffSearchVo search);
}
