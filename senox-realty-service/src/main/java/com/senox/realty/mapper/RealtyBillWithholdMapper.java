package com.senox.realty.mapper;

import com.senox.realty.domain.BankWithhold;
import com.senox.realty.vo.BankOfferRealtyBillVo;
import com.senox.realty.vo.RealtyBillSearchVo;
import com.senox.realty.vo.WithholdPaidVo;
import com.senox.realty.vo.WithholdSumVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/17 11:56
 */
@Mapper
@Repository
public interface RealtyBillWithholdMapper {

    /**
     * 物业月账单银行托收报盘
     * @param withhold
     * @return
     */
    int addBillBankWithhold(BankWithhold withhold);

    /**
     * 银行账单托收免滞纳金
     * @param withhold
     * @return
     */
    int updateBillBankWithholdIgnorePenalty(BankWithhold withhold);

    /**
     * 删除物业月账单银行托收报盘记录
     * @param year
     * @param month
     * @return
     */
    int deleteBillBankWithhold(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 物业月账单银行托收回盘
     * @param paid
     * @return
     */
    int updateBillBankWithholdBack(WithholdPaidVo paid);


    /**
     * 查找物业银行托收账单
     * @param year
     * @param month
     * @param realtySerial
     * @return
     */
    List<BankOfferRealtyBillVo> listRealtyBankOfferBill(@Param("year") Integer year, @Param("month") Integer month,
                                                        @Param("realtySerial") String realtySerial);

    /**
     * 银行托收物业账单数量（待申请）
     * @param search
     * @return
     */
    int countBankOfferBillApplying(RealtyBillSearchVo search);

    /**
     * 银行托收物业账单列表（待申请）
     * @param search
     * @return
     */
    List<BankOfferRealtyBillVo> listBankOfferBillApplying(RealtyBillSearchVo search);

    /**
     * 合计银行托收物业账单金额（待申请）
     * @param search
     * @return
     */
    WithholdSumVo sumBankOfferBillApplying(RealtyBillSearchVo search);

    /**
     * 银行托收物业账单数量（已提交）
     * @param search
     * @return
     */
    int countBankOfferBillSubmitted(RealtyBillSearchVo search);

    /**
     * 银行托收物业账单列表（已提交）
     * @param search
     * @return
     */
    List<BankOfferRealtyBillVo> listBankOfferBillSubmitted(RealtyBillSearchVo search);

    /**
     * 合计银行托收物业账单金额（已提交）
     * @param search
     * @return
     */
    WithholdSumVo sumBankOfferBillSubmitted(RealtyBillSearchVo search);
}
