package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingTemplate;
import com.senox.realty.vo.FireFightingTemplateSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 9:13
 */
@Mapper
@Repository
public interface FirefightingTemplateMapper extends BaseMapper<FirefightingTemplate> {

    /**
     * 消防告知单模板统计
     * @param search
     * @return
     */
    int countTemplate(FireFightingTemplateSearchVo search);


    /**
     * 消防告知单模板列表
     * @param search
     * @return
     */
    List<FirefightingTemplate> listTemplate(FireFightingTemplateSearchVo search);
}
