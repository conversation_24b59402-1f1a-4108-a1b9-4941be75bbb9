package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.common.vo.BillPaidVo;
import com.senox.realty.domain.MaintainCharge;
import com.senox.realty.vo.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/6 11:53
 */
@Mapper
public interface MaintainChargeMapper extends BaseMapper<MaintainCharge> {


    /**
     * 获取最大的收费单号
     *
     * @param prefix
     * @return
     */
    String findMaxChargeNo(String prefix);

    /**
     * 统计物维账单
     * @param searchVo
     * @return
     */
    int countMaintainCharge(MaintainChargeSearchVo searchVo);

    /**
     * 物维账单列表
     * @param searchVo
     * @return
     */
    List<MaintainChargeDataVo> listMaintainCharge(MaintainChargeSearchVo searchVo);

    /**
     * 根据id获取账单信息
     * @param id
     * @return
     */
    MaintainChargeDataVo chargeDataVoById(Long id);

    /**
     * 根据派工id获取账单信息
     * @param jobId
     * @return
     */
    MaintainChargeDataVo chargeDataVoByJobId(Long jobId);

    /**
     * 物维账单明细合计
     * @param searchVo
     * @return
     */
    MaintainChargeDataVo sumMaintainCharge(MaintainChargeSearchVo searchVo);

    /**
     * 通过收费账单id更新收费账单状态
     * @param billPaid
     * @return
     */
    int updateChargeStatusById(BillPaidVo billPaid);

    /**
     * 通过订单id更新收费账单状态
     * @param billPaid
     * @return
     */
    int updateChargeStatusByOrderId(BillPaidVo billPaid);

    /**
     * 更新票据号
     * @param chargeSerial
     * @return
     */
    int updateChargeSerial(MaintainChargeSerialVo chargeSerial);

    /**
     * 根据派工id查询账单
     * @param jobId
     * @return
     */
    MaintainCharge findChargeByJobId(Long jobId);

    /**
     * 根据订单id获取账单信息
     * @param orderId
     * @return
     */
    List<MaintainChargeDataVo> listChargeDataVoByOrderId(Long orderId);

    /**
     * 更新账单备注
     * @param chargeRemarkList
     * @return
     */
    int updateChargeRemark(List<MaintainChargeRemarkVo> chargeRemarkList);

}
