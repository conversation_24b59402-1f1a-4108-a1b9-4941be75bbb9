package com.senox.realty.mapper;

import com.senox.common.vo.BillPaidVo;
import com.senox.realty.domain.OneTimeFeeBill;
import com.senox.realty.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/14 9:21
 */
@Mapper
@Repository
public interface OneTimeFeeBillMapper {

    /**
     * 添加一次性收费账单
     * @param bill
     * @return
     */
    int addOneTimeFeeBill(OneTimeFeeBill bill);

    /**
     * 更新一次性收费账单
     * @param bill
     * @return
     */
    int updateOneTimeFeeBill(OneTimeFeeBill bill);

    /**
     * 更新一次性收费账单票据号
     * @param serial
     * @return
     */
    int updateOneTimeFeeBillSerial(TollSerialVo serial);

    /**
     * 支付一次性收费账单
     * @param toll
     * @return
     */
    int updateOneTimeFeeBillPaidStatus(BillTollVo toll);

    /**
     * 撤销一次性收费
     * @param toll
     * @return
     */
    int revokeOneTimeFeeBillPaid(BillTollVo toll);

    /**
     * 一次性收费退费
     * @param toll
     * @return
     */
    int refundOneTimeFeeBill(BillTollVo toll);

    /**
     * 撤销一次性收费退费
     * @param toll
     * @return
     */
    int revokeOneTimeFeeRefund(BillTollVo toll);

    /**
     * 获取最大的账单号
     * @param prefix
     * @return
     */
    String findMaxBillNo(String prefix);

    /**
     * 根据id查找一次性收费账单
     * @param id
     * @return
     */
    OneTimeFeeBillVo findById(Long id);

    /**
     * 根据id批量查找一次性收费账单
     * @param ids
     * @return
     */
    List<OneTimeFeeBillVo> listByIds(List<Long> ids);

    /**
     * 根据票据号查找一次性收费账单
     * @param billNo
     * @return
     */
    OneTimeFeeBillVo findByBillNo(String billNo);

    /**
     * 根据id查找一次性收费账单明细
     * @param id
     * @return
     */
    OneTimeFeeBillTradeVo findWithDetailById(Long id);

    /**
     * 一次性收费账单合计
     * @param search
     * @return
     */
    OneTimeFeeBillSummaryVo sumOneTimeFeeBill(OneTimeFeeBillSearchVo search);

    /**
     * 统计一次性收费账单数
     * @param search
     * @return
     */
    int countOneTimeFeeBill(OneTimeFeeBillSearchVo search);

    /**
     * 一次性收费账单列表
     * @param search
     * @return
     */
    List<OneTimeFeeBillTradeVo> listOneTimeFeeBill(OneTimeFeeBillSearchVo search);

    /**
     * 一次性收费账单交易合计
     * @param search
     * @return
     */
    OneTimeFeeBillSummaryVo sumOneTimeFeeBillTrade(OneTimeFeeBillTradeSearchVo search);

    /**
     * 统计一次性收费账单交易明细记录数
     * @param search
     * @return
     */
    int countOneTimeFeeBillTrade(OneTimeFeeBillTradeSearchVo search);

    /**
     * 一次性收费账单交易明细
     * @param search
     * @return
     */
    List<OneTimeFeeBillTradeVo> listOneTimeFeeBillTrade(OneTimeFeeBillTradeSearchVo search);

    /**
     * 根据账单id更新一次性费用账单状态
     *
     * @param billPaid
     * @return
     */
    int updateBillPaidById(BillPaidVo billPaid);

    /**
     * 根据支付订单id更新一次性费用账单状态
     * @param billPaid
     * @return
     */
    int updateBillPaidByRemoteOrder(BillPaidVo billPaid);


    /**
     * 通过账单id一次性收费退费
     * @param billPaid
     * @return
     */
    int updateBillRefundById(BillPaidVo billPaid);

    /**
     * 通过支付订单id一次性收费退费
     * @param billPaid
     * @return
     */
    int updateBillRefundByRemoteOrder(BillPaidVo billPaid);

    /**
     * 冷藏押金
     * @param search
     * @return
     */
    List<OneTimeFeeDepositVo> listOneTimeFeeDeposit(OneTimeFeeDepositSearchVo search);
}
