package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.ContractRents;
import com.senox.realty.vo.ContractRentsSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ContractRentsMapper extends BaseMapper<ContractRents> {

    /**
     * 查询赁合同的租金，管理费，押金，押金状态，代租合同号，业主等
     * @param contractRents
    * @return
     * */
    List<ContractRents> contractRentsList(ContractRentsSearchVo contractRents);
}
