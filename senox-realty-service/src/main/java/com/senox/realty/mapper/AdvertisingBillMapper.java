package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillReceiptBriefVo;
import com.senox.realty.domain.AdvertisingBill;
import com.senox.realty.domain.AdvertisingStatistics;
import com.senox.realty.vo.AdvertisingBillSearchVo;
import com.senox.realty.vo.AdvertisingBillVo;
import com.senox.realty.vo.StatisticsGenerateVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/25 15:49
 */
@Mapper
@Repository
public interface AdvertisingBillMapper extends BaseMapper<AdvertisingBill> {

    /**
     * 添加广告应收账单
     * @param bill
     * @return
     */
    int addBill(AdvertisingBill bill);

    /**
     * 根据账单id更新账单支付结果
     * @param billPaid
     * @return
     */
    int updateBillPaidById(BillPaidVo billPaid);

    /**
     * 根据支付订单id更新账单支付结果
     * @param billPaid
     * @return
     */
    int updateBillPaidByRemoteOrder(BillPaidVo billPaid);

    /**
     * 更新开票信息
     * @param receipt
     * @return
     */
    int updateBillReceipt(BillReceiptBriefVo receipt);

    /**
     * 根据id查找应收账单
     * @param ids
     * @return
     */
    List<AdvertisingBillVo> listByIds(List<Long> ids);

    /**
     * 广告应收账单合计
     * @param search
     * @return
     */
    AdvertisingBill sumBill(AdvertisingBillSearchVo search);

    /**
     * 广告应收账单统计
     * @param search
     * @return
     */
    int countBill(AdvertisingBillSearchVo search);

    /**
     * 广告应收账单列表
     * @param search
     * @return
     */
    List<AdvertisingBillVo> listBill(AdvertisingBillSearchVo search);

    /**
     * 广告位应收统计
     * @param generateVo
     * @return
     */
    AdvertisingStatistics advertisingBillStatistics(StatisticsGenerateVo generateVo);
}
