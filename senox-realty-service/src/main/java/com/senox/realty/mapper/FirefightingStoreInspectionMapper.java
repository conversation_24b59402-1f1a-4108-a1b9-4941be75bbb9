package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingStoreInspection;
import com.senox.realty.vo.FirefightingStoreInspectionSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 11:02
 */
@Mapper
@Repository
public interface FirefightingStoreInspectionMapper extends BaseMapper<FirefightingStoreInspection> {

    /**
     * 商铺消防巡检记录统计
     * @param search
     * @return
     */
    int countStoreInspection(FirefightingStoreInspectionSearchVo search);

    /**
     * 商铺消防巡检记录查询列表
     * @param search
     * @return
     */
    List<FirefightingStoreInspection> listStoreInspection(FirefightingStoreInspectionSearchVo search);
}
