package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingUtilityInspection;
import com.senox.realty.vo.FirefightingUtilityInspectionSearchVo;
import com.senox.realty.vo.FirefightingUtilityInspectionVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13 16:40
 */
@Mapper
@Repository
public interface FirefightingUtilityInspectionMapper extends BaseMapper<FirefightingUtilityInspection> {

    /**
     * 公共消防设施巡检合计
     * @param search
     * @return
     */
    int countInspection(FirefightingUtilityInspectionSearchVo search);

    /**
     * 公共消防设施巡检列表
     * @param search
     * @return
     */
    List<FirefightingUtilityInspectionVo> listInspection(FirefightingUtilityInspectionSearchVo search);
}
