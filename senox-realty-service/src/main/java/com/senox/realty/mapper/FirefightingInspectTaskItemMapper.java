package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingInspectTaskItem;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.vo.FirefightingInspectTaskItemSearchVo;
import com.senox.realty.vo.FirefightingInspectPropertyTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/14 13:38
 */
@Mapper
@Repository
public interface FirefightingInspectTaskItemMapper extends BaseMapper<FirefightingInspectTaskItem> {


    /**
     * 任务统计
     * @param search
     * @return
     */
    int countTaskItem(FirefightingInspectTaskItemSearchVo search);

    /**
     * 任务列表
     * @param search
     * @return
     */
    List<FirefightingInspectPropertyTaskVo> listTaskItem(FirefightingInspectTaskItemSearchVo search);

    /**
     * 根据履行的任务查找巡检任务
     * @param fulfilledEvent
     * @return
     */
    List<FirefightingInspectTaskItem> listTaskItemByFulfilledEvent(InspectTaskFulfilledEvent fulfilledEvent);
}
