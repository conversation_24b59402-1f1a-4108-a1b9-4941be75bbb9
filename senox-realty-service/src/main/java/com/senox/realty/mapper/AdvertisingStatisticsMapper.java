package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.AdvertisingStatistics;
import com.senox.realty.vo.StatisticsSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 10:25
 */
@Mapper
public interface AdvertisingStatisticsMapper extends BaseMapper<AdvertisingStatistics> {

    /**
     * 广告位统计数量
     * @param searchVo
     * @return
     */
    int countAdvertisingStatistics(StatisticsSearchVo searchVo);

    /**
     * 广告位统计列表
     * @param searchVo
     * @return
     */
    List<AdvertisingStatistics> listAdvertisingStatistics(StatisticsSearchVo searchVo);
}
