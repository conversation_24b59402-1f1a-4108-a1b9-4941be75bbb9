package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.constant.AntiFraudWorkRecordMediaSource;
import com.senox.realty.domain.AntiFraudWorkRecordMedia;
import com.senox.realty.vo.AntiFraudWorkRecordMediaSearchVo;

import java.util.List;

public interface AntiFraudWorkRecordMediaMapper extends BaseMapper<AntiFraudWorkRecordMedia> {

    /**
     * 批量添加媒体资源
     * @param medias 媒体资源集
     */
    void addBatch(List<AntiFraudWorkRecordMedia> medias);

    /**
     * 根据媒体资源id批量删除
     * @param mediaIds 媒体资源id集
     */
    void deleteByIds(List<Long> mediaIds);

    /**
     * 根据产品id批量删除
     * @param productIds 产品id集
     * @param sources 来源集
     */
    void deleteByProductIds(List<Long> productIds, List<AntiFraudWorkRecordMediaSource> sources);

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(AntiFraudWorkRecordMediaSearchVo search);


    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    List<AntiFraudWorkRecordMedia> list(AntiFraudWorkRecordMediaSearchVo search);
}
