package com.senox.realty.mapper;

import com.senox.realty.domain.RealtyPayoff;
import com.senox.realty.domain.RealtyPayoffItem;
import com.senox.realty.vo.RealtyPayoffSearchVo;
import com.senox.realty.vo.RealtyPayoffVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/22 14:40
 */
@Mapper
public interface RealtyPayoffMapper {

    /**
     * 添加应付账单
     * @param payoff
     * @return
     */
    int addPayoff(RealtyPayoff payoff);

    /**
     * 更新应付账单
     * @param payoff
     * @return
     */
    int updatePayoff(RealtyPayoff payoff);

    /**
     * 删除应付账单
     * @param id
     */
    int deletePayoff(Long id);

    /**
     * 根据id查找应付账单
     * @param year
     * @param month
     * @param id
     * @return
     */
    RealtyPayoff findById(@Param("year") Integer year,
                          @Param("month") Integer month,
                          @Param("id") Long id);

    /**
     * 根据合同号查找应付账单
     * @param year
     * @param month
     * @param contractNo
     * @return
     */
    List<RealtyPayoff> findByContractNo(@Param("year") Integer year,
                                        @Param("month") Integer month,
                                        @Param("contractNo") String contractNo);

    /**
     * 查找应付账单详情
     * @param id
     * @return
     */
    RealtyPayoffVo findDetailById(Long id);

    /**
     * 应付账单数合计
     * @param search
     * @return
     */
    int countPayoff(RealtyPayoffSearchVo search);

    /**
     * 应付账单合计
     * @param search
     * @return
     */
    RealtyPayoffVo sumPayoff(RealtyPayoffSearchVo search);

    /**
     * 应付账单列表
     * @param search
     * @return
     */
    List<RealtyPayoffVo> listPayoff(RealtyPayoffSearchVo search);



    /**
     * 批量添加应付帐单明细
     * @param items
     * @return
     */
    int batchAddItems(@Param("list") List<RealtyPayoffItem> items);

    /**
     * 批量更新应付帐单明细
     * @param items
     * @return
     */
    int batchUpdateItems(@Param("list") List<RealtyPayoffItem> items);

    /**
     * 删除应付账单明细
     * @param billId
     * @param ids
     * @return
     */
    int batchDelItems(@Param("billId") Long billId, @Param("ids") List<Long> ids);

    /**
     * 删除应付账单明细
     * @param billId
     */
    void deleteItems(Long billId);

    /**
     * 应付账单明细
     * @param billId
     * @return
     */
    List<RealtyPayoffItem> listPayoffItems(Long billId);
}
