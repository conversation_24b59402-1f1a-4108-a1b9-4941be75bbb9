package com.senox.realty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.realty.domain.FirefightingNotice;
import com.senox.realty.vo.FirefightingNoticeSearchVo;
import com.senox.realty.vo.FirefightingNoticeVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 9:58
 */
@Mapper
@Repository
public interface FirefightingNoticeMapper extends BaseMapper<FirefightingNotice> {

    /**
     * 告知单统计
     * @param search
     * @return
     */
    int countNotice(FirefightingNoticeSearchVo search);

    /**
     * 告知单列表
     * @param search
     * @return
     */
    List<FirefightingNoticeVo> listNotice(FirefightingNoticeSearchVo search);
}
