package com.senox.realty.schedule;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.realty.domain.RealtyReceipt;
import com.senox.realty.event.RealtyReceiptMessageEvent;
import com.senox.realty.service.RealtyReceiptService;
import com.senox.realty.vo.RealtyReceiptSearchVo;
import com.senox.realty.vo.RealtyReceiptVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-2-5
 */
@Slf4j
@AllArgsConstructor
@Component
public class RealtyReceiptSchedule {
    private final RealtyReceiptService receiptService;
    private final ApplicationEventPublisher publisher;

    @XxlJob("billReceiptStatusJob")
    public void billReceiptStatusJob() throws IOException {
        List<Long> billIds = new ArrayList<>();
        String jobParam = XxlJobHelper.getJobParam();
        if (!StringUtils.isBlank(jobParam)){
            billIds = JsonUtils.json2List(jobParam,Long.class);
        }
        receiptService.refreshBillReceiptStatus(billIds);

    }

    @XxlJob("receiptSendJob")
    public void receiptSendJob() throws IOException {
        RealtyReceiptSearchVo searchVo = new RealtyReceiptSearchVo();
        searchVo.setSend(false);
        searchVo.setApplyStatus(ReceiptStatus.AUDIT_APPROVED.getStatus());
        searchVo.setStatus(ReceiptStatus.ISSUED.getStatus());
        List<RealtyReceiptVo> receiptList = receiptService.list(searchVo);
        log.info("【Receipt】需要发送发票开具消息: {}", JsonUtils.object2Json(receiptList));
        if (CollectionUtils.isEmpty(receiptList)) {
            return;
        }
        publisher.publishEvent(new RealtyReceiptMessageEvent(this, receiptList));
        List<RealtyReceipt> updateReceiptList = receiptList.stream().map(r -> {
            RealtyReceipt updateReceipt = new RealtyReceipt();
            updateReceipt.setId(r.getId());
            updateReceipt.setSend(true);
            return updateReceipt;
        }).collect(Collectors.toList());
        receiptService.updateBatch(updateReceiptList);
    }
}
