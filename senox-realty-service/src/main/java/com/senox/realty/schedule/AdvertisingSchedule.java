package com.senox.realty.schedule;

import com.senox.common.utils.DateUtils;
import com.senox.realty.service.AdvertisingContractService;
import com.senox.realty.service.ContractRentsService;
import com.senox.realty.vo.ContractEnableDto;
import com.senox.realty.vo.ContractSuspendDto;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/8/1 13:47
 */
@Component
@RequiredArgsConstructor
public class AdvertisingSchedule {

    private final AdvertisingContractService contractService;

    private final ContractRentsService contractRentsService;

    /**
     * 到期启用合同
     */
    @XxlJob("enableAdvertisingContractJob")
    public void enableAdvertisingContractJob() {
        XxlJobHelper.log("广告合同启用开始...");
        long execStartTime = System.currentTimeMillis();

        LocalDate tillDate = prepareTillDate(XxlJobHelper.getJobParam());
        XxlJobHelper.log("广告合同启用截止日期 {} ...", tillDate);

        ContractEnableDto enableDto = new ContractEnableDto();
        enableDto.setEnableDate(tillDate);
        contractService.enableContractTillDate(enableDto);

        XxlJobHelper.log("广告合同启用结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    /**
     * 到期停用合同
     */
    @XxlJob("suspendAdvertisingContractJob")
    public void suspendAdvertisingContractJob() {
        XxlJobHelper.log("广告合同停用开始...");
        long execStartTime = System.currentTimeMillis();

        LocalDate tillDate = prepareTillDate(XxlJobHelper.getJobParam());
        XxlJobHelper.log("广告合同停用截止日期 {} ...", tillDate);

        ContractSuspendDto suspendDto = new ContractSuspendDto();
        suspendDto.setSuspendDate(tillDate);
        contractService.suspendContractTillDate(suspendDto);

        XxlJobHelper.log("广告合同停用结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    private LocalDate prepareTillDate(String dateStr) {
        LocalDate result = DateUtils.parseDate(dateStr, DateUtils.PATTERN_FULL_DATE);
        result = result == null ? LocalDate.now() : result;
        return result;
    }
}
