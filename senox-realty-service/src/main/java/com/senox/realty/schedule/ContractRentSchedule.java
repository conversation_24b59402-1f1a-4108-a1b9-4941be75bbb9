package com.senox.realty.schedule;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.realty.domain.ContractRents;
import com.senox.realty.service.ContractRentsService;
import com.senox.realty.vo.ContractRentsSearchVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-23 8:28
 */
@Component
@RequiredArgsConstructor
public class ContractRentSchedule {

    private final ContractRentsService contractRentsService;


    /**
     * 查询赁合同的租金，管理费，押金，押金状态，代租合同号，业主等
     * */
    @XxlJob("syncLeaseContractRentsJob")
    public void syncLeaseContractRentsJob(){
        long execStartTime = System.currentTimeMillis();
        ContractRentsSearchVo contractRents = initContractParams(XxlJobHelper.getJobParam());
        XxlJobHelper.log("同步合同其他信息开始...");
        List<ContractRents> rentsList = contractRentsService.contractRentsList(contractRents);
        contractRentsService.saveContractRents(rentsList);
        XxlJobHelper.log("同步合同其他信息结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    private ContractRentsSearchVo initContractParams(String params) {
        ContractRentsSearchVo generateVo = new ContractRentsSearchVo();
        if (!StringUtils.isBlank(params)) {
            generateVo = JsonUtils.json2Object(params, ContractRentsSearchVo.class);
        } else {
            generateVo.setStartTime(LocalDate.now().minusDays(1).atTime(LocalTime.MIN));
        }
        return generateVo;
    }
}
