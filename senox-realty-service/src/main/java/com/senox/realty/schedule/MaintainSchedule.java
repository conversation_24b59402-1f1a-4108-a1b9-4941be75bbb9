package com.senox.realty.schedule;

import com.senox.common.utils.DateUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.realty.component.AdminComponent;
import com.senox.realty.config.MaintainAutoCompleteConfig;
import com.senox.realty.constant.MaintainJobStatus;
import com.senox.realty.constant.MaintainOrderStatus;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.convert.MaintainJobConvertor;
import com.senox.realty.domain.MaintainJob;
import com.senox.realty.domain.MaintainOrder;
import com.senox.realty.service.MaintainJobService;
import com.senox.realty.service.MaintainOrderDayReportService;
import com.senox.realty.service.MaintainOrderMonthReportService;
import com.senox.realty.service.MaintainOrderService;
import com.senox.realty.vo.*;
import com.senox.user.constant.MaintainManType;
import com.senox.user.vo.AdminUserSearchVo;
import com.senox.user.vo.AdminUserVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/9/8 10:15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MaintainSchedule {

    private final MaintainJobService maintainJobService;
    private final MaintainJobConvertor jobConvertor;
    private final MaintainOrderService maintainOrderService;
    private final AdminComponent adminComponent;
    private final RabbitTemplate rabbitTemplate;
    private final MaintainOrderDayReportService dayReportService;
    private final MaintainOrderMonthReportService monthReportService;
    private final MaintainAutoCompleteConfig autoCompleteConfig;

    @XxlJob("autoCompleteExamine")
    public void autoCompleteExamine() {
        XxlJobHelper.log("审核节点派工单自动完成开始...");
        long execStartTime = System.currentTimeMillis();
        LocalDateTime now = LocalDateTime.now();
        List<MaintainJob> examineList = maintainJobService.findExamineList();
        examineList.stream()
                .filter(job -> Duration.between(job.getCreateTime(), now).toHours() >= 24)  // 过滤超过24小时的任务
                .forEach(this::processJob);
        XxlJobHelper.log("审核节点派工单自动完成结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    private void processJob(MaintainJob job) {
        MaintainOrder maintainOrder = maintainOrderService.findById(job.getOrderId());

        if (autoCompleteConfig.getSkipDepartmentIds().contains(maintainOrder.getManagementDeptId())) {
            XxlJobHelper.log("跳过派工单自动完成，单号：{}", maintainOrder.getOrderNo());
            return;
        }

        MaintainJob maintainJob = new MaintainJob();
        maintainJob.setOrderId(job.getOrderId());
        maintainJob.setJobNo(job.getJobNo());
        maintainJob.setStatus(MaintainJobStatus.DONE.getValue());

        MaintainJobVo maintainJobVo = jobConvertor.toVo(maintainJob);
        maintainJobVo.setMaintainJobItemVos(Collections.emptyList());

        maintainJobService.addMaintainJob(maintainJob, maintainJobVo);
    }

    @XxlJob("autoEvaluate")
    public void autoEvaluate() {
        XxlJobHelper.log("自动五星好评任务开始...");
        long execStartTime = System.currentTimeMillis();
        LocalDateTime now = LocalDateTime.now();
        MaintainOrderSearchVo searchVo = new MaintainOrderSearchVo();
        searchVo.setStatus(Collections.singletonList(MaintainOrderStatus.DONE.getValue()));
        searchVo.setEvaluated(Boolean.FALSE);
        List<MaintainOrderVo> orderVoList = maintainOrderService.listMaintainOrder(searchVo);
        for (MaintainOrderVo orderVo : orderVoList) {
            if (Duration.between(orderVo.getModifiedTime(), now).toHours() >= 168) {
                MaintainOrderEvaluateVo evaluateVo = new MaintainOrderEvaluateVo();
                evaluateVo.setEvaluateTime(LocalDateTime.now());
                evaluateVo.setEvaluateRating(5);
                evaluateVo.setId(orderVo.getId());
                evaluateVo.setEvaluateOpenid(StringUtils.EMPTY);
                maintainOrderService.evaluateOrder(evaluateVo);
            }
        }
        XxlJobHelper.log("自动五星好评任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    @XxlJob("untreatedNotice")
    public void untreatedNotice() {
        XxlJobHelper.log("维修单未处理提醒任务开始...");
        long execStartTime = System.currentTimeMillis();
        //获取所有未处理的维修单
        MaintainOrderSearchVo orderSearchVo = new MaintainOrderSearchVo();
        orderSearchVo.setPageNo(1);
        orderSearchVo.setPageSize(100);
        orderSearchVo.setStatus(Collections.singletonList(MaintainOrderStatus.INIT.getValue()));
        List<MaintainOrderVo> orderVoList = maintainOrderService.listMaintainOrder(orderSearchVo);
        if (CollectionUtils.isEmpty(orderVoList)) {
            return;
        }

        //获取所有的维修主管
        AdminUserSearchVo adminUserSearchVo = new AdminUserSearchVo();
        adminUserSearchVo.setPageNo(1);
        adminUserSearchVo.setPageSize(100);
        adminUserSearchVo.setMaintainManType(Collections.singletonList(MaintainManType.MAINTAIN_MANAGER.getValue()));
        List<AdminUserVo> adminUserVoList = adminComponent.listAdminUserPage(adminUserSearchVo).getDataList();
        adminUserVoList.forEach(adminUserVo -> sendUntreatedMessage(adminUserVo, orderVoList.size()));
        XxlJobHelper.log("维修单未处理提醒任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    @XxlJob("generateMaintainOrderDayReport")
    public void generateMaintainOrderDayReport() {
        XxlJobHelper.log("物维订单日报表生成开始...");
        long execStartTime = System.currentTimeMillis();

        LocalDate startDate = prepareTillDate(XxlJobHelper.getJobParam());
        XxlJobHelper.log("物维订单日报表生成日期 {} ...", startDate);

        dayReportService.generateDayReport(startDate.getYear(), startDate.getMonthValue(), startDate.getDayOfMonth());
        XxlJobHelper.log("物维订单日报表生成结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    @XxlJob("generateMaintainOrderMonthReport")
    public void generateMaintainOrderMonthReport() {
        XxlJobHelper.log("物维订单月报表生成开始...");
        long execStartTime = System.currentTimeMillis();

        LocalDate startDate = prepareTillDate(XxlJobHelper.getJobParam());
        XxlJobHelper.log("物维订单月报表生成日期 {} ...", startDate);

        monthReportService.generateMonthReport(startDate.getYear(), startDate.getMonthValue());
        XxlJobHelper.log("物维订单月报表生成结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    private LocalDate prepareTillDate(String dateStr) {
        LocalDate result = DateUtils.parseDate(dateStr, DateUtils.PATTERN_FULL_DATE);
        result = result == null ? LocalDate.now().minusDays(1L) : result;
        return result;
    }

    /**
     * 发送未处理的维修单消息
     * @param adminUserVo
     * @param untreatedCount
     */
    private void sendUntreatedMessage(AdminUserVo adminUserVo, Integer untreatedCount) {
        MaintainOrderUntreatedMessageVo messageVo = new MaintainOrderUntreatedMessageVo();
        messageVo.setAdminUserId(adminUserVo.getId());
        messageVo.setUntreatedCount(untreatedCount);
        messageVo.setNoticeTime(LocalDateTime.now());
        rabbitTemplate.convertAndSend(RealtyConst.MQ.MQ_REALTY_AC_UNTREATED,messageVo);
        log.info("发送未处理的维修单至消息队列 {}", JsonUtils.object2Json(messageVo));
    }
}
