package com.senox.realty.schedule;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.dm.constant.HolleyQueryType;
import com.senox.dm.vo.HolleyPointMeterQueryBatchRequest;
import com.senox.realty.service.EnergyMeteringPointReadingsService;
import com.senox.realty.service.EnergyMeteringPointService;
import com.senox.realty.service.EnergyRtuService;
import com.senox.realty.vo.EnergyMeteringPointReadingsSearchVo;
import com.senox.user.annotation.PluginEnvBuilder;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@RequiredArgsConstructor
@Component
public class EnergySchedule {
    private final EnergyRtuService rtuService;
    private final EnergyMeteringPointService meteringPointService;
    private final EnergyMeteringPointReadingsService meteringPointReadingService;

    /**
     * 同步集中器
     */
    @PluginEnvBuilder(value = "xxl")
    @XxlJob("energyRtuSyncJob")
    public void energyRtuSyncJob() {
        long execTime = System.currentTimeMillis();
        XxlJobHelper.log("【集抄】同步集中器开始...");
        rtuService.rtuSync();
        XxlJobHelper.log("【集抄】同步集中器结束，耗时: {}", System.currentTimeMillis() - execTime);
    }

    /**
     * 同步计量点
     */
    @PluginEnvBuilder(value = "xxl")
    @XxlJob("energyMeteringPointSyncJob")
    public void energyMeteringPointSyncJob() {
        long execTime = System.currentTimeMillis();
        XxlJobHelper.log("【集抄】同步计量点开始...");
        String meteringPointCode = XxlJobHelper.getJobParam();
        meteringPointService.batchRefresh(meteringPointCode);
        XxlJobHelper.log("【集抄】同步计量点结束，耗时: ｛｝", System.currentTimeMillis() - execTime);
    }

    /**
     * 同步计量点读数
     */
    @PluginEnvBuilder(value = "xxl")
    @XxlJob("energyMeteringPointReadingSyncJob")
    public void energyMeteringPointReadingSyncJob() {
        long execTime = System.currentTimeMillis();
        XxlJobHelper.log("【集抄】同步计量点读数开始...");
        HolleyPointMeterQueryBatchRequest batchReq = getSettingHolleyPointMeterQueryParam(XxlJobHelper.getJobParam());
        meteringPointReadingService.batchSync(batchReq);
        XxlJobHelper.log("【集抄】同步计量点读数结束，耗时: ｛｝", System.currentTimeMillis() - execTime);

    }

    /**
     * 计量点读数同步物业信息
     */
    @PluginEnvBuilder(value = "xxl")
    @XxlJob("energyMeteringPointReadingRealtyInfoSyncJob")
    public void energyMeteringPointReadingRealtyInfoSyncJob() {
        long execTime = System.currentTimeMillis();
        XxlJobHelper.log("【集抄】计量点读数同步物业信息开始...");
        EnergyMeteringPointReadingsSearchVo search = StringUtils.isBlank(XxlJobHelper.getJobParam()) ? null : JsonUtils.json2Object(XxlJobHelper.getJobParam(), EnergyMeteringPointReadingsSearchVo.class);
        meteringPointReadingService.energyMeteringPointReadingRealtyInfoSync(search);
        XxlJobHelper.log("【集抄】计量点读数同步物业信息结束，耗时: ｛｝", System.currentTimeMillis() - execTime);

    }

    private HolleyPointMeterQueryBatchRequest getSettingHolleyPointMeterQueryParam(String paramJson) {
        if (StringUtils.isBlank(paramJson)) {
            HolleyPointMeterQueryBatchRequest defaultBatchReq = new HolleyPointMeterQueryBatchRequest();
            defaultBatchReq.setRtuCode("default");
            //默认查实时
            defaultBatchReq.setType(HolleyQueryType.REAL_TIME.ordinal());
            return defaultBatchReq;
        }
        HolleyPointMeterQueryBatchRequest batchReq = JsonUtils.json2Object(paramJson, HolleyPointMeterQueryBatchRequest.class);
        if (null != batchReq.getType() && null != HolleyQueryType.fromValue(batchReq.getType())) {
            if (null == batchReq.getEndTime()) {
                batchReq.setEndTime(LocalDateTime.now());
            }
            //默认查询最近30天的
            if (null == batchReq.getStartTime()) {
                batchReq.setStartTime(LocalDateTime.now().minusDays(30));
            }
        }
        return batchReq;
    }
}
