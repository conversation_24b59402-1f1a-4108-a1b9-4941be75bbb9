package com.senox.realty.schedule;

import com.senox.realty.service.RealtyBillPenaltyService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/9/13 10:48
 */
@AllArgsConstructor
@Component
public class RealtyBillPenaltySchedule {


    private final RealtyBillPenaltyService penaltyService;


    @XxlJob("realtyBillPenaltyJob")
    public void exec() {
        XxlJobHelper.log("物业账单滞纳金计算开始...");
        long execStartTime = System.currentTimeMillis();
        penaltyService.calBillPenalty(XxlJobHelper.getJobParam());
        XxlJobHelper.log("物业账单滞纳金计算结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }



}
