package com.senox.realty.dto;


import com.senox.pm.vo.TaxHeaderVo;
import com.senox.realty.domain.RealtyBillItem;
import com.senox.realty.vo.RealtyBillVo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-7-21
 */
@Getter
@Setter
public class BillReceiptApplyDto {

    /**
     * 账单map
     */
    private Map<RealtyBillVo, List<RealtyBillItem>> billMap;

    /**
     * 抬头
     */
    private TaxHeaderVo taxHeader;

    /**
     * 最小时间戳
     */
    private long minTimeLong;

    /**
     * 最大时间戳
     */
    private long maxTimeLong;
}
