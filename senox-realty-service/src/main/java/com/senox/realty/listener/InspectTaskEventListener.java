package com.senox.realty.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.domain.FirefightingInspectTaskItem;
import com.senox.realty.event.InspectTaskDropEvent;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.service.FirefightingInspectTaskItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/20 15:19
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InspectTaskEventListener {

    private final FirefightingInspectTaskItemService taskItemService;

    /**
     * 创建巡检创建事件
     * @param event
     */
    @EventListener(classes = InspectTaskFulfilledEvent.class)
    public void listenInspectFulfilledEvent(InspectTaskFulfilledEvent event) {
        log.info("收到巡检记录创建事件 {}", JsonUtils.object2Json(event));
        prepareTaskEvent(event);

        if (!WrapperClassUtils.biggerThanLong(event.getTaskId(), 0L)) {
            log.info("巡检记录无匹配的任务 {}", JsonUtils.object2Json(event));
            return;
        }

        List<FirefightingInspectTaskItem> taskItems = taskItemService.listItemByFulfilledEvent(event);
        if (!CollectionUtils.isEmpty(taskItems)) {
            taskItemService.fulfillInspectTaskItem(taskItems.stream().map(FirefightingInspectTaskItem::getId).collect(Collectors.toList()),
                    event.getInspectId());
        }
    }

    /**
     * 创建巡检移除事件
     * @param event
     */
    @EventListener(classes = InspectTaskDropEvent.class)
    public void listenInspectDropEvent(InspectTaskDropEvent event) {
        log.info("收到巡检记录删除事件 {}", JsonUtils.object2Json(event));

        taskItemService.dropInspectTaskItem(event.getInspectId(), event.getInspectType());
    }

    /**
     * 查找任务id
     * @param event
     */
    private void prepareTaskEvent(InspectTaskFulfilledEvent event) {
        if (WrapperClassUtils.biggerThanLong(event.getTaskId(), 0L)) {
            return;
        }

        List<FirefightingInspectTaskItem> taskItems = taskItemService.listTaskItem(event.getUtilityId(),
                event.getRealtySerials(), event.getInspectType());
        if (!CollectionUtils.isEmpty(taskItems)) {
            log.info("巡检事件 {} 任务匹配 {}", JsonUtils.object2Json(event), JsonUtils.object2Json(taskItems));
            event.setTaskId(taskItems.get(0).getTaskId());
        }
    }

    private FirefightingInspectTaskItem inspectTaskEventToTaskItem(InspectTaskFulfilledEvent event) {
        FirefightingInspectTaskItem result = new FirefightingInspectTaskItem();
        result.setTaskId(event.getTaskId());
        result.setUtilityId(event.getUtilityId());
        result.setRealtySerial(event.getRealtySerials().get(0));
        result.setInspectType(event.getInspectType());
        result.setInspectId(event.getInspectId());
        return result;
    }
}
