package com.senox.realty.listener;


import com.rabbitmq.client.Channel;
import com.senox.common.utils.JsonUtils;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.realty.service.RealtyReceiptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.senox.realty.constant.RealtyConst.MQ.*;


@Slf4j
@RequiredArgsConstructor
@Component
public class ReceiptConsumerListener {
    private final RealtyReceiptService realtyReceiptService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = MQ_RECEIPT_REALTY_STATUS, durable = "true", exclusive = "false", autoDelete = "false"),
            exchange = @Exchange(name = EX_RECEIPT_REALTY_STATUS),
            key = MQ_RECEIPT_REALTY_STATUS
    ), containerFactory = "containerFactory")
    public void receiptRealtyListener(@NotNull Message message, Channel channel) throws IOException {
        String queue = message.getMessageProperties().getConsumerQueue();
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【MQ】接收到队列[{}]: {}", queue, body);
        //更新状态
        List<ReceiptApplyVo> applyList = JsonUtils.json2List(body, ReceiptApplyVo.class);
        realtyReceiptService.updateBillItemReceiptStatus(applyList);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }
}
