package com.senox.realty.listener;

import com.senox.common.utils.DateUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.realty.event.RealtyReceiptMessageEvent;
import com.senox.realty.vo.RealtyReceiptVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.senox.realty.constant.RealtyConst.MQ.RECEIPT_REALTY_EXCHANGE;
import static com.senox.realty.constant.RealtyConst.MQ.RECEIPT_WECHAT_MESSAGE_SUCCESS;

/**
 * 物业发票消息事件处理
 *
 * <AUTHOR>
 * @date 2023-10-7
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RealtyReceiptMessageEventListener {
    private final RabbitTemplate rabbitTemplate;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = {RealtyReceiptMessageEvent.class}, fallbackExecution = true)
    public void onBillReceiptEvent(RealtyReceiptMessageEvent event) {
        List<RealtyReceiptVo> receiptList = event.getReceiptList();
        log.info("【Receipt】Message event listener. data:{}", JsonUtils.object2Json(receiptList));
        for (RealtyReceiptVo realtyReceipt : receiptList) {
            if (StringUtils.isBlank(realtyReceipt.getApplyManName())) {
                log.info("【Receipt】 message:排除申请人为空的,data:{}", JsonUtils.object2Json(realtyReceipt));
                return;
            }
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("title", "物业-全电发票");
            messageMap.put("applyId", realtyReceipt.getId());
            messageMap.put("applyMan", realtyReceipt.getApplyManName());
            messageMap.put("amount", realtyReceipt.getApplyAmount().toString());
            messageMap.put("successTime", DateUtils.formatDateTime(realtyReceipt.getReceiptTime(), DateUtils.PATTERN_FULL_DATE));
            messageMap.put("customerName", realtyReceipt.getTaxHeader());
            log.info("【Receipt】 message:发送物业发票开具成功消息,data:{}", JsonUtils.object2Json(messageMap));
            rabbitTemplate.convertAndSend(RECEIPT_REALTY_EXCHANGE, RECEIPT_WECHAT_MESSAGE_SUCCESS, messageMap);
        }

    }
}
