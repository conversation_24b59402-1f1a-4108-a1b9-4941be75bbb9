package com.senox.realty.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.realty.component.ResidentAccessComponent;
import com.senox.realty.event.ResidentAccessAddEvent;
import com.senox.realty.event.ResidentAccessDelEvent;
import com.senox.realty.service.ContractService;
import com.senox.realty.vo.ContractVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 2024/6/26 9:34
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ResidentAccessListener {

    private final ResidentAccessComponent residentAccessComponent;
    private final ContractService contractService;

    @Async
    @TransactionalEventListener(classes = ResidentAccessAddEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void accessAddEvent(ResidentAccessAddEvent event) {
        log.info("收到恢复用户门禁权限事件 {}", JsonUtils.object2Json(event));
        //恢复该合同用户门禁权限
        residentAccessComponent.renewalAccess(event.getNewContractNo(), event.getNewContractNo());
        //是否续签,恢复续签的用户门禁权限
        ContractVo renewFrom = contractService.findRenewFrom(event.getContractId());
        if (renewFrom != null) {
            log.info("续签源的合同号是:{}， 新的合同号是：{}", renewFrom.getContractNo(), event.getNewContractNo());
            //续签恢复用户门禁权限
            residentAccessComponent.renewalAccess(renewFrom.getContractNo(), event.getNewContractNo());
        }
    }

    @Async
    @TransactionalEventListener(classes = ResidentAccessDelEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void accessDelEvent(ResidentAccessDelEvent event) {
        log.info("收到移除合同相关用户的门禁权限事件 {}", JsonUtils.object2Json(event));
        residentAccessComponent.deleteAccessByContractNo(event.getContractNo());
    }
}
