package com.senox.realty.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.ContractType;
import com.senox.realty.constant.RealtyNature;
import com.senox.realty.domain.Realty;
import com.senox.realty.event.ContractEnableEvent;
import com.senox.realty.event.RealtyBillChangedEvent;
import com.senox.realty.service.ContractBillService;
import com.senox.realty.service.RealtyBillPenaltyService;
import com.senox.realty.service.RealtyBillService;
import com.senox.realty.service.RealtyService;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.ContractVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 2024/3/28 10:03
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContractEventListener {

    private final RealtyService realtyService;
    private final RealtyBillService billService;
    private final RealtyBillPenaltyService penaltyService;
    private final ContractBillService contractBillService;


    /**
     * 合同启用同步更新了业主，同步更新物业信息
     * @param event
     */
    @Async
    @TransactionalEventListener(classes = ContractEnableEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void realtyOwnerListener(ContractEnableEvent event) {
        ContractVo contract = event.getContract();
        log.info("收到物业合同启用事件 {}", JsonUtils.object2Json(contract));

        ContractType contractType = ContractType.fromValue(contract.getType());
        // 只处理物业合同
        if (contractType != ContractType.ESTATE) {
            return;
        }
        if (!WrapperClassUtils.biggerThanLong(contract.getRealtyId(), 0L)
                || !WrapperClassUtils.biggerThanLong(contract.getCustomerId(), 0L)
                || StringUtils.isBlank(contract.getCustomerName())) {
            return;
        }

        log.info("更新物业 {} 业主 {}", contract.getRealtySerial(), contract.getCustomerName());
        // 更新业主
        Realty realty = new Realty();
        realty.setId(contract.getRealtyId());
        realty.setNature(RealtyNature.SALE.ordinal());
        realty.setOwnerId(contract.getCustomerId());
        realty.setOwnerName(contract.getCustomerName());
        realty.setModifierId(event.getOperator().getUserId());
        realty.setModifierName(event.getOperator().getUsername());
        realtyService.updateRealty(realty, null);
    }

    /**
     * 合同启用同步生成应付账单
     * @param event
     */
    @Async
    @TransactionalEventListener(classes = ContractEnableEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void payoffBillListener(ContractEnableEvent event) {
        ContractVo contract = event.getContract();
        log.info("应付账单...收到合同启用事件 {}", JsonUtils.object2Json(contract));

        if (contract.getStartDate().isAfter(event.getBillDate())) {
            log.info("{} 未到账单期 {}，不生成应付账单", contract.getContractNo(), event.getBillDate());
            return;
        }

        // 生成应付合同
        contractBillService.buildAndSavePayoff(contract, event.getBillDate(), event.getOperator(), true);
    }

    /**
     * 账单金额变更
     * @param billChangedEvent
     */
    @Async
    @TransactionalEventListener(classes = RealtyBillChangedEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void billChangedListener(RealtyBillChangedEvent billChangedEvent) {
        log.info("收到账单金额变更事件。{}", JsonUtils.object2Json(billChangedEvent.getBillMonth()));
        BillMonthVo billMonth = billChangedEvent.getBillMonth();
        // 更新账单金额
        billService.updateBillAmount(billMonth);
        // 删除0元账单
        billService.deleteZeroBill(billMonth);

        // 重新计算滞纳金
        penaltyService.calBillPenalty(billMonth.getContractNo());
    }
}
