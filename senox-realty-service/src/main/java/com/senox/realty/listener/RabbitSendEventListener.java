package com.senox.realty.listener;

import com.senox.common.utils.StringUtils;
import com.senox.realty.event.RabbitSendEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 2024-2-19
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RabbitSendEventListener {
    private final RabbitTemplate rabbitTemplate;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = {RabbitSendEvent.class}, fallbackExecution = true)
    public void onSendEvent(RabbitSendEvent event) {
        if (StringUtils.isBlank(event.getExchange())) {
            rabbitTemplate.convertAndSend(event.getQueue(), event.getObject());
        } else {
            rabbitTemplate.convertAndSend(event.getExchange(), event.getQueue(), event.getObject());
        }
    }
}
