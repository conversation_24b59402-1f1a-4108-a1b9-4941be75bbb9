package com.senox.realty.listener;


import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.domain.AdvertisingBill;
import com.senox.realty.domain.AdvertisingBillPaidEvent;
import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.service.AdvertisingBillService;
import com.senox.realty.service.AdvertisingContractService;
import com.senox.realty.service.AdvertisingPayoffService;
import com.senox.realty.vo.ContractEnableDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/3 11:16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AdvertisingBillPaidEventListener {

    private final AdvertisingContractService contractService;
    private final AdvertisingBillService billService;
    private final AdvertisingPayoffService payoffService;

    /**
     * 支付账单后，启用合同
     * @param event
     */
    @Async
    @TransactionalEventListener(classes = AdvertisingBillPaidEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void advertisingContractEnableListener(AdvertisingBillPaidEvent event) {
        if (BooleanUtils.isFalse(event.getBillPaid().getPaid())) {
            return;
        }

        log.info("收到广告应收账单支付成功事件，启用当天已支付成功，开始日期为当天的合同");
        ContractEnableDto enableDto = new ContractEnableDto();
        enableDto.setEnableDate(LocalDate.now());
        contractService.enableContractTillDate(enableDto);
    }

    /**
     * 支付账单后，自动生成应付
     * @param event
     */
    @Async
    @TransactionalEventListener(classes = AdvertisingBillPaidEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void payoffGenerateListener(AdvertisingBillPaidEvent event) {
        log.info("收到广告应收账单支付结果事件 {}。", JsonUtils.object2Json(event.getBillPaid()));
        BillPaidVo billPaid = event.getBillPaid();
        if (BooleanUtils.isFalse(billPaid.getPaid())) {
            return;
        }

        List<AdvertisingBill> bills = CollectionUtils.isEmpty(billPaid.getBillIds())
                ? billService.listByRemoteOrderId(billPaid.getOrderId())
                : billService.listById(billPaid.getBillIds());
        bills = bills.stream().filter(x -> BillStatus.fromStatus(x.getStatus()) == BillStatus.PAID).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }

        List<String> contractNos = bills.stream().map(AdvertisingBill::getContractNo).distinct().collect(Collectors.toList());
        for (String contractNo : contractNos) {
            log.info("根据合同生成广告应付账单。{}", contractNo);
            AdvertisingContract contract = contractService.findByContractNo(contractNo);
            if (contract != null) {
                payoffService.savePayoff(contract);
            }
        }
    }
}
