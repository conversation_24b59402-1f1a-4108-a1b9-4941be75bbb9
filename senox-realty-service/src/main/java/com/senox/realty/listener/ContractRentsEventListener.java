package com.senox.realty.listener;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.event.ContractRentsEvent;
import com.senox.realty.service.ContractRentsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class ContractRentsEventListener {

    private final ContractRentsService contractRentsService;

    /**
     * 根据账单id或者订单id更新contractRent表的押金账单状态-事件
     * */
    @Async
    @TransactionalEventListener(classes = ContractRentsEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void contractRentsListener(ContractRentsEvent event) {
        if (event.getBillPaidVo() == null
                || (!WrapperClassUtils.biggerThanLong(event.getBillPaidVo().getOrderId(), 0L) && CollectionUtils.isEmpty(event.getBillPaidVo().getBillIds()))) {
            //订单id和账单id集合都为空则return
            return;
        }
        contractRentsService.updateContractRent(event.getBillPaidVo());
    }
}
