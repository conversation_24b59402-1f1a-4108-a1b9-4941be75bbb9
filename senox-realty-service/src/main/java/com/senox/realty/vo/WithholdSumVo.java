package com.senox.realty.vo;

import java.math.BigDecimal;

/**
 * 代扣合计
 * <AUTHOR>
 * @date 2022/1/24 9:13
 */
public class WithholdSumVo {

    /**
     * 报盘记录数
     */
    private Integer offerCount;
    /**
     * 回盘记录数
     */
    private Integer backCount;
    /**
     * 报盘金额
     */
    private BigDecimal offerAmount;
    /**
     * 回盘金额
     */
    private BigDecimal backAmount;


    public Integer getOfferCount() {
        return offerCount;
    }

    public void setOfferCount(Integer offerCount) {
        this.offerCount = offerCount;
    }

    public Integer getBackCount() {
        return backCount;
    }

    public void setBackCount(Integer backCount) {
        this.backCount = backCount;
    }

    public BigDecimal getOfferAmount() {
        return offerAmount;
    }

    public void setOfferAmount(BigDecimal offerAmount) {
        this.offerAmount = offerAmount;
    }

    public BigDecimal getBackAmount() {
        return backAmount;
    }

    public void setBackAmount(BigDecimal backAmount) {
        this.backAmount = backAmount;
    }
}
