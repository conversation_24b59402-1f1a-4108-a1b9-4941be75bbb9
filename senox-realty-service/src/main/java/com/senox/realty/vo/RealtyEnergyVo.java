package com.senox.realty.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/13 11:05
 */
@Getter
@Setter
public class RealtyEnergyVo {

    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 水表读数
     */
    private Integer waterReadings;
    /**
     * 电表读数
     */
    private Integer electricReadings;
    /**
     * 水单价
     */
    private BigDecimal waterPrice;
    /**
     * 电单价
     */
    private BigDecimal electricPrice;
    /**
     * 水消费单元
     */
    private Integer waterConsumeUnit;
    /**
     * 电消费单元
     */
    private Integer electricConsumeUnit;

}
