package com.senox.realty.vo;

import com.senox.context.AdminUserDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 合同启用参数
 * <AUTHOR>
 * @date 2022/11/17 11:21
 */
@Getter
@Setter
@ToString
public class ContractEnableDto {

    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 启用日期
     */
    private LocalDate enableDate;
    /**
     * 操作员
     */
    private AdminUserDto operator;

    public ContractEnableDto() {
    }

    public ContractEnableDto(String contractNo, LocalDate enableDate) {
        this.contractNo = contractNo;
        this.enableDate = enableDate;
    }
}
