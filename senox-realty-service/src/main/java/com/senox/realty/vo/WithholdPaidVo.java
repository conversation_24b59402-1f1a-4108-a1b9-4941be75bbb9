package com.senox.realty.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 代扣支付结果信息
 * <AUTHOR>
 * @date 2022/1/25 14:03
 */
public class WithholdPaidVo {

    /**
     * 账单id
     */
    private Long billId;
    /**
     * 支付订单id
     */
    private Long remoteOrderId;
    /**
     * 支付结果
     */
    private Boolean paid;
    /**
     * 支付金额
     */
    private BigDecimal amount;
    /**
     * 支付账号
     */
    private String accountNo;
    /**
     * 支付账户名
     */
    private String accountName;
    /**
     * 支付时间
     */
    private LocalDateTime paidTime;
    /**
     * 收费员
     */
    private Long tollMan;


    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public Long getRemoteOrderId() {
        return remoteOrderId;
    }

    public void setRemoteOrderId(Long remoteOrderId) {
        this.remoteOrderId = remoteOrderId;
    }

    public Boolean getPaid() {
        return paid;
    }

    public void setPaid(Boolean paid) {
        this.paid = paid;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public LocalDateTime getPaidTime() {
        return paidTime;
    }

    public void setPaidTime(LocalDateTime paidTime) {
        this.paidTime = paidTime;
    }

    public Long getTollMan() {
        return tollMan;
    }

    public void setTollMan(Long tollMan) {
        this.tollMan = tollMan;
    }
}
