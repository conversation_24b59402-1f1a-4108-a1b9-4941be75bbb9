package com.senox.realty.vo;

import com.senox.context.AdminUserDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022/9/13 14:32
 */
@Getter
@Setter
@ToString
public class RealtyContractSuspendDto {

    private static final long serialVersionUID = 2019362761764931769L;

    /**
     * 年
     */
    private Integer year;
    /**
     * 月
     */
    private Integer month;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 合同类型
     */
    private Integer contractType;
    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 合同开始时间
     */
    private LocalDate contractStartDate;
    /**
     * 停用日期
     */
    private LocalDate suspendDate;
    /**
     * 操作人
     */
    private AdminUserDto operator;

    public RealtyContractSuspendDto() {
    }

    public RealtyContractSuspendDto(String contractNo, LocalDate suspendDate) {
        this.contractNo = contractNo;
        this.suspendDate = suspendDate;
        this.year = suspendDate.getYear();
        this.month = suspendDate.getMonthValue();
    }
}
