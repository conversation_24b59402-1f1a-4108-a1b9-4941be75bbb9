package com.senox.realty.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/8 11:59
 */
@Getter
@Setter
@ToString
public class ContractBillSearchVo {

    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 合同类型
     */
    private List<Integer> types;
    /**
     * 合同状态
     */
    private Integer status;
    /**
     * 无状态结束时间起
     */
    private LocalDate statelessEndDateBegin;
    /**
     * 无状态结束时间止
     */
    private LocalDate statelessEndDateEnd;
    /**
     * 合同开始时间起
     */
    private LocalDate startDateBegin;
    /**
     * 合同开始时间止
     */
    private LocalDate startDateEnd;
    /**
     * 合同结束时间起
     */
    private LocalDate endDateBegin;
    /**
     * 合同结束时间止
     */
    private LocalDate endDateEnd;
    /**
     * 账单年
     */
    private Integer billYear;
    /**
     * 账单月
     */
    private Integer billMonth;
    /**
     * 应收账单生成否
     */
    private Boolean billGenerated;
    /**
     * 应付账单生成否
     */
    private Boolean payoffGenerated;


}
