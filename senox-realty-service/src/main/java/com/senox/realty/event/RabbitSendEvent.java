package com.senox.realty.event;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-2-19
 */
@Getter
public class RabbitSendEvent extends RabbitEvent {
    private static final long serialVersionUID = 7392990525437734621L;
    private final String exchange;
    private final String queue;
    private final Object object;

    public RabbitSendEvent(Object source, String exchange, String queue, Object object) {
        super(source);
        this.exchange = exchange;
        this.queue = queue;
        this.object = object;
    }

    public RabbitSendEvent(Object source, String queue, Object object) {
        this(source, null, queue, object);
    }
}
