package com.senox.realty.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;


/**
 * <AUTHOR>
 * @date 2024/6/26 9:30
 */
@Getter
public class ResidentAccessAddEvent extends ApplicationEvent {

    private final Long contractId;

    private final String newContractNo;


    private static final long serialVersionUID = -3410561398739375452L;

    public ResidentAccessAddEvent(Object source, Long contractId, String newContractNo) {
        super(source);
        this.contractId = contractId;
        this.newContractNo = newContractNo;
    }
}
