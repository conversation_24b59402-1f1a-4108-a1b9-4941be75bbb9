package com.senox.realty.event;

import com.senox.common.vo.BillPaidVo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;


@Getter
public class ContractRentsEvent extends ApplicationEvent {
    private static final long serialVersionUID = -4410561398739375452L;
    private final BillPaidVo billPaidVo;

    public ContractRentsEvent(Object source, BillPaidVo billPaidVo) {
        super(source);
        this.billPaidVo = billPaidVo;
    }
}
