package com.senox.realty.event;

import com.senox.realty.constant.InspectionType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2024/5/20 15:06
 */
@Getter
@Setter
public class InspectTaskDropEvent extends ApplicationEvent {

    private static final long serialVersionUID = 2677544327344906815L;

    /**
     * 任务类型
     */
    private InspectionType inspectType;
    /**
     * 巡检记录id
     */
    private Long inspectId;

    public InspectTaskDropEvent(Object source, InspectionType inspectType, Long inspectId) {
        super(source);
        this.inspectId = inspectId;
        this.inspectType = inspectType;
    }
}
