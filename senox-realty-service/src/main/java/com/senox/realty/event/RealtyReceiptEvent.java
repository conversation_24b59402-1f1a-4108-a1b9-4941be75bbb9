package com.senox.realty.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;



/**
 * 物业发票事件
 *
 * <AUTHOR>
 * @date 2023-10-7
 */
@Getter
@Setter
public class RealtyReceiptEvent extends ApplicationEvent {
    private static final long serialVersionUID = -2131589844950419796L;

    public RealtyReceiptEvent(Object context) {
        super(context);
    }
}
