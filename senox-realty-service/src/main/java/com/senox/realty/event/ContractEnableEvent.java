package com.senox.realty.event;

import com.senox.context.AdminUserDto;
import com.senox.realty.vo.ContractVo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/3/28 9:45
 */
@Getter
public class ContractEnableEvent extends ApplicationEvent {

    private static final long serialVersionUID = -2556700725761342962L;

    /**
     * 合同
     */
    private final ContractVo contract;
    /**
     * 账单日
     */
    private final LocalDate billDate;
    /**
     * 操作人
     */
    private final AdminUserDto operator;

    public ContractEnableEvent(Object source, ContractVo contract, LocalDate billDate, AdminUserDto operator) {
        super(source);
        this.contract = contract;
        this.billDate = billDate;
        this.operator = operator;
    }
}
