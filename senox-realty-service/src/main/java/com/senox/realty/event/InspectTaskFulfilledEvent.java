package com.senox.realty.event;

import com.senox.realty.constant.InspectionType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/20 15:06
 */
@Getter
@Setter
public class InspectTaskFulfilledEvent extends ApplicationEvent {

    private static final long serialVersionUID = 2677544327344906815L;

    /**
     * 任务类型
     */
    private InspectionType inspectType;
    /**
     * 巡检记录id
     */
    private Long inspectId;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 公共消防设施id
     */
    private Long utilityId;
    /**
     * 经营户id
     */
    private Long enterpriseId;
    /**
     * 物业编号
     */
    private List<String> realtySerials;


    public InspectTaskFulfilledEvent(Object source, InspectionType inspectionType, Long inspectId) {
        super(source);
        this.inspectType = inspectionType;
        this.inspectId = inspectId;
    }
}
