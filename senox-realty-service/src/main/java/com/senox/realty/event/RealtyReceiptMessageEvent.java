package com.senox.realty.event;

import com.senox.realty.vo.RealtyReceiptVo;
import lombok.Getter;

import java.util.List;

/**
 * 物业发票消息事件
 *
 * <AUTHOR>
 * @date 2023-10-7
 */
@Getter
public class RealtyReceiptMessageEvent extends RealtyReceiptEvent {
    private static final long serialVersionUID = -6958233132604195966L;
    private final List<RealtyReceiptVo> receiptList;

    public RealtyReceiptMessageEvent(Object context, List<RealtyReceiptVo> receiptList) {
        super(context);
        this.receiptList = receiptList;
    }
}
