package com.senox.realty.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;


/**
 * <AUTHOR>
 * @date 2024/6/26 9:28
 */
@Getter
public class ResidentAccessDelEvent extends ApplicationEvent {

    private static final long serialVersionUID = -286899289575744931L;

    private final String contractNo;

    public ResidentAccessDelEvent(Object source, String contractNo) {
        super(source);
        this.contractNo = contractNo;
    }
}
