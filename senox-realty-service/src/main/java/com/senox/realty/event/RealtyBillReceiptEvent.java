package com.senox.realty.event;

import com.senox.realty.domain.RealtyBillItem;
import lombok.Getter;

import java.util.List;

/**
 * 物业账单发票事件
 *
 * <AUTHOR>
 * @date 2023-10-7
 */
@Getter
public class RealtyBillReceiptEvent extends RealtyReceiptEvent {
    private static final long serialVersionUID = -4311369645191942240L;
    private final List<RealtyBillItem> billItems;


    public RealtyBillReceiptEvent(Object context,List<RealtyBillItem> billItems) {
        super(context);
        this.billItems = billItems;
    }
}
