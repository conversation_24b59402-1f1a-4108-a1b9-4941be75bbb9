package com.senox.realty.controller;

import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.realty.service.MaintainOrderDayReportService;
import com.senox.realty.service.MaintainOrderMonthReportService;
import com.senox.realty.vo.MaintainOrderDayReportSearchVo;
import com.senox.realty.vo.MaintainOrderDayReportVo;
import com.senox.realty.vo.MaintainOrderMonthReportSearchVo;
import com.senox.realty.vo.MaintainOrderMonthReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/7/15 14:32
 */
@Api(tags = "维修单报表")
@RestController
@RequiredArgsConstructor
@RequestMapping("/maintain/order/report")
public class MaintainOrderReportController {

    private final MaintainOrderDayReportService dayReportService;
    private final MaintainOrderMonthReportService monthReportService;

    @ApiOperation("物维单日报表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/page")
    public PageStatisticsResult<MaintainOrderDayReportVo, MaintainOrderDayReportVo> dayListPage(@RequestBody MaintainOrderDayReportSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return dayReportService.listPage(search);
    }

    @ApiOperation("物维单月报表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/page")
    public PageStatisticsResult<MaintainOrderMonthReportVo, MaintainOrderMonthReportVo> monthListPage(@RequestBody MaintainOrderMonthReportSearchVo search) {
        if (search.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return monthReportService.listPage(search);
    }
}
