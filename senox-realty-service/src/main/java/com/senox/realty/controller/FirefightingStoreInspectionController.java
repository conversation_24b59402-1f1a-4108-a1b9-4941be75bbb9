package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.convert.FirefightingStoreInspectionConvertor;
import com.senox.realty.domain.FirefightingInspectMedia;
import com.senox.realty.domain.FirefightingInspectRealty;
import com.senox.realty.domain.FirefightingStoreInspection;
import com.senox.realty.service.FirefightingInspectMediaService;
import com.senox.realty.service.FirefightingInspectRealtyService;
import com.senox.realty.service.FirefightingStoreInspectionService;
import com.senox.realty.vo.FirefightingStoreInspectionBriefVo;
import com.senox.realty.vo.FirefightingStoreInspectionSearchVo;
import com.senox.realty.vo.FirefightingStoreInspectionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/25 15:50
 */
@Api(tags = "商铺消防巡检记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/inspection/store")
public class FirefightingStoreInspectionController extends BaseController {

    private final FirefightingStoreInspectionService storeInspectionService;
    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final FirefightingStoreInspectionConvertor storeInspectionConvertor;


    @ApiOperation("添加商铺消防巡检记录")
    @PostMapping("/add")
    public Long addStoreInspection(@Validated(Add.class) @RequestBody FirefightingStoreInspectionVo inspection) {
        FirefightingStoreInspection entity = storeInspectionConvertor.toDo(inspection);

        initEntityCreator(entity);
        initEntityModifier(entity);
        return storeInspectionService.addInspection(inspection.getTaskId(), entity,
                inspection.getRealtySerials(), inspection.getMedias(), inspection.getReinspectMedias());
    }

    @ApiOperation("更新商铺消防巡检记录")
    @PostMapping("/update")
    public void updateStoreInspection(@Validated(Update.class) @RequestBody FirefightingStoreInspectionVo inspection) {
        FirefightingStoreInspection entity = storeInspectionConvertor.toDo(inspection);

        initEntityModifier(entity);
        storeInspectionService.updateInspection(entity, inspection.getRealtySerials(),
                inspection.getMedias(), inspection.getReinspectMedias());
    }

    @ApiOperation("删除商铺消防巡检记录")
    @PostMapping("/delete/{id}")
    public void deleteStoreInspection(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        storeInspectionService.deleteInspection(id);
    }

    @ApiOperation("获取商铺消防巡检记录")
    @GetMapping("/get/{id}")
    public FirefightingStoreInspectionVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        FirefightingStoreInspection inspection = storeInspectionService.findById(id);

        FirefightingStoreInspectionVo result = inspection == null ? null : storeInspectionConvertor.toVo(inspection);
        if (result != null) {
            List<FirefightingInspectRealty> realtyList = inspectRealtyService.findByInspectId(id, InspectionType.STORE);
            result.setRealtySerials(realtyList.stream().map(FirefightingInspectRealty::getRealtySerial).collect(Collectors.toList()));

            List<FirefightingInspectMedia> medias = inspectMediaService.findByInspectId(id, InspectionType.STORE);
            if (!CollectionUtils.isEmpty(medias)) {
                List<String> inspectMedias = medias.stream()
                        .filter(x -> x.getInspectTimes() == 1)
                        .map(FirefightingInspectMedia::getMediaUrl)
                        .collect(Collectors.toList());
                List<String> reinspectMedias = medias.stream()
                        .filter(x -> x.getInspectTimes() > 1)
                        .map(FirefightingInspectMedia::getMediaUrl)
                        .collect(Collectors.toList());
                result.setMedias(inspectMedias);
                result.setReinspectMedias(reinspectMedias);
            }
        }
        return result;
    }

    @ApiOperation("商铺消防巡检记录数统计")
    @PostMapping("/count")
    public int countStoreInspection(@RequestBody FirefightingStoreInspectionSearchVo search) {
        return storeInspectionService.countInspection(search);
    }

    @ApiOperation("商铺消防巡检记录页")
    @PostMapping("/page")
    public PageResult<FirefightingStoreInspectionBriefVo> listStoreInspectionPage(@RequestBody FirefightingStoreInspectionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> storeInspectionService.countInspection(search), () -> {
            List<FirefightingStoreInspection> list = storeInspectionService.listInspection(search);
            return storeInspectionConvertor.toBriefVo(list);
        });
    }


}
