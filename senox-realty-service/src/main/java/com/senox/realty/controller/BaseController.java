package com.senox.realty.controller;

import com.senox.common.domain.OperateEntity;
import com.senox.common.exception.UnAuthorizedException;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;

/**
 * <AUTHOR>
 * @Date 2020/12/22 16:45
 */
public class BaseController {

    /**
     * 初始化实体创建人信息
     * @param entity
     */
    protected void initEntityCreator(OperateEntity entity) {
        if (entity.getCreatorId() == null || entity.getCreatorId() < 1L) {
            AdminUserDto adminUser = getUserInContext();
            entity.setCreatorId(adminUser.getUserId());
            entity.setCreatorName(adminUser.getUsername());
        }
    }

    /**
     * 初始化实体修改人信息
     * @param entity
     */
    protected void initEntityModifier(OperateEntity entity) {
        if (entity.getModifierId() == null || entity.getModifierId() < 1L) {
            AdminUserDto adminUser = getUserInContext();
            entity.setModifierId(adminUser.getUserId());
            entity.setModifierName(adminUser.getUsername());
        }
    }

    /**
     * 获取上下文用户信息
     * @return
     */
    protected AdminUserDto getUserInContext() {
        if (!AdminContext.isUserValid()) {
            throw new UnAuthorizedException();
        }
        return AdminContext.getUser();
    }


}
