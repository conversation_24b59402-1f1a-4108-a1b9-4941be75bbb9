package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.convert.MaintainOrderConvertor;
import com.senox.realty.domain.MaintainEvaluateMedia;
import com.senox.realty.domain.MaintainMedia;
import com.senox.realty.domain.MaintainOrder;
import com.senox.realty.service.MaintainEvaluateMediaService;
import com.senox.realty.service.MaintainJobService;
import com.senox.realty.service.MaintainMediaService;
import com.senox.realty.service.MaintainOrderService;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "维修单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/maintain/order")
public class MaintainOrderController extends BaseController {

    private final MaintainOrderService maintainOrderService;

    private final MaintainOrderConvertor convertor;

    private final MaintainMediaService maintainMediaService;

    private final MaintainJobService maintainJobService;

    private final MaintainEvaluateMediaService evaluateMediaService;

    @ApiOperation("添加维修单")
    @PostMapping("/add")
    public Long addMaintainOrder(@RequestBody MaintainOrderVo maintainOrderVo) {
        MaintainOrder maintainOrder = convertor.toDo(maintainOrderVo);
        initEntityCreator(maintainOrder);
        initEntityModifier(maintainOrder);
        if (StringUtils.isBlank(maintainOrderVo.getCreateOpenid())) {
            maintainOrder.setCreateOpenid(StringUtils.EMPTY);
        }
        return maintainOrderService.addMaintainOrder(maintainOrder, maintainOrderVo.getMediaUrls());
    }

    @ApiOperation("修改维修单")
    @PostMapping("/update")
    public void updateMaintainOrder(@RequestBody MaintainOrderVo maintainOrderVo) {
        MaintainOrder maintainOrder = convertor.toDo(maintainOrderVo);
        initEntityModifier(maintainOrder);
        maintainOrderService.updateMaintainOrder(maintainOrder, maintainOrderVo.getMediaUrls());
    }

    @ApiOperation("获取维修单")
    @GetMapping("/get/{id}")
    public MaintainOrderVo findMaintainOrder(@PathVariable Long id, @RequestParam Boolean media) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        MaintainOrder maintainOrder = maintainOrderService.findById(id);
        MaintainOrderVo orderVo = maintainOrder == null ? null : convertor.toVo(maintainOrder);
        if (BooleanUtils.isTrue(media) && orderVo != null) {
            List<MaintainMedia> list = maintainMediaService.listByOrderId(id);
            orderVo.setMediaUrls(CollectionUtils.isEmpty(list) ? null : list.stream().map(MaintainMedia::getMediaUrl).collect(Collectors.toList()));
            List<MaintainEvaluateMedia> evaluateMedia = evaluateMediaService.listMediasByOrderId(id);
            orderVo.setEvaluateMediaUrls(CollectionUtils.isEmpty(evaluateMedia) ? null : evaluateMedia.stream().map(MaintainEvaluateMedia::getMediaUrl).collect(Collectors.toList()));
        }
        return orderVo;
    }

    @ApiOperation("查询维修单列表")
    @PostMapping("/list")
    public PageResult<MaintainOrderVo> listMaintainOrder(@RequestBody MaintainOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (StringUtils.isBlank(search.getCreateOpenid())) {
            search.setCreateOpenid(StringUtils.EMPTY);
        }
        return maintainOrderService.pageMaintainOrder(search);
    }

    @ApiOperation("删除维修单")
    @PostMapping("/delete/{id}")
    public void deleteMaintainOrder(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        List<MaintainJobVo> list = maintainJobService.listDispatchByOrderId(id);
        if (CollectionUtils.isEmpty(list)) {
            //如果没有派工单则可删除维修单 且只可删除初始化中的维修单
            maintainOrderService.deleteMaintainOrder(id);
        }
    }

    @ApiOperation("维修单列表导出所有处理节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/export/list")
    public List<MaintainOrderVo> exportListMaintainOrder(@RequestBody MaintainOrderSearchVo searchVo) {
        return maintainOrderService.exportListMaintainOrder(searchVo);
    }

    @ApiOperation("维修单数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count")
    public int countMaintainOrder(@RequestBody MaintainOrderSearchVo searchVo) {
        return maintainOrderService.countMaintainOrder(searchVo);
    }

    @ApiOperation("维修单费用统计分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistic/page")
    public PageResult<MaintainOrderStatisticVo> pageOrderStatistic(@RequestBody MaintainOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return maintainOrderService.pageOrderStatistic(search);
    }

    @ApiOperation("维修单费用合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistic/sum")
    public MaintainOrderStatisticVo sumOrderStatistic(@RequestBody MaintainOrderSearchVo searchVo) {
        return maintainOrderService.sumOrderStatistic(searchVo);
    }

    @ApiOperation("维修单评价")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/evaluate")
    public void evaluateOrder(@RequestBody MaintainOrderEvaluateVo evaluateVo) {
        maintainOrderService.evaluateOrder(evaluateVo);
    }

    @ApiOperation("重置维修单评价")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/evaluate/reset/{orderId}")
    public void resetEvaluate(@PathVariable Long orderId) {
        maintainOrderService.resetEvaluate(orderId);
    }

    @ApiOperation("维修单评价分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/evaluate/page")
    public PageResult<MaintainOrderVo> evaluateOrderPage(@RequestBody MaintainOrderEvaluateSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return maintainOrderService.evaluateOrderPage(searchVo);
    }
}
