package com.senox.realty.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.convert.AdvertisingStatisticsConvertor;
import com.senox.realty.convert.RealtyStatisticsConvertor;
import com.senox.realty.domain.AdvertisingStatistics;
import com.senox.realty.domain.RealtyStatistics;
import com.senox.realty.service.AdvertisingStatisticsService;
import com.senox.realty.service.RealtyStatisticsService;
import com.senox.realty.vo.AdvertisingStatisticsVo;
import com.senox.realty.vo.StatisticsGenerateVo;
import com.senox.realty.vo.StatisticsSearchVo;
import com.senox.realty.vo.RealtyStatisticsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/24 11:42
 */
@Api("统计")
@RequiredArgsConstructor
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    private final RealtyStatisticsService realtyStatisticsService;
    private final RealtyStatisticsConvertor realtyStatisticsConvertor;
    private final AdvertisingStatisticsService advertisingStatisticsService;
    private final AdvertisingStatisticsConvertor advertisingStatisticsConvertor;

    @ApiOperation("物业统计生成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/realty/generate")
    public void generateRealtyStatistics(@RequestBody StatisticsGenerateVo generateVo) {
        realtyStatisticsService.generateRealtyStatistics(generateVo);
    }

    @ApiOperation("物业统计分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/realty/page")
    public PageResult<RealtyStatisticsVo> realtyStatisticsPageResult(@RequestBody StatisticsSearchVo searchVo) {
        PageResult<RealtyStatistics> result = realtyStatisticsService.realtyStatisticsPageResult(searchVo);
        return PageResult.convertPage(result, realtyStatisticsConvertor::toVo);
    }

    @ApiOperation("根据统计日期获取物业统计记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/realty/findByDate")
    public RealtyStatisticsVo findRealtyStatisticsByDate(@RequestParam(name = "statisticsDate") @DateTimeFormat(pattern="yyyy-MM-d") LocalDate statisticsDate) {
        return realtyStatisticsConvertor.toVo(realtyStatisticsService.findRealtyStatisticsByDate(statisticsDate));
    }

    @ApiOperation("广告位统计生成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/advertising/generate")
    public void generateAdvertisingStatistics(@RequestBody StatisticsGenerateVo generateVo) {
        advertisingStatisticsService.generateAdvertisingStatistics(generateVo);
    }

    @ApiOperation("广告位统计分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/advertising/page")
    public PageResult<AdvertisingStatisticsVo> advertisingStatisticsPageResult(@RequestBody StatisticsSearchVo searchVo) {
        PageResult<AdvertisingStatistics> result = advertisingStatisticsService.advertisingStatisticsPageResult(searchVo);
        return PageResult.convertPage(result, advertisingStatisticsConvertor::toVo);
    }

    @ApiOperation("根据统计日期获取广告位统计记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/advertising/findByDate")
    public AdvertisingStatisticsVo findAdvertisingStatisticsByDate(@RequestParam(name = "statisticsDate") @DateTimeFormat(pattern="yyyy-MM-d") LocalDate statisticsDate) {
        return advertisingStatisticsConvertor.toVo(advertisingStatisticsService.findAdvertisingStatisticsByDate(statisticsDate));
    }
}
