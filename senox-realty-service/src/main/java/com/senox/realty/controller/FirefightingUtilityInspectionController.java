package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.convert.FirefightingUtilityInspectionConvertor;
import com.senox.realty.domain.FirefightingInspectMedia;
import com.senox.realty.domain.FirefightingUtilityInspection;
import com.senox.realty.service.FirefightingInspectMediaService;
import com.senox.realty.service.FirefightingUtilityInspectionService;
import com.senox.realty.vo.FirefightingUtilityInspectionSearchVo;
import com.senox.realty.vo.FirefightingUtilityInspectionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/14 9:01
 */
@Api(tags = "公共消防设施巡检")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/inspection/utility")
public class FirefightingUtilityInspectionController extends BaseController {

    private final FirefightingUtilityInspectionService inspectionService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final FirefightingUtilityInspectionConvertor inspectionConvertor;


    @ApiOperation("添加公共消防设施巡检记录")
    @PostMapping("/add")
    public Long addUtilityInspection(@Validated @RequestBody FirefightingUtilityInspectionVo inspection) {
        FirefightingUtilityInspection entity = inspectionConvertor.toDo(inspection);
        initEntityCreator(entity);
        initEntityModifier(entity);

        return inspectionService.addInspection(inspection.getTaskId(), entity, inspection.getMedias());
    }

    @ApiOperation("更新公共消防设施巡检记录")
    @PostMapping("/update")
    public void updateUtilityInspection(@Validated @RequestBody FirefightingUtilityInspectionVo inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingUtilityInspection entity = inspectionConvertor.toDo(inspection);
        initEntityModifier(entity);
        inspectionService.updateInspection(entity, inspection.getMedias());
    }

    @ApiOperation("删除公共消防设施巡检记录")
    @PostMapping("/delete/{id}")
    public void deleteUtilityInspection(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        inspectionService.deleteInspection(id);
    }

    @ApiOperation("获取公共消防设施巡检记录")
    @GetMapping("/get/{id}")
    public FirefightingUtilityInspectionVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingUtilityInspection inspection = inspectionService.findById(id);
        FirefightingUtilityInspectionVo result = inspection == null ? null : inspectionConvertor.toVo(inspection);
        if (result != null) {
            List<FirefightingInspectMedia> medias = inspectMediaService.findByInspectId(id, InspectionType.UTILITY);
            if (!CollectionUtils.isEmpty(medias)) {
                result.setMedias(medias.stream().map(FirefightingInspectMedia::getMediaUrl).collect(Collectors.toList()));
            }
        }
        return result;
    }

    @ApiOperation("公共消防设施巡检记录页")
    @PostMapping("/page")
    public PageResult<FirefightingUtilityInspectionVo> listInspectionPage(@RequestBody FirefightingUtilityInspectionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> inspectionService.countInspection(search), () -> inspectionService.listInspection(search));
    }
}
