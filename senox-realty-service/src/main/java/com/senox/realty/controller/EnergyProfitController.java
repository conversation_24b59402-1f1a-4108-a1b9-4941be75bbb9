package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.PageResult;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.convert.EnergyProfitConvertor;
import com.senox.realty.convert.EnergyProfitItemConvertor;
import com.senox.realty.domain.EnergyProfit;
import com.senox.realty.domain.EnergyProfitItem;
import com.senox.realty.service.EnergyProfitItemService;
import com.senox.realty.service.EnergyProfitService;
import com.senox.realty.vo.EnergyProfitEditVo;
import com.senox.realty.vo.EnergyProfitItemVo;
import com.senox.realty.vo.EnergyProfitSearchVo;
import com.senox.realty.vo.EnergyProfitVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/25 15:56
 */

@Api(tags = "能源损益")
@RestController
@RequiredArgsConstructor
@RequestMapping("/energy/profit")
public class EnergyProfitController extends BaseController {

    private final EnergyProfitService profitService;
    private final EnergyProfitItemService profitItemService;
    private final EnergyProfitConvertor profitConvertor;
    private final EnergyProfitItemConvertor profitItemConvertor;

    @ApiOperation("生成能源损益")
    @PostMapping("/generate")
    public EnergyProfitVo generate(@RequestBody BillTimeVo billTime, @RequestParam EnergyType type) {
        if (!WrapperClassUtils.biggerThanInt(billTime.getYear(), 0)
                || !WrapperClassUtils.biggerThanInt(billTime.getMonth(), 0)) {
            throw new InvalidParameterException();
        }

        EnergyProfit result = profitService.generateProfits(billTime, type);
        return profitConvertor.toVo(result);
    }

    @ApiOperation("保存能源损益")
    @PostMapping("/save")
    public EnergyProfitVo save(@Validated @RequestBody EnergyProfitEditVo profitEdit) {
        EnergyProfit profit = profitConvertor.editVoToDo(profitEdit);
        initEntityModifier(profit);

        List<EnergyProfitItem> systemProfits = profitItemConvertor.toDo(profitEdit.getSystemProfits());
        List<EnergyProfitItem> balanceProfits = profitItemConvertor.toDo(profitEdit.getBalanceProfits());

        EnergyProfit result = profitService.saveProfits(profit, systemProfits, balanceProfits);
        return profitConvertor.toVo(result);
    }

    @ApiOperation("获取能源损益")
    @GetMapping("/get/{id}")
    public EnergyProfitVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        EnergyProfit result = profitService.findById(id);
        return result == null ? null : profitConvertor.toVo(result);
    }

    @ApiOperation("能源损益页")
    @PostMapping("/page")
    public PageResult<EnergyProfitVo> listProfitPage(@RequestBody EnergyProfitSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<EnergyProfit> page = profitService.listPage(search);
        return PageResult.convertPage(page, profitConvertor::toVo);
    }

    @ApiOperation("能源损益明细")
    @PostMapping("/item/list/{profitId}")
    public List<EnergyProfitItemVo> listProfitItem(@PathVariable Long profitId) {
        if (!WrapperClassUtils.biggerThanLong(profitId, 0L)) {
            throw new InvalidParameterException();
        }

        List<EnergyProfitItem> list = profitItemService.listByProfitId(profitId);
        return profitItemConvertor.toVo(list);
    }
}
