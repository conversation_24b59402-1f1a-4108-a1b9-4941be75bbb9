package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.domain.BusinessRegion;
import com.senox.realty.service.BusinessRegionService;
import com.senox.realty.vo.BusinessRegionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/23 14:16
 */
@Api(tags = "经营区域管理")
@RestController
@RequestMapping("/businessRegion")
public class BusinessRegionController extends BaseController {

    @Autowired
    private BusinessRegionService regionService;

    @ApiOperation("添加经营区域")
    @PostMapping("/add")
    public Long addRegion(@Validated({Add.class}) @RequestBody BusinessRegionVo regionVo) {
        BusinessRegion region = regionVo2Region(regionVo);
        initEntityCreator(region);
        initEntityModifier(region);
        return this.regionService.addRegion(region);
    }

    @ApiOperation("更新经营区域")
    @PostMapping("/update")
    public void updateRegion(@Validated({Update.class}) @RequestBody BusinessRegionVo regionVo) {
        if (regionVo.getId() < 1L) {
            throw new InvalidParameterException("无效id");
        }
        BusinessRegion region = regionVo2Region(regionVo);
        initEntityModifier(region);

        this.regionService.updateRegion(region);
    }

    @ApiOperation("获取经营区域")
    @GetMapping("/get/{id}")
    public BusinessRegionVo getRegion(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        BusinessRegion region = this.regionService.findById(id);
        return region == null ? null : region2Vo(region);
    }

    @ApiOperation("经营区域列表")
    @PostMapping("/list")
    public List<BusinessRegionVo> listRegion() {
        List<BusinessRegion> list = regionService.listAll();
        return CollectionUtils.isEmpty(list) ? Collections.emptyList()
                : list.stream().map(this::region2Vo).collect(Collectors.toList());
    }

    private BusinessRegion regionVo2Region(BusinessRegionVo areaVo) {
        BusinessRegion result = new BusinessRegion();
        BeanUtils.copyProperties(areaVo, result);
        return result;
    }

    private BusinessRegionVo region2Vo(BusinessRegion businessRegion) {
        BusinessRegionVo result = new BusinessRegionVo();
        BeanUtils.copyProperties(businessRegion, result);
        return result;
    }

}
