package com.senox.realty.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.service.PowerOutageNoticeService;
import com.senox.realty.vo.PowerOutageNoticeSearchVo;
import com.senox.realty.vo.PowerOutageNoticeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

@Api(tags = "物业停电通知")
@RequiredArgsConstructor
@RequestMapping("/realty/power/outage/notice")
@RestController
public class PowerOutageNoticeController extends BaseController {
    private final PowerOutageNoticeService noticeService;

    @ApiOperation("添加")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void add(@RequestBody PowerOutageNoticeVo notice) {
        noticeService.addBatch(Collections.singletonList(notice));
    }

    @ApiOperation("根据id更新")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/updateById")
    public void updateById(@RequestBody PowerOutageNoticeVo notice) {
        noticeService.updateBatchById(Collections.singletonList(notice));
    }

    @ApiOperation("根据id查找")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/findById/{id}")
    public PowerOutageNoticeVo findById(@PathVariable Long id) {
        return noticeService.findById(id);
    }

    @ApiOperation("根据id删除")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Long id) {
        noticeService.deleteByIds(Collections.singletonList(id));
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list/page")
    public PageResult<PowerOutageNoticeVo> pageList(@RequestBody PowerOutageNoticeSearchVo search) {
        return noticeService.pageList(search);
    }

}
