package com.senox.realty.controller;

import com.senox.common.constant.BillStatus;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.convert.RealtyBillWeConvertor;
import com.senox.realty.domain.RealtyBillWe;
import com.senox.realty.service.ContractBillService;
import com.senox.realty.service.RealtyBillWeService;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyBillPageResult;
import com.senox.realty.vo.RealtyBillWeSearchVo;
import com.senox.realty.vo.RealtyBillWeVo;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2022/11/7 14:55
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/realty/bill/we")
public class RealtyBillWeController extends BaseController {

    private final RealtyBillWeService billWeService;
    private final RealtyBillWeConvertor billWeConvertor;
    private final ContractBillService contractBillService;

    @ApiOperation("生成并更新水电账单")
    @PostMapping("/generate")
    public void generateWeBill(@Validated @RequestBody BillMonthVo month) {
        billWeService.buildAndSaveWeBill(month, getUserInContext());
    }

    @ApiOperation("更新水电账单")
    @PostMapping("/update")
    public void updateWeBill(@RequestBody RealtyBillWeVo bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        RealtyBillWe item = billWeConvertor.toDo(bill);
        initEntityModifier(item);
        billWeService.updateWeBill(item);
    }

    @ApiOperation("删除水电账单")
    @PostMapping("/delete/{id}")
    public void deleteWeBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        RealtyBillWeVo weBill = billWeService.findById(id);
        if (weBill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (weBill.isBillPaid()) {
            throw new BusinessException("账单已支付");
        }

        billWeService.deleteWeBill(id);

        if (WrapperClassUtils.biggerThanLong(weBill.getBillId(), 0L)) {
            // 更新水电读数
            contractBillService.saveRealtyBillWeData(weBill.getBillId());
        }
    }

    @ApiOperation("同步水电账单至应收账单")
    @PostMapping("/syncOne/{id}")
    public void syncWeBill2RealtyBill(@PathVariable Long id, @RequestBody BillMonthVo billMonth) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        RealtyBillWeVo weBill = billWeService.findById(id);
        if (weBill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到水电账单");
        }
        if (BillStatus.fromValue(weBill.getStatus()) == BillStatus.PAID) {
            log.info("同步水电账单 {} 至应收账单失败，应收账单已支付！", id);
            return;
        }

        if (WrapperClassUtils.biggerThanLong(weBill.getBillId(), 0L)) {
            // 更新水电读数
            contractBillService.saveRealtyBillWeData(weBill.getBillId());
        } else {
            // 新增只有水电费的应收
            if (billMonth.getYear() == null) {
                billMonth.setYear(LocalDate.now().getYear());
            }
            if (billMonth.getMonth() == null) {
                billMonth.setYear(LocalDate.now().getMonthValue());
            }
            contractBillService.saveRealtyBillWithWeDataOnly(billMonth, Collections.singletonList(billWeConvertor.toDo(weBill)), getUserInContext());
        }
    }

    @ApiOperation("水电账单明细")
    @GetMapping("/get/{id}")
    public RealtyBillWeVo getRealtyBillWe(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return billWeService.findById(id);
    }


    @ApiOperation("水电账单合计")
    @PostMapping("/sum")
    public RealtyBillWeVo sumWeBill(@RequestBody RealtyBillWeSearchVo search) {
        return billWeService.sumWeBill(search);
    }

    @ApiOperation("物业账单水电列表")
    @PostMapping("/list")
    public PageResult<RealtyBillWeVo> listRealtyBillWe(@RequestBody RealtyBillWeSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return billWeService.listWeBillPage(search);
    }
}
