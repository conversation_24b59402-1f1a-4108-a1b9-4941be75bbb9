package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.convert.RealtyDepositConvertor;
import com.senox.realty.domain.RealtyDeposit;
import com.senox.realty.service.RealtyDepositService;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/8 14:30
 */
@Api(tags = "物业押金")
@RestController
@RequestMapping("/deposit/realty")
@RequiredArgsConstructor
public class RealtyDepositController extends BaseController {

    private final RealtyDepositService depositService;
    private final RealtyDepositConvertor depositConvertor;

    @ApiOperation("添加物业押金")
    @PostMapping("/add")
    public Long addDeposit(@RequestBody RealtyDepositVo depositVo) {
        // 物业、客户判定
        checkDeposit(depositVo);

        // 添加物业押金
        RealtyDeposit deposit = depositConvertor.toDo(depositVo);
        initEntityCreator(deposit);
        initEntityModifier(deposit);
        return depositService.addDeposit(deposit);
    }

    @ApiOperation("批量保存物业押金")
    @PostMapping("/batchAdd")
    public void batchAddDeposit(@RequestBody List<RealtyDepositVo> depositVoList) {
        depositVoList = depositVoList.stream().filter(x -> DecimalUtils.isPositive(x.getAmount())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(depositVoList)) {
            throw new InvalidParameterException();
        }

        List<RealtyDeposit> depositList = new ArrayList<>(depositVoList.size());
        for (RealtyDepositVo item : depositVoList) {
            checkDeposit(item);

            RealtyDeposit deposit = depositConvertor.toDo(item);
            initEntityCreator(deposit);
            initEntityModifier(deposit);
            depositList.add(deposit);
        }
        depositService.batchAddDeposit(depositList);
    }


    @ApiOperation("更新物业押金")
    @PostMapping("/update")
    public void updateDeposit(@RequestBody RealtyDepositVo depositVo) {
        if (!WrapperClassUtils.biggerThanLong(depositVo.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        // 物业押金
        RealtyDeposit deposit = depositConvertor.toDo(depositVo);
        initEntityModifier(deposit);
        depositService.updateDeposit(deposit);
    }

    @ApiOperation("更新押金支付结果")
    @PostMapping("/paid/update")
    public void updateDepositStatus(@Validated @RequestBody BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            throw new InvalidParameterException("无效的订单id");
        }
        if (!WrapperClassUtils.biggerThanLong(billPaid.getTollMan(), 0L)) {
            throw new InvalidParameterException("无效的收费员");
        }

        depositService.updateOneTimeFeeBillStatus(billPaid);
    }

    @ApiOperation("更新物业押金票据号")
    @PostMapping("/serial/update")
    public void updateDepositSerial(@Validated @RequestBody TollSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        serial.setOperatorId(getUserInContext().getUserId());
        serial.setOperatorName(getUserInContext().getUsername());
        depositService.updateDepositTollSerial(serial);
    }

    @ApiOperation("支付物业押金")
    @PostMapping("/pay/{id}")
    public void payDeposit(@PathVariable Long id, @RequestBody BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        if (!WrapperClassUtils.biggerThanLong(toll.getOperator(), 0L)) {
            toll.setOperator(getUserInContext().getUserId());
        }
        depositService.payDeposit(id, toll);
    }

    @ApiOperation("撤销支付物业押金")
    @PostMapping("/payRevoke/{id}")
    public void revokeDepositPayment(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        BillTollVo toll = new BillTollVo();
        toll.setId(id);
        toll.setOperator(getUserInContext().getUserId());
        toll.setOperatorName(getUserInContext().getUsername());
        depositService.revokeDepositPayment(toll);
    }

    @ApiOperation("物业押金退费")
    @PostMapping("/refund/{id}")
    public void refundDeposit(@PathVariable Long id, @RequestBody BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        if (toll.getAmount() == null) {
            throw new InvalidParameterException("无效的退档金额");
        }
        if (!WrapperClassUtils.biggerThanLong(toll.getOperator(), 0L)) {
            toll.setOperator(getUserInContext().getUserId());
        }
        depositService.refundDeposit(id, toll);
    }

    @ApiOperation("撤销物业押金退费")
    @PostMapping("/refundRevoke/{id}")
    public void revokeDepositRefund(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        BillTollVo toll = new BillTollVo();
        toll.setId(id);
        toll.setOperator(getUserInContext().getUserId());
        toll.setOperatorName(getUserInContext().getUsername());
        depositService.revokeDepositRefund(toll);
    }

    @ApiOperation("获取物业押金")
    @GetMapping("/get/{id}")
    public RealtyDepositVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return depositService.findById(id);
    }

    @ApiOperation("获取物业押金")
    @PostMapping("/listByIds")
    public List<RealtyDepositVo> listByIds(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        return depositService.listByIds(ids);
    }

    @ApiOperation("合同押金")
    @GetMapping("/contractDeposit/list/{contractId}")
    public List<RealtyDepositVo> listContractDeposit(@PathVariable Long contractId) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)) {
            throw new InvalidParameterException();
        }
        return depositService.listContractDeposit(contractId);
    }

    @ApiOperation("物业押金记录")
    @PostMapping("/list")
    public RefundBillPageResult<RealtyDepositVo> listDepositPage(@Validated @RequestBody RealtyDepositSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefundBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<RealtyDepositVo> page = depositService.listDepositPage(search);

        // result
        RefundBillPageResult<RealtyDepositVo> resultPage = new RefundBillPageResult<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());
        resultPage.initTotalPage();

        // 总计
        RealtyDepositVo summary = depositService.sumDeposit(search);
        if (summary != null) {
            resultPage.setAmount(summary.getAmount() == null ? BigDecimal.ZERO : summary.getAmount());
            resultPage.setRefundAmount(summary.getRefundAmount() == null ? BigDecimal.ZERO : summary.getRefundAmount());
            resultPage.setTotalAmount(summary.getTotalAmount() == null ? BigDecimal.ZERO : summary.getTotalAmount());
        }
        return resultPage;
    }

    private void checkDeposit(RealtyDepositVo deposit) {
        // 物业、客户判定
        if (!WrapperClassUtils.biggerThanLong(deposit.getContractId(), 0L)) {
            if (!WrapperClassUtils.biggerThanLong(deposit.getCustomerId(), 0L)) {
                throw new InvalidParameterException("无效的客户信息");
            }
            if (!WrapperClassUtils.biggerThanLong(deposit.getRealtyId(), 0L)) {
                throw new InvalidParameterException("无效的物业信息");
            }
        }

        if (!DecimalUtils.isPositive(deposit.getAmount())) {
            throw new InvalidParameterException("无效的押金金额");
        }
    }

}
