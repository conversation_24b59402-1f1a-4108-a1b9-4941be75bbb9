package com.senox.realty.controller;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.TemplateStatus;
import com.senox.realty.convert.FirefightingTemplateConvertor;
import com.senox.realty.domain.FirefightingFormTemplate;
import com.senox.realty.domain.FirefightingTemplate;
import com.senox.realty.domain.FirefightingTemplateVariables;
import com.senox.realty.service.FirefightingFormTemplateService;
import com.senox.realty.service.FirefightingTemplateService;
import com.senox.realty.service.FirefightingTemplateVariablesService;
import com.senox.realty.vo.FireFightingTemplateSearchVo;
import com.senox.realty.vo.FirefightingFormTemplateVo;
import com.senox.realty.vo.FirefightingTemplateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 14:54
 */
@Api(tags = "消防模板")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/template")
public class FirefightingTemplateController extends BaseController {

    private final FirefightingTemplateService templateService;
    private final FirefightingTemplateConvertor templateConvertor;
    private final FirefightingTemplateVariablesService templateVariablesService;
    private final FirefightingFormTemplateService formTemplateService;

    @ApiOperation("添加消防模板")
    @PostMapping("/add")
    public Long addTemplate(@Validated(Add.class) @RequestBody FirefightingTemplateVo template,
                            @RequestParam(required = false, defaultValue = "true") Boolean newVersion) {
        FirefightingTemplate entity = templateConvertor.toDo(template);
        entity.setStatus(TemplateStatus.INIT.getValue());
        if (BooleanUtils.isTrue(newVersion)) {
            entity.setVersion(null);
        }

        List<FirefightingTemplateVariables> attrs = null;
        if (!CollectionUtils.isEmpty(template.getAttrs())) {
            attrs = templateConvertor.toAttrsDo(template.getAttrs());
        }

        initEntityCreator(entity);
        initEntityModifier(entity);
        return templateService.addTemplate(entity, attrs);
    }

    @ApiOperation("更新消防模板")
    @PostMapping("/update")
    public void updateTemplate(@Validated(Update.class) @RequestBody FirefightingTemplateVo template) {
        FirefightingTemplate entity = templateConvertor.toDo(template);
        entity.setStatus(null);

        List<FirefightingTemplateVariables> attrs = null;
        if (!CollectionUtils.isEmpty(template.getAttrs())) {
            attrs = templateConvertor.toAttrsDo(template.getAttrs());
        }

        initEntityModifier(entity);
        templateService.updateTemplate(entity, attrs);
    }

    @ApiOperation("启用消防模板")
    @PostMapping("/enable/{id}")
    public void enableTemplate(@PathVariable Long id) {
        FirefightingTemplate dbItem = templateService.findTemplateById(id);
        if (dbItem == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (TemplateStatus.fromStatus(dbItem.getStatus()) == TemplateStatus.VALID) {
            return;
        }

        FirefightingTemplate entity = new FirefightingTemplate();
        entity.setId(id);
        entity.setStatus(TemplateStatus.VALID.getValue());
        entity.setValidDate(LocalDate.now());

        initEntityModifier(entity);
        templateService.updateTemplate(entity);
    }

    @ApiOperation("停用消防模板")
    @PostMapping("/disable/{id}")
    public void disableTemplate(@PathVariable Long id) {
        FirefightingTemplate dbItem = templateService.findTemplateById(id);
        if (dbItem == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (TemplateStatus.fromStatus(dbItem.getStatus()) != TemplateStatus.VALID) {
            throw new BusinessException("只允许停用已启用的模板");
        }

        FirefightingTemplate entity = new FirefightingTemplate();
        entity.setId(id);
        entity.setStatus(TemplateStatus.INVALID.getValue());
        entity.setInvalidDate(LocalDate.now());

        initEntityModifier(entity);
        templateService.updateTemplate(entity);
    }

    @ApiOperation("删除消防模板")
    @PostMapping("/delete/{id}")
    public void deleteTemplate(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        templateService.deleteTemplate(id);
    }

    @ApiOperation("获取消防模板")
    @GetMapping("/get/{id}")
    public FirefightingTemplateVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingTemplate template = templateService.findTemplateById(id);
        if (template == null) {
            return null;
        }

        FirefightingTemplateVo result = templateConvertor.toVo(template);
        List<FirefightingTemplateVariables> variables = templateVariablesService.listVariablesByCodeAndVersion(template.getCode(), template.getVersion());
        if (!CollectionUtils.isEmpty(variables)) {
            result.setAttrs(templateConvertor.toAttrsVo(variables));
        }
        return result;
    }

    @ApiOperation("获取最新消防模板")
    @PostMapping("/latest")
    public FirefightingTemplateVo findLatestByCode(@RequestBody FireFightingTemplateSearchVo search) {
        if (StringUtils.isBlank(search.getCode())) {
            throw new InvalidParameterException();
        }

        FirefightingTemplate template = templateService.findLatestTemplateByCode(search);
        FirefightingTemplateVo result = template == null ? null : templateConvertor.toVo(template);
        if (result != null) {
            List<FirefightingTemplateVariables> variables = templateVariablesService.listVariablesByCodeAndVersion(template.getCode(), template.getVersion());
            if (!CollectionUtils.isEmpty(variables)) {
                result.setAttrs(templateConvertor.toAttrsVo(variables));
            }
        }
        return result;
    }

    @ApiOperation("消防模板页")
    @PostMapping("/page")
    public PageResult<FirefightingTemplateVo> listTemplatePage(@RequestBody FireFightingTemplateSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> templateService.countTemplate(search), () -> {
            List<FirefightingTemplate> list = templateService.listTemplate(search);
            return templateConvertor.toVo(list);
        });
    }

    @ApiOperation("添加消防表单模板")
    @PostMapping("/form/add")
    public Long addFormTemplate(@Validated @RequestBody FirefightingFormTemplateVo formTemplate) {
        FirefightingFormTemplate entity = templateConvertor.toFormDo(formTemplate);
        initEntityCreator(entity);
        initEntityModifier(entity);
        return formTemplateService.addFormTemplate(entity);
    }

    @ApiOperation("修改消防表单模板")
    @PostMapping("/form/update")
    public void updateFormTemplate(@Validated @RequestBody FirefightingFormTemplateVo formTemplate) {
        if (!WrapperClassUtils.biggerThanLong(formTemplate.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingFormTemplate entity = templateConvertor.toFormDo(formTemplate);
        initEntityModifier(entity);
        formTemplateService.updateFormTemplate(entity);
    }

    @ApiOperation("删除消防表单模板")
    @PostMapping("/form/delete/{id}")
    public void deleteFormTemplate(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        formTemplateService.deleteFormTemplate(id);
    }

    @ApiOperation("获取消防表单模板")
    @GetMapping("/form/get/{id}")
    public FirefightingFormTemplateVo findFormTemplateById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingFormTemplate result = formTemplateService.findById(id);
        return result == null ? null : templateConvertor.toFormVo(result);
    }

    @ApiOperation("消防表单模板列表")
    @PostMapping("/form/list")
    public List<FirefightingFormTemplateVo> listFormTemplate(@RequestParam String form) {
        if (StringUtils.isBlank(form)) {
            throw new InvalidParameterException();
        }

        List<FirefightingFormTemplate> resultList = formTemplateService.listByForm(form);
        return CollectionUtils.isEmpty(resultList) ? Collections.emptyList() : templateConvertor.toFormVo(resultList);
    }
}
