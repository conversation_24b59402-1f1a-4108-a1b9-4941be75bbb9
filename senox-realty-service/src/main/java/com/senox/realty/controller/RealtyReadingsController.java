package com.senox.realty.controller;

import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.convert.RealtyWeConvertor;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.service.RealtyWeService;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyWeBatchVo;
import com.senox.realty.vo.RealtyWePageResult;
import com.senox.realty.vo.RealtyWeSearchVo;
import com.senox.realty.vo.RealtyWeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-25
 */
@Api(tags = "物业水电")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/realty/readings")
public class RealtyReadingsController extends BaseController {

    private final RealtyWeService weService;
    private final RealtyWeConvertor weConvertor;


    @PostMapping("/we/check")
    public Boolean checkReadings(@Validated @RequestBody BillMonthVo billMonth) {
        return weService.checkMonthReadings(billMonth.getYear(), billMonth.getMonth());
    }

    @PostMapping("/we/batchAdd")
    public void batchAddReadings(@Validated @RequestBody RealtyWeBatchVo batchWeData) {
        if (batchWeData.getData().stream().map(RealtyWeVo::getRealtySerial).distinct().count() < batchWeData.getData().size()) {
            throw new InvalidParameterException("物业数据重复");
        }

        List<RealtyWe> list = weConvertor.toDo(batchWeData.getData());
        // 参数初始化
        list.forEach(x -> {
            x.setManMade(true);
            weService.prepareRealtyWe(x, batchWeData.getYear(), batchWeData.getMonth());
        });
        weService.batchAddWeData(list, batchWeData.getOverWrite());
    }

    @PostMapping("/we/update")
    public void update(@RequestBody RealtyWeVo weVo) {
        if (!WrapperClassUtils.biggerThanLong(weVo.getId(), 0)) {
            throw new BusinessException("参数错误");
        }

        RealtyWe we = weConvertor.toDo(weVo);
        initEntityModifier(we);
        weService.update(we);
    }

    @PostMapping("/we/delete")
    public void delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            throw new BusinessException("参数错误");
        }
        weService.delete(ids);
    }

    @PostMapping("/we/reset")
    public void resetMonth(@Validated @RequestBody BillMonthVo month) {
        weService.deleteMonthReadings(month);
    }

    @GetMapping("/we/get/{id}")
    public RealtyWeVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return weService.findById(id);
    }

    @ApiOperation("水电读数列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/we/list")
    public RealtyWePageResult<RealtyWeVo> list(@RequestBody RealtyWeSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyWePageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<RealtyWeVo> page = weService.listReadingsPage(search);

        // sum
        RealtyWeVo sumWe = weService.sumReadings(search);

        // sum
        RealtyWePageResult<RealtyWeVo> result = new RealtyWePageResult<>(search.getPageNo(), search.getPageSize());
        result.setTotalSize(page.getTotalSize());
        result.setDataList(page.getDataList());
        result.initTotalPage();

        if (sumWe != null) {
            result.setLastElectricReadings(sumWe.getLastElectricReadings());
            result.setElectricReadings(sumWe.getElectricReadings());
            result.setElectricCost(sumWe.getElectricCost());
            result.setLastWaterReadings(sumWe.getLastWaterReadings());
            result.setWaterReadings(sumWe.getWaterReadings());
            result.setWaterCost(sumWe.getWaterCost());
        }

        return result;
    }





}
