package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.constant.MaintainFee;
import com.senox.realty.convert.MaintainChargeConvertor;
import com.senox.realty.convert.MaintainChargeItemConvertor;
import com.senox.realty.domain.MaintainCharge;
import com.senox.realty.domain.MaintainChargeItem;
import com.senox.realty.service.MaintainChargeItemService;
import com.senox.realty.service.MaintainChargeService;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/7 8:12
 */
@Api("物维收费")
@RequiredArgsConstructor
@RestController
@RequestMapping("/maintain/charge")
public class MaintainChargeController extends BaseController {

    private final MaintainChargeConvertor convertor;

    private final MaintainChargeService chargeService;

    private final MaintainChargeItemConvertor itemConvertor;

    private final MaintainChargeItemService chargeItemService;

    @ApiOperation("添加维修收费帐单")
    @PostMapping("/add")
    public void addMaintainCharge(@RequestBody MaintainChargeVo chargeVo) {
        MaintainCharge charge = convertor.toDo(chargeVo);
        initEntityCreator(charge);
        initEntityModifier(charge);
        List<MaintainChargeItem> chargeItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(chargeVo.getChargeItemVos())){
            chargeItems = itemConvertor.toDo(chargeVo.getChargeItemVos());
        }
        chargeItems.forEach(item -> {
            initEntityCreator(item);
            initEntityModifier(item);
            item.setFeeTitle(MaintainFee.fromFeeId(item.getFeeId()).getName());
            item.setCreateTime(LocalDateTime.now());
            item.setModifiedTime(LocalDateTime.now());
        });
        chargeService.addMaintainCharge(charge, chargeItems);
    }

    @ApiOperation("维修收费账单列表")
    @PostMapping("/list")
    public MaintainChargePageResult<MaintainChargeDataVo> listMaintainCharge(@RequestBody MaintainChargeSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return MaintainChargePageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        // page
        PageResult<MaintainChargeDataVo> chargePage = chargeService.listMaintainCharge(searchVo);

        // result
        MaintainChargePageResult<MaintainChargeDataVo> resultPage = new MaintainChargePageResult<>(searchVo.getPageNo(), searchVo.getPageSize());
        resultPage.setTotalSize(chargePage.getTotalSize());
        resultPage.setDataList(chargePage.getDataList());
        resultPage.initTotalPage();

        //total
        MaintainChargeDataVo sumCharge = chargeService.sumMaintainCharge(searchVo);
        if (sumCharge != null) {
            resultPage.setReceivableTotalAmount(DecimalUtils.nullToZero(sumCharge.getReceivableAmount()));
            resultPage.setPaidAmount(DecimalUtils.nullToZero(sumCharge.getPaidAmount()));
            resultPage.setLaborTotalAmount(DecimalUtils.nullToZero(sumCharge.getLaborAmount()));
            resultPage.setMaterialTotalAmount(DecimalUtils.nullToZero(sumCharge.getMaterialAmount()));
        }

        return resultPage;
    }

    @ApiOperation("维修收费账单详情")
    @GetMapping("/get/{id}")
    public MaintainChargeDataVo chargeDataVoById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return chargeService.chargeDataVoById(id);
    }

    @ApiOperation("根据派工单号查询维修收费账单详情")
    @GetMapping("/getByJobId/{jobId}")
    public MaintainChargeDataVo chargeDataVoByJobId(@PathVariable Long jobId) {
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            throw new InvalidParameterException();
        }
        return chargeService.chargeDataVoByJobId(jobId);
    }

    @ApiOperation("根据订单号查询维修收费账单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/getByOrderId/{orderId}")
    public List<MaintainChargeDataVo> listChargeDataVoByOrderId(@PathVariable Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new InvalidParameterException();
        }
        return chargeService.listChargeDataVoByOrderId(orderId);
    }

    @ApiOperation("根据收费单id查询物维收费单费项")
    @GetMapping("/get/chargeId/{chargeId}")
    public List<MaintainChargeItemVo> chargeItemList(@PathVariable Long chargeId) {
        if (!WrapperClassUtils.biggerThanLong(chargeId, 0L)) {
            throw new InvalidParameterException();
        }
        return chargeItemService.listChargeItemByChargeId(chargeId);
    }

    @ApiOperation("根据派工id查询物维收费单集合")
    @GetMapping("/get/jobId/{jobId}")
    public List<MaintainChargeItemVo> listChargeItemByJobId(@PathVariable Long jobId){
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            throw new InvalidParameterException();
        }
        return chargeItemService.listChargeItemByJobId(jobId);
    }

    @ApiOperation("根据订单id查询物维收费单集合")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/orderId/{orderId}")
    public List<MaintainChargeItemVo> listChargeItemByOrderId(@PathVariable Long orderId){
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new InvalidParameterException();
        }
        return chargeItemService.listChargeItemByOrderId(orderId);
    }

    @ApiOperation("根据id物维收费单集合")
    @PostMapping("/listByIds")
    public List<MaintainChargeVo> listMaintainChargeByIds(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        return chargeItemService.listMaintainChargeByIds(ids);
    }

    @ApiOperation("更新支付状态")
    @PostMapping("/paid/update")
    public void updateChargeStatus(@Validated @RequestBody BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            throw new InvalidParameterException("无效的订单id");
        }
        if (!WrapperClassUtils.biggerThanLong(billPaid.getTollMan(), 0L)) {
            throw new InvalidParameterException("无效的收费员");
        }

        chargeService.updateChargeStatus(billPaid);
    }

    @ApiOperation("更新维修账单流水号")
    @PostMapping("/serial/save")
    public void saveChargeSerial(@Validated @RequestBody MaintainChargeSerialVo chargeSerial) {
        chargeSerial.setOperatorId(getUserInContext().getUserId());
        chargeService.updateChargeSerial(chargeSerial);
    }

    @ApiOperation("更新账单备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/remark/update")
    public void updateChargeRemark(@Validated @RequestBody List<MaintainChargeRemarkVo> chargeRemarkList) {
        if (CollectionUtils.isEmpty(chargeRemarkList)) {
            throw new InvalidParameterException();
        }
        chargeRemarkList.forEach(billRemark -> {
            billRemark.setOperatorId(ContextUtils.getUserInContext().getUserId());
            billRemark.setOperatorName(ContextUtils.getUserInContext().getUsername());
        });
        chargeService.updateChargeRemark(chargeRemarkList);
    }

    @ApiOperation("批量添加维修所需账单")
    @PostMapping("/batch/add")
    public void batchSaveCharge(@RequestBody List<MaintainChargeItemVo> chargeItemVos) {
        if (CollectionUtils.isEmpty(chargeItemVos)) {
            return;
        }
        Map<Long, List<MaintainChargeItemVo>> jobMap = chargeItemVos.stream().collect(Collectors.groupingBy(MaintainChargeItemVo::getJobId));
        Map<MaintainCharge, List<MaintainChargeItem>> chargeMap = new HashMap<>();
        jobMap.forEach((jobId, item) -> {
            MaintainChargeItemVo itemVo = item.get(0);
            MaintainCharge charge = chargeService.buildCharge(itemVo.getJobId(), itemVo.getOrderId());
            List<MaintainChargeItem> chargeItems = itemConvertor.toDo(item);
            chargeService.initChargeItem(chargeItems);
            chargeMap.put(charge, chargeItems);
        });
        chargeService.batchSaveCharge(chargeMap);
    }

    @ApiOperation("删除维修账单")
    @GetMapping("/delete/{id}")
    public void deleteMaintainCharge(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        chargeService.deleteMaintainCharge(id);
    }

    @ApiOperation("删除维修账单明细")
    @GetMapping("/item/delete/{id}")
    public void deleteMaintainChargeItem(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        chargeService.deleteMaintainChargeItem(id);
    }
}
