package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.convert.FirefightingAccommodateInspectionConvertor;
import com.senox.realty.convert.FirefightingInspectionAttrConvertor;
import com.senox.realty.domain.FirefightingAccommodateInspection;
import com.senox.realty.domain.FirefightingInspectMedia;
import com.senox.realty.domain.FirefightingInspectRealty;
import com.senox.realty.domain.FirefightingInspectionAttr;
import com.senox.realty.service.FirefightingAccommodateInspectionService;
import com.senox.realty.service.FirefightingInspectMediaService;
import com.senox.realty.service.FirefightingInspectRealtyService;
import com.senox.realty.service.FirefightingInspectionAttrService;
import com.senox.realty.vo.FirefightingAccommodateInspectionBriefVo;
import com.senox.realty.vo.FirefightingAccommodateInspectionSearchVo;
import com.senox.realty.vo.FirefightingAccommodateInspectionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/9 14:33
 */
@Api(tags = "三小场所、出租屋消防巡检记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/inspection/accommodate")
public class FirefightingAccommodateInspectionController extends BaseController {

    private final FirefightingAccommodateInspectionService inspectionService;
    private final FirefightingInspectionAttrService inspectionAttrService;
    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final FirefightingAccommodateInspectionConvertor inspectionConvertor;
    private final FirefightingInspectionAttrConvertor inspectionAttrConvertor;

    @ApiOperation("添加违规住人消防巡检记录")
    @PostMapping("/add")
    public Long addAccommodateInspection(@Validated(Add.class) @RequestBody FirefightingAccommodateInspectionVo inspection)  {
        FirefightingAccommodateInspection entity = inspectionConvertor.toDo(inspection);
        List<FirefightingInspectionAttr> attrs = inspectionAttrConvertor.toAttrDo(inspection.getAttrs());

        initEntityCreator(entity);
        initEntityModifier(entity);
        return inspectionService.addInspection(inspection.getTaskId(), entity, attrs,
                inspection.getRealtySerials(), inspection.getMedias(), inspection.getReinspectMedias());
    }

    @ApiOperation("更新违规住人消防巡检记录")
    @PostMapping("/update")
    public void updateAccommodateInspection(@Validated(Update.class) @RequestBody FirefightingAccommodateInspectionVo inspection) {
        FirefightingAccommodateInspection entity = inspectionConvertor.toDo(inspection);
        List<FirefightingInspectionAttr> attrs = inspectionAttrConvertor.toAttrDo(inspection.getAttrs());

        initEntityModifier(entity);
        inspectionService.updateInspection(entity, attrs, inspection.getRealtySerials(), inspection.getMedias(), inspection.getReinspectMedias());
    }

    @ApiOperation("删除违规住人消防巡检记录")
    @PostMapping("/delete/{id}")
    public void deleteAccommodateInspection(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        inspectionService.deleteInspection(id);
    }

    @ApiOperation("获取违规住人消防巡检记录")
    @GetMapping("/get/{id}")
    public FirefightingAccommodateInspectionVo findAccommodateInspectionById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingAccommodateInspectionVo result = null;
        FirefightingAccommodateInspection inspection = inspectionService.findById(id);
        if (inspection != null) {
            result = inspectionConvertor.toVo(inspection);
            // 物业
            List<FirefightingInspectRealty> realtyList = inspectRealtyService.findByInspectId(id, InspectionType.ACCOMMODATE);
            result.setRealtySerials(realtyList.stream().map(FirefightingInspectRealty::getRealtySerial).collect(Collectors.toList()));

            // 参数
            List<FirefightingInspectionAttr> attrs = inspectionAttrService.listInspectionAttrs(InspectionType.ACCOMMODATE, id);
            if (!CollectionUtils.isEmpty(attrs)) {
                result.setAttrs(inspectionAttrConvertor.toAttrVo(attrs));
            }

            // 多媒体资料
            List<FirefightingInspectMedia> medias = inspectMediaService.findByInspectId(id, InspectionType.ACCOMMODATE);
            if (!CollectionUtils.isEmpty(medias)) {
                List<String> inspectMedias = medias.stream()
                        .filter(x -> Objects.equals(1, x.getInspectTimes()))
                        .map(FirefightingInspectMedia::getMediaUrl)
                        .collect(Collectors.toList());
                List<String> reinspectMedias = medias.stream()
                        .filter(x -> Objects.equals(2, x.getInspectTimes()))
                        .map(FirefightingInspectMedia::getMediaUrl)
                        .collect(Collectors.toList());
                result.setMedias(inspectMedias);
                result.setReinspectMedias(reinspectMedias);
            }
        }
        return result;
    }

    @ApiOperation("违规住人消防巡检记录数统计")
    @PostMapping("/count")
    public Integer countAccommodateInspection(@RequestBody FirefightingAccommodateInspectionSearchVo search) {
        return inspectionService.countInspection(search);
    }

    @ApiOperation("违规住人消防巡检记录列表页")
    @PostMapping("/page")
    public PageResult<FirefightingAccommodateInspectionBriefVo> listAccommodateInspectionPage(@RequestBody FirefightingAccommodateInspectionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search,
                () -> inspectionService.countInspection(search),
                () -> inspectionService.listInspection(search));
    }
}
