package com.senox.realty.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.SecurityEvent;
import com.senox.realty.convert.SecurityJournalConvertor;
import com.senox.realty.domain.SecurityJournal;
import com.senox.realty.domain.SecurityMedia;
import com.senox.realty.service.SecurityJournalService;
import com.senox.realty.service.SecurityMediaService;
import com.senox.realty.vo.SecurityJournalSearchVo;
import com.senox.realty.vo.SecurityJournalVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/27 8:45
 */
@Api(tags = "安保日志")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/security/journal")
public class SecurityJournalController extends BaseController {

    private final SecurityJournalService journalService;
    private final SecurityMediaService mediaService;
    private final SecurityJournalConvertor journalConvertor;

    @ApiOperation("事件类别清单")
    @PostMapping("/types")
    public SecurityEvent[] listSecurityEventTypes() {
        return SecurityEvent.values();
    }

    @ApiOperation("添加安保日志")
    @PostMapping("/add")
    public Long addJournal(@Validated(Add.class) @RequestBody SecurityJournalVo journal) {
        SecurityJournal entity = journalConvertor.toDo(journal);
        initEntityCreator(entity);
        initEntityModifier(entity);

        return journalService.addJournal(entity, journal.getMedias(), journal.getReprocessMedias());
    }

    @ApiOperation("更新安保日志")
    @PostMapping("/update")
    public void updateJournal(@Validated(Update.class) @RequestBody SecurityJournalVo journal) {
        SecurityJournal entity = journalConvertor.toDo(journal);
        initEntityModifier(entity);

        journalService.updateJournal(entity, journal.getMedias(), journal.getReprocessMedias());
    }

    @ApiOperation("删除安保日志")
    @PostMapping("/delete/{id}")
    public void deleteJournal(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        journalService.deleteJournal(id);
    }

    @ApiOperation("获取安保日志")
    @GetMapping("/get/{id}")
    public SecurityJournalVo findJournalById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        SecurityJournalVo result = null;

        SecurityJournal journal = journalService.findById(id);
        if (journal != null) {
            result = journalConvertor.toVo(journal);

            // 多媒体资料
            List<SecurityMedia> medias = mediaService.listByJournalId(id);
            if (!CollectionUtils.isEmpty(medias)) {
                List<String> processMedias = medias.stream()
                        .filter(x -> Objects.equals(x.getProcessTimes(), 1))
                        .map(SecurityMedia::getMediaUrl)
                        .collect(Collectors.toList());
                List<String> reprocessMedias = medias.stream()
                        .filter(x -> Objects.equals(x.getProcessTimes(), 2))
                        .map(SecurityMedia::getMediaUrl)
                        .collect(Collectors.toList());
                result.setMedias(processMedias);
                result.setReprocessMedias(reprocessMedias);
            }
        }
        return result;
    }

    @ApiOperation("安保日志列表页")
    @PostMapping("/page")
    public PageResult<SecurityJournalVo> listJournalPage(@RequestBody SecurityJournalSearchVo search) {
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        PageResult<SecurityJournal> page = PageUtils.commonPageResult(search,
                () -> journalService.countJournal(search),
                () -> journalService.listJournal(search));
        return PageResult.convertPage(page, journalConvertor::toVo);
    }
}
