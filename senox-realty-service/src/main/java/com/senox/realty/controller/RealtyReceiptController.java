package com.senox.realty.controller;


import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.realty.service.RealtyReceiptService;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-3-23
 */
@Api(tags = "物业发票")
@AllArgsConstructor
@RestController
@RequestMapping("/realty/receipt")
public class RealtyReceiptController extends BaseController {
    private final RealtyReceiptService realtyReceiptService;

    @ApiOperation("物业发票申请")
    @PostMapping("/apply")
    public void apply(@Validated @RequestBody RealtyReceiptMangerVo receiptManger) {
        realtyReceiptService.apply(receiptManger);
    }

    @ApiOperation("pc物业发票申请")
    @PostMapping("/pc/apply")
    public void pcApply(@Validated @RequestBody RealtyReceiptMangerVo receiptManger) {
        realtyReceiptService.pcApply(receiptManger);
    }

    @ApiOperation("物业发票申请列表")
    @PostMapping("/apply/list")
    public PageResult<RealtyReceiptVo> applyList(@RequestBody RealtyReceiptSearchVo search) {
        return realtyReceiptService.listPage(search);
    }

    @ApiOperation("物业发票申请账单信息列表")
    @GetMapping("/apply/bill/info/list/{id}")
    public List<RealtyBillReceiptApplyInfoVo> applyBillInfoList(@PathVariable Long id) {
         return realtyReceiptService.applyBillInfoList(id);

    }


    @ApiOperation("发票申请列表")
    @GetMapping("/apply/info/list/{id}/{detail}")
    public List<ReceiptApplyVo> applyInfoList(@PathVariable Long id, @PathVariable Boolean detail) {
        return realtyReceiptService.receiptApplyListByRealtyReceiptId(id, detail);
    }


    @ApiOperation("物业发票申请审核")
    @PostMapping("/apply/audit")
    public void applyAudit(@RequestBody ReceiptApplyAuditVo receiptApplyAudit) {
        realtyReceiptService.audit(receiptApplyAudit.getId(),receiptApplyAudit.getStatus(),receiptApplyAudit.getRemark(), AdminContext.getUser().getUserId());
    }

}
