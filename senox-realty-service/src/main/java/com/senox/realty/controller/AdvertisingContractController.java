package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.convert.AdvertisingContractConvertor;
import com.senox.realty.convert.AdvertisingProfitShareConvertor;
import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.domain.AdvertisingMedia;
import com.senox.realty.domain.AdvertisingProfitShare;
import com.senox.realty.service.AdvertisingContractService;
import com.senox.realty.service.AdvertisingMediaService;
import com.senox.realty.service.AdvertisingProfitShareService;
import com.senox.realty.vo.AdvertisingContractEditVo;
import com.senox.realty.vo.AdvertisingContractListVo;
import com.senox.realty.vo.AdvertisingContractSearchVo;
import com.senox.realty.vo.AdvertisingContractVo;
import com.senox.realty.vo.AdvertisingCostVo;
import com.senox.realty.vo.AdvertisingIncomeVo;
import com.senox.realty.vo.ContractSuspendDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/20 15:33
 */
@Api(tags = "广告合同")
@RestController
@RequiredArgsConstructor
@RequestMapping("/advertising/contract")
public class AdvertisingContractController extends BaseController {

    private final AdvertisingContractService contractService;
    private final AdvertisingMediaService mediaService;
    private final AdvertisingProfitShareService shareService;
    private final AdvertisingContractConvertor contractConvertor;
    private final AdvertisingProfitShareConvertor shareConvertor;

    @ApiOperation("添加合同")
    @PostMapping("/add")
    public Long addContract(@Validated @RequestBody AdvertisingContractEditVo contract) {
        AdvertisingContract entity = contractConvertor.editVoToDo(contract);
        initEntityCreator(entity);
        return contractService.saveContract(entity, contract.getMedias(), contract.getShares());
    }

    @ApiOperation("修改合同")
    @PostMapping("/update")
    public void updateContract(@Validated @RequestBody AdvertisingContractEditVo contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        AdvertisingContract entity = contractConvertor.editVoToDo(contract);
        initEntityModifier(entity);
        contractService.saveContract(entity, contract.getMedias(), contract.getShares());
    }

    @ApiOperation("修改已缴费合同")
    @PostMapping("/paid/update")
    public void updatePaidContract(@RequestBody AdvertisingContractEditVo contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        AdvertisingContract entity = contractConvertor.editVoToDo(contract);
        initEntityModifier(entity);
        contractService.savePaidContract(entity, contract.getMedias(), contract.getShares());
    }

    @ApiOperation("修改合同成本")
    @PostMapping("/cost/update")
    public void updateContractCost(@Validated @RequestBody AdvertisingCostVo cost) {
        cost.setOperatorId(getUserInContext().getUserId());
        cost.setOperatorName(getUserInContext().getUsername());
        contractService.updateContractCost(cost);
    }

    @ApiOperation("停用合同")
    @PostMapping("/suspend")
    public void suspendContract(@Validated @RequestBody ContractSuspendDto suspendDto) {
        suspendDto.setOperatorId(suspendDto.getOperatorId());
        suspendDto.setOperatorName(suspendDto.getOperatorName());

        contractService.suspendContract(suspendDto);
    }

    @ApiOperation("删除合同")
    @PostMapping("/delete/{id}")
    public void deleteContract(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        contractService.deleteContract(id);
    }

    @ApiOperation("获取合同详情")
    @GetMapping("/get/{id}")
    public AdvertisingContractVo getContract(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        AdvertisingContractVo result = contractService.findDetailById(id);
        if (result != null) {
            // 多媒体资料
            List<AdvertisingMedia> mediaList = mediaService.listByContractId(id);
            result.setMedias(CollectionUtils.isEmpty(mediaList) ? Collections.emptyList()
                    : mediaList.stream().map(AdvertisingMedia::getMediaUrl).collect(Collectors.toList()));

            // 收益分成
            List<AdvertisingProfitShare> shareList = shareService.listByContractId(id);
            result.setShares(CollectionUtils.isEmpty(shareList) ? Collections.emptyList() : shareConvertor.toVo(shareList));
        }
        return result;
    }

    @ApiOperation("合同统计")
    @PostMapping("/count")
    public int countContract(@RequestBody AdvertisingContractSearchVo search) {
        return contractService.countContract(search);
    }

    @ApiOperation("合同视图列表")
    @PostMapping("/page")
    public PageResult<AdvertisingContractListVo> listContractPage(@RequestBody AdvertisingContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> contractService.countContract(search), () -> contractService.listContractView(search));
    }

    @ApiOperation("广告收益合计")
    @PostMapping("/income/sum")
    public AdvertisingIncomeVo sumContractIncome(@RequestBody AdvertisingContractSearchVo search) {
        return contractService.sumIncome(search);
    }

    @ApiOperation("广告收益列表")
    @PostMapping("/income/page")
    public PageResult<AdvertisingIncomeVo> listContractIncomePage(@RequestBody AdvertisingContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> contractService.countContract(search), () -> contractService.listIncome(search));
    }

    @ApiOperation("广告收益列表")
    @PostMapping("/income/list")
    public List<AdvertisingIncomeVo> listContractIncome(@RequestBody AdvertisingContractSearchVo search) {
        search.setPage(false);
        return contractService.listIncome(search);
    }
}
