package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.domain.Street;
import com.senox.realty.service.StreetService;
import com.senox.realty.vo.StreetVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/1/19 17:31
 */
@Api(tags = "街道管理")
@RestController
@RequestMapping("/street")
public class StreetController extends BaseController {

    @Autowired
    private StreetService streetService;

    @ApiOperation("添加街道")
    @PostMapping("/add")
    public Long addStreet(@Validated({Add.class}) @RequestBody StreetVo streetVo) {
        Street street = streetVo2Street(streetVo);
        initEntityCreator(street);
        initEntityModifier(street);
        return streetService.addStreet(street);
    }

    @ApiOperation("更新街道")
    @PostMapping("/update")
    public void updateStreet(@Validated({Update.class}) @RequestBody StreetVo streetVo) {
        if (streetVo.getId() < 1L) {
            throw new InvalidParameterException("无效id");
        }
        Street street = streetVo2Street(streetVo);
        initEntityModifier(street);

       streetService.updateStreet(street);
    }

    @ApiOperation("获取街道")
    @GetMapping("/get/{id}")
    public StreetVo getStreet(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        Street street = streetService.findById(id);
        return street == null ? null : street2Vo(street);
    }

    @ApiOperation("街道列表")
    @PostMapping("/list")
    public List<StreetVo> listStreet() {
        List<Street> list = streetService.listAll();
        return CollectionUtils.isEmpty(list) ? Collections.emptyList()
                : list.stream().map(this::street2Vo).collect(Collectors.toList());
    }

    @ApiOperation("区域街道列表")
    @PostMapping("/listRS/{regionId}")
    public List<StreetVo> listRegionStreet(@PathVariable Long regionId) {
        if (!WrapperClassUtils.biggerThanLong(regionId, 0L)) {
            throw new InvalidParameterException("无效的区域id");
        }
        List<Street> list = streetService.listRegionStreet(regionId);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList()
                : list.stream().map(this::street2Vo).collect(Collectors.toList());
    }

    private Street streetVo2Street(StreetVo streetVo) {
        Street result = new Street();
        BeanUtils.copyProperties(streetVo, result);
        return result;
    }

    private StreetVo street2Vo(Street street) {
        StreetVo result = new StreetVo();
        BeanUtils.copyProperties(street, result);
        return result;
    }
}
