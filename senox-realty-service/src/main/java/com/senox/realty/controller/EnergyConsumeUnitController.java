package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.convert.EnergyConsumeUnitConvertor;
import com.senox.realty.domain.EnergyConsumeUnit;
import com.senox.realty.service.EnergyConsumeUnitService;
import com.senox.realty.vo.EnergyConsumeUnitVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/31 14:44
 */
@Api(tags = "能源消费单元")
@RestController
@RequiredArgsConstructor
@RequestMapping("/energy/consumeUnit")
public class EnergyConsumeUnitController extends BaseController {

    private final EnergyConsumeUnitService unitService;
    private final EnergyConsumeUnitConvertor unitConvertor;


    @ApiOperation("添加能源消费单元")
    @PostMapping("/add")
    public Long addConsumeUnit(@Validated @RequestBody EnergyConsumeUnitVo unit) {
        EnergyConsumeUnit entity = unitConvertor.toDo(unit);
        initEntityCreator(entity);
        initEntityModifier(entity);
        return unitService.addUnit(entity);
    }

    @ApiOperation("更新能源消费单元")
    @PostMapping("/update")
    public void updateConsumeUnit(@Validated @RequestBody EnergyConsumeUnitVo unit) {
        if (!WrapperClassUtils.biggerThanLong(unit.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        EnergyConsumeUnit entity = unitConvertor.toDo(unit);
        initEntityModifier(entity);
        unitService.updateUnit(entity);
    }

    @ApiOperation("获取能源消费单元详情")
    @GetMapping("/get/{id}")
    public EnergyConsumeUnitVo get(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        EnergyConsumeUnit result = unitService.findById(id);
        return result == null ? null : unitConvertor.toVo(result);
    }


    @ApiOperation("能源消费单元列表")
    @PostMapping("/list")
    public List<EnergyConsumeUnitVo> list(@RequestParam Integer type) {
        EnergyType energyType = EnergyType.fromValue(type);
        if (energyType == null) {
            throw new InvalidParameterException();
        }

        List<EnergyConsumeUnit> resultList = unitService.listByType(energyType);
        return CollectionUtils.isEmpty(resultList) ? Collections.emptyList() : unitConvertor.toVo(resultList);
    }
}
