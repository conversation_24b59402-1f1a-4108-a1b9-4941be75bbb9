package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillCancelVo;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.domain.RealtyBill;
import com.senox.realty.service.ContractBillService;
import com.senox.realty.service.FeeService;
import com.senox.realty.service.RealtyBillPenaltyService;
import com.senox.realty.service.RealtyBillService;
import com.senox.realty.service.RealtyBillWeService;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyBillBatchVo;
import com.senox.realty.vo.RealtyBillPageResult;
import com.senox.realty.vo.RealtyBillRemarkVo;
import com.senox.realty.vo.RealtyBillSearchVo;
import com.senox.realty.vo.RealtyBillSendVo;
import com.senox.realty.vo.RealtyBillSerialVo;
import com.senox.realty.vo.RealtyBillVo;
import com.senox.realty.vo.RealtyBillWeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/9 15:19
 */
@Api(tags = "应收账单")
@AllArgsConstructor
@RestController
@RequestMapping("/realtyBill")
public class RealtyBillController extends BaseController {

    private final FeeService feeService;
    private final RealtyBillService billService;
    private final RealtyBillWeService billWeService;
    private final ContractBillService contractBillService;
    private final RealtyBillPenaltyService billPenaltyService;


    @ApiOperation("生成应收账单")
    @PostMapping("/generate")
    public void generateBill(@Validated @RequestBody BillMonthVo month) {
        contractBillService.buildAndSaveBill(month, getUserInContext());
    }

    @ApiOperation("重新生成应收账单")
    @PostMapping("/regenerate")
    public void regenerateBill(@Validated @RequestBody BillMonthVo month) {
        // 清空该月应收账单
        billService.deleteBill(month.getYear(), month.getMonth());
        // 重新生成
        contractBillService.buildAndSaveBill(month, getUserInContext());
    }

    @ApiOperation("更新应收账单")
    @PostMapping("/update")
    public void updateBill(@Validated @RequestBody RealtyBillVo bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        billService.saveBill(bill, feeService.listAll(), getUserInContext());
        // 重新计算滞纳金
        billPenaltyService.calBillPenalty(bill.getContractNo());
    }

    @ApiOperation("免滞纳金")
    @PostMapping("/ignorePenalty")
    public void ignorePenalty(@RequestBody BillPenaltyIgnoreVo penaltyIgnore) {
        if (CollectionUtils.isEmpty(penaltyIgnore.getBillIds())) {
            throw new InvalidParameterException();
        }
        if (penaltyIgnore.getPenaltyIgnore() == null && !DecimalUtils.isPositive(penaltyIgnore.getPenaltyIgnoreAmount())) {
            throw new InvalidParameterException();
        }
        if (penaltyIgnore.getPenaltyIgnore() == null) {
            penaltyIgnore.setPenaltyIgnore(Boolean.TRUE);
        }

        penaltyIgnore.setOperatorId(getUserInContext().getUserId());
        penaltyIgnore.setOperatorName(getUserInContext().getUsername());
        billService.updateRealtyBillPenaltyIgnore(penaltyIgnore);
    }

    @ApiOperation("发票")
    @PostMapping("/receipt")
    public void receipt(@RequestBody RealtyBillBatchVo batchBills) {
        if (WrapperClassUtils.biggerThanLong(batchBills.getId(), 0L)) {
            batchBills.setIds(Collections.singletonList(batchBills.getId()));
        }
        if (CollectionUtils.isEmpty(batchBills.getIds())) {
            throw new InvalidParameterException();
        }
        if (batchBills.getReceipt() == null) {
            throw new InvalidParameterException();
        }

        batchBills.setModifierId(getUserInContext().getUserId());
        batchBills.setModifierName(getUserInContext().getUsername());
        billService.updateBillReceipt(batchBills , true);
    }

    @ApiOperation("更新应收账单备注")
    @PostMapping("/remark/save")
    public void saveBillRemark(@Validated @RequestBody RealtyBillRemarkVo remarkVo) {
        RealtyBill bill = new RealtyBill();
        bill.setId(remarkVo.getId());
        bill.setRemark(remarkVo.getRemark());
        initEntityModifier(bill);
        billService.updateBill(bill);
    }

    @ApiOperation("保存应收账单票据号")
    @PostMapping("/billSerial/save")
    public void saveBillSerial(@Validated @RequestBody RealtyBillSerialVo billSerial) {
        RealtyBill bill = new RealtyBill();
        bill.setId(billSerial.getBillId());
        bill.setBillSerial(billSerial.getBillSerial());
        initEntityModifier(bill);
        billService.updateBill(bill);
    }

    @ApiOperation("更新应收账单支付结果")
    @PostMapping("/paid/update")
    public void updateBillStatus(@Validated @RequestBody BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            throw new InvalidParameterException("无效的订单id");
        }
        if (!WrapperClassUtils.biggerThanLong(billPaid.getTollMan(), 0L)) {
            throw new InvalidParameterException("无效的收费员");
        }

        billService.updateBillStatus(billPaid);
    }

    @ApiOperation("下发应收账单")
    @PostMapping("/send")
    public void sendBill(@RequestBody RealtyBillSendVo sendVo) {
        if ((sendVo.getBillYear() == null || sendVo.getBillMonth() == null)
                && CollectionUtils.isEmpty(sendVo.getBillIds())) {
            throw new InvalidParameterException("未明的下发的物业账单");
        }
        billService.sendBill(sendVo);
    }

    @ApiOperation("撤销支付")
    @PostMapping("/cancel")
    public void cancelBill(@RequestBody BillCancelVo cancelVo) {
        if (!WrapperClassUtils.biggerThanLong(cancelVo.getBillId(), 0L)) {
            throw new InvalidParameterException();
        }
        cancelVo.setOperatorId(getUserInContext().getUserId());
        cancelVo.setOperatorName(getUserInContext().getUsername());
        billService.cancelBill(cancelVo);
    }

    @ApiOperation("删除应收账单")
    @PostMapping("/delete/{id}")
    public void deleteBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        billService.deleteBill(id);
    }

    @ApiOperation("获取应收账单明细")
    @GetMapping("/get/{id}")
    public RealtyBillVo getBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的账单id");
        }
        return billService.findDetailById(id);
    }

    @ApiOperation("根据id列表获取应收账单")
    @GetMapping("/listById")
    public List<RealtyBillVo> listBillById(@RequestParam("ids") List<Long> ids,
                                           @RequestParam(name = "withDetail", required = false) Boolean withDetail) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        if (ids.size() > 50) {
            throw new InvalidParameterException("一次性最多只允许查询50个物业");
        }
        return billService.listBillById(ids, BooleanUtils.isTrue(withDetail));
    }

    @ApiOperation("查找物业月账单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/monthlyBillList")
    public List<RealtyBillVo> monthlyBillList(@RequestBody BillMonthVo monthVo) {
        return billService.monthlyBillList(monthVo);
    }

    @ApiOperation("新增物业账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/saveBill")
    public void saveBill(@RequestBody BillMonthVo monthVo) {
        if (StringUtils.isBlank(monthVo.getContractNo()) || monthVo.getYear() == null || monthVo.getMonth() == null) {
            return;
        }
        LocalDate billDate = DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth() + 1, 1, true).minusDays(1);
        contractBillService.buildAndSaveBill(monthVo.getContractNo(), billDate);
    }

    @ApiOperation("应收账单列表")
    @PostMapping("/list")
    public RealtyBillPageResult<RealtyBillVo> listBill(@Validated @RequestBody RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<RealtyBillVo> page = billService.listRealtyBillPage(search);

        // result
        RealtyBillPageResult<RealtyBillVo> resultPage = new RealtyBillPageResult<>(search.getPageNo(), search.getPageSize());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());
        resultPage.initTotalPage();

        // 总计
        RealtyBillVo sumBill = billService.sumRealtyBill(search);
        if (sumBill != null) {
            resultPage.setPaidAmount(DecimalUtils.nullToZero(sumBill.getPaidAmount()));
            resultPage.setPaidStillAmount(DecimalUtils.nullToZero(sumBill.getPaidStillAmount()));
            resultPage.setRefundAmount(DecimalUtils.nullToZero(sumBill.getRefundAmount()));
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sumBill.getTotalAmount()));
        }
        return resultPage;
    }

    @ApiOperation("发票列表")
    @PostMapping("/receiptList")
    public RealtyBillPageResult<RealtyBillVo> listReceiptBill(@Validated @RequestBody RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<RealtyBillVo> page = billService.listReceiptBill(search);

        // result
        RealtyBillPageResult<RealtyBillVo> resultPage = new RealtyBillPageResult<>(search.getPageNo(), search.getPageSize());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());
        resultPage.initTotalPage();

        // 统计金额
        RealtyBillVo sumAmount = billService.sumReceiptBillAmount(search);
        for (RealtyBillVo realtyBill : page.getDataList()) {
            realtyBill.setPaidAmount(realtyBill.getManageAmount().add(realtyBill.getRentAmount()).add(realtyBill.getWaterAmount()).add(realtyBill.getElectricAmount()));
        }
        // 总计
        resultPage.setManageAmount(DecimalUtils.nullToZero(sumAmount.getManageAmount()));
        resultPage.setRentAmount(DecimalUtils.nullToZero(sumAmount.getRentAmount()));
        resultPage.setWaterAmount(DecimalUtils.nullToZero(sumAmount.getWaterAmount()));
        resultPage.setElectricAmount(DecimalUtils.nullToZero(sumAmount.getElectricAmount()));
        resultPage.setPaidAmount(resultPage.getManageAmount().add(resultPage.getRentAmount()).add(resultPage.getWaterAmount()).add(resultPage.getElectricAmount()));
        return resultPage;
    }

    @ApiOperation("应收账单明细合计")
    @PostMapping("/detail/sum")
    public RealtyBillVo sumBillDetail(@RequestBody RealtyBillSearchVo search) {
        return billService.sumRealtyBillWithDetail(search);
    }

    @ApiOperation("应收账单明细页")
    @PostMapping("/detail/page")
    public PageResult<RealtyBillVo> listBillDetailPage(@Validated @RequestBody RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search,
                () -> billService.countRealtyBill(search),
                () -> billService.listRealtyBillWithDetail(search));
    }

    @ApiOperation("应收账单明细列表")
    @PostMapping("/detailList")
    public RealtyBillPageResult<RealtyBillVo> listBillDetail(@Validated @RequestBody RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<RealtyBillVo> billPage = billService.listRealtyBillPageWithDetail(search);

        // 总计
        RealtyBillVo sumBill = billService.sumRealtyBillWithDetail(search);

        return buildResultPage(search, sumBill, billPage);
    }

    @ApiOperation("应收账单明细页信息")
    @PostMapping("/detail/info/page")
    public PageResult<RealtyBillVo> listBillDetailInfoPage(@Validated @RequestBody RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search,
                () -> billService.countRealtyBill(search),
                () -> billService.listBillWithDetailInfo(search));
    }

    @ApiOperation("应收账单明细列表信息")
    @PostMapping("/detail/info/list")
    public RealtyBillPageResult<RealtyBillVo> listBillDetailInfo(@Validated @RequestBody RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RealtyBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<RealtyBillVo> billPage = billService.listRealtyBillPageWithDetailInfo(search);

        // 总计
        RealtyBillVo sumBill = billService.sumRealtyBillWithDetail(search);

        return buildResultPage(search, sumBill, billPage);
    }

    private RealtyBillPageResult<RealtyBillVo> buildResultPage(RealtyBillSearchVo search, RealtyBillVo sumBill, PageResult<RealtyBillVo> billPage) {
        // result
        RealtyBillPageResult<RealtyBillVo> resultPage = new RealtyBillPageResult<>(search.getPageNo(), search.getPageSize());
        resultPage.setTotalSize(billPage.getTotalSize());
        resultPage.setDataList(billPage.getDataList());
        resultPage.initTotalPage();


        if (sumBill != null) {
            resultPage.setManageAmount(DecimalUtils.nullToZero(sumBill.getManageAmount()));
            resultPage.setRentAmount(DecimalUtils.nullToZero(sumBill.getRentAmount()));
            resultPage.setElectricAmount(DecimalUtils.nullToZero(sumBill.getElectricAmount()));
            resultPage.setWaterAmount(DecimalUtils.nullToZero(sumBill.getWaterAmount()));
            resultPage.setPenaltyAmount(DecimalUtils.nullToZero(sumBill.getPenaltyAmount()));
            resultPage.setPenaltyPaidAmount(DecimalUtils.nullToZero(sumBill.getPenaltyPaidAmount()));
            resultPage.setTotalAmount(DecimalUtils.nullToZero(sumBill.getTotalAmount()));
            resultPage.setPaidAmount(DecimalUtils.nullToZero(sumBill.getPaidAmount()));
            resultPage.setPaidStillAmount(DecimalUtils.nullToZero(sumBill.getPaidStillAmount()));
            resultPage.setRefundAmount(DecimalUtils.nullToZero(sumBill.getRefundAmount()));
            resultPage.setReceivableAmount(DecimalUtils.nullToZero(sumBill.getReceivableAmount()));
            resultPage.setTotalAmountIgnorePenalty(DecimalUtils.nullToZero(sumBill.getTotalAmountIgnorePenalty()));
        }
        return resultPage;
    }

    @ApiOperation("应收账单水电明细列表")
    @GetMapping("/we/{billId}")
    public List<RealtyBillWeVo> listBillWeDetail(@PathVariable Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            throw new InvalidParameterException("无效的账单id");
        }
        return billWeService.listWeBill(billId);
    }

    @ApiOperation("同步应收账单水电数据")
    @PostMapping("/we/sync")
    public void updateBillWeData(@Validated @RequestBody BillMonthVo billTime) {
        contractBillService.saveRealtyBillWeData(billTime, getUserInContext());
    }

}
