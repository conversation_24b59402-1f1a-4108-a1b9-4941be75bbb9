package com.senox.realty.controller;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.PayWay;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.convert.AdvertisingPayoffConvertor;
import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.domain.AdvertisingPayoff;
import com.senox.realty.service.AdvertisingContractService;
import com.senox.realty.service.AdvertisingPayoffService;
import com.senox.realty.vo.AdvertisingPayoffDetailVo;
import com.senox.realty.vo.AdvertisingPayoffSearchVo;
import com.senox.realty.vo.AdvertisingPayoffVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/3 10:50
 */
@Api(tags = "广告应付账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/advertising/payoff")
public class AdvertisingPayoffController extends BaseController {

    private final AdvertisingPayoffService payoffService;
    private final AdvertisingContractService contractService;
    private final AdvertisingPayoffConvertor payoffConvertor;

    @ApiOperation("生成广告应付账单")
    @PostMapping("/generate")
    public void generatePayoff(@RequestParam String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            throw new InvalidParameterException();
        }

        AdvertisingContract contract = contractService.findByContractNo(contractNo);
        if (contract == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        payoffService.savePayoff(contract);
    }

    @ApiOperation("更新广告应付账单")
    @PostMapping("/update")
    public void updatePayoff(@RequestBody AdvertisingPayoffVo payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        AdvertisingPayoff dbItem = payoffService.findById(payoff.getId());
        if (dbItem == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BillStatus.PAID == BillStatus.fromStatus(dbItem.getStatus())) {
            throw new BusinessException("账单已支付");
        }

        AdvertisingPayoff entity = payoffConvertor.toDo(payoff);
        initEntityModifier(entity);
        payoffService.updatePayoff(entity);
    }


    @ApiOperation("删除广告应付账单")
    @PostMapping("/delete/{id}")
    public void deletePayoff(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        payoffService.deletePayoff(id);
    }

    @ApiOperation("更新应付账单状态")
    @PostMapping("/paid/update")
    public void updatePayoffStatus(@RequestBody BillPaidVo billPaid, @RequestParam("payway") int payway) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            throw new InvalidParameterException();
        }
        PayWay pw = PayWay.fromValue(payway);
        if (pw != PayWay.CASH && pw != PayWay.TRANSFER) {
            throw new InvalidParameterException("应付账单仅支持现金及转账");
        }

        if (!WrapperClassUtils.biggerThanLong(billPaid.getTollMan(), 0L)) {
            billPaid.setTollMan(getUserInContext().getUserId());
        }
        if (billPaid.getPaidTime() == null) {
            billPaid.setPaidTime(LocalDateTime.now());
        }

        payoffService.updatePayoffStatus(billPaid, pw, getUserInContext());
    }


    @ApiOperation("广告合同应付账单")
    @PostMapping("/listByContract")
    public List<AdvertisingPayoffVo> listPayoffByContract(@RequestParam String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            throw new InvalidParameterException();
        }

        List<AdvertisingPayoff> resultList = payoffService.listByContractNo(contractNo);
        return CollectionUtils.isEmpty(resultList) ? Collections.emptyList() : payoffConvertor.toVo(resultList);
    }

    @ApiOperation("广告应付账单合计")
    @PostMapping("/sum")
    public AdvertisingPayoffDetailVo sumPayoff(@RequestBody AdvertisingPayoffSearchVo search) {
        AdvertisingPayoffDetailVo result = payoffService.sumPayoff(search);

        result = result == null ? new AdvertisingPayoffDetailVo() : result;
        result.setAmount(DecimalUtils.nullToZero(result.getAmount()));
        result.setShareAmount(DecimalUtils.nullToZero(result.getShareAmount()));
        return result;
    }

    @ApiOperation("广告应付账单页")
    @PostMapping("/page")
    public PageResult<AdvertisingPayoffDetailVo> listPayoffPage(@RequestBody AdvertisingPayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return payoffService.listPayoffPage(search);
    }

}
