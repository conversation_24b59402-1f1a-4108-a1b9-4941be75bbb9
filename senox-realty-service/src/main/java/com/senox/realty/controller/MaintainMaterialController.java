package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.convert.MaintainMaterialConvertor;
import com.senox.realty.convert.MaintainMaterialItemConvertor;
import com.senox.realty.domain.MaintainMaterial;
import com.senox.realty.domain.MaintainMaterialItem;
import com.senox.realty.service.MaintainMaterialItemService;
import com.senox.realty.service.MaintainMaterialService;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/6 16:26
 */
@Api(tags = "维修物料")
@RestController
@RequiredArgsConstructor
@RequestMapping("/maintain/material")
public class MaintainMaterialController extends BaseController {

    private final MaintainMaterialService maintainMaterialService;

    private final MaintainMaterialConvertor convertor;

    private final MaintainMaterialItemService maintainMaterialItemService;

    private final MaintainMaterialItemConvertor itemConvertor;

    @ApiOperation("添加维修所需物料")
    @PostMapping("/add")
    public Long saveMaintainMaterial(@RequestBody MaintainMaterialVo materialVo) {
        MaintainMaterial material = convertor.toDo(materialVo);
        initEntityCreator(material);
        initEntityModifier(material);
        material.setCreateTime(LocalDateTime.now());
        material.setModifiedTime(LocalDateTime.now());
        List<MaintainMaterialItem> materialItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(materialVo.getItemVos())) {
            materialItems = itemConvertor.toDo(materialVo.getItemVos());
        }
        materialItems.forEach(item -> {
            initEntityCreator(item);
            initEntityModifier(item);
            item.setCreateTime(LocalDateTime.now());
            item.setModifiedTime(LocalDateTime.now());
        });
        return maintainMaterialService.saveMaintainMaterial(material, materialItems);
    }

    @ApiOperation("删除维修物料")
    @GetMapping("/delete/{id}")
    public void deleteMaintainMaterial(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        maintainMaterialService.deleteMaintainMaterial(id);
    }

    @ApiOperation("批量删除物料及明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batch/delete")
    public void batchDeleteMaterial(@RequestBody List<Long> ids) {
        maintainMaterialService.batchDeleteMaterial(ids);
    }

    @ApiOperation("物料列表")
    @PostMapping("/list")
    public PageResult<MaintainMaterialDataVo> listMaintainMaterial(@RequestBody MaintainMaterialSearchVo search) {
        if (search.getPageSize() < 1) {
            return MaintainChargePageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return maintainMaterialService.listMaintainMaterial(search);
    }

    @ApiOperation("更新出库单号")
    @PostMapping("/outNo/save")
    public void saveOutNo(@Validated @RequestBody MaintainMaterialOutNoVo outNoVo) {
        outNoVo.setOperatorId(getUserInContext().getUserId());
        outNoVo.setOperatorName(getUserInContext().getUsername());
        maintainMaterialService.updateOutNo(outNoVo);
    }

    @ApiOperation("撤销出库")
    @GetMapping("/revoke/{outNo}")
    public void cancelOutBound(@PathVariable String outNo) {
        maintainMaterialService.cancelOutBound(outNo);
    }

    @ApiOperation("获取维修所需物料")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{orderId}")
    public List<MaintainMaterialItemVo> listMaterialByOrderIdAndJobId(@PathVariable Long orderId, @RequestParam(required = false) Long jobId) {
        if (orderId < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        return maintainMaterialItemService.findByOrderIdAndJobId(orderId, jobId);
    }

    @ApiOperation("更新维修物料单")
    @PostMapping("/update")
    public void updateMaintainMaterial(@RequestBody MaintainMaterialVo materialVo) {
        if (!WrapperClassUtils.biggerThanLong(materialVo.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        MaintainMaterial material = convertor.toDo(materialVo);
        initEntityModifier(material);
        maintainMaterialService.updateMaintainMaterial(material);
    }

    @ApiOperation("批量添加维修所需物料")
    @PostMapping("/batch/add")
    public void batchSaveMaterial(@RequestBody List<MaintainMaterialItemVo> materialItemVos) {
        if (CollectionUtils.isEmpty(materialItemVos)) {
            return;
        }
        Map<Long, List<MaintainMaterialItemVo>> jobMap = materialItemVos.stream().collect(Collectors.groupingBy(MaintainMaterialItemVo::getJobId));
        Map<MaintainMaterial, List<MaintainMaterialItem>> materialMap = new HashMap<>();
        jobMap.forEach((jobId, item) -> {
            Map<Long, List<MaintainMaterialItemVo>> materialIdMap = item.stream().collect(Collectors.groupingBy(MaintainMaterialItemVo::getMaterialId));
            materialIdMap.forEach((materialId, groupItem) -> {
                MaintainMaterialItemVo itemVo = groupItem.get(0);
                MaintainMaterial material = maintainMaterialService.buildMaterial(itemVo.getMaterialId(), itemVo.getJobId(), itemVo.getOrderId());
                List<MaintainMaterialItem> materialItems = itemConvertor.toDo(groupItem);
                maintainMaterialService.initMaterialItem(materialItems);
                materialMap.put(material, materialItems);
            });
        });
        LinkedHashMap<MaintainMaterial, List<MaintainMaterialItem>> sortedMap = materialMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.comparingLong(MaintainMaterial::getId).reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
        maintainMaterialService.batchSaveMaterial(sortedMap);
    }

}
