package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillReceiptBriefVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.TollSerialVo;
import com.senox.realty.convert.AdvertisingBillConvertor;
import com.senox.realty.service.AdvertisingBillService;
import com.senox.realty.vo.AdvertisingBillSearchVo;
import com.senox.realty.vo.AdvertisingBillVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/26 17:06
 */
@Api(tags = "广告应收账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/advertising/bill")
public class AdvertisingBillController extends BaseController {

    private final AdvertisingBillService billService;
    private final AdvertisingBillConvertor billConvertor;


    @ApiOperation("更新账单状态")
    @PostMapping("/paid/update")
    public void updateBillStatus(@Validated @RequestBody BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            throw new InvalidParameterException("无效的订单id");
        }
        if (!WrapperClassUtils.biggerThanLong(billPaid.getTollMan(), 0L)) {
            throw new InvalidParameterException("无效的收费员");
        }

        billService.updateBillStatus(billPaid);
    }

    @ApiOperation("更新账单票据号")
    @PostMapping("/tollSerial/update")
    public void updateTollSerial(@Validated @RequestBody TollSerialVo tollSerial) {
        tollSerial.setOperatorId(getUserInContext().getUserId());
        billService.updateBillTollSerial(tollSerial);
    }

    @ApiOperation("广告账单开票")
    @PostMapping("/receipt/add")
    public void receiptBill(@Validated @RequestBody BillReceiptBriefVo receipt) {
        if (StringUtils.isBlank(receipt.getTaxHeader())) {
            throw new InvalidParameterException();
        }

        receipt.setReceipt(Boolean.TRUE);
        receipt.setReceiptTime(LocalDateTime.now());
        receipt.setOperatorId(getUserInContext().getUserId());
        receipt.setOperatorName(getUserInContext().getUsername());
        billService.updateBillReceipt(receipt);
    }

    @ApiOperation("广告账单取消开票")
    @PostMapping("/receipt/cancel")
    public void cancelReceiptBill(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        BillReceiptBriefVo receipt = new BillReceiptBriefVo();
        receipt.setBillIds(ids);
        receipt.setReceipt(Boolean.FALSE);
        receipt.setOperatorId(getUserInContext().getUserId());
        receipt.setOperatorName(getUserInContext().getUsername());
        billService.updateBillReceipt(receipt);
    }

    @ApiOperation("获取账单详情")
    @GetMapping("/get/{id}")
    public AdvertisingBillVo getBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return billService.findDetailById(id);
    }

    @ApiOperation("根据id列表获取账单详情")
    @PostMapping("/listByIds")
    public List<AdvertisingBillVo> listBillByIds(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        return billService.listDetailByIds(ids);
    }

    @ApiOperation("广告应收账单合计")
    @PostMapping("/sum")
    public AdvertisingBillVo sumBill(@RequestBody AdvertisingBillSearchVo search) {
        return billConvertor.toVo(billService.sumBill(search));
    }

    @ApiOperation("广告应收账单列表页")
    @PostMapping("/page")
    public PageResult<AdvertisingBillVo> listBillPage(@RequestBody AdvertisingBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> billService.countBill(search), () -> billService.listBill(search));
    }


}
