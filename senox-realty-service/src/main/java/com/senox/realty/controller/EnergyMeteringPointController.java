package com.senox.realty.controller;

import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.common.constant.device.DeviceState;
import com.senox.dm.vo.EnergyPointRefreshResult;
import com.senox.realty.convert.EnergyMeteringPointConvertor;
import com.senox.realty.domain.EnergyMeteringPoint;
import com.senox.realty.service.EnergyMeteringPointService;
import com.senox.realty.vo.EnergyMeteringPointVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Api(tags = "物业集抄-计量点")
@RequestMapping("/realty/energy/metering/point")
@RestController
@RequiredArgsConstructor
public class EnergyMeteringPointController {
    private final EnergyMeteringPointService meteringPointService;
    private final EnergyMeteringPointConvertor meteringPointConvertor;

    @ApiOperation("更新计量点")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update")
    public void update(@Validated(Update.class) @RequestBody EnergyMeteringPointVo point) {
        meteringPointService.updateByCode(meteringPointConvertor.toDo(point));
    }

    @ApiOperation("更新计量点状态")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/state/update")
    public void updateState(@RequestParam String pointCode, @RequestParam DeviceState deviceState) {
        EnergyMeteringPoint updatePoint = new EnergyMeteringPoint();
        updatePoint.setCode(pointCode);
        updatePoint.setStatus(deviceState.getState());
        meteringPointService.updateByCode(updatePoint);
    }

    @ApiOperation("计量点编码获取计量点")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/get/{code}")
    public EnergyMeteringPointVo getByCode(@PathVariable String code) {
        return meteringPointConvertor.toVo(meteringPointService.findByCode(code));
    }

    @ApiOperation("刷新计量点")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/refresh")
    public EnergyPointRefreshResult refresh(@RequestParam(required = false) String meteringPointCode) {
        return meteringPointService.batchRefresh(meteringPointCode);
    }
}
