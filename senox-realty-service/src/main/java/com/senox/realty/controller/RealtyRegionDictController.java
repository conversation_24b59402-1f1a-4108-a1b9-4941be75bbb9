package com.senox.realty.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.convert.RealtyRegionDictConvert;
import com.senox.realty.service.RealtyRegionDictService;
import com.senox.realty.vo.RealtyRegionDictSearchVo;
import com.senox.realty.vo.RealtyRegionDictVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

@Api(tags = "物业区域字典")
@AllArgsConstructor
@RestController
@RequestMapping("/realty/region/dict")
public class RealtyRegionDictController extends BaseController {
    private final RealtyRegionDictService realtyRegionDictService;
    private final RealtyRegionDictConvert realtyRegionDictConvert;

    @ApiOperation("添加字典")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public void add(@RequestBody RealtyRegionDictVo regionDict) {
        realtyRegionDictService.addBatch(Collections.singletonList(realtyRegionDictConvert.toDo(regionDict)));
    }

    @ApiOperation("删除字典")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Long id) {
        realtyRegionDictService.deleteBatch(Collections.singletonList(id));
    }

    @ApiOperation("修改字典")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/updateById")
    public void updateById(@RequestBody RealtyRegionDictVo regionDict) {
        realtyRegionDictService.updateBatch(Collections.singletonList(realtyRegionDictConvert.toDo(regionDict)));
    }

    @ApiOperation("查询字典")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list/page")
    public PageResult<RealtyRegionDictVo> pageList(@RequestBody RealtyRegionDictSearchVo search) {
        return PageResult.convertPage(realtyRegionDictService.pageList(search), realtyRegionDictConvert::toV);
    }
}
