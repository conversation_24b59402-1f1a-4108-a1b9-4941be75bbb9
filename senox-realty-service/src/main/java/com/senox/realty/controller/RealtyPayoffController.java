package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.service.ContractBillService;
import com.senox.realty.service.RealtyPayoffService;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyPayoffSearchVo;
import com.senox.realty.vo.RealtyPayoffVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/25 9:49
 */
@Api(tags = "应付账单")
@RequiredArgsConstructor
@RestController
@RequestMapping("/payoff")
public class RealtyPayoffController extends BaseController {

    private final RealtyPayoffService payoffService;
    private final ContractBillService contractBillService;

    @ApiOperation("生成应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generatePayoff(@Validated @RequestBody BillMonthVo month) {
        contractBillService.buildAndSavePayoff(month, getUserInContext());
    }

    @ApiOperation("更新应付账单")
    @PostMapping("/update")
    public void updatePayoff(@RequestBody RealtyPayoffVo payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        payoffService.savePayoff(payoff, getUserInContext());
    }

    @ApiOperation("删除应付账单")
    @PostMapping("/delete/{id}")
    public void deletePayoff(@PathVariable Long id) {
        payoffService.deletePayoff(id);
    }

    @ApiOperation("获取应付账单详情")
    @GetMapping("/get/{id}")
    public RealtyPayoffVo getPayoff(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return payoffService.findDetailById(id);
    }

    @ApiOperation("应付账单合计")
    @PostMapping("/sum")
    public RealtyPayoffVo sumPayoff(@RequestBody RealtyPayoffSearchVo search) {
        return payoffService.sumPayoff(search);
    }

    @ApiOperation("应付账单列表")
    @PostMapping("/list")
    public List<RealtyPayoffVo> listPayoff(@RequestBody RealtyPayoffSearchVo search) {
        search.setPageNo(1);
        search.setPage(Boolean.FALSE);
        return payoffService.listPayoff(search);
    }

    @ApiOperation("应付账单列表页")
    @PostMapping("/page")
    public PageResult<RealtyPayoffVo> listPayoffPage(@RequestBody RealtyPayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search
                , () -> payoffService.countPayoff(search)
                , () -> payoffService.listPayoff(search));
    }
}
