package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.domain.OneTimeFee;
import com.senox.realty.service.OneTimeFeeService;
import com.senox.realty.vo.OneTimeFeeSearchVo;
import com.senox.realty.vo.OneTimeFeeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12 14:10
 */
@Api(tags = "一次性收费项目")
@RestController
@RequestMapping("/oneTimeFee")
public class OneTimeFeeController extends BaseController {

    @Autowired
    private OneTimeFeeService oneTimeFeeService;

    @ApiOperation("新增一次性收费项目")
    @PostMapping("/add")
    public Long addOneTimeFee(@Validated({Add.class}) @RequestBody OneTimeFeeVo fee) {
        if (CollectionUtils.isEmpty(fee.getDepartmentIds())) {
            throw new InvalidParameterException("部门不能为空");
        }

        OneTimeFee oneTimeFee = oneTimeFeeVo2Entity(fee);
        initEntityCreator(oneTimeFee);
        initEntityModifier(oneTimeFee);

        return oneTimeFeeService.addOneTimeFee(oneTimeFee, fee.getDepartmentIds());
    }

    @ApiOperation("更新一次性收费项目")
    @PostMapping("/update")
    public void updateOneTimeFee(@Validated({Update.class}) @RequestBody OneTimeFeeVo fee) {
        OneTimeFee oneTimeFee = oneTimeFeeVo2Entity(fee);
        initEntityModifier(oneTimeFee);
        oneTimeFeeService.updateOneTimeFee(oneTimeFee, fee.getDepartmentIds());
    }

    @ApiOperation("根据id查找一次性收费项目")
    @GetMapping("/get/{id}")
    public OneTimeFeeVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return oneTimeFeeService.findById(id);
    }

    @ApiOperation("一次性收费项目列表")
    @PostMapping("/list")
    public PageResult<OneTimeFeeVo> listOneTimeFee(@Validated @RequestBody OneTimeFeeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return oneTimeFeeService.listOneTimeFee(search);
    }

    @ApiOperation("部门一次性收入项目")
    @GetMapping("/listByDepartment")
    public List<OneTimeFeeVo> listDepartmentOneTimeFee(@RequestParam(required = false) List<Long> departmentIds) {
        return oneTimeFeeService.listOneTimeFeeByDepartment(departmentIds == null ? Collections.emptyList() : departmentIds);
    }

    /**
     * 一次性收费项目视图转实体
     * @param fee
     * @return
     */
    private OneTimeFee oneTimeFeeVo2Entity(OneTimeFeeVo fee) {
        OneTimeFee result = new OneTimeFee();
        result.setId(fee.getId());
        result.setName(fee.getName());
        result.setDisabled(fee.getDisabled());
        if (null != fee.getMobileEditable()) {
            result.setMobileEditable(fee.getMobileEditable());
        }
        if (null != fee.getRefrigeration()) {
            result.setRefrigeration(fee.getRefrigeration());
        }
        return result;
    }
}
