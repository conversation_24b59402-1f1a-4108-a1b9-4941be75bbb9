package com.senox.realty.controller;

import com.senox.common.annotation.Debounce;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.convert.MaintainJobConvertor;
import com.senox.realty.convert.MaintainJobItemConvertor;
import com.senox.realty.domain.MaintainEvaluateMedia;
import com.senox.realty.domain.MaintainJob;
import com.senox.realty.domain.MaintainJobItem;
import com.senox.realty.domain.MaintainMedia;
import com.senox.realty.service.MaintainEvaluateMediaService;
import com.senox.realty.service.MaintainJobItemService;
import com.senox.realty.service.MaintainJobService;
import com.senox.realty.service.MaintainMediaService;
import com.senox.realty.vo.MaintainDispatchJobVo;
import com.senox.realty.vo.MaintainJobItemVo;
import com.senox.realty.vo.MaintainJobSearchVo;
import com.senox.realty.vo.MaintainJobVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/4/3 10:44
 */
@Api(tags = "派工单")
@RestController
@RequestMapping("/maintain/job")
@RequiredArgsConstructor
public class MaintainJobController extends BaseController {

    private final MaintainJobConvertor convertor;
    private final MaintainJobService maintainJobService;
    private final MaintainJobItemConvertor jobItemConvertor;
    private final MaintainMediaService maintainMediaService;
    private final MaintainJobItemService jobItemService;
    private final MaintainEvaluateMediaService evaluateMediaService;



    @ApiOperation("添加派工单")
    @PostMapping("/add")
    @Debounce(expire = 2)
    public Long addMaintainJob(@RequestBody MaintainJobVo maintainJobVo) {
        if (maintainJobVo.getOrderId() == null) {
            throw new InvalidParameterException("无效维修单id");
        }
        MaintainJob maintainJob = convertor.toDo(maintainJobVo);
        initEntityCreator(maintainJob);
        initEntityModifier(maintainJob);
        return maintainJobService.addMaintainJob(maintainJob, maintainJobVo);
    }

    @ApiOperation("修改派工单")
    @PostMapping("/update")
    public void updateMaintainJob(@RequestBody MaintainJobVo maintainJobVo) {
        if (!WrapperClassUtils.biggerThanLong(maintainJobVo.getId(), 0L)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        MaintainJob maintainJob = convertor.toDo(maintainJobVo);
        initEntityModifier(maintainJob);
        maintainJobService.updateMaintainJob(maintainJob, maintainJobVo);
    }

    @ApiOperation("修改派工人员信息")
    @PostMapping("/item/update")
    @Debounce(expire = 2)
    public void updateMaintainJobItem(@RequestBody MaintainJobItemVo maintainJobItemVo) {
        MaintainJobItem maintainJobItem = jobItemConvertor.toDo(maintainJobItemVo);
        initEntityModifier(maintainJobItem);
        maintainJobService.updateMaintainJobItem(maintainJobItem, maintainJobItemVo.getMediaUrls());
    }

    @ApiOperation("获取派工单")
    @GetMapping("/get/{id}")
    public MaintainJobVo findMaintainJob(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return maintainJobService.findDispatchById(id);
    }


    @ApiOperation("查询派工单")
    @GetMapping("/dispatch/{orderId}")
    public List<MaintainJobVo> listDispatchJobByOrderId(@PathVariable Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new InvalidParameterException();
        }
        return maintainJobService.listDispatchByOrderId(orderId);
    }

    @ApiOperation("查询派工单列表")
    @PostMapping("/dispatch/list")
    public PageResult<MaintainDispatchJobVo> listDispatchJob(@RequestBody MaintainJobSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return maintainJobService.listDispatchJob(searchVo);
    }

    @ApiOperation("根据派工子单查询")
    @GetMapping("/dispatch/get/{itemId}")
    public MaintainDispatchJobVo findDispatchByJobItemId(@PathVariable Long itemId) {
        MaintainDispatchJobVo dispatchJobVo = maintainJobService.findDispatchByJobItemId(itemId);
        if (dispatchJobVo != null) {
            List<MaintainMedia> list = maintainMediaService.listByOrderIdAndJobId(dispatchJobVo.getOrderId(), dispatchJobVo.getId(), dispatchJobVo.getJobItemId());
            dispatchJobVo.setJobMediaUrls(CollectionUtils.isEmpty(list) ? null : list.stream().map(MaintainMedia::getMediaUrl).collect(Collectors.toList()));
            List<MaintainJobItem> items = jobItemService.maintainJobItemListByJobId(dispatchJobVo.getId(), null);
            dispatchJobVo.setHandlerNameList(CollectionUtils.isEmpty(items) ? null
                    : items.stream().filter(x -> !Objects.equals(x.getId(), dispatchJobVo.getJobItemId())).map(MaintainJobItem::getHandlerName).collect(Collectors.toList()));
            List<MaintainEvaluateMedia> evaluateMedia = evaluateMediaService.listMediasByOrderId(dispatchJobVo.getOrderId());
            dispatchJobVo.setEvaluateMediaUrls(CollectionUtils.isEmpty(evaluateMedia) ? null : evaluateMedia.stream().map(MaintainEvaluateMedia::getMediaUrl).collect(Collectors.toList()));
        }
        return dispatchJobVo;
    }

    @ApiOperation("删除派工单")
    @PostMapping("/delete/{jobId}")
    public void deleteMaintainJob(@PathVariable Long jobId) {
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            throw new InvalidParameterException();
        }
        MaintainJob maintainJob = new MaintainJob();
        maintainJob.setId(jobId);
        initEntityModifier(maintainJob);
        maintainJobService.deleteMaintainJob(maintainJob);
    }
}
