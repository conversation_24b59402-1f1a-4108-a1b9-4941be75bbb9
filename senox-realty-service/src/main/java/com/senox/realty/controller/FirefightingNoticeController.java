package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.convert.FireFightingNoticeConvertor;
import com.senox.realty.domain.FirefightingInspectRealty;
import com.senox.realty.domain.FirefightingNotice;
import com.senox.realty.domain.FirefightingTemplate;
import com.senox.realty.service.FirefightingInspectRealtyService;
import com.senox.realty.service.FirefightingNoticeService;
import com.senox.realty.service.FirefightingTemplateService;
import com.senox.realty.vo.FirefightingNoticeSearchVo;
import com.senox.realty.vo.FirefightingNoticeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/24 11:42
 */
@Api(tags = "店铺消防安全责任告知单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/notice")
public class FirefightingNoticeController extends BaseController {

    private final FirefightingNoticeService noticeService;
    private final FireFightingNoticeConvertor noticeConvertor;
    private final FirefightingTemplateService templateService;
    private final FirefightingInspectRealtyService inspectRealtyService;

    @ApiOperation("添加店铺消防安全责任告知单")
    @PostMapping("/add")
    public Long addNotice(@Validated(Add.class) @RequestBody FirefightingNoticeVo notice) {
        FirefightingNotice entity = noticeConvertor.toDo(notice);

        initEntityCreator(entity);
        initEntityModifier(entity);
        return noticeService.addNotice(notice.getTaskId(), entity, notice.getRealtySerials());
    }

    @ApiOperation("更新店铺消防安全责任告知单")
    @PostMapping("/update")
    public void updateNotice(@Validated(Update.class) @RequestBody FirefightingNoticeVo notice) {
        FirefightingNotice entity = noticeConvertor.toDo(notice);

        initEntityModifier(entity);
        noticeService.updateNotice(entity, notice.getRealtySerials());
    }

    @ApiOperation("删除店铺消防安全责任告知单")
    @PostMapping("/delete/{id}")
    public void deleteNotice(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        noticeService.deleteNotice(id);
    }

    @ApiOperation("获取店铺消防安全责任告知单")
    @GetMapping("/get/{id}")
    public FirefightingNoticeVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingNotice notice = noticeService.findById(id);
        FirefightingNoticeVo result =  notice == null ? null : noticeConvertor.toVo(notice);
        if (result != null) {
            List<FirefightingInspectRealty> realtyList = inspectRealtyService.findByInspectId(id, InspectionType.NOTICE);
            result.setRealtySerials(realtyList.stream().map(FirefightingInspectRealty::getRealtySerial).collect(Collectors.toList()));

            FirefightingTemplate template = templateService.findTemplateByCodeAndVersion(notice.getTemplateCode(), notice.getTemplateVersion());
            if (template != null) {
                result.setTitle(template.getTitle());
                result.setContent(template.getContent());
            }
        }
        return result;
    }

    @ApiOperation("店铺消防安全责任告知单页")
    @PostMapping("/page")
    public PageResult<FirefightingNoticeVo> listNoticePage(@RequestBody FirefightingNoticeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> noticeService.countNotice(search), () -> noticeService.listNotice(search));
    }

}
