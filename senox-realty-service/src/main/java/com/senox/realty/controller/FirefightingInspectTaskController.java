package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.InspectCategory;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.convert.FirefightingInspectTaskConvertor;
import com.senox.realty.domain.FirefightingInspectTask;
import com.senox.realty.domain.FirefightingInspectTaskItem;
import com.senox.realty.domain.FirefightingUtility;
import com.senox.realty.domain.Realty;
import com.senox.realty.service.EnterpriseService;
import com.senox.realty.service.FirefightingInspectTaskItemService;
import com.senox.realty.service.FirefightingInspectTaskService;
import com.senox.realty.service.FirefightingUtilityService;
import com.senox.realty.service.RealtyService;
import com.senox.realty.vo.FirefightingInspectTaskItemSearchVo;
import com.senox.realty.vo.FirefightingInspectPropertyTaskVo;
import com.senox.realty.vo.FirefightingInspectTaskSearchVo;
import com.senox.realty.vo.FirefightingInspectTaskVo;
import com.senox.realty.vo.FirefightingTaskItemDropVo;
import com.senox.realty.vo.FirefightingUtilitySearchVo;
import com.senox.realty.vo.InspectionTargetVo;
import com.senox.realty.vo.RealtySearchVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/14 15:43
 */
@Api(tags = "巡检任务")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/task")
public class FirefightingInspectTaskController extends BaseController {

    private final RealtyService realtyService;
    private final EnterpriseService enterpriseService;
    private final FirefightingUtilityService utilityService;
    private final FirefightingInspectTaskService taskService;
    private final FirefightingInspectTaskItemService taskItemService;
    private final FirefightingInspectTaskConvertor taskConvertor;

    @ApiOperation("添加巡检任务")
    @PostMapping("/add")
    public Long addInspectTask(@Validated @RequestBody FirefightingInspectTaskVo task) {
        if (!checkTaskTargetValidated(task.getTypes(), task.getInspectTarget())) {
            throw new InvalidParameterException();
        }
        // 任务
        FirefightingInspectTask entity = taskConvertor.toDo(task);
        initEntityCreator(entity);
        initEntityModifier(entity);

        // 补全任务对象
        prepareInspectTargetItems(task.getCategory(), task.getInspectTarget());
        return taskService.saveInspectTask(entity, buildTaskItems(task.getTypes(), task.getInspectTarget()));
    }

    @ApiOperation("更新巡检任务")
    @PostMapping("/update")
    public void updateInspectTask(@RequestBody FirefightingInspectTaskVo task) {
        if (!WrapperClassUtils.biggerThanLong(task.getId(), 0L)
                || !checkTaskTargetValidated(task.getTypes(), task.getInspectTarget())) {
            throw new InvalidParameterException();
        }

        // 任务
        FirefightingInspectTask entity = taskConvertor.toDo(task);
        initEntityModifier(entity);

        // 补全任务对象
        prepareInspectTargetItems(task.getCategory(), task.getInspectTarget());
        taskService.saveInspectTask(entity, buildTaskItems(task.getTypes(), task.getInspectTarget()));
    }

    @ApiOperation("获取巡检任务详情")
    @GetMapping("/get/{id}")
    public FirefightingInspectTaskVo findTaskById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return taskService.findVoById(id);
    }

    @ApiOperation("巡检任务列表页")
    @PostMapping("/page")
    public PageResult<FirefightingInspectTaskVo> listTaskPage(@RequestBody FirefightingInspectTaskSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> taskService.countTask(search), () -> taskService.listTask(search));
    }

    @ApiOperation("删除巡检任务明细")
    @PostMapping("/item/delete")
    public void deleteTaskItem(@Validated @RequestBody FirefightingTaskItemDropVo drop) {
        if (CollectionUtils.isEmpty(drop.getTaskItems())) {
            throw new InvalidParameterException();
        }

        taskItemService.deleteTaskItemAndInspection(drop);
    }

    @ApiOperation("巡检任务明细页")
    @PostMapping("/item/page")
    public PageResult<FirefightingInspectPropertyTaskVo> listTaskItemPage(@RequestBody FirefightingInspectTaskItemSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> taskItemService.countTaskItem(search), () -> taskItemService.listTaskItem(search));
    }

    /**
     * 任务对象处理
     * @param category
     * @param target
     */
    private void prepareInspectTargetItems(Integer category, InspectionTargetVo target) {
        if (target == null || !BooleanUtils.isTrue(target.getWholeArea())) {
            return;
        }

        InspectCategory inspectCategory = InspectCategory.fromValue(category);
        if (inspectCategory == InspectCategory.UTILITY) {
            // 公共消防设施
            List<FirefightingUtility> utilityList = listWholeUtilityRegion(target.getRegionId(), target.getStreetId());
            if (!CollectionUtils.isEmpty(utilityList)) {
                target.setUtilityIds(utilityList.stream().map(FirefightingUtility::getId).collect(Collectors.toList()));
            }

        } else if (inspectCategory == InspectCategory.REALTY) {
            if (BooleanUtils.isTrue(target.getEnterprise())) {
                // 经营户
                List<EnterpriseViewVo> enterpriseList = listWholeEnterpriseRegion(target.getRegionId(), target.getStreetId(), target.getEmphasis());
                if (!CollectionUtils.isEmpty(enterpriseList)) {
                    target.setEnterpriseIds(enterpriseList.stream().map(EnterpriseViewVo::getId).collect(Collectors.toList()));
                }

            } else {
                // 物业
                List<Realty> realtyList = listWholeRealtyRegion(target.getRegionId(), target.getStreetId());
                if (!CollectionUtils.isEmpty(realtyList)) {
                    target.setSerials(realtyList.stream().map(Realty::getSerialNo).collect(Collectors.toList()));
                }
            }
        }
    }

    /**
     * 子任务列表
     * @param types
     * @param target
     * @return
     */
    private List<FirefightingInspectTaskItem> buildTaskItems(List<InspectionType> types, InspectionTargetVo target) {
        if (CollectionUtils.isEmpty(types) || target == null) {
            return null;
        }

        List<FirefightingInspectTaskItem> resultList = null;
        if (!CollectionUtils.isEmpty(target.getUtilityIds())) {
            // 公共消防设施巡检任务
            resultList = new ArrayList<>(target.getUtilityIds().size() * types.size());
            for (Long id : target.getUtilityIds()) {
                for (InspectionType type : types) {
                    resultList.add(newInspectTaskItem(type, id, null, null));
                }
            }

        } else if (BooleanUtils.isTrue(target.getEnterprise())) {
            // 经营户维度的巡检任务
            resultList = new ArrayList<>(target.getEnterpriseIds().size() * types.size());
            for (Long enterpriseId : target.getEnterpriseIds()) {
                for (InspectionType type : types) {
                    resultList.add(newInspectTaskItem(type, null, null, enterpriseId));
                }
            }

        } else {
            // 物业消防巡检任务
            resultList = new ArrayList<>(target.getSerials().size() * types.size());
            for (String serial : target.getSerials()) {
                for (InspectionType type : types) {
                    resultList.add(newInspectTaskItem(type, null, serial, null));
                }
            }
        }
        return resultList;
    }

    /**
     * 子任务
     * @param type
     * @param utilityId
     * @param realtySerial
     * @return
     */
    private FirefightingInspectTaskItem newInspectTaskItem(InspectionType type, Long utilityId, String realtySerial, Long enterpriseId) {
        FirefightingInspectTaskItem result = new FirefightingInspectTaskItem();
        result.setUtilityId(utilityId == null ? 0L : utilityId);
        result.setRealtySerial(StringUtils.trimToEmpty(realtySerial));
        result.setEnterpriseId(enterpriseId == null ? 0L : enterpriseId);
        result.setInspectType(type);
        return result;
    }

    /**
     * 任务明细是否设置合理
     * @param types
     * @param target
     * @return
     */
    private boolean checkTaskTargetValidated(List<InspectionType> types, InspectionTargetVo target) {
        return (CollectionUtils.isEmpty(types) && target == null)
                || (!CollectionUtils.isEmpty(types) && target != null);
    }

    /**
     * 区域内消防公共设施
     * @param regionId
     * @param streetId
     * @return
     */
    private List<FirefightingUtility> listWholeUtilityRegion(Long regionId, Long streetId) {
        FirefightingUtilitySearchVo search = new FirefightingUtilitySearchVo();
        search.setRegionId(regionId);
        search.setStreetId(streetId);
        search.setPage(false);
        return utilityService.listUtility(search);
    }

    /**
     * 区域内物业
     * @param regionId
     * @param streetId
     * @return
     */
    private List<Realty> listWholeRealtyRegion(Long regionId, Long streetId) {
        RealtySearchVo search = new RealtySearchVo();
        search.setRegionId(regionId);
        search.setStreetId(streetId);
        search.setPage(false);
        return realtyService.listRealty(search);
    }

    /**
     * 区域内经营户
     * @param regionId
     * @param streetId
     * @return
     */
    private List<EnterpriseViewVo> listWholeEnterpriseRegion(Long regionId, Long streetId, Boolean isEmphasis) {
        EnterpriseSearchVo search = new EnterpriseSearchVo();
        search.setRegionId(regionId);
        search.setStreetId(streetId);
        if (isEmphasis != null) {
            search.setFirefightingEmphasis(isEmphasis);
        }
        return enterpriseService.listEnterprise(search);
    }

}
