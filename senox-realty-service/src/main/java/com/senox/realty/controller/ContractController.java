package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.constant.ContractFeeCategory;
import com.senox.realty.convert.ContractConvertor;
import com.senox.realty.convert.RealtyWeConvertor;
import com.senox.realty.domain.Contract;
import com.senox.realty.domain.ContractExt;
import com.senox.realty.domain.ContractFee;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.service.ContractBillService;
import com.senox.realty.service.ContractService;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/2/19 15:31
 */
@Api(tags = "合同管理")
@RestController
@RequestMapping("/contract")
@RequiredArgsConstructor
public class ContractController extends BaseController {

    private final ContractService contractService;
    private final RealtyWeConvertor realtyWeConvertor;
    private final ContractBillService contractBillService;
    private final ContractConvertor contractConvertor;

    @ApiOperation("新增合同")
    @PostMapping("/add")
    public Long addContract(@Validated({Add.class}) @RequestBody ContractVo contractVo) {
        // 校验合同费项
        checkContractFee(contractVo.getFees(), contractVo.getStartDate(), contractVo.getEndDate());
        // 合同
        Contract contract = contractConvertor.toDo(contractVo);
        initEntityCreator(contract);
        initEntityModifier(contract);

        // 合同扩展信息
        ContractExt ext = contractConvertor.voToExtDo(contractVo);
        return contractService.addContract(contract, ext, contractFeeNodes2List(contractVo.getFees()));
    }

    @ApiOperation("更新合同")
    @PostMapping("/update")
    public void updateContract(@Validated({Update.class}) @RequestBody ContractVo contractVo) {
        if (contractVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        // 校验合同费项
        checkContractFee(contractVo.getFees(), contractVo.getStartDate(), contractVo.getEndDate());
        // 合同
        Contract contract = contractConvertor.toDo(contractVo);
        initEntityModifier(contract);

        // 合同扩展信息
        ContractExt ext = contractConvertor.voToExtDo(contractVo);
        contractService.updateContract(contract, ext, contractFeeNodes2List(contractVo.getFees()));
    }

    @ApiOperation("根据id获取合同信息")
    @GetMapping("/get/{id}")
    public ContractVo getContract(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        // 合同
        ContractVo contract = contractService.findWithRealtyAndCustomerById(id);
        ContractExt ext = null;
        List<ContractFee> fees = null;
        if (contract != null) {
            ext = contractService.findExtByContractId(id);
            fees = contractService.listContractFee(id);

            prepareExtContractVo(contract, ext);
            contract.setFees(contractFee2NodeList(fees));
        }
        return contract;
    }

    @GetMapping("/renew/from/{id}")
    public ContractVo getRenewFrom(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        // 合同
        ContractVo contract = contractService.findRenewFrom(id);
        if (contract != null) {
            ContractExt ext = contractService.findExtByContractId(id);
            prepareExtContractVo(contract, ext);
        }
        return contract;
    }

    @ApiOperation("获取物业最新合同")
    @GetMapping("/listRealtyContract/{realtyId}")
    public List<ContractVo> listRealtyContract(@PathVariable Long realtyId) {
        if (realtyId < 1L) {
            throw new InvalidParameterException("无效的物业id");
        }
        List<Contract> list = contractService.listRealtyAvailableContract(realtyId);
        return list == null ? Collections.emptyList()
                : list.stream().map(contractConvertor::toVo).collect(Collectors.toList());
    }

    @ApiOperation("根据合同编号获取合同信息")
    @GetMapping("/getByContractNo")
    public ContractVo getContractByContractNo(@RequestParam String contractNo) {
        if (StringUtils.isBlank(contractNo) || contractNo.length() < 5) {
            throw new InvalidParameterException("无效的合同号");
        }

        Contract contract = contractService.findByContractNo(contractNo);
        return contract == null ? null : contractConvertor.toVo(contract);
    }

    @ApiOperation("物业租赁合同")
    @PostMapping("/realtyLease/get")
    public ContractVo getRealtyLeaseContract(@Validated @RequestBody RealtyDateVo realtyDateVo) {
        // 合同
        ContractVo contract = contractService.findRealtyLatestLeaseContract(realtyDateVo.getRealtyId(), realtyDateVo.getDate());
        if (contract == null) {
            return null;
        }

        // 扩展对象
        ContractExt ext = contractService.findExtByContractId(contract.getId());
        List<ContractFee> fees = contractService.listContractFee(contract.getId());

        // 结果
        prepareExtContractVo(contract, ext);
        contract.setFees(contractFee2NodeList(fees));
        return contract;
    }


    @ApiOperation("合同列表")
    @PostMapping("/list")
    public PageResult<ContractVo> listContractPage(@Validated @RequestBody ContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return contractService.listContractPage(search);
    }

    @ApiOperation("租赁合同列表")
    @PostMapping("/lease/list")
    public PageResult<LeaseContractListVo> listLeaseContractPage(@Validated @RequestBody ContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return contractService.listLeaseContractPage(search);
    }

    @ApiOperation("启用合同")
    @PostMapping("/enable")
    public void enableContract(@RequestParam String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            throw new InvalidParameterException();
        }

        ContractEnableDto enableDto = new ContractEnableDto(contractNo, LocalDate.now());
        enableDto.setOperator(getUserInContext());
        contractBillService.enableContract(enableDto);
    }

    @ApiOperation("中止合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/suspend")
    public void suspendContract(@Validated @RequestBody RealtyContractSuspendRequestDto suspendReq) {
        if (suspendReq.getWeList().stream().map(RealtyWeVo::getRealtySerial).distinct().count() < suspendReq.getWeList().size()) {
            throw new InvalidParameterException("不允许重复录入水电");
        }

        // 中止参数
        RealtyContractSuspendDto suspendDto = new RealtyContractSuspendDto(suspendReq.getContractNo(), suspendReq.getSuspendDate());
        suspendDto.setOperator(getUserInContext());

        // 水电读数
        List<RealtyWe> weList = CollectionUtils.isEmpty(suspendReq.getWeList()) ? Collections.emptyList()
                : suspendReq.getWeList().stream().map(realtyWeConvertor::toDo).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(weList)) {
            weList.forEach(x -> {
                x.setBillYear(suspendReq.getSuspendDate().getYear());
                x.setBillMonth(suspendReq.getSuspendDate().getMonthValue());
                x.setCreatorId(getUserInContext().getUserId());
                x.setCreatorName(getUserInContext().getUsername());
                x.setCreateTime(LocalDateTime.now());
                x.setModifierId(getUserInContext().getUserId());
                x.setModifierName(getUserInContext().getUsername());
                x.setModifiedTime(LocalDateTime.now());
                x.setType(RealtyWeType.SUSPEND_CONTRACT.getValue());
            });
        }

        contractBillService.suspendContract(suspendDto, weList);
    }


    @ApiOperation("银行代扣信息列表")
    @PostMapping("/bankDelegate/list")
    public PageResult<ContractBankVo> listContractBank(@Validated @RequestBody ContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return contractService.listContractBank(search);
    }

    /**
     * 实体扩展对象转视图对象
     * @param vo
     * @param ext
     */
    private void prepareExtContractVo(ContractVo vo, ContractExt ext) {
        if (ext == null) {
            return;
        }

        vo.setCostType(ext.getCostType());
        vo.setBankName(ext.getBankName());
        vo.setBankAccountNo(ext.getBankAccountNo());
        vo.setBankAccountName(ext.getBankAccountName());
        vo.setBankAccountIdcard(ext.getBankAccountIdcard());
        vo.setFirstRate(ext.getFirstRate());
        vo.setMonthlyRate(ext.getMonthlyRate());
        vo.setMonthlyFeeAbs(ext.getMonthlyFeeAbs());
        vo.setWaterPriceType(ext.getWaterPriceType());
        vo.setElectricPriceType(ext.getElectricPriceType());
        vo.setPenaltyStartDate(ext.getPenaltyStartDate());
        vo.setPenaltyRate(ext.getPenaltyRate());
        vo.setArchiveUrl(ext.getArchiveUrl());
        vo.setRemark(ext.getRemark());
    }

    /**
     * 校验合同费项
     * @param feeNodes
     * @param startDate
     * @param endDate
     */
    private void checkContractFee(List<ContractFeeNode> feeNodes, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(feeNodes)) {
            return;
        }

        // 默认每个费项只有一个默认收费
        if (feeNodes.size() != feeNodes.stream().map(ContractFeeNode::getFeeId).distinct().count()) {
            throw new InvalidParameterException("重复的费项");
        }

        for (ContractFeeNode item : feeNodes) {
            checkContractFee(item, startDate, endDate);

            // 阶段费项
            if (CollectionUtils.isEmpty(item.getDetail())) {
                continue;
            }

            checkContractIntervalFee(item, startDate, endDate);
        }
    }

    /**
     * 费项校验
     * @param contractFee
     * @param startDate
     * @param endDate
     */
    private void checkContractFee(ContractFeeNode contractFee, LocalDate startDate, LocalDate endDate) {
        if (!WrapperClassUtils.biggerThanLong(contractFee.getFeeId(), 0L)) {
            throw new InvalidParameterException();
        }

        contractFee.setCategory(ContractFeeCategory.DEFAULT.ordinal());
        contractFee.setStartDate(startDate);
        contractFee.setEndDate(endDate);
        contractFee.setAmount(DecimalUtils.nullToZero(contractFee.getAmount()));
    }

    /**
     * 期间费项校验
     * @param parentFee
     * @param startDate
     * @param endDate
     */
    private void checkContractIntervalFee(ContractFeeNode parentFee, LocalDate startDate, LocalDate endDate) {
        List<ContractFeeNode> childNodes = parentFee.getDetail();
        if (CollectionUtils.isEmpty(childNodes)) {
            return;
        }

        // 按阶段费项时间升序排列
        childNodes = childNodes.stream()
                .sorted(Comparator.comparing(ContractFeeNode::getStartDate, Comparator.nullsFirst(LocalDate::compareTo)))
                .collect(Collectors.toList());

        for (ContractFeeNode item : childNodes) {
            if (item.getStartDate() == null || item.getEndDate() == null || item.getEndDate().isBefore(item.getStartDate())) {
                throw new InvalidParameterException();
            }
            if (item.getStartDate().isBefore(startDate) || item.getEndDate().isAfter(endDate)) {
                throw new InvalidParameterException("无效的费项周期");
            }

            item.setFeeId(parentFee.getFeeId());
            item.setCategory(ContractFeeCategory.PERIOD.ordinal());
            item.setAmount(DecimalUtils.nullToZero(item.getAmount()));

            startDate = item.getStartDate();
        }
    }

    /**
     * 费项节点列表
     * @param nodes
     * @return
     */
    private List<ContractFee> contractFeeNodes2List(List<ContractFeeNode> nodes) {
        if (nodes == null) {
            return Collections.emptyList();
        }
        List<ContractFee> resultList = new ArrayList<>(nodes.size());
        for (ContractFeeNode node : nodes) {
            resultList.add(contractFeeNode2Entity(node));
            if (!CollectionUtils.isEmpty(node.getDetail())) {
                resultList.addAll(contractFeeNodes2List(node.getDetail()));
            }

        }
        return resultList;
    }

    /**
     * 费项节点转实体
     * @param node
     * @return
     */
    private ContractFee contractFeeNode2Entity(ContractFeeNode node) {
        ContractFee result = new ContractFee();
        result.setFeeId(node.getFeeId());
        result.setAmount(node.getAmount());
        result.setCategory(node.getCategory());
        result.setRentFreePeriod(node.getRentFreePeriod());
        result.setStartDate(node.getStartDate());
        result.setEndDate(node.getEndDate());
        return result;
    }

    private List<ContractFeeNode> contractFee2NodeList(List<ContractFee> list) {
        if (list == null) {
            return Collections.emptyList();
        }

        List<ContractFeeNode> resultList = list.stream()
                .filter(x -> Objects.equals(ContractFeeCategory.DEFAULT.ordinal(), x.getCategory()))
                .map(this::contractFee2Node)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(resultList)) {
            for (ContractFeeNode item : resultList) {
                List<ContractFeeNode> childList = list.stream()
                        .filter(x -> Objects.equals(ContractFeeCategory.PERIOD.ordinal(), x.getCategory()) && Objects.equals(item.getFeeId(), x.getFeeId()))
                        .map(this::contractFee2Node)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(childList)) {
                    item.setDetail(childList);
                }
            }
        }
        return resultList;
    }

    /**
     * 实体转费项节点
     * @param fee
     * @return
     */
    private ContractFeeNode contractFee2Node(ContractFee fee) {
        ContractFeeNode result = new ContractFeeNode();
        result.setFeeId(fee.getFeeId());
        result.setPeriod(fee.getPeriod());
        result.setAmount(fee.getAmount());
        result.setRentFreePeriod(fee.getRentFreePeriod());
        result.setStartDate(fee.getStartDate());
        result.setEndDate(fee.getEndDate());
        return result;
    }
}
