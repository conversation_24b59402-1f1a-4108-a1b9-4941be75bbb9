package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.constant.PriceType;
import com.senox.realty.domain.WaterElectricPriceType;
import com.senox.realty.service.WaterElectricPriceTypeService;
import com.senox.realty.vo.WaterElectricPriceTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/9 15:35
 */
@Api(tags = "水电价格类别")
@RestController
@RequestMapping("/waterElectricPriceType")
public class WaterElectricPriceTypeController extends BaseController {

    @Autowired
    private WaterElectricPriceTypeService priceTypeService;


    @ApiOperation("添加水/电价格类别")
    @PostMapping("/add")
    public Long addPriceType(@Validated({Add.class}) @RequestBody WaterElectricPriceTypeVo priceTypeVo) {
        WaterElectricPriceType priceType = priceTypeVo2Entity(priceTypeVo);
        initEntityCreator(priceType);
        initEntityModifier(priceType);

        return priceTypeService.addPriceType(priceType);
    }

    @ApiOperation("更新水/电价格类别")
    @PostMapping("/update")
    public void updatePriceType(@Validated({Update.class}) @RequestBody WaterElectricPriceTypeVo priceTypeVo) {
        if (priceTypeVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        WaterElectricPriceType priceType = priceTypeVo2Entity(priceTypeVo);
        initEntityModifier(priceType);
        priceTypeService.updatePriceType(priceType);
    }

    @ApiOperation("获取水/电价格类别详情")
    @GetMapping("/get/{id}")
    public WaterElectricPriceTypeVo getPriceType(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        WaterElectricPriceType result = priceTypeService.findById(id);
        return result == null ? null : priceType2Vo(result);
    }

    @ApiOperation("水/电价格类别列表")
    @PostMapping("/list")
    public List<WaterElectricPriceTypeVo> list(@RequestParam(required = false) Integer type) {
        List<WaterElectricPriceType> resultList = priceTypeService.listAll();
        if (type != null && type > 0 && !CollectionUtils.isEmpty(resultList)) {
            PriceType priceType = PriceType.fromValue(type);
            if (priceType == null) {
                throw new InvalidParameterException("无效的类别");
            }
            resultList = resultList.stream().filter(x -> Objects.equals(x.getType(), type)).collect(Collectors.toList());
        }
        return CollectionUtils.isEmpty(resultList) ? Collections.emptyList()
                : resultList.stream().map(this::priceType2Vo).collect(Collectors.toList());
    }

    private WaterElectricPriceType priceTypeVo2Entity(WaterElectricPriceTypeVo priceTypeVo) {
        WaterElectricPriceType result = new WaterElectricPriceType();
        BeanUtils.copyProperties(priceTypeVo, result);
        return result;
    }

    private WaterElectricPriceTypeVo priceType2Vo(WaterElectricPriceType priceType) {
        WaterElectricPriceTypeVo result = new WaterElectricPriceTypeVo();
        BeanUtils.copyProperties(priceType, result);
        return result;
    }
}
