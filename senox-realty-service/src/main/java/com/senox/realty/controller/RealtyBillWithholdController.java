package com.senox.realty.controller;

import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BeanUtils;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.domain.BankWithhold;
import com.senox.realty.service.RealtyBillWithholdService;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/17 15:33
 */
@Api(tags = "银行托收")
@RestController
@RequestMapping("/realtyBill/withhold")
public class RealtyBillWithholdController extends BaseController {

    @Autowired
    private RealtyBillWithholdService withholdService;

    @ApiOperation("账单银行托收报盘")
    @PostMapping("/apply")
    public void apply(@Validated @RequestBody BankWithholdVo withholdVo) {
        BankWithhold withhold = BeanUtils.copyProperties(withholdVo, new BankWithhold());
        initEntityCreator(withhold);
        initEntityModifier(withhold);
        withholdService.applyBillBankWithhold(withhold);
    }

    @ApiOperation("取消账单银行托收报盘")
    @PostMapping("/cancel")
    public void cancel(@Validated @RequestBody BankWithholdVo withholdVo) {
        BankWithhold withhold = BeanUtils.copyProperties(withholdVo, new BankWithhold());
        initEntityModifier(withhold);
        withholdService.cancelBillBankWithhold(withhold);
    }

    @ApiOperation("账单银行托收回盘")
    @PostMapping("/back")
    public void back(@Validated @RequestBody BankWithholdVo withholdVo) {
        BankWithhold withhold = BeanUtils.copyProperties(withholdVo, new BankWithhold());
        initEntityModifier(withhold);
        withholdService.backBillBankWithhold(withhold);
    }

    @ApiOperation("账单银行托收支付")
    @PostMapping("/pay")
    public void withholdPay(@Validated @RequestBody WithholdBackVo withholdBack) {
        BankWithhold withhold = withholdService.findByYearMonth(withholdBack.getBillYear(), withholdBack.getBillMonth());
        if (!isWithholdSubmitted(withhold)) {
            throw new BusinessException("该月账单未报盘");
        }
        if (BooleanUtils.isTrue(withhold.getBack())) {
            throw new BusinessException("该月账单已回盘");
        }
        withholdService.withholdPayBill(withholdBack);
    }

    @ApiOperation("获取银行托收信息")
    @GetMapping("/get")
    public BankWithholdVo getWithHoldApply(@RequestParam("year") Integer year, @RequestParam("month") Integer month) {
        if (!WrapperClassUtils.biggerThanInt(year, 0)
                || !WrapperClassUtils.biggerThanInt(month, 0)) {
            throw new InvalidParameterException();
        }
        BankWithhold result = withholdService.findByYearMonth(year, month);
        return result == null ? null : BeanUtils.copyProperties(result, new BankWithholdVo());
    }

    @ApiOperation("账单银行托收列表")
    @PostMapping("/apply/page")
    public WithholdPage<BankOfferRealtyBillVo> listPage(@RequestBody RealtyBillSearchVo search) {
        if (!WrapperClassUtils.biggerThanInt(search.getBillYear(), 0)
                || !WrapperClassUtils.biggerThanInt(search.getBillMonth(), 0)) {
            throw new InvalidParameterException();
        }

        // 报盘记录
        BankWithhold withhold = withholdService.findByYearMonth(search.getBillYear(), search.getBillMonth());

        PageResult<BankOfferRealtyBillVo> page = null;
        WithholdSumVo sum = null;
        if (isWithholdSubmitted(withhold)) {
            page = withholdService.listBankOfferBillSubmitted(search);
            sum = withholdService.sumBankOfferBillSubmitted(search);
        } else {
            page = withholdService.listBankOfferBillApplying(search);
            sum = withholdService.sumBankOfferBillApplying(search);
        }

        WithholdPage<BankOfferRealtyBillVo> resultPage = new WithholdPage<>(search.getPageNo(), search.getPageSize());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());
        resultPage.initTotalPage();
        if (sum != null) {
            resultPage.setOfferCount(sum.getOfferCount());
            resultPage.setOfferAmount(sum.getOfferAmount());
            resultPage.setBackCount(sum.getBackCount());
            resultPage.setBackAmount(sum.getBackAmount());
        }
        return resultPage;
    }

    @ApiOperation("账单银行托收列表（不分页）")
    @PostMapping("/apply/list")
    public List<BankOfferRealtyBillVo> list(@RequestBody RealtyBillSearchVo search) {
        if (!WrapperClassUtils.biggerThanInt(search.getBillYear(), 0)
                || !WrapperClassUtils.biggerThanInt(search.getBillMonth(), 0)) {
            throw new InvalidParameterException();
        }

        // 报盘记录
        BankWithhold withhold = withholdService.findByYearMonth(search.getBillYear(), search.getBillMonth());

        return isWithholdSubmitted(withhold) ? withholdService.listBankOfferBillSubmittedAll(search)
                : withholdService.listBankOfferBillApplyingAll(search);
    }


    private boolean isWithholdSubmitted(BankWithhold withhold) {
        return withhold != null && BooleanUtils.isTrue(withhold.getOffer());
    }

}
