package com.senox.realty.controller;

import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.convert.AdvertisingSpaceConvertor;
import com.senox.realty.convert.AdvertisingSpacePositionConvertor;
import com.senox.realty.domain.AdvertisingSpace;
import com.senox.realty.domain.AdvertisingSpacePosition;
import com.senox.realty.service.AdvertisingContractService;
import com.senox.realty.service.AdvertisingSpacePositionService;
import com.senox.realty.service.AdvertisingSpaceService;
import com.senox.realty.vo.AdvertisingSpaceListVo;
import com.senox.realty.vo.AdvertisingSpaceSearchVo;
import com.senox.realty.vo.AdvertisingSpaceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18 9:20
 */
@Api(tags = "广告位")
@RestController
@RequiredArgsConstructor
@RequestMapping("/advertising/space")
public class AdvertisingSpaceController extends BaseController {

    private final AdvertisingSpaceService spaceService;
    private final AdvertisingSpacePositionService positionService;
    private final AdvertisingContractService contractService;
    private final AdvertisingSpaceConvertor spaceConvertor;
    private final AdvertisingSpacePositionConvertor positionConvertor;

    @ApiOperation("添加广告位")
    @PostMapping("/add")
    public Long addSpace(@Validated(Add.class) @RequestBody AdvertisingSpaceVo space) {
        AdvertisingSpace entity = spaceConvertor.toDo(space);
        initEntityCreator(entity);
        return spaceService.saveSpace(entity, space.getPositions());
    }


    @ApiOperation("更新广告位信息")
    @PostMapping("/update")
    public void updateSpace(@Validated(Update.class) @RequestBody AdvertisingSpaceVo space) {
        AdvertisingSpace entity = spaceConvertor.toDo(space);
        initEntityModifier(entity);
        spaceService.saveSpace(entity, space.getPositions());
    }

    @ApiOperation("删除广告位")
    @PostMapping("/delete/{id}")
    public void deleteSpace(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        // 判断是否有待启用/已启用的合同
        if (contractService.checkSpaceRent(id)) {
            throw new BusinessException("广告位已使用");
        }

        AdvertisingSpace entity = new AdvertisingSpace();
        entity.setId(id);
        entity.setDisabled(Boolean.TRUE);
        initEntityModifier(entity);
        spaceService.updateSpace(entity);
    }

    @ApiOperation("获取广告位")
    @GetMapping("/get/{id}")
    public AdvertisingSpaceVo getSpace(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        AdvertisingSpace space = spaceService.findById(id);
        AdvertisingSpaceVo result = space == null ? null : spaceConvertor.toVo(space);
        // 位置信息
        if (result != null) {
            List<AdvertisingSpacePosition> positions = positionService.listBySpaceId(id);
            result.setPositions(positionConvertor.toVo(positions));
        }
        return result;
    }

    @ApiOperation("广告位统计")
    @PostMapping("/count")
    public int count(@RequestBody AdvertisingSpaceSearchVo search) {
        return spaceService.countSpace(search);
    }

    @ApiOperation("广告位列表")
    @PostMapping("/page")
    public PageResult<AdvertisingSpaceListVo> list(@RequestBody AdvertisingSpaceSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return spaceService.listSpacePage(search);
    }
}
