package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.convert.FirefightingInspectionAttrConvertor;
import com.senox.realty.convert.FirefightingSmallPlacesInspectionConvertor;
import com.senox.realty.domain.FirefightingInspectMedia;
import com.senox.realty.domain.FirefightingInspectRealty;
import com.senox.realty.domain.FirefightingSmallPlacesInspection;
import com.senox.realty.domain.FirefightingInspectionAttr;
import com.senox.realty.domain.FirefightingSmallPlacesInspectionDetail;
import com.senox.realty.service.FirefightingInspectMediaService;
import com.senox.realty.service.FirefightingInspectRealtyService;
import com.senox.realty.service.FirefightingInspectionAttrService;
import com.senox.realty.service.FirefightingSmallPlacesInspectionDetailService;
import com.senox.realty.service.FirefightingSmallPlacesInspectionService;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionBriefVo;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionSearchVo;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/6 14:43
 */
@Api(tags = "三小场所、出租屋消防巡检记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/inspection/smallPlaces")
public class FirefightingSmallPlacesInspectionController extends BaseController {

    private final FirefightingSmallPlacesInspectionService inspectionService;
    private final FirefightingSmallPlacesInspectionDetailService inspectionDetailService;
    private final FirefightingInspectionAttrService inspectionAttrService;
    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final FirefightingSmallPlacesInspectionConvertor inspectionConvertor;
    private final FirefightingInspectionAttrConvertor inspectionAttrConvertor;

    @ApiOperation("添加三小场所、出租屋消防巡检记录")
    @PostMapping("/add")
    public Long addSmallPlacesInspection(@Validated(Add.class) @RequestBody FirefightingSmallPlacesInspectionVo inspection) {
        FirefightingSmallPlacesInspection entity = inspectionConvertor.toInspectionDo(inspection);
        FirefightingSmallPlacesInspectionDetail entityDetail = inspectionConvertor.toInspectionDetailDo(inspection);
        List<FirefightingInspectionAttr> attrs = inspectionAttrConvertor.toAttrDo(inspection.getAttrs());

        initEntityCreator(entity);
        initEntityModifier(entity);
        return inspectionService.addInspection(inspection.getTaskId(), entity, entityDetail, attrs,
                inspection.getRealtySerials(), inspection.getMedias());
    }

    @ApiOperation("更新三小场所、出租屋消防巡检记录")
    @PostMapping("/update")
    public void updateSmallPlacesInspection(@Validated(Update.class) @RequestBody FirefightingSmallPlacesInspectionVo inspection) {
        FirefightingSmallPlacesInspection entity = inspectionConvertor.toInspectionDo(inspection);
        FirefightingSmallPlacesInspectionDetail entityDetail = inspectionConvertor.toInspectionDetailDo(inspection);
        List<FirefightingInspectionAttr> attrs = inspectionAttrConvertor.toAttrDo(inspection.getAttrs());

        initEntityModifier(entity);
        inspectionService.updateInspection(entity, entityDetail, attrs, inspection.getRealtySerials(), inspection.getMedias());
    }

    @ApiOperation("删除三小场所、出租屋消防巡检记录")
    @PostMapping("/delete/{id}")
    public void deleteSmallPlacesInspection(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        inspectionService.deleteInspection(id);
    }

    @ApiOperation("获取三小场所、出租屋消防巡检记录")
    @GetMapping("/get/{id}")
    public FirefightingSmallPlacesInspectionVo findSmallPlacesInspectionById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingSmallPlacesInspectionVo result = null;
        FirefightingSmallPlacesInspection inspection = inspectionService.findById(id);
        if (inspection != null) {
            result = inspectionConvertor.toInspectionVo(inspection);

            // 明细
            FirefightingSmallPlacesInspectionDetail detail = inspectionDetailService.findDetailByInspectionId(id);
            inspectionDetailVo2Inspection(result, detail);

            // 物业
            List<FirefightingInspectRealty> realtyList = inspectRealtyService.findByInspectId(id, InspectionType.SMALL_PLACES);
            result.setRealtySerials(realtyList.stream().map(FirefightingInspectRealty::getRealtySerial).collect(Collectors.toList()));

            // 参数
            List<FirefightingInspectionAttr> attrs = inspectionAttrService.listInspectionAttrs(InspectionType.SMALL_PLACES, id);
            if (!CollectionUtils.isEmpty(attrs)) {
                result.setAttrs(inspectionAttrConvertor.toAttrVo(attrs));
            }

            // 多媒体资料
            List<FirefightingInspectMedia> medias = inspectMediaService.findByInspectId(id, InspectionType.SMALL_PLACES);
            if (!CollectionUtils.isEmpty(medias)) {
                result.setMedias(medias.stream().map(FirefightingInspectMedia::getMediaUrl).collect(Collectors.toList()));
            }
        }
        return result;
    }

    @ApiOperation("三小场所、出租屋消防巡检记录数统计")
    @PostMapping("/count")
    public int countSmallPlacesInspection(@RequestBody FirefightingSmallPlacesInspectionSearchVo search) {
        return inspectionService.countInspection(search);
    }

    @ApiOperation("三小场所、出租屋消防巡检记录列表页")
    @PostMapping("/page")
    public PageResult<FirefightingSmallPlacesInspectionBriefVo> listSmallPlacesInspectionPage(@RequestBody FirefightingSmallPlacesInspectionSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search,
                () -> inspectionService.countInspection(search),
                () -> inspectionService.listInspection(search));
    }

    /**
     * 三小场所、出租屋消防巡检明细视图合并
     * @param inspection
     * @param detail
     */
    private void inspectionDetailVo2Inspection(FirefightingSmallPlacesInspectionVo inspection,
                                               FirefightingSmallPlacesInspectionDetail detail) {
        if (detail == null) {
            return;
        }

        inspection.setFloor1Usage(detail.getFloor1Usage());
        inspection.setFloor2Usage(detail.getFloor2Usage());
        inspection.setFloor3Usage(detail.getFloor3Usage());
        inspection.setFloor4Usage(detail.getFloor4Usage());
        inspection.setFloor5Usage(detail.getFloor5Usage());
        inspection.setFloor6Usage(detail.getFloor6Usage());
        inspection.setFloorOthersUsage(detail.getFloorOthersUsage());
        inspection.setFloor1RunnerUsage(detail.getFloor1RunnerUsage());
        inspection.setFloor2RunnerUsage(detail.getFloor2RunnerUsage());
        inspection.setFloor3RunnerUsage(detail.getFloor3RunnerUsage());
        inspection.setFloor4RunnerUsage(detail.getFloor4RunnerUsage());
        inspection.setFloor5RunnerUsage(detail.getFloor5RunnerUsage());
        inspection.setFloor6RunnerUsage(detail.getFloor6RunnerUsage());
        inspection.setFloorOthersRunnerUsage(detail.getFloorOthersRunnerUsage());
        inspection.setRemark(detail.getRemark());
        inspection.setInspectOpinion(detail.getInspectOpinion());
        inspection.setInspectOpinionCode(detail.getInspectOpinionCode());
        inspection.setInspectOpinionVersion(detail.getInspectOpinionVersion());
        inspection.setReinspectOpinion(detail.getReinspectOpinion());
        inspection.setReinspectOpinionCode(detail.getReinspectOpinionCode());
        inspection.setReinspectOpinionVersion(detail.getReinspectOpinionVersion());
        inspection.setStoreRectification(detail.getStoreRectification());
        inspection.setStoreRectificationCode(detail.getStoreRectificationCode());
        inspection.setStoreRectificationVersion(detail.getStoreRectificationVersion());
        inspection.setWorkshopRectification(detail.getWorkshopRectification());
        inspection.setWorkshopRectificationCode(detail.getWorkshopRectificationCode());
        inspection.setWorkshopRectificationVersion(detail.getWorkshopRectificationVersion());
        inspection.setRentalRectification(detail.getRentalRectification());
        inspection.setRentalRectificationCode(detail.getRentalRectificationCode());
        inspection.setRentalRectificationVersion(detail.getRentalRectificationVersion());
    }


}
