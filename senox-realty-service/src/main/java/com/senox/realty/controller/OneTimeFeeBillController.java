package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.domain.OneTimeFeeBill;
import com.senox.realty.service.OneTimeFeeBillService;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/18 14:56
 */
@Api(tags = "一次性收费账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/oneTimeFeeBill")
public class OneTimeFeeBillController extends BaseController {

    private final OneTimeFeeBillService billService;

    @ApiOperation("添加一次性收费账单")
    @PostMapping("/add")
    public Long addOneTimeFeeBill(@Validated(Add.class) @RequestBody OneTimeFeeBillVo billVo) {
        OneTimeFeeBill bill = billVo2Entity(billVo);
        if (bill.getOperateBy() == null) {
            bill.setOperateBy(getUserInContext().getUserId());
        }
        initEntityCreator(bill);
        initEntityModifier(bill);
        return billService.addBill(bill);
    }

    @ApiOperation("更新一次性收费账单")
    @PostMapping("/update")
    public void updateOneTimeFeeBill(@Validated(Update.class) @RequestBody OneTimeFeeBillVo billVo) {
        if (!WrapperClassUtils.biggerThanLong(billVo.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        OneTimeFeeBill bill = billVo2Entity(billVo);
        initEntityModifier(bill);
        billService.updateBill(bill);
    }

    @ApiOperation("更新账单票据号")
    @PostMapping("/serial/update")
    public void updateOneTimeFeeBillSerial(@Validated @RequestBody TollSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        serial.setOperatorId(getUserInContext().getUserId());
        serial.setOperatorName(getUserInContext().getUsername());
        billService.updateBillSerial(serial);
    }

    @ApiOperation("支付一次性账单")
    @PostMapping("/pay/{id}")
    public void payOneTimeFeeBill(@PathVariable Long id, @RequestBody BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        if (!WrapperClassUtils.biggerThanLong(toll.getOperator(), 0L)) {
            toll.setOperator(getUserInContext().getUserId());
        }
        billService.payOneTimeFeeBill(id, toll);
    }

    @ApiOperation("撤销支付一次性账单")
    @PostMapping("/payRevoke/{id}")
    public void revokeOneTimeFeeBillPayment(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        BillTollVo toll = new BillTollVo();
        toll.setId(id);
        toll.setOperator(getUserInContext().getUserId());
        toll.setOperatorName(getUserInContext().getUsername());
        billService.revokeOneTimeFeeBillPayment(toll);
    }

    @ApiOperation("一次性账单退费")
    @PostMapping("/refund/{id}")
    public void refundOneTimeFeeBill(@PathVariable Long id, @RequestBody BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        if (toll.getAmount() == null) {
            throw new InvalidParameterException("无效的退费金额");
        }
        if (!WrapperClassUtils.biggerThanLong(toll.getOperator(), 0L)) {
            toll.setOperator(getUserInContext().getUserId());
        }
        billService.refundOneTimeFeeBill(id, toll);
    }

    @ApiOperation("撤销一次性账单退费")
    @PostMapping("/refundRevoke/{id}")
    public void revokeOneTimeFeeBillRefund(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        BillTollVo toll = new BillTollVo();
        toll.setId(id);
        toll.setOperator(getUserInContext().getUserId());
        toll.setOperatorName(getUserInContext().getUsername());
        billService.revokeOneTimeFeeBillRefund(toll);
    }

    @ApiOperation("获取一次性账单信息")
    @GetMapping("/get/{id}")
    public OneTimeFeeBillVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return billService.findById(id);
    }

    @ApiOperation("获取一次性账单信息列表")
    @PostMapping("/listByIds")
    public List<OneTimeFeeBillVo> listByIds(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        return billService.listByIds(ids);
    }

    @ApiOperation("获取一次性账单信息及缴费退费信息")
    @GetMapping("/getDetail/{id}")
    public OneTimeFeeBillTradeVo findDetailById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return billService.findDetailById(id);
    }

    @ApiOperation("一次性账单列表")
    @PostMapping("/list")
    public RefundBillPageResult<OneTimeFeeBillTradeVo> listOneTimeFeeBill(@RequestBody OneTimeFeeBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefundBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<OneTimeFeeBillTradeVo> page = billService.listOneTimeFeeBillPage(search);

        // result
        RefundBillPageResult<OneTimeFeeBillTradeVo> resultPage = new RefundBillPageResult<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());
        resultPage.initTotalPage();

        // 总计
        OneTimeFeeBillSummaryVo summary = billService.sumOneTimeFeeBill(search);
        if (summary != null) {
            resultPage.setAmount(summary.getAmount() == null ? BigDecimal.ZERO : summary.getAmount());
            resultPage.setRefundAmount(summary.getRefundAmount() == null ? BigDecimal.ZERO : summary.getRefundAmount());
            resultPage.setTotalAmount(summary.getTotalAmount() == null ? BigDecimal.ZERO : summary.getTotalAmount());
        }
        return resultPage;
    }

    @ApiOperation("一次性账单交易明细列表")
    @PostMapping("/tradeList")
    public RefundBillPageResult<OneTimeFeeBillTradeVo> listOneTimeFeeBillTrade(@RequestBody OneTimeFeeBillTradeSearchVo search) {
        if (search.getPageSize() < 1) {
            return RefundBillPageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        // page
        PageResult<OneTimeFeeBillTradeVo> page = billService.listOneTimeFeeBillTrade(search);

        // result
        RefundBillPageResult<OneTimeFeeBillTradeVo> resultPage = new RefundBillPageResult<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());
        resultPage.initTotalPage();

        // 总计
        OneTimeFeeBillSummaryVo summary = billService.sumOneTimeFeeBillTrade(search);
        if (summary != null) {
            resultPage.setAmount(summary.getAmount() == null ? BigDecimal.ZERO : summary.getAmount());
            resultPage.setRefundAmount(summary.getRefundAmount() == null ? BigDecimal.ZERO : summary.getRefundAmount());
            resultPage.setTotalAmount(summary.getTotalAmount() == null ? BigDecimal.ZERO : summary.getTotalAmount());
        }
        return resultPage;
    }

    @ApiOperation("更新一次性费用账单支付结果")
    @PostMapping("/paid/update")
    public void updateOneTimeFeeBillStatus(@Validated @RequestBody BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            throw new InvalidParameterException("无效的订单id");
        }
        if (!WrapperClassUtils.biggerThanLong(billPaid.getTollMan(), 0L)) {
            throw new InvalidParameterException("无效的收费员");
        }

        billService.updateOneTimeFeeBillStatus(billPaid);
    }

    @ApiOperation("费用列表")
    @PostMapping("/deposit/list")
    public List<OneTimeFeeDepositVo> listOneTimeFeeDeposit(@RequestBody OneTimeFeeDepositSearchVo search) {
        return billService.listOneTimeFeeDeposit(search);
    }

    /**
     * 账单信息转实体
     * @param billVo
     * @return
     */
    private OneTimeFeeBill billVo2Entity(OneTimeFeeBillVo billVo) {
        OneTimeFeeBill result = new OneTimeFeeBill();
        BeanUtils.copyProperties(billVo, result);
        return result;
    }

}
