package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.convert.FirefightingUtilityConvertor;
import com.senox.realty.domain.FirefightingUtility;
import com.senox.realty.service.FirefightingUtilityService;
import com.senox.realty.vo.FirefightingUtilitySearchVo;
import com.senox.realty.vo.FirefightingUtilityVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13 10:26
 */
@Api(tags = "公共消防设施")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/utility")
public class FirefightingUtilityController extends BaseController {

    private final FirefightingUtilityService utilityService;
    private final FirefightingUtilityConvertor utilityConvertor;

    @ApiOperation("添加公共消防设施")
    @PostMapping("/add")
    public Long addUtility(@Validated @RequestBody FirefightingUtilityVo utility) {
        FirefightingUtility entity = utilityConvertor.toDo(utility);
        initEntityCreator(entity);
        initEntityModifier(entity);

        return utilityService.addUtility(entity);
    }

    @ApiOperation("更新公共消防设施")
    @PostMapping("/update")
    public void updateUtility(@Validated @RequestBody FirefightingUtilityVo utility) {
        if (!WrapperClassUtils.biggerThanLong(utility.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingUtility entity = utilityConvertor.toDo(utility);
        initEntityModifier(entity);
        utilityService.updateUtility(entity);
    }

    @ApiOperation("删除公共消防设施")
    @PostMapping("/delete/{id}")
    public void deleteUtility(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        utilityService.deleteUtility(id, getUserInContext());
    }

    @ApiOperation("获取公共消防设施")
    @GetMapping("/get/{id}")
    public FirefightingUtilityVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingUtility utility = utilityService.findById(id);
        return utility == null ? null : utilityConvertor.toVo(utility);
    }

    @ApiOperation("获取公共消防设施列表")
    @PostMapping("/list")
    public List<FirefightingUtilityVo> listUtility(@RequestBody FirefightingUtilitySearchVo search) {
        search.setPage(false);
        List<FirefightingUtility> list = utilityService.listUtility(search);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : utilityConvertor.toVo(list);
    }

    @ApiOperation("公共消防设施页")
    @PostMapping("/page")
    public PageResult<FirefightingUtilityVo> listUtilityPage(@RequestBody FirefightingUtilitySearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search,
                () -> utilityService.countUtility(search),
                () -> {
                    List<FirefightingUtility> list = utilityService.listUtility(search);
                    return utilityConvertor.toVo(list);
                }
                );
    }
}
