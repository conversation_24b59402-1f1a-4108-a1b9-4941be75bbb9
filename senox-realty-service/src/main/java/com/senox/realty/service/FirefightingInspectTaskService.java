package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.domain.FirefightingInspectTask;
import com.senox.realty.domain.FirefightingInspectTaskItem;
import com.senox.realty.mapper.FirefightingInspectTaskMapper;
import com.senox.realty.vo.FirefightingInspectTaskSearchVo;
import com.senox.realty.vo.FirefightingInspectTaskVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/14 11:42
 */
@Service
@RequiredArgsConstructor
public class FirefightingInspectTaskService extends ServiceImpl<FirefightingInspectTaskMapper, FirefightingInspectTask> {

    private final FirefightingInspectTaskItemService taskItemService;

    /**
     * 保存巡检任务
     * @param task
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveInspectTask(FirefightingInspectTask task, List<FirefightingInspectTaskItem> list) {
        Long taskId = task.getId();
        if (WrapperClassUtils.biggerThanLong(task.getId(), 0L)) {
            updateInspectTask(task);
        } else {
            taskId = addInspectTask(task);
        }

        taskItemService.batchSaveTaskItem(taskId, list);
        return taskId;
    }

    /**
     * 获取消防巡检任务
     * @param id
     * @return
     */
    public FirefightingInspectTaskVo findVoById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getBaseMapper().findVoById(id) : null;
    }

    /**
     * 消防巡检任务统计
     * @param search
     * @return
     */
    public int countTask(FirefightingInspectTaskSearchVo search) {
        return getBaseMapper().countTask(search);
    }

    /**
     * 消防巡检任务列表
     * @param search
     * @return
     */
    public List<FirefightingInspectTaskVo> listTask(FirefightingInspectTaskSearchVo search) {
        return getBaseMapper().listTask(search);
    }

    /**
     * 添加巡检任务
     * @param task
     * @return
     */
    private Long addInspectTask(FirefightingInspectTask task) {
        task.setCreateTime(LocalDateTime.now());
        task.setModifiedTime(LocalDateTime.now());

        save(task);
        return task.getId();
    }

    /**
     * 更新巡检任务
     * @param task
     */
    private void updateInspectTask(FirefightingInspectTask task) {
        if (!WrapperClassUtils.biggerThanLong(task.getId(), 0L)) {
            return;
        }

        task.setCreatorId(null);
        task.setCreatorName(null);
        task.setCreateTime(LocalDateTime.now());
        task.setModifiedTime(LocalDateTime.now());
        updateById(task);
    }
}
