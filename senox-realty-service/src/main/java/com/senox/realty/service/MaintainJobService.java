package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.realty.component.AdminComponent;
import com.senox.realty.constant.*;
import com.senox.realty.convert.*;
import com.senox.realty.domain.*;
import com.senox.realty.mapper.MaintainJobMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.*;
import com.senox.user.constant.MaintainManType;
import com.senox.user.vo.AdminUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.senox.realty.constant.RealtyConst.MQ.MQ_REALTY_AC_MAINTAIN;

/**
 * <AUTHOR>
 * @date 2023/4/3 9:36
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MaintainJobService extends ServiceImpl<MaintainJobMapper, MaintainJob> {
    @Value("${senox.maintainJob.serial.prefix}")
    private String jobNoPrefix;
    @Value("${senox.maintainJob.serial.length}")
    private Integer jobNoPostfixLength;
    @Value("${senox.maintainJob.serial.fill-char}")
    private Character fillChar;

    @Value("${senox.maintainJob.default.handlerId}")
    private Long defaultHandlerId;
    @Value("${senox.maintainJob.default.handlerName}")
    private String defaultHandlerName;

    private final MaintainOrderService maintainOrderService;
    private final RabbitTemplate rabbitTemplate;
    private final MaintainChargeService maintainChargeService;
    private final MaintainMaterialService maintainMaterialService;
    private final AdminComponent adminComponent;
    private final MaintainJobItemService maintainJobItemService;
    private final MaintainJobItemConvertor jobItemConvertor;
    private final MaintainMaterialItemConvertor materialItemConvertor;
    private final MaintainChargeConvertor chargeConvertor;
    private final MaintainChargeItemConvertor chargeItemConvertor;
    private final MaintainMediaService maintainMediaService;


    /**
     * 新增派工单
     *
     * @param job
     * @param maintainJobVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addMaintainJob(MaintainJob job, MaintainJobVo maintainJobVo) {
        if (!WrapperClassUtils.biggerThanLong(job.getOrderId(), 0L)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        MaintainOrder dbItem = maintainOrderService.findById(job.getOrderId());
        if (dbItem == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        List<MaintainJobItemVo> jobItemVos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(maintainJobVo.getMaintainJobItemVos())) {
            jobItemVos =  maintainJobVo.getMaintainJobItemVos();
        }

        //第一次开始派单 多新增一个派单信息
        if (dbItem.getStatus() != null && dbItem.getStatus() == MaintainOrderStatus.INIT.getValue()) {
            log.info("第一次开始派单---- :{}", JsonUtils.object2Json(job));
            // 信息初始化
            MaintainJob maintainJob = initJob(job);
            MaintainJobItemVo itemVo = initJobItemVo(job);
            addMaintainJob(maintainJob, Collections.singletonList(itemVo));

            updateOrder(maintainJob, MaintainOrderStatus.DOING, Collections.emptyList());
        }

        //如果订单无法处理则不需要添加派工单
        if (job.getStatus() != null && job.getStatus() == MaintainJobStatus.INCAPABLE.getValue()) {
            log.info("订单无法处理---- :{}", JsonUtils.object2Json(job));
            //校验账单支付
            checkChargePaid(job.getOrderId());
            //无法处理
            updateMaintainJob(dbItem, MaintainJobStatus.INCAPABLE, maintainJobVo);
            updateOrder(job, MaintainOrderStatus.INCAPABLE, Collections.emptyList());
            return 0L;
        }

        if (job.getStatus() != null && job.getStatus() == MaintainJobStatus.INIT.getValue()) {

            List<AdminUserVo> adminUserVoList = new ArrayList<>(jobItemVos.size());
            for (MaintainJobItemVo itemVo : jobItemVos) {
                adminUserVoList.add(adminComponent.getAdminUser(itemVo.getHandlerId()));
            }
            if (adminUserVoList.stream().filter(adminUserVo -> adminUserVo.getMaintainManType()
                    ==  MaintainManType.MAINTAIN_MANAGER.getValue()).count() > 1 && adminUserVoList.size() > 1) {
                throw new BusinessException("审核节点派单只能分给一个主管！");
            }
            MaintainJob maintainJob = findByJobNo(dbItem.getJobNo());
            if (maintainJob != null && maintainJob.getDispatchType() != MaintainDispatchType.EXAMINE.getValue()) {
                throw new BusinessException("当前订单不处于审核节点，请刷新！");
            }

            updateMaintainJob(dbItem, MaintainJobStatus.DONE, maintainJobVo);
            //新增派发到下一单
            job.setJobNo(StringUtils.EMPTY);
            addMaintainJob(job, jobItemVos);

            //发送消息
            sendMessage(job.getId());

            //修改维修单
            updateOrder(job, MaintainOrderStatus.DOING, jobItemVos);
        } else if (job.getStatus() != null && job.getStatus() == MaintainJobStatus.DONE.getValue()) {
            log.info("订单完成---- :{}", JsonUtils.object2Json(job));
            //校验账单支付
            checkChargePaid(job.getOrderId());
            //已经完成
            MaintainJob lastJob = getBaseMapper().findLastJobByOrderId(job.getOrderId());
            log.info("最后一单lastJob:{}", JsonUtils.object2Json(lastJob));
            if (lastJob == null || lastJob.getStatus() != MaintainJobStatus.INCAPABLE.getValue()) {
                updateMaintainJob(dbItem, MaintainJobStatus.DONE, maintainJobVo);
                updateOrder(job, MaintainOrderStatus.DONE, jobItemVos);
            } else {
                updateMaintainJob(dbItem, MaintainJobStatus.INCAPABLE, maintainJobVo);
                updateOrder(job, MaintainOrderStatus.INCAPABLE, jobItemVos);
            }
            return 0L;
        }

        if (!CollectionUtils.isEmpty(maintainJobVo.getMaterialItemVos())) {
            //添加物料
            saveMaterial(job, maintainJobVo.getMaterialItemVos());
        }
        if (!CollectionUtils.isEmpty(maintainJobVo.getChargeItemVos())) {
            //添加账单
            saveCharge(job, maintainJobVo.getChargeItemVos());
        }

        return job.getId();
    }

    /**
     * 查询账单是否支付
     * @param orderId
     */
    public void checkChargePaid(Long orderId) {
        List<MaintainCharge> charges = maintainChargeService.listChargeByOrderId(orderId);
        if (CollectionUtils.isEmpty(charges)) {
            return;
        }
        if (charges.stream().anyMatch(charge -> Objects.equals(MaintainChargeStatus.INIT.getStatus(), charge.getStatus()))) {
            throw new BusinessException("维修单尚有未支付的账单，请完成支付！");
        }
    }

    /**
     * 根据派工单号查询派工单
     * @param jobNo
     * @return
     */
    public MaintainJob findByJobNo(String jobNo) {
        if (StringUtils.isBlank(jobNo)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<MaintainJob>().eq(MaintainJob::getJobNo, jobNo));
    }

    /**
     * 第一次派工维修工初始化
     * @return
     */
    private MaintainJobItemVo initJobItemVo(MaintainJob job) {
        AdminUserDto adminUserDto = ContextUtils.getUserInContext();
        MaintainJobItemVo jobItem = new MaintainJobItemVo();
        jobItem.setHandlerId(adminUserDto.getUserId());
        jobItem.setHandlerName(adminUserDto.getRealName());
        if (job.getStatus() != MaintainJobStatus.INIT.getValue()) {
            jobItem.setHandlerStatus(job.getStatus());
        } else {
            jobItem.setHandlerStatus(MaintainJobStatus.DONE.getValue());
        }
        return jobItem;
    }

    /**
     * 根据订单id查询审核节点的派工单
     * @param orderId
     * @return
     */
    public MaintainJob findExamineByOrderId(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return getOne(new QueryWrapper<MaintainJob>().lambda()
                .eq(MaintainJob::getOrderId, orderId)
                .eq(MaintainJob::getDispatchType, MaintainDispatchType.EXAMINE.getValue())
                .eq(MaintainJob::getStatus, MaintainJobStatus.INIT.getValue()));
    }

    /**
     * 更新派工信息
     * @param job
     * @param maintainJobVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMaintainJob(MaintainJob job, MaintainJobVo maintainJobVo) {
        boolean result = updateById(job);
        //如果修改成功，并且维修员工不为空
        if (result && !CollectionUtils.isEmpty(maintainJobVo.getMaintainJobItemVos())) {
            List<MaintainJobItem> jobItems = jobItemConvertor.toDo(maintainJobVo.getMaintainJobItemVos());
            maintainJobItemService.saveMaintainJobItem(job.getId(), jobItems);
        }
    }

    /**
     * 添加账单
     * @param job
     * @param chargeItemVos
     */
    private void saveCharge(MaintainJob job, List<MaintainChargeItemVo> chargeItemVos) {
        log.info("构建物维账单, 派工单为：{}， 账单为：{}", JsonUtils.object2Json(job), JsonUtils.object2Json(chargeItemVos));
        Map<Long, List<MaintainChargeItemVo>> jobMap = chargeItemVos.stream().collect(Collectors.groupingBy(MaintainChargeItemVo::getJobId));
        Map<MaintainCharge, List<MaintainChargeItem>> chargeMap = new HashMap<>();
        jobMap.forEach((jobId, item) -> {
            MaintainCharge charge = maintainChargeService.buildCharge(job.getId(), job.getOrderId());
            List<MaintainChargeItem> chargeItems = chargeItemConvertor.toDo(item);
            maintainChargeService.initChargeItem(chargeItems);
            chargeMap.put(charge, chargeItems);
        });
        maintainChargeService.batchSaveCharge(chargeMap);
    }

    /**
     * 添加物料
     * @param job
     * @param materialItemVos
     */
    private void saveMaterial(MaintainJob job, List<MaintainMaterialItemVo> materialItemVos) {
        log.info("构建物维物料，派工单为{}， 物料为{}", JsonUtils.object2Json(job), JsonUtils.object2Json(materialItemVos));
        Map<Long, List<MaintainMaterialItemVo>> jobMap = materialItemVos.stream().collect(Collectors.groupingBy(MaintainMaterialItemVo::getJobId));
        Map<MaintainMaterial, List<MaintainMaterialItem>> materialMap = new HashMap<>();
        jobMap.forEach((jobId, item) -> {
            MaintainMaterial material = maintainMaterialService.buildMaterial(0L, job.getId(), job.getOrderId());
            List<MaintainMaterialItem> materialItems = materialItemConvertor.toDo(item);
            maintainMaterialService.initMaterialItem(materialItems);
            materialMap.put(material, materialItems);
        });
        maintainMaterialService.batchSaveMaterial(materialMap);
    }


    /**
     * 新增派工单
     * @param job
     * @param jobItemVos
     */
    public void addMaintainJob(MaintainJob job, List<MaintainJobItemVo> jobItemVos) {
        log.info("新增派工单参数为：{}, {}", JsonUtils.object2Json(job), JsonUtils.object2Json(jobItemVos));
        prepareJob(job, jobItemVos);
        getBaseMapper().addMaintainJob(job);
        if (!CollectionUtils.isEmpty(jobItemVos)) {
            List<MaintainJobItem> jobItems = jobItemConvertor.toDo(jobItemVos);
            maintainJobItemService.saveMaintainJobItem(job.getId(), jobItems);
        }
    }

    /**
     * 修改派工单的信息
     *
     * @param order 维修单
     */
    public void updateMaintainJob(MaintainOrder order, MaintainJobStatus status, MaintainJobVo jobVo) {
        log.info("修改派工单信息-----参数维修单为：{}, 状态为：{}, 派工单为：{}", JsonUtils.object2Json(order), status, JsonUtils.object2Json(jobVo));
        MaintainOrder maintainOrder = maintainOrderService.findById(order.getId());
        if (maintainOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        MaintainJob maintainJob = getOne(new QueryWrapper<MaintainJob>().lambda()
                .eq(MaintainJob::getJobNo, maintainOrder.getJobNo()));
        if (maintainJob != null) {
            maintainJob.setStatus(status.getValue());
            ContextUtils.initEntityModifier(maintainJob);
            //修改该单的信息
            updateById(maintainJob);
            //修改该派工子单的信息
            MaintainJobItem lastExamine = maintainJobItemService.findLastExamineByOrderId(order.getId());
            if (lastExamine == null) {
                throw new BusinessException("物维主管才有权限派单！");
            }
            lastExamine.setRemark(jobVo.getRemark());
            lastExamine.setHandlerStatus(status.getValue());
            maintainJobItemService.updateById(lastExamine);
        }
    }


    /**
     * 根据id获取派工单
     *
     * @param id
     * @return
     */
    public MaintainJob findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 根据id查询派工单及派发流转
     * @param id
     * @return
     */
    public MaintainJobVo findDispatchById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        MaintainJobVo maintainJobVo = getBaseMapper().findDispatchById(id);
        return prepareMaintainJobVoMedia(maintainJobVo);
    }


    /**
     * 构造媒体资源
     * @param maintainJobVo
     * @return
     */
    public MaintainJobVo prepareMaintainJobVoMedia(MaintainJobVo maintainJobVo) {
        if (maintainJobVo != null) {
            maintainJobVo.getMaintainJobItemVos().forEach(x->{
                List<MaintainMedia> mediaList = maintainMediaService.listByOrderIdAndJobId(maintainJobVo.getOrderId(), x.getJobId(), x.getId());
                x.setMediaUrls(CollectionUtils.isEmpty(mediaList) ? null : mediaList.stream().map(MaintainMedia::getMediaUrl).collect(Collectors.toList()));
            });
            return maintainJobVo;
        }
        return null;
    }

    /**
     * 查询派工单的派发流转
     *
     * @param orderId
     * @return
     */
    public List<MaintainJobVo> listDispatchByOrderId(Long orderId) {
        List<MaintainJobVo> maintainJobVos = getBaseMapper().listDispatchByOrderId(orderId);
        maintainJobVos.forEach(this::prepareMaintainJobVoMedia);
        return maintainJobVos;
    }

    /**
     * 查询派单列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<MaintainDispatchJobVo> listDispatchJob(MaintainJobSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        // total
        int total = this.getBaseMapper().count(searchVo);
        //开始分页查询
        searchVo.prepare();
        if (total <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }
        List<MaintainDispatchJobVo> maintainDispatchJobVoList = this.getBaseMapper().listDispatchJob(searchVo);
        return PageUtils.resultPage(searchVo, total, maintainDispatchJobVoList);
    }

    /**
     * 查询所有未完成的审核节点派工单
     * @return
     */
    public List<MaintainJob> findExamineList() {
        return getBaseMapper().findExamineList();
    }

    /**
     * 更新维修单
     * @param job
     * @param status
     * @param jobItemVos
     */
    public void updateOrder(MaintainJob job, MaintainOrderStatus status, List<MaintainJobItemVo> jobItemVos) {
        log.info("更新维修单，派工单信息为：{}, 状态为：{} , 派工子单信息为：{}", JsonUtils.object2Json(job), status, JsonUtils.object2Json(jobItemVos));
        MaintainOrder order = new MaintainOrder();
        order.setId(job.getOrderId());
        order.setJobNo(job.getJobNo());
        order.setStatus(status.getValue());
        if (!CollectionUtils.isEmpty(jobItemVos)) {
            order.setHandlerName(jobItemVos.stream().map(MaintainJobItemVo::getHandlerName).collect(Collectors.joining(",")));
        }
        ContextUtils.initEntityModifier(order);
        if (MaintainOrderStatus.DONE == status) {
            //完成维修单
            order.setFinishTime(LocalDateTime.now());
        }
        MaintainJobItem lastExamine = maintainJobItemService.findLastExamineByOrderId(job.getOrderId());
        log.info("物维主管相互转换派工单。。。转派给:{}", JsonUtils.object2Json(lastExamine));
        if (lastExamine != null) {
            AdminUserVo adminUser = adminComponent.getAdminUser(lastExamine.getHandlerId());
            if (adminUser != null && adminUser.getMaintainManType() == MaintainManType.MAINTAIN_MANAGER.getValue()) {
                order.setManagementDeptId(adminUser.getDepartmentId());
                order.setManagementDeptName(adminUser.getDepartmentName());
            }
        }
        maintainOrderService.updateMaintainOrder(order);
    }

    /**
     * 更新派工人员信息
     * @param jobItem
     * @param urls
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMaintainJobItem(MaintainJobItem jobItem, List<String> urls) {
        if (!WrapperClassUtils.biggerThanLong(jobItem.getId(), 0L) || !WrapperClassUtils.biggerThanLong(jobItem.getJobId(), 0L)) {
            return;
        }
        MaintainJob maintainJob = findById(jobItem.getJobId());
        if (maintainJob == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        MaintainJobItem dbItem = maintainJobItemService.findById(jobItem.getId());
        if (dbItem == null || dbItem.getHandlerStatus() == MaintainJobStatus.DONE.getValue()) {
            throw new BusinessException("记录不存在或已完成！");
        }
        log.info("派工单信息为：{}， 派工子单信息为：{}", JsonUtils.object2Json(maintainJob), JsonUtils.object2Json(dbItem));
        jobItem.setModifiedTime(LocalDateTime.now());
        maintainJobItemService.updateById(jobItem);
        if (!CollectionUtils.isEmpty(urls)) {
            maintainMediaService.saveMaintainMedia(maintainJob.getOrderId(), dbItem.getJobId(), dbItem.getId(), urls);
        }

        //如果是处理完成,或者是无法处理
        if (jobItem.getHandlerStatus() == MaintainJobStatus.DONE.getValue()
                || jobItem.getHandlerStatus() == MaintainJobStatus.INCAPABLE.getValue()) {
            //判断是否需要多人完成
            if (BooleanUtils.isTrue(maintainJob.getMultipleComplete())) {
                //是需要多人完成
                List<MaintainJobItem> jobItems = maintainJobItemService.maintainJobItemListByJobId(maintainJob.getId(), null);
                boolean allMatch = jobItems.stream().allMatch(item -> item.getHandlerStatus() == MaintainJobStatus.DONE.getValue()
                        || item.getHandlerStatus() == MaintainJobStatus.INCAPABLE.getValue());
                if (allMatch) {
                    log.info("新增审核节点----{}", JsonUtils.object2Json(maintainJob));
                    //如果都完成了则新增审核节点派工单
                    saveMaintainJob(maintainJob, jobItem.getHandlerStatus());
                }
            } else {
                log.info("新增审核节点----{}", JsonUtils.object2Json(maintainJob));
                //删除其他维修员未完成的派工单
                maintainJobItemService.deleteMaintainJobItemByJobId(maintainJob.getId(), MaintainJobStatus.INIT);
                //不需要多人完成则直接新增审核节点派工单
                saveMaintainJob(maintainJob, jobItem.getHandlerStatus());
            }
        }

    }

    /**
     * 新增审核节点派工单
     * @param maintainJob
     * @param status
     */
    public void saveMaintainJob(MaintainJob maintainJob, Integer status) {
        //修改上一单的状态
        maintainJob.setStatus(status);
        updateById(maintainJob);
        log.info("处理节点完成，新增审核节点。。");
        //创建新的审核节点派工单
        MaintainJob job = new MaintainJob();
        job.setOrderId(maintainJob.getOrderId());
        job.setMaintainType(maintainJob.getMaintainType());
        job.setStatus(MaintainJobStatus.INIT.getValue());
        ContextUtils.initEntityCreator(job);
        //查询上一个审核节点
        MaintainJobItem lastExamine = maintainJobItemService.findLastExamineByOrderId(maintainJob.getOrderId());
        MaintainJobItemVo jobItemVo = jobItemConvertor.toVo(lastExamine);
        jobItemVo.setId(0L);
        jobItemVo.setHandlerStatus(MaintainJobStatus.INIT.getValue());
        //新增审核节点
        addMaintainJob(job, Collections.singletonList(jobItemVo));
        //修改维修单
        updateOrder(job, MaintainOrderStatus.DOING, Collections.singletonList(jobItemVo));
    }

    /**
     * 第一次派工单初始化
     * @return 派工单
     */
    public MaintainJob initJob(MaintainJob job){
        MaintainJob maintainJob = new MaintainJob();
        maintainJob.setMaintainType(job.getMaintainType());
        if (job.getStatus() != MaintainJobStatus.INIT.getValue()) {
            maintainJob.setStatus(job.getStatus());
        } else {
            maintainJob.setStatus(MaintainJobStatus.DONE.getValue());
        }
        maintainJob.setOrderId(job.getOrderId());
        ContextUtils.initEntityCreator(maintainJob);
        return maintainJob;
    }

    /**
     * 派工单信息初始化
     * @param job
     * @param jobItemVoList
     */
    private void prepareJob(MaintainJob job, List<MaintainJobItemVo> jobItemVoList) {
        if (job.getCreateTime() == null) {
            job.setCreateTime(LocalDateTime.now());
        }
        if (job.getModifiedTime() == null) {
            job.setModifiedTime(LocalDateTime.now());
        }
        if (job.getMultipleComplete() == null) {
            job.setMultipleComplete(false);
        }
        ContextUtils.initEntityModifier(job);
        job.setDispatchType(MaintainDispatchType.EXAMINE.getValue());
        jobItemVoList.forEach(x-> {
            AdminUserVo adminUser = adminComponent.getAdminUser(x.getHandlerId());
            if (adminUser != null && adminUser.getMaintainManType() != MaintainManType.MAINTAIN_MANAGER.getValue()) {
                log.info("【处理节点】分派的维修员");
                //只要有一个不是维修主管则为处理节点
                job.setDispatchType(MaintainDispatchType.HANDLE.getValue());
            }
        });

        // 订单号
        if (StringUtils.isBlank(job.getJobNo())) {
            String prefix = jobNoPrefix.concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
            job.setJobNo(prepareOrderNo(prefix, jobNoPostfixLength, fillChar));
        }

        // 维修类型
        if (MaintainType.fromValue(job.getMaintainType()) == null) {
            job.setMaintainType(MaintainType.CUSTOMER_OTHERS.getValue());
        }
        log.info("构建完成的派工单信息为: {}", JsonUtils.object2Json(job));
    }


    /**
     * 工单号初始化
     *
     * @param prefix
     * @param length
     * @param fillChar
     * @return
     */
    private String prepareOrderNo(String prefix, int length, char fillChar) {
        // get serial from cache
        String cacheKey = String.format(RealtyConst.Cache.KEY_MAINTAIN_JOB_NO, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, RealtyConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxOrderSerial(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, RealtyConst.Cache.TTL_2D);
        }

        return prefix.concat(StringUtils.fixLength(String.valueOf(result), length, fillChar));
    }

    /**
     * 获取最大工单序号
     *
     * @param prefix
     * @return
     */
    private Long findMaxOrderSerial(String prefix) {
        String maxJobNo = getBaseMapper().findMaxJobNo(prefix);
        return (StringUtils.isBlank(maxJobNo) ? 0L : NumberUtils.parseLong(maxJobNo.substring(prefix.length()), 0L));
    }

    /**
     * 根据派工子单查询
     * @param itemId
     * @return
     */
    public MaintainDispatchJobVo findDispatchByJobItemId(Long itemId) {
        if (!WrapperClassUtils.biggerThanLong(itemId, 0L)) {
            return null;
        }
        return getBaseMapper().findDispatchByJobItemId(itemId);
    }

    /**
     * 更新派工单
     * @param job
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaintainJob(MaintainJob job) {
        MaintainCharge maintainCharge = maintainChargeService.findChargeByJobId(job.getId());
        //判断是否已支付
        if (maintainCharge != null && MaintainChargeStatus.fromStatus(maintainCharge.getStatus()) == MaintainChargeStatus.PAID) {
            throw new BusinessException("账单已支付，不能删除");
        }
        //判断是否已领料
        List<MaintainMaterial> maintainMaterialList = maintainMaterialService.findMaterialByJobId(job.getId());
        if (!CollectionUtils.isEmpty(maintainMaterialList) && maintainMaterialList.stream().anyMatch(x -> x.getOutNo() != null)) {
            throw new BusinessException("物料已领料，不能删除");
        }
        //删除原有的派工人员
        maintainJobItemService.deleteMaintainJobItemByJobId(job.getId(), null);
        log.info("删除订单原有的维修员。。。");
        //使用配置的默认人员接单
        MaintainJobItem jobItem = new MaintainJobItem();
        jobItem.setHandlerId(defaultHandlerId);
        jobItem.setHandlerName(defaultHandlerName);
        jobItem.setHandlerStatus(MaintainJobStatus.INIT.getValue());
        maintainJobItemService.saveMaintainJobItem(job.getId(), Collections.singletonList(jobItem));
        log.info("使用默认配置的人员接单:{}", JsonUtils.object2Json(jobItem));
        job.setModifiedTime(LocalDateTime.now());
        //为审核节点
        job.setDispatchType(MaintainDispatchType.EXAMINE.getValue());
        boolean result = updateById(job);
        if (result) {
            sendMessage(job.getId());
        }
    }

    /**
     * 发送消息
     * @param jobId
     */
    private void sendMessage(Long jobId) {
        List<MaintainJobItem> maintainJobItems = maintainJobItemService.maintainJobItemListByJobId(jobId, null);
        MaintainJob maintainJob = findById(jobId);
        maintainJobItems.forEach(item -> {
            //发送消息队列
            MaintainJobMessageVo messageVo = new MaintainJobMessageVo();
            messageVo.setId(maintainJob.getId());
            messageVo.setJobItemId(item.getId());
            messageVo.setHandlerId(item.getHandlerId());
            messageVo.setHandlerName(item.getHandlerName());
            messageVo.setJobNo(maintainJob.getJobNo());
            messageVo.setCreateTime(item.getCreateTime());
            rabbitTemplate.convertAndSend(MQ_REALTY_AC_MAINTAIN, messageVo);
            log.info("发送派工信息消息队列 {}", JsonUtils.object2Json(messageVo));
        });
    }
}
