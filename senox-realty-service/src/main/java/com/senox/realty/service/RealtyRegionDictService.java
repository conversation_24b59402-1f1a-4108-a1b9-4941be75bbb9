package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.domain.RealtyRegionDict;
import com.senox.realty.mapper.RealtyRegionDictMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.RealtyRegionDictSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class RealtyRegionDictService extends ServiceImpl<RealtyRegionDictMapper, RealtyRegionDict> {

    /**
     * 批量添加
     * @param dictList 字典集
     */
    public void addBatch(List<RealtyRegionDict> dictList) {
        if (CollectionUtils.isEmpty(dictList)) {
            return;
        }
        dictList.forEach(x -> {
            ContextUtils.initEntityCreator(x);
            ContextUtils.initEntityModifier(x);
            x.setCreateTime(LocalDateTime.now());
            x.setModifiedTime(LocalDateTime.now());
        });
        super.saveBatch(dictList);
    }

    /**
     * 批量删除
     * @param dictIds 字典集
     */
    public void deleteBatch(List<Long> dictIds) {
        if (CollectionUtils.isEmpty(dictIds)) {
            return;
        }
        removeByIds(dictIds);
    }

    /**
     * 批量更新
     * @param dictList 字典集
     */
    public void updateBatch(List<RealtyRegionDict> dictList) {
        if (CollectionUtils.isEmpty(dictList)) {
            return;
        }
        dictList.forEach(x -> {
            ContextUtils.initEntityModifier(x);
            x.setModifiedTime(LocalDateTime.now());
        });
        super.updateBatchById(dictList);
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(RealtyRegionDictSearchVo search) {
        return count(getQueryWrapper(search));
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<RealtyRegionDict> list(RealtyRegionDictSearchVo search) {
        return list(getQueryWrapper(search));
    }


    /**
     * 分页列表
     *
     * @param search 查询参数
     * @return 返回查询到的分页数据
     */
    public PageResult<RealtyRegionDict> pageList(RealtyRegionDictSearchVo search) {
        return PageUtils.commonPageResult(search, () -> countList(search), () -> list(search));
    }

    /**
     * 获取查询wrapper
     * @param search 查询参数
     * @return 返回构建后的wrapper
     */
    private LambdaQueryWrapper<RealtyRegionDict> getQueryWrapper(RealtyRegionDictSearchVo search) {
        LambdaQueryWrapper<RealtyRegionDict> wrapper = new QueryWrapper<RealtyRegionDict>().lambda();
        if (!StringUtils.isBlank(search.getRegion())) {
            wrapper.eq(RealtyRegionDict::getRegion, search.getRegion());
        }
        if (BooleanUtils.isTrue(search.isPage())) {
            wrapper.last(String.format("limit %s, %s", search.getOffset(), search.getPageSize()));
        }
        return wrapper;
    }
}
