package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.domain.MaintainEvaluateMedia;
import com.senox.realty.mapper.MaintainEvaluateMediaMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/5 15:58
 */
@Service
public class MaintainEvaluateMediaService extends ServiceImpl<MaintainEvaluateMediaMapper, MaintainEvaluateMedia> {

    /**
     * 保存评价媒体信息
     * @param orderId
     * @param mediaUrl
     */
    public void saveMedias(Long orderId, List<String> mediaUrl) {
        List<MaintainEvaluateMedia> mediaList = mediaUrl.stream().map(x -> newMaintainEvaluateMedia(orderId, x)).collect(Collectors.toList());
        saveBatch(mediaList);
    }


    private MaintainEvaluateMedia newMaintainEvaluateMedia(Long orderId, String mediaUrl) {
        MaintainEvaluateMedia result = new MaintainEvaluateMedia(orderId, mediaUrl);
        result.setModifiedTime(LocalDateTime.now());
        return result;
    }

    /**
     * 根据订单id删除媒体
     * @param orderIds
     */
    public void batchRemoveMedias(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        remove(new QueryWrapper<MaintainEvaluateMedia>().lambda().in(MaintainEvaluateMedia::getOrderId, orderIds));
    }

    /**
     * 根据订单id获取媒体信息
     * @param orderId
     * @return
     */
    public List<MaintainEvaluateMedia> listMediasByOrderId(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<MaintainEvaluateMedia>().lambda().eq(MaintainEvaluateMedia::getOrderId, orderId));
    }
}
