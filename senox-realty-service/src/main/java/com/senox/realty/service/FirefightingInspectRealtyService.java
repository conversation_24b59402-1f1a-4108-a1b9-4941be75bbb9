package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingInspectRealty;
import com.senox.realty.mapper.FirefightingInspectRealtyMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/12 15:11
 */
@Service
public class FirefightingInspectRealtyService
        extends ServiceImpl<FirefightingInspectRealtyMapper, FirefightingInspectRealty> {

    /**
     * 保存巡检物业
     * @param inspectId
     * @param inspectType
     * @param realtySerials
     */
    public void saveInspectRealities(Long inspectId,
                                     InspectionType inspectType,
                                     List<String> realtySerials) {
        if (realtySerials == null) {
            return;
        }

        // 原数据
        List<FirefightingInspectRealty> srcList = findByInspectId(inspectId, inspectType);
        // 目标数据
        List<FirefightingInspectRealty> targetList = buildInspectRealities(inspectId, inspectType, realtySerials);
        // 数据比较
        DataSepDto<FirefightingInspectRealty> sepDto = separateData(srcList, targetList);

        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            saveBatch(sepDto.getAddList());
        }

        if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
            List<Long> removeIds = sepDto.getRemoveList().stream()
                    .map(FirefightingInspectRealty::getId)
                    .collect(Collectors.toList());
            removeByIds(removeIds);
        }
    }

    /**
     * 根据巡检id删除巡检物业
     * @param inspectId
     * @param inspectType
     */
    public void deleteByInspectId(Long inspectId, InspectionType inspectType) {
        if (!WrapperClassUtils.biggerThanLong(inspectId, 0L) || inspectType == null) {
            return;
        }
        Wrapper<FirefightingInspectRealty> queryWrapper = new QueryWrapper<FirefightingInspectRealty>()
                .lambda()
                .eq(FirefightingInspectRealty::getInspectId, inspectId)
                .eq(FirefightingInspectRealty::getInspectType, inspectType);
        remove(queryWrapper);
    }

    /**
     * 根据巡检id查找巡检物业信息
     * @param inspectId
     * @param inspectType
     * @return
     */
    public List<FirefightingInspectRealty> findByInspectId(Long inspectId, InspectionType inspectType) {
        if (!WrapperClassUtils.biggerThanLong(inspectId, 0L) || inspectType == null) {
            return Collections.emptyList();
        }

        Wrapper<FirefightingInspectRealty> queryWrapper = new QueryWrapper<FirefightingInspectRealty>()
                .lambda()
                .eq(FirefightingInspectRealty::getInspectId, inspectId)
                .eq(FirefightingInspectRealty::getInspectType, inspectType);
        return list(queryWrapper);
    }

    /**
     * 巡检物业
     * @param inspectId
     * @param inspectType
     * @param realtySerials
     * @return
     */
    private List<FirefightingInspectRealty> buildInspectRealities(Long inspectId,
                                                                  InspectionType inspectType,
                                                                  List<String> realtySerials) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return Collections.emptyList();
        }

        List<FirefightingInspectRealty> resultList = new ArrayList<>(realtySerials.size());
        for (String item : realtySerials) {
            FirefightingInspectRealty media = new FirefightingInspectRealty(inspectId, inspectType, item);
            media.setModifiedTime(LocalDateTime.now());
            resultList.add(media);
        }
        return resultList;
    }


    /**
     * 数据比较
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<FirefightingInspectRealty> separateData(List<FirefightingInspectRealty> srcList,
                                                               List<FirefightingInspectRealty> targetList) {
        DataSepDto<FirefightingInspectRealty> result = new DataSepDto<>();

        if (CollectionUtils.isEmpty(srcList)) {
            result.setAddList(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            result.setRemoveList(targetList);

        } else {
            List<FirefightingInspectRealty> addList = targetList.stream()
                    .filter(x -> !srcList.contains(x))
                    .collect(Collectors.toList());

            List<FirefightingInspectRealty> removeList = srcList.stream()
                    .filter(x -> !targetList.contains(x))
                    .collect(Collectors.toList());

            result.setAddList(addList);
            result.setRemoveList(removeList);
        }


        return result;
    }
}
