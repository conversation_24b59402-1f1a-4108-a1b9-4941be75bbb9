package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.NumberUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.convert.AdvertisingSpacePositionConvertor;
import com.senox.realty.domain.AdvertisingSpace;
import com.senox.realty.domain.AdvertisingSpacePosition;
import com.senox.realty.mapper.AdvertisingSpaceMapper;
import com.senox.realty.vo.AdvertisingSpaceListVo;
import com.senox.realty.vo.AdvertisingSpacePositionVo;
import com.senox.realty.vo.AdvertisingSpaceSearchVo;
import com.senox.realty.vo.AdvertisingSpaceVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/18 9:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvertisingSpaceService extends ServiceImpl<AdvertisingSpaceMapper, AdvertisingSpace> {

    private final AdvertisingSpacePositionService positionService;
    private final AdvertisingSpacePositionConvertor positionConvertor;

    /**
     * 保存广告位
     * @param space
     * @param positions
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveSpace(AdvertisingSpace space, List<AdvertisingSpacePositionVo> positions) {
        if (positions != null) {
            String[] regions = positions.stream().map(AdvertisingSpacePositionVo::getRegionName).distinct().toArray(String[]::new);
            String[] streets = positions.stream().map(AdvertisingSpacePositionVo::getStreetName).distinct().toArray(String[]::new);
            space.setRegion(StringUtils.join(regions, '，'));
            space.setStreet(StringUtils.join(streets, '，'));
        }

        if (WrapperClassUtils.biggerThanLong(space.getId(), 0L)) {
            updateSpace(space);
        } else {
            addSpace(space);
        }

        // 广告位置
        if (positions != null) {
            positionService.saveAdvertisingSpacePosition(space.getId(), prepareSpacePositions(space, positions));
        }
        return space.getId();
    }

    /**
     * 添加广告位
     * @param space
     * @return
     */
    public Long addSpace(AdvertisingSpace space) {
        prepareAdvertisingSpace(space);
        space.setCreateTime(LocalDateTime.now());

        space.setModifierId(space.getCreatorId());
        space.setModifierName(space.getCreatorName());
        space.setModifiedTime(space.getCreateTime());
        boolean result = save(space);
        return result ? space.getId() : 0L;
    }

    /**
     * 更新广告位
     * @param space
     */
    public void updateSpace(AdvertisingSpace space) {
        if (!WrapperClassUtils.biggerThanLong(space.getId(), 0L)) {
            return;
        }

        // 忽略更新属性
        space.setSerialNo(null);
        space.setCreatorId(null);
        space.setCreatorName(null);
        space.setCreateTime(null);
        space.setModifiedTime(LocalDateTime.now());

        updateById(space);
    }

    /**
     * 根据id查找广告位信息
     * @param id
     * @return
     */
    public AdvertisingSpace findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 统计广告位
     * @param search
     * @return
     */
    public int countSpace(AdvertisingSpaceSearchVo search) {
        return getBaseMapper().countSpace(search);
    }

    /**
     * 广告位列表
     * @param search
     * @return
     */
    public List<AdvertisingSpaceListVo> listSpace(AdvertisingSpaceSearchVo search) {
        return getBaseMapper().listSpace(search);
    }

    /**
     * 广告位列表页
     * @param search
     * @return
     */
    public PageResult<AdvertisingSpaceListVo> listSpacePage(AdvertisingSpaceSearchVo search) {
        return PageUtils.commonPageResult(search, () -> countSpace(search), () -> listSpace(search));
    }


    /**
     * 广告位初始化
     * @param space
     */
    private void prepareAdvertisingSpace(AdvertisingSpace space) {
        if (StringUtils.isBlank(space.getSerialNo())) {
            space.setSerialNo(prepareSerial());
        }
    }


    /**
     * 广告位编号
     * @return
     */
    private String prepareSerial() {
        Long index  = RedisUtils.incr(RealtyConst.Cache.KEY_ADVERTISING_SERIAL);
        RedisUtils.expire(RealtyConst.Cache.KEY_ADVERTISING_SERIAL, RealtyConst.Cache.TTL_7D);

        Long maxSerial = getMaxSpaceIndex();
        if (index <= maxSerial) {
            RedisUtils.set(RealtyConst.Cache.KEY_ADVERTISING_SERIAL, maxSerial, RealtyConst.Cache.TTL_7D);
            index = RedisUtils.incr(RealtyConst.Cache.KEY_ADVERTISING_SERIAL);
        }
        return RealtyConst.ADVERTISING_SERIAL_PREFIX.concat(StringUtils.fixLength(String.valueOf(index), 5, '0'));
    }

    /**
     * 当前系统最大编号
     * @return
     */
    private Long getMaxSpaceIndex() {
        String maxSerial = getBaseMapper().findMaxSerial();
        return StringUtils.isBlank(maxSerial) ? 0L
                : NumberUtils.parseLong(maxSerial.substring(RealtyConst.ADVERTISING_SERIAL_PREFIX.length()), 0L);
    }

    /**
     * 广告区域街道信息初始化
     * @param space
     * @param positionList
     * @return
     */
    private List<AdvertisingSpacePosition> prepareSpacePositions(AdvertisingSpace space,
                                                                 List<AdvertisingSpacePositionVo> positionList) {
        List<AdvertisingSpacePosition> resultList = new ArrayList<>(positionList.size());
        for (AdvertisingSpacePositionVo item : positionList) {
            resultList.add(prepareSpacePosition(space, item));
        }
        return resultList;
    }

    /**
     * 广告区域街道信息初始化
     * @param space
     * @param position
     * @return
     */
    private AdvertisingSpacePosition prepareSpacePosition(AdvertisingSpace space,
                                                          AdvertisingSpacePositionVo position) {
        AdvertisingSpacePosition result = positionConvertor.toDo(position);
        result.setSpaceId(position.getStreetId());
        result.setCreatorId(space.getModifierId());
        result.setCreatorName(space.getModifierName());
        result.setCreateTime(space.getModifiedTime());
        result.setModifierId(space.getModifierId());
        result.setModifierName(space.getModifierName());
        result.setModifiedTime(space.getModifiedTime());
        return result;
    }
}
