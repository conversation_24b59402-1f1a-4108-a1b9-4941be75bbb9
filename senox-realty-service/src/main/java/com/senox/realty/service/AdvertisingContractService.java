package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.NumberUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.convert.AdvertisingPayoffConvertor;
import com.senox.realty.convert.AdvertisingProfitShareConvertor;
import com.senox.realty.domain.AdvertisingBill;
import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.domain.AdvertisingMedia;
import com.senox.realty.domain.AdvertisingProfitShare;
import com.senox.realty.mapper.AdvertisingContractMapper;
import com.senox.realty.vo.AdvertisingContractListVo;
import com.senox.realty.vo.AdvertisingContractSearchVo;
import com.senox.realty.vo.AdvertisingContractVo;
import com.senox.realty.vo.AdvertisingCostVo;
import com.senox.realty.vo.AdvertisingIncomeVo;
import com.senox.realty.vo.AdvertisingPayoffVo;
import com.senox.realty.vo.AdvertisingProfitShareVo;
import com.senox.realty.vo.ContractEnableDto;
import com.senox.realty.vo.ContractSuspendDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/19 14:06
 */
@Service
@RequiredArgsConstructor
public class AdvertisingContractService extends ServiceImpl<AdvertisingContractMapper, AdvertisingContract> {

    private final AdvertisingMediaService mediaService;
    private final AdvertisingProfitShareService profitShareService;
    private final AdvertisingBillService billService;
    private final AdvertisingProfitShareConvertor profitShareConvertor;
    private final AdvertisingPayoffService payoffService;
    private final AdvertisingPayoffConvertor payoffConvertor;


    /**
     * 保存合同
     * @param contract
     * @param medias
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveContract(AdvertisingContract contract, List<String> medias, List<AdvertisingProfitShareVo> shareList) {
        if (WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            updateContract(contract);
        } else {
            addContract(contract);
        }

        // 多媒体信息
        if (medias != null) {
            mediaService.batchSaveMedia(contract.getId(), prepareMedias(contract, medias));
        }
        // 收益分成
        if (shareList != null) {
            profitShareService.batchSaveProfitShare(contract.getId(), prepareProfitShares(contract, shareList));
        }

        // 生成/更新账单
        billService.saveBill(findById(contract.getId()));
        return contract.getId();
    }

    /**
     * 保存已支付广告合同信息
     * @param contract
     * @param medias
     * @param shareList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long savePaidContract(AdvertisingContract contract, List<String> medias, List<AdvertisingProfitShareVo> shareList) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            return 0L;
        }
        // 更新合同信息
        updatePaidContract(contract);

        // 多媒体信息
        if (medias != null) {
            mediaService.batchSaveMedia(contract.getId(), prepareMedias(contract, medias));
        }
        // 收益分成
        if (shareList != null) {
            List<AdvertisingProfitShare> shares = prepareProfitShares(contract, shareList);
            profitShareService.batchSaveProfitShare(contract.getId(), shares);

            contract = findById(contract.getId());
            payoffService.savePayoff(contract.getContractNo(), payoffService.buildPayoff(contract, shares));
        }
        return contract.getId();
    }

    /**
     * 添加合同
     * @param contract
     * @return
     */
    public Long addContract(AdvertisingContract contract) {
        prepareContract(contract);
        if (checkSpaceRent(contract.getSpaceId(), contract.getStartDate(), contract.getEndDate())) {
            throw new BusinessException("广告位在此合同期已出租");
        }

        // 合同号处理
        contract.setContractNo(prepareContractNo(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE)));

        contract.setCreateTime(LocalDateTime.now());
        contract.setModifierId(contract.getCreatorId());
        contract.setModifierName(contract.getCreatorName());
        contract.setModifiedTime(contract.getCreateTime());
        boolean result = save(contract);
        return result ? contract.getId() : 0L;
    }

    /**
     * 更新合同
     * @param contract
     */
    public void updateContract(AdvertisingContract contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            return;
        }
        // 已支付或已启用的合同不允许修改
        AdvertisingContract dbContract = findById(contract.getId());
        if (BooleanUtils.isTrue(dbContract.getPaid()) || ContractStatus.EFFECTIVE == ContractStatus.fromValue(dbContract.getStatus())) {
            throw new BusinessException("合同已支付或已生效");
        }

        // 变更了租赁期限，重新计算合同到期日
        if (contract.getPresentMonths() != null || contract.getRentMonths() != null) {
            contract.setEndDate(prepareEndDate(contract.getStartDate(), contract.getPresentMonths() + contract.getRentMonths()));

            // 判断合同期是否有重叠
            if (checkSpaceRent(contract.getSpaceId(), contract.getStartDate(), contract.getEndDate(), contract.getId())) {
                throw new BusinessException("广告位在此合同期已出租");
            }
        }

        // 忽略更新属性
        contract.setContractNo(null);
        contract.setCreatorId(null);
        contract.setCreatorName(null);
        contract.setCreateTime(null);

        // 根据id更新
        updateById(contract);
    }

    public void updatePaidContract(AdvertisingContract contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            return;
        }

        AdvertisingContract updateContract = new AdvertisingContract();
        updateContract.setId(contract.getId());
        if (contract.getCost() != null) {
            updateContract.setCost(contract.getCost());
        }
        updateContract.setRemark(contract.getRemark());
        updateContract.setModifierId(contract.getModifierId());
        updateContract.setModifierName(contract.getModifierName());
        updateContract.setModifiedTime(LocalDateTime.now());

        // 根据id更新
        updateById(updateContract);
    }

    /**
     * 更新合同成本
     * @param cost
     */
    public void updateContractCost(AdvertisingCostVo cost) {
        if (!WrapperClassUtils.biggerThanLong(cost.getContractId(), 0L)) {
            return;
        }

        AdvertisingContract updateContract = new AdvertisingContract();
        updateContract.setId(cost.getContractId());
        updateContract.setCost(cost.getCost());
        updateContract.setModifierId(cost.getOperatorId());
        updateContract.setModifierName(cost.getOperatorName());
        updateContract.setModifiedTime(LocalDateTime.now());

        updateById(updateContract);
    }

    /**
     * 启用合同
     * @param enableDto
     */
    public void enableContractTillDate(ContractEnableDto enableDto) {
        if (enableDto.getEnableDate() == null) {
            enableDto.setEnableDate(LocalDate.now());
        }

        LambdaQueryWrapper<AdvertisingContract> queryWrapper =
                new QueryWrapper<AdvertisingContract>().lambda()
                        .eq(!StringUtils.isBlank(enableDto.getContractNo()), AdvertisingContract::getContractNo, enableDto.getContractNo())
                        .eq(AdvertisingContract::getStatus, ContractStatus.INEFFECTIVE.ordinal())
                        .eq(AdvertisingContract::getPaid, Boolean.TRUE)
                        .le(AdvertisingContract::getStartDate, enableDto.getEnableDate());
        update(prepareEnableData(enableDto), queryWrapper);
    }

    /**
     * 停用合同
     * @param suspendDto
     */
    public void suspendContract(ContractSuspendDto suspendDto) {
        if (StringUtils.isBlank(suspendDto.getContractNo())) {
            return;
        }

        LambdaQueryWrapper<AdvertisingContract> queryWrapper =
                new QueryWrapper<AdvertisingContract>().lambda()
                        .eq(AdvertisingContract::getContractNo, suspendDto.getContractNo())
                        .eq(AdvertisingContract::getStatus, ContractStatus.EFFECTIVE.ordinal());
        update(prepareSuspendData(suspendDto), queryWrapper);
    }

    /**
     * 到期停用合同
     * @param suspendDto
     */
    public void suspendContractTillDate(ContractSuspendDto suspendDto) {
        if (suspendDto.getSuspendDate() == null) {
            return;
        }

        LambdaQueryWrapper<AdvertisingContract> queryWrapper =
                new QueryWrapper<AdvertisingContract>().lambda()
                        .eq(!StringUtils.isBlank(suspendDto.getContractNo()), AdvertisingContract::getContractNo, suspendDto.getContractNo())
                        .lt(AdvertisingContract::getEndDate, suspendDto.getSuspendDate())
                        .eq(AdvertisingContract::getStatus, ContractStatus.EFFECTIVE.ordinal());
        update(prepareSuspendData(suspendDto), queryWrapper);
    }

    /**
     * 删除合同
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteContract(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        AdvertisingContract contract = findById(id);
        if (contract == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BooleanUtils.isTrue(contract.getPaid())
                || ContractStatus.EFFECTIVE == ContractStatus.fromValue(contract.getStatus())) {
            throw new BusinessException("合同已支付或合同已生效");
        }

        LambdaQueryWrapper<AdvertisingContract> queryWrapper = new QueryWrapper<AdvertisingContract>().lambda()
                .eq(AdvertisingContract::getId, id)
                .eq(AdvertisingContract::getPaid, Boolean.FALSE)
                .eq(AdvertisingContract::getStatus, ContractStatus.INEFFECTIVE.ordinal());
        AdvertisingBill bill = billService.findByContractNo(contract.getContractNo());

        // 删除合同
        if (remove(queryWrapper) && bill != null) {
            // 删除应收账单
            billService.deleteBill(bill.getId());
        }
    }


    /**
     * 根据id查找广告位合同
     * @param id
     * @return
     */
    public AdvertisingContract findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return getById(id);
    }

    /**
     * 根据id获取合同明细
     * @param id
     * @return
     */
    public AdvertisingContractVo findDetailById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return getBaseMapper().findDetailById(id);
    }

    /**
     * 根据合同号查找广告位合同
     * @param contractNo
     * @return
     */
    public AdvertisingContract findByContractNo(String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            return null;
        }

        LambdaQueryWrapper<AdvertisingContract> queryWrapper =
                new QueryWrapper<AdvertisingContract>().lambda().eq(AdvertisingContract::getContractNo, contractNo);
        return getOne(queryWrapper);
    }

    /**
     * 广告位是否已出租
     * @param spaceId
     * @return
     */
    public boolean checkSpaceRent(Long spaceId) {
        return checkSpaceRent(spaceId, null, null, null);
    }

    /**
     * 统计合同
     * @param search
     * @return
     */
    public int countContract(AdvertisingContractSearchVo search) {
        return getBaseMapper().countContract(search);
    }

    /**
     * 合同视图列表
     * @param search
     * @return
     */
    public List<AdvertisingContractListVo> listContractView(AdvertisingContractSearchVo search) {
        return getBaseMapper().listContractView(search);
    }

    /**
     * 广告收益合计
     * @param search
     * @return
     */
    public AdvertisingIncomeVo sumIncome(AdvertisingContractSearchVo search) {
        search.setPaid(Boolean.TRUE);
        return getBaseMapper().sumContractIncome(search);
    }

    /**
     * 广告收益列表
     * @param search
     * @return
     */
    public List<AdvertisingIncomeVo> listIncome(AdvertisingContractSearchVo search) {
        search.setPaid(Boolean.TRUE);
        // 广告收入
        List<AdvertisingIncomeVo> resultList = getBaseMapper().listContractIncome(search);

        // 应付明细
        if (!CollectionUtils.isEmpty(resultList) && BooleanUtils.isTrue(search.getWithPayoff())) {
            List<String> list = resultList.stream()
                    .filter(x -> DecimalUtils.isPositive(x.getShareAmount()))
                    .map(AdvertisingIncomeVo::getContractNo)
                    .distinct()
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(list)) {
                List<AdvertisingPayoffVo> payoffs = payoffConvertor.toVo(payoffService.listByContractNos(list));
                for (AdvertisingIncomeVo item : resultList) {
                    if (DecimalUtils.isPositive(item.getShareAmount())) {
                        item.setShares(payoffs.stream().filter(x -> Objects.equals(item.getContractNo(), x.getContractNo())).collect(Collectors.toList()));
                    }
                }
            }
        }
        return resultList;
    }


    /**
     * 合同信息初始化
     * @param contract
     */
    private void prepareContract(AdvertisingContract contract) {
        if (contract.getSignDate() == null) {
            contract.setSignDate(LocalDate.now());
        }
        if (contract.getPresentMonths() == null) {
            contract.setPresentMonths(0);
        }
        if (contract.getCost() == null) {
            contract.setCost(BigDecimal.ZERO);
        }
        if (contract.getStatus() == null) {
            contract.setStatus(ContractStatus.INEFFECTIVE.ordinal());
        }
        if (contract.getPaid() == null) {
            contract.setPaid(Boolean.FALSE);
        }
        if (contract.getStopBy() == null) {
            contract.setStopBy(0L);
        }

        // 合同终止日期
        contract.setEndDate(prepareEndDate(contract.getStartDate(), contract.getPresentMonths() + contract.getRentMonths()));
    }

    /**
     * 合同截止日期
     * @param startDate
     * @param months
     * @return
     */
    private LocalDate prepareEndDate(LocalDate startDate, Integer months) {
        return startDate.plusMonths(months).minusDays(1L);
    }


    /**
     * 生成合同号
     * @param prefix
     * @return
     */
    private String prepareContractNo(String prefix) {
        String cacheKey = String.format(RealtyConst.Cache.KEY_ADVERTISING_CONTRACT, prefix);
        Long index = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, RealtyConst.Cache.TTL_2D);

        // db 最大合同序号
        Long dbIndex = getMaxContractIndex(prefix);
        if (index <= dbIndex) {
            RedisUtils.set(cacheKey, dbIndex, RealtyConst.Cache.TTL_2D);
            index = RedisUtils.incr(cacheKey);
        }

        return prefix.concat(StringUtils.fixLength(String.valueOf(index), 4, '0'));
    }

    /**
     * 获取最大合同号
     * @param prefix
     * @return
     */
    private Long getMaxContractIndex(String prefix) {
        String maxContractNo = getBaseMapper().findMaxContractNo(prefix);
        return StringUtils.isBlank(maxContractNo) ? 0L
                : NumberUtils.parseLong(maxContractNo.substring(prefix.length()), 0L);
    }

    /**
     * 广告位是否出租
     * @param spaceId
     * @param startDate
     * @param endDate
     * @return
     */
    private boolean checkSpaceRent(Long spaceId, LocalDate startDate, LocalDate endDate) {
        return checkSpaceRent(spaceId, startDate, endDate, null);
    }

    /**
     * 广告位是否已出租
     * @param spaceId
     * @param startDate
     * @param endDate
     * @param sourceId
     * @return
     */
    private boolean checkSpaceRent(Long spaceId, LocalDate startDate, LocalDate endDate, Long sourceId) {
        if (WrapperClassUtils.biggerThanLong(spaceId, 0L)) {
            // 检查未生效及已生效的合同的时间范围
            LambdaQueryWrapper<AdvertisingContract> queryWrapper =
                    new QueryWrapper<AdvertisingContract>().lambda()
                            .select(AdvertisingContract::getId)
                            .eq(AdvertisingContract::getSpaceId, spaceId)
                            .ge(endDate != null, AdvertisingContract::getEndDate, startDate)
                            .le(startDate != null, AdvertisingContract::getStartDate, endDate)
                            .in(AdvertisingContract::getStatus, Arrays.asList(ContractStatus.INEFFECTIVE.ordinal(), ContractStatus.EFFECTIVE.ordinal()));
            AdvertisingContract contract = getOne(queryWrapper);

            if (contract != null) {
                return !WrapperClassUtils.biggerThanLong(sourceId, 0L) || !Objects.equals(sourceId, contract.getId());
            }
        }
        return false;
    }

    /**
     * 启用参数
     * @param enableDto
     * @return
     */
    private AdvertisingContract prepareEnableData(ContractEnableDto enableDto) {
        AdvertisingContract result = new AdvertisingContract();
        result.setStatus(ContractStatus.EFFECTIVE.ordinal());
        result.setModifiedTime(LocalDateTime.now());
        if (enableDto.getOperator() != null) {
            result.setModifierId(enableDto.getOperator().getUserId());
            result.setModifierName(enableDto.getOperator().getUsername());
        }
        return result;
    }

    /**
     * 停用参数
     * @param suspendDto
     * @return
     */
    private AdvertisingContract prepareSuspendData(ContractSuspendDto suspendDto) {
        AdvertisingContract result = new AdvertisingContract();
        result.setStatus(ContractStatus.SUSPEND.ordinal());
        result.setEndDate(suspendDto.getSuspendDate());
        result.setStopTime(LocalDateTime.now());
        result.setModifiedTime(LocalDateTime.now());
        if (WrapperClassUtils.biggerThanLong(suspendDto.getOperatorId(), 0L)) {
            result.setStopBy(suspendDto.getOperatorId());
            result.setModifierId(suspendDto.getOperatorId());
        }
        if (!StringUtils.isBlank(suspendDto.getOperatorName())) {
            result.setModifierName(suspendDto.getOperatorName());
        }
        return result;
    }

    /**
     * 广告媒体资料初始化
     * @param contract
     * @param medias
     * @return
     */
    private List<AdvertisingMedia> prepareMedias(AdvertisingContract contract, List<String> medias) {
        List<AdvertisingMedia> resultList = new ArrayList<>(medias.size());
        for (String item : medias) {
            resultList.add(prepareMedia(contract, item));
        }
        return resultList;
    }

    /**
     * 广告媒体资料初始化
     * @param contract
     * @param media
     * @return
     */
    private AdvertisingMedia prepareMedia(AdvertisingContract contract, String media) {
        AdvertisingMedia result = new AdvertisingMedia(contract.getId(), media);
        result.setCreatorId(contract.getModifierId());
        result.setCreatorName(contract.getModifierName());
        result.setCreateTime(LocalDateTime.now());
        result.setModifierId(contract.getModifierId());
        result.setModifierName(contract.getModifierName());
        result.setModifiedTime(LocalDateTime.now());
        return result;
    }

    /**
     * 广告收益分成初始化
     * @param contract
     * @param shareList
     * @return
     */
    private List<AdvertisingProfitShare> prepareProfitShares(AdvertisingContract contract,
                                                             List<AdvertisingProfitShareVo> shareList) {
        List<AdvertisingProfitShare> resultList = new ArrayList<>(shareList.size());
        for (AdvertisingProfitShareVo item : shareList) {
            resultList.add(prepareProfitShare(contract, item));
        }
        return resultList;
    }

    /**
     * 广告收益分成初始化
     * @param contract
     * @param share
     * @return
     */
    private AdvertisingProfitShare prepareProfitShare(AdvertisingContract contract,
                                                      AdvertisingProfitShareVo share) {
        AdvertisingProfitShare result = profitShareConvertor.toDo(share);
        result.setContractId(contract.getId());
        result.setCreatorId(contract.getModifierId());
        result.setCreatorName(contract.getModifierName());
        result.setCreateTime(LocalDateTime.now());
        result.setModifierId(contract.getModifierId());
        result.setModifierName(contract.getModifierName());
        result.setModifiedTime(LocalDateTime.now());
        return result;
    }
}
