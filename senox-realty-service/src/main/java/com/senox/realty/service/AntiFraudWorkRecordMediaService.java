package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.constant.AntiFraudWorkRecordMediaSource;
import com.senox.realty.domain.AntiFraudWorkRecordMedia;
import com.senox.realty.mapper.AntiFraudWorkRecordMediaMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.AntiFraudWorkRecordMediaSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class AntiFraudWorkRecordMediaService extends ServiceImpl<AntiFraudWorkRecordMediaMapper, AntiFraudWorkRecordMedia> {

    /**
     * 添加媒体资源
     * @param media 媒体资源
     */
    public void add(AntiFraudWorkRecordMedia media) {
        addBatch(Collections.singletonList(media));
    }

    /**
     * 批量添加媒体资源
     * @param medias 媒体资源集
     */
    public void addBatch(List<AntiFraudWorkRecordMedia> medias) {
        if (CollectionUtils.isEmpty(medias)) {
            return;
        }
        medias.forEach(x -> {
            ContextUtils.initEntityCreator(x);
            x.setCreateTime(LocalDateTime.now());
        });
        baseMapper.addBatch(medias);
    }

    /**
     * 批量添加媒体资源
     * @param productId 产品id
     * @param source 媒体资源来源
     * @param medias 媒体资源集
     */
    public void addBatch(Long productId, AntiFraudWorkRecordMediaSource source, List<AntiFraudWorkRecordMedia> medias) {
        medias.forEach(x -> {
            x.setProductId(productId);
            x.setSource(source.getNumber());
        });
        addBatch(medias);
    }


    /**
     * 根据媒体资源id删除
     * @param mediaId 媒体资源id
     */
    public void deleteById(Long mediaId) {
        if (!WrapperClassUtils.biggerThanLong(mediaId, 0)) {
            return;
        }
        deleteByIds(Collections.singletonList(mediaId));
    }

    /**
     * 根据媒体资源id批量删除
     * @param mediaIds 媒体资源id集
     */
    public void deleteByIds(List<Long> mediaIds) {
        if (CollectionUtils.isEmpty(mediaIds)) {
            return;
        }
        baseMapper.deleteByIds(mediaIds);
    }

    /**
     * 根据产品id批量删除
     * @param productIds 产品id集
     * @param sources 来源集
     */
    public void deleteByProductIds(List<Long> productIds, List<AntiFraudWorkRecordMediaSource> sources) {
        if (CollectionUtils.isEmpty(productIds) || CollectionUtils.isEmpty(sources)) {
            return;
        }
        baseMapper.deleteByProductIds(productIds, sources);
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(AntiFraudWorkRecordMediaSearchVo search) {
        return baseMapper.countList(search);
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<AntiFraudWorkRecordMedia> list(AntiFraudWorkRecordMediaSearchVo search) {
        return baseMapper.list(search);
    }

    /**
     * 批量添加或删除
     * @param medias 媒体资源集
     */
    public void addOrDeleteBatch(List<AntiFraudWorkRecordMedia> medias) {
        if (CollectionUtils.isEmpty(medias)) {
            return;
        }
        List<Long> productIds = new ArrayList<>(medias.size());
        List<AntiFraudWorkRecordMediaSource> sources = new ArrayList<>(medias.size());
        medias.forEach(x -> {
            productIds.add(x.getProductId());
            sources.add(AntiFraudWorkRecordMediaSource.fromNumber(x.getSource()));
        });
        AntiFraudWorkRecordMediaSearchVo search = new AntiFraudWorkRecordMediaSearchVo();
        search.setProductIds(productIds);
        search.setSources(sources);
        List<AntiFraudWorkRecordMedia> dbMedias = list(search);
        DataSepDto<AntiFraudWorkRecordMedia> dataSep = SeparateUtils.separateData(dbMedias, medias);
        if (!CollectionUtils.isEmpty(dataSep.getAddList())) {
            addBatch(dataSep.getAddList());
        }
        if (!CollectionUtils.isEmpty(dataSep.getRemoveList())) {
            deleteByIds(dataSep.getRemoveList().stream().map(AntiFraudWorkRecordMedia::getId).collect(Collectors.toList()));
        }
    }
}
