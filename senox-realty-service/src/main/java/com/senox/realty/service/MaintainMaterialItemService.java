package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.MaintainMaterialItem;
import com.senox.realty.mapper.MaintainMaterialItemMapper;
import com.senox.realty.vo.MaintainMaterialItemVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/25 10:44
 */
@Service
@Slf4j
public class MaintainMaterialItemService extends ServiceImpl<MaintainMaterialItemMapper, MaintainMaterialItem> {

    /**
     * 添加物料明细
     * @param materialId
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMaintainMaterialItem(Long materialId, List<MaintainMaterialItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //合并同类
        list = mergeCollect(list);
        if (!WrapperClassUtils.biggerThanLong(materialId, 0L)) {
            throw new BusinessException("物料id有误");
        }
        list.forEach(x -> x.setMaterialId(materialId));
        DataSepDto<MaintainMaterialItem> sepData = SeparateUtils.separateData(maintainMaterialItemList(materialId), list);
        log.info("转换后的结果:{}", JsonUtils.object2Json(sepData));

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            validateAndCalculateAmount(sepData.getAddList());
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            validateAndCalculateAmount(sepData.getUpdateList());
            updateBatchById(sepData.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(MaintainMaterialItem::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 合并同类项
     * @param list
     * @return
     */
    private static List<MaintainMaterialItem> mergeCollect(List<MaintainMaterialItem> list) {
        //如果价格和商品都一样就合并
        return new ArrayList<>(list.stream().collect(Collectors.toMap(Function.identity(), x -> x, (item1, item2) -> {
            //单价一样，合并数量和总金额
            item1.setQuantity(item1.getQuantity() + item2.getQuantity());
            item1.setAmount(item1.getAmount().add(item2.getAmount()));
            return item1;
        })).values());
    }

    /**
     * 删除维修物料
     * @param id
     */
    public void deleteMaintainMaterialItem(Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        removeById(id);
    }

    /**
     * 根据物料id删除明细
     * @param materialId
     */
    public void removeByMaterialId(Long materialId) {
        if (!WrapperClassUtils.biggerThanLong(materialId, 0L)){
            return;
        }
        batchRemoveByMaterialIds(Collections.singletonList(materialId));
    }

    /**
     * 批量删除物料明细
     * @param ids
     */
    public void batchRemoveByMaterialIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        remove(new QueryWrapper<MaintainMaterialItem>().lambda().in(MaintainMaterialItem::getMaterialId, ids));
    }

    /**
     * 根据订单号和派单号查询维修物料
     *
     * @param orderId
     * @param jobId
     * @return
     */
    public List<MaintainMaterialItemVo> findByOrderIdAndJobId(Long orderId, Long jobId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            //只根据订单查询
            return getBaseMapper().findByOrderId(orderId);
        }
        return getBaseMapper().findByOrderIdAndJobId(orderId, jobId);
    }

    /**
     * 物维物料id查询列表
     *
     * @param materialId
     * @return
     */
    public List<MaintainMaterialItem> maintainMaterialItemList(Long materialId) {
        LambdaQueryWrapper<MaintainMaterialItem> wrapper = new LambdaQueryWrapper<>();
        if (!WrapperClassUtils.biggerThanLong(materialId, 0L)) {
            return Collections.emptyList();
        }
        wrapper.eq(MaintainMaterialItem::getMaterialId, materialId);
        wrapper.eq(MaintainMaterialItem::getDisabled, Boolean.FALSE);
        return list(wrapper);
    }

    /**
     * 物维物料查询
     * @param materialIds
     * @return
     */
    public List<MaintainMaterialItem> maintainMaterialItemList(List<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<MaintainMaterialItem>().lambda().in(MaintainMaterialItem::getMaterialId, materialIds));
    }

    /**
     * 计算总金额
     * @param materials
     */
    private void validateAndCalculateAmount(List<MaintainMaterialItem> materials) {
        materials.forEach(material -> {
            BigDecimal price = material.getPrice();
            Integer quantity = material.getQuantity();

            if (!(price != null && price.compareTo(BigDecimal.ZERO) > 0
                    && quantity != null && quantity > 0)) {
                throw new BusinessException("金额或数量错误");
            }

            material.setAmount(DecimalUtils.multiple(price, BigDecimal.valueOf(quantity)));
        });
    }
}
