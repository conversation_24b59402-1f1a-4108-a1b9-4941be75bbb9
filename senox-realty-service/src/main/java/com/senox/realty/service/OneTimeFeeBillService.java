package com.senox.realty.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.NumberUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.vo.OrderCancelVo;
import com.senox.realty.component.DepartmentComponent;
import com.senox.realty.component.OrderComponent;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.OneTimeFeeBill;
import com.senox.realty.mapper.OneTimeFeeBillMapper;
import com.senox.realty.vo.*;
import com.senox.user.vo.DepartmentVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/14 14:51
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OneTimeFeeBillService {

    @Value("${senox.oneTimeFee.serial.length}")
    private Integer billNoPostfixLength;
    @Value("${senox.oneTimeFee.serial.fillChar}")
    private Character billNoFillChar;

    private final OneTimeFeeService feeService;
    private final OneTimeFeeBillMapper billMapper;
    private final OrderComponent orderComponent;
    private final DepartmentComponent departmentComponent;

    /**
     * 添加一次性账单
     *
     * @param bill
     * @return
     */
    public Long addBill(OneTimeFeeBill bill) {
        prepareBill(bill);
        billMapper.addOneTimeFeeBill(bill);
        return bill.getId();
    }

    /**
     * 更新账单
     *
     * @param bill
     */
    public void updateBill(OneTimeFeeBill bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            return;
        }

        OneTimeFeeBillVo dbBill = findById(bill.getId());
        if (dbBill != null && dbBill.getStatus() != BillStatus.INIT.getStatus()) {
            throw new BusinessException("账单已支付，无法更改");
        }

        prepareBillRedundance(bill);
        // 这个操作不做状态变更
        bill.setStatus(null);
        billMapper.updateOneTimeFeeBill(bill);
    }


    /**
     * 更新账单票据号
     *
     * @param serial
     */
    public void updateBillSerial(TollSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getId(), 0L)) {
            return;
        }

        OneTimeFeeBillVo bill = findById(serial.getId());
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到账单");
        }
        BillStatus billStatus = BillStatus.fromStatus(bill.getStatus());
        if (BooleanUtils.isTrue(serial.getRefund())) {
            if (billStatus != BillStatus.REFUND) {
                throw new BusinessException("账单未退费");
            }
        } else {
            if (billStatus == null || billStatus == BillStatus.INIT) {
                throw new BusinessException("账单未缴费");
            }
        }
        billMapper.updateOneTimeFeeBillSerial(serial);
    }

    /**
     * 支付一次性收费账单
     *
     * @param id
     * @param toll
     */
    public void payOneTimeFeeBill(Long id, BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        // 订单状态校验
        OneTimeFeeBillVo bill = findById(id);
        if (bill != null && bill.getStatus() != BillStatus.INIT.getStatus()) {
            throw new BusinessException("订单已支付");
        }

        if (StringUtils.isBlank(toll.getSerial())) {
            toll.setSerial(StringUtils.EMPTY);
        }
        toll.setId(id);
        toll.setStatus(BillStatus.PAID.getStatus());
        billMapper.updateOneTimeFeeBillPaidStatus(toll);
    }

    /**
     * 撤销一次性收费账单
     *
     * @param billToll
     */
    @Transactional(rollbackFor = Exception.class)
    public void revokeOneTimeFeeBillPayment(BillTollVo billToll) {
        if (!WrapperClassUtils.biggerThanLong(billToll.getId(), 0L)) {
            return;
        }

        // 订单状态校验
        OneTimeFeeBillVo bill = findById(billToll.getId());
        if (bill != null && bill.getStatus() != BillStatus.PAID.getStatus()) {
            throw new BusinessException("订单未支付或已退费");
        }

        // 撤销支付
        billMapper.revokeOneTimeFeeBillPaid(billToll);

        // 取消 p_order 表支付
        if (bill != null && WrapperClassUtils.biggerThanLong(bill.getRemoteOrderId(), 0L)) {
            OrderCancelVo cancel = new OrderCancelVo();
            cancel.setOrderId(bill.getRemoteOrderId());
            cancel.setProductIds(Collections.singletonList(billToll.getId()));
            cancel.setRemark(RealtyConst.REMARK_ONE_TIME_FEE_PAY_CANCEL);
            orderComponent.cancelOrder(cancel);
        }
    }

    /**
     * 一次性收费账单退费
     *
     * @param id
     * @param toll
     */
    public void refundOneTimeFeeBill(Long id, BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        // 订单状态校验
        OneTimeFeeBillVo bill = findById(id);
        if (bill != null && bill.getStatus() != BillStatus.PAID.getStatus()) {
            throw new BusinessException("订单未支付或已退费");
        }

        if (StringUtils.isBlank(toll.getSerial())) {
            toll.setSerial(StringUtils.EMPTY);
        }
        toll.setId(id);
        toll.setStatus(BillStatus.REFUND.getStatus());
        billMapper.refundOneTimeFeeBill(toll);
    }

    /**
     * 撤销一次性收费退费
     *
     * @param billToll
     */
    @Transactional(rollbackFor = Exception.class)
    public void revokeOneTimeFeeBillRefund(BillTollVo billToll) {
        if (!WrapperClassUtils.biggerThanLong(billToll.getId(), 0L)) {
            return;
        }

        // 订单状态校验
        OneTimeFeeBillVo bill = findById(billToll.getId());
        if (bill != null && bill.getStatus() != BillStatus.REFUND.getStatus()) {
            throw new BusinessException("订单未退费");
        }

        // 撤销退费
        billMapper.revokeOneTimeFeeRefund(billToll);

        // 取消 p_order 表退费订单
        if (bill != null && WrapperClassUtils.biggerThanLong(bill.getRefundOrderId(), 0L)) {
            OrderCancelVo cancel = new OrderCancelVo();
            cancel.setOrderId(bill.getRefundOrderId());
            cancel.setProductIds(Collections.singletonList(billToll.getId()));
            cancel.setRemark(RealtyConst.REMARK_ONE_TIME_FEE_REFUND_CANCEL);
            orderComponent.cancelOrder(cancel);
        }
    }

    /**
     * 根据id获取账单信息
     *
     * @param id
     * @return
     */
    public OneTimeFeeBillVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return billMapper.findById(id);
    }

    /**
     * 根据票据号查找一次性收费账单
     *
     * @param billNo
     * @return
     */
    public OneTimeFeeBillVo findByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return null;
        }

        return billMapper.findByBillNo(billNo);
    }

    /**
     * 根据id获取账单详情
     *
     * @param id
     * @return
     */
    public OneTimeFeeBillTradeVo findDetailById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return billMapper.findWithDetailById(id);
    }

    /**
     * 一次性收费账单交易合计
     *
     * @param search
     * @return
     */
    public OneTimeFeeBillSummaryVo sumOneTimeFeeBill(OneTimeFeeBillSearchVo search) {
        return billMapper.sumOneTimeFeeBill(search);
    }

    /**
     * 根据id一次性收费账单列表
     * @param ids
     * @return
     */
    public List<OneTimeFeeBillVo> listByIds(List<Long> ids) {
        return CollectionUtils.isEmpty(ids) ? Collections.emptyList() : billMapper.listByIds(ids);
    }

    /**
     * 一次性收费账单列表
     *
     * @param search
     * @return
     */
    public PageResult<OneTimeFeeBillTradeVo> listOneTimeFeeBillPage(OneTimeFeeBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = billMapper.countOneTimeFeeBill(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<OneTimeFeeBillTradeVo> resultList = billMapper.listOneTimeFeeBill(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 一次性收费账单交易合计
     *
     * @param search
     * @return
     */
    public OneTimeFeeBillSummaryVo sumOneTimeFeeBillTrade(OneTimeFeeBillTradeSearchVo search) {
        return billMapper.sumOneTimeFeeBillTrade(search);
    }

    /**
     * 一次性收费账单交易列表
     *
     * @param search
     * @return
     */
    public PageResult<OneTimeFeeBillTradeVo> listOneTimeFeeBillTrade(OneTimeFeeBillTradeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = billMapper.countOneTimeFeeBillTrade(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<OneTimeFeeBillTradeVo> resultList = billMapper.listOneTimeFeeBillTrade(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 冷藏押金
     * @param search
     * @return
     */
    public List<OneTimeFeeDepositVo> listOneTimeFeeDeposit(OneTimeFeeDepositSearchVo search) {
        if (CollectionUtils.isEmpty(search.getCustomers())) {
            return Collections.emptyList();
        }

        List<OneTimeFeeDepositVo> resultList = new ArrayList<>(search.getCustomers().size());
        List<String> serials = search.getCustomers();
        int index = 0;
        while (index < serials.size()) {
            List<String> list = serials.stream().skip(index).limit(RealtyConst.BATCH_SIZE_2000).collect(Collectors.toList());
            search.setCustomers(list);
            resultList.addAll(billMapper.listOneTimeFeeDeposit(search));
            index += RealtyConst.BATCH_SIZE_2000;
        }

        return resultList;
    }

    /**
     * 账单数据初始化
     *
     * @param bill
     */
    private void prepareBill(OneTimeFeeBill bill) {
        if (bill.getOperateDate() == null) {
            bill.setOperateDate(LocalDate.now());
        }
        if (bill.getBillYear() == null) {
            bill.setBillYear(bill.getOperateDate().getYear());
        }
        if (bill.getBillMonth() == null) {
            bill.setBillMonth(bill.getOperateDate().getMonthValue());
        }
        if (bill.getAmount() == null) {
            bill.setAmount(BigDecimal.ZERO);
        }
        if (bill.getRealtyId() == null) {
            bill.setRealtyId(0L);
        }
        if (bill.getCustomerSerial() == null) {
            bill.setCustomerSerial(StringUtils.EMPTY);
        }
        if (bill.getStatus() == null) {
            bill.setStatus(BillStatus.INIT.getStatus());
        }
        if (StringUtils.isBlank(bill.getBillNo())) {
            String prefix = LocalDate.now().format(DateTimeFormatter.ofPattern(RealtyConst.SERIAL_DATE_FORMAT));
            bill.setBillNo(prepareBillNo(prefix, billNoPostfixLength, billNoFillChar));
        }

        // 冗余信息处理
        prepareBillRedundance(bill);
    }

    /**
     * 账单冗余信息初始化
     *
     * @param bill
     */
    private void prepareBillRedundance(OneTimeFeeBill bill) {
        if (WrapperClassUtils.biggerThanLong(bill.getFeeId(), 0L)) {
            OneTimeFeeVo fee = feeService.findById(bill.getFeeId());
            if (fee != null) {
                bill.setFeeName(fee.getName());
            }
        }
        if (WrapperClassUtils.biggerThanLong(bill.getDepartmentId(), 0L)) {
            DepartmentVo department = departmentComponent.findById(bill.getDepartmentId());
            if (department != null) {
                bill.setDepartmentName(department.getFullName());
            }
        }
    }

    /**
     * 生成单号
     *
     * @param prefix
     * @param length
     * @param fillChar
     * @return
     */
    private String prepareBillNo(String prefix, int length, char fillChar) {
        // redis 单号
        String cacheKey = String.format(RealtyConst.Cache.KEY_ONE_TIME_FEE_BILL_NO, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, RealtyConst.Cache.TTL_2D);

        // db 单号
        Long dbSerial = getMaxBillNo(prefix);
        if (result < dbSerial) {
            result = dbSerial + 1;
            RedisUtils.set(cacheKey, result, RealtyConst.Cache.TTL_2D);
        }
        return prefix.concat(StringUtils.fixLength(String.valueOf(result), length, fillChar));
    }

    /**
     * 最大账单号
     *
     * @param prefix
     * @return
     */
    private Long getMaxBillNo(String prefix) {
        String result = billMapper.findMaxBillNo(prefix);
        if (StringUtils.isBlank(result)) {
            return 0L;
        }
        return NumberUtils.parseLong(result.substring(prefix.length()), 0L);
    }

    /**
     * 更新一次性费用账单状态
     *
     * @param billPaid
     */
    public void updateOneTimeFeeBillStatus(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return;
        }
        if (!CollectionUtils.isEmpty(billPaid.getBillIds())) {
            log.info("根据一次性费用账单id更新一次性费用账单状态-{}", JsonUtils.object2Json(billPaid));
            updateBillPaidById(billPaid);

        } else {
            log.info("根据支付订单id更新一次性费用账单状态-{}", JsonUtils.object2Json(billPaid));
            updateBillPaidByOrderId(billPaid);
        }
    }

    /**
     * 根据订单id更新账单状态
     * @param billPaid
     * @return
     */
    private int updateBillPaidByOrderId(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return 0;
        }
        return BooleanUtils.isTrue(billPaid.getRefund())
                ? billMapper.updateBillPaidByRemoteOrder(billPaid)
                : billMapper.updateBillRefundByRemoteOrder(billPaid);
    }

    /**
     * 根据账单更新账单状态
     *
     * @param billPaid
     * @return
     */
    private int updateBillPaidById(BillPaidVo billPaid) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            return 0;
        }

        return BooleanUtils.isTrue(billPaid.getRefund())
                ? billMapper.updateBillRefundById(billPaid)
                : billMapper.updateBillPaidById(billPaid);
    }
}
