package com.senox.realty.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.OneTimeFee;
import com.senox.realty.mapper.OneTimeFeeMapper;
import com.senox.realty.vo.OneTimeFeeSearchVo;
import com.senox.realty.vo.OneTimeFeeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/10/12 10:34
 */
@Service
public class OneTimeFeeService {

    @Autowired
    private OneTimeFeeMapper oneTimeFeeMapper;

    /**
     * 添加一次性收费项目
     * @param fee
     * @param departments
     * @return
     */
    public Long addOneTimeFee(OneTimeFee fee, List<Long> departments) {
        // 判断费项名是否在
        if (checkFeeExist(fee)) {
            throw new BusinessException("费项已存在");
        }
        if (fee.getRefrigeration() == null) {
            fee.setRefrigeration(Boolean.FALSE);
        }

        // add fee
        int result = oneTimeFeeMapper.addOneTimeFee(fee);
        // add fee department
        if (result > 0 && departments != null) {
            oneTimeFeeMapper.batchAddOneTimeFeeDepartment(fee, departments);
        }
        return fee.getId();
    }

    /**
     * 更新一次性收费项目
     * @param fee
     * @param departments
     */
    public void updateOneTimeFee(OneTimeFee fee, List<Long> departments) {
        if (BooleanUtils.isTrue(fee.getDisabled())) {
            deleteOneTimeFee(fee);
            return;
        }
        // 判断费项名是否存在
        if (checkFeeExist(fee)) {
            throw new BusinessException("费项已存在");
        }

        // update fee
        int result = oneTimeFeeMapper.updateOneTimeFee(fee);
        if (result > 0 && departments != null) {
            // list department
            List<Long> deptList = oneTimeFeeMapper.listOneTimeFeeDepartments(fee.getId());
            DataSepDto<Long> sepResult = compareFeeDepartments(deptList, departments);
            if (!CollectionUtils.isEmpty(sepResult.getAddList())) {
                oneTimeFeeMapper.batchAddOneTimeFeeDepartment(fee, sepResult.getAddList());
            }
            if (!CollectionUtils.isEmpty(sepResult.getRemoveList())) {
                oneTimeFeeMapper.batchDelOneTimeFeeDepartment(fee.getId(), sepResult.getRemoveList());
            }
        }
    }

    /**
     * 删除一次性费项
     * @param fee
     */
    private void deleteOneTimeFee(OneTimeFee fee) {
        OneTimeFeeVo dbFee = oneTimeFeeMapper.findById(fee.getId());
        fee.setName(dbFee.getName().concat("_").concat(String.valueOf(fee.getId())));
        oneTimeFeeMapper.updateOneTimeFee(fee);
    }

    /**
     * 根据id查找一次性费项
     * @param id
     * @return
     */
    public OneTimeFeeVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        OneTimeFeeVo result = oneTimeFeeMapper.findWithDepartmentById(id);
        if (result != null) {
            result.setDepartmentIds(departmentStr2List(result.getDepartmentIdStr()));
            result.setDepartmentIdStr(null);
        }
        return result;
    }

    /**
     * 根据名字查找一次性收入项目
     * @param name
     * @return
     */
    public OneTimeFeeVo findByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        OneTimeFeeVo result = oneTimeFeeMapper.findWithDepartmentByName(name);
        if (result != null) {
            result.setDepartmentIds(departmentStr2List(result.getDepartmentIdStr()));
            result.setDepartmentIdStr(null);
        }
        return result;
    }

    /**
     * 按部门加载一次性收入项目
     * @param departmentIds
     * @return
     */
    public List<OneTimeFeeVo> listOneTimeFeeByDepartment(List<Long> departmentIds) {
        OneTimeFeeSearchVo search = new OneTimeFeeSearchVo();
        search.setDepartmentIds(departmentIds);
        search.setPageNo(1);
        search.setPageSize(RealtyConst.BATCH_SIZE_5000);
        search.prepare();
        return oneTimeFeeMapper.listOneTimeFee(search);
    }



    /**
     * 一次性费项列表
     * @param search
     * @return
     */
    public PageResult<OneTimeFeeVo> listOneTimeFee(OneTimeFeeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = oneTimeFeeMapper.countOneTimeFee(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<OneTimeFeeVo> resultList = oneTimeFeeMapper.listOneTimeFee(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 费项是否存在
     * @param fee
     * @return
     */
    private boolean checkFeeExist(OneTimeFee fee) {
        OneTimeFeeVo dbFee = oneTimeFeeMapper.findByName(fee.getName());
        if (dbFee == null) {
            return false;
        }
        return !WrapperClassUtils.biggerThanLong(fee.getId(), 0L)
                || !Objects.equals(fee.getId(), dbFee.getId());
    }

    /**
     * 费项部门比对
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<Long> compareFeeDepartments(List<Long> srcList, List<Long> targetList) {
        DataSepDto<Long> result = new DataSepDto<>();
        result.setAddList(targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList()));
        result.setRemoveList(srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList()));
        return result;
    }

    /**
     * 部门转list
     * @param departments
     * @return
     */
    private List<Long> departmentStr2List(String departments) {
        if (StringUtils.isBlank(departments)) {
            return Collections.emptyList();
        }

        return Stream.of(departments.split(","))
                .filter(x -> !StringUtils.isBlank(x))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }
}
