package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.RealtyAlias;
import com.senox.realty.mapper.RealtyAliasMapper;
import com.senox.realty.vo.RealtyReadingsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-8-12
 */
@Slf4j
@Service
public class RealtyAliasServiceImpl extends ServiceImpl<RealtyAliasMapper, RealtyAlias> implements RealtyAliasService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSaveRealtyAlias(Long realtyId, List<RealtyAlias> aliasList) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            return;
        }

        // 原别名记录
        List<RealtyAlias> dbAliasList = listRealtyAlias(realtyId);
        // 数据拆分结果
        DataSepDto<RealtyAlias> sepData = compareAndSepData(dbAliasList, aliasList);

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            batchAddAlias(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            batchUpdateAlias(sepData.getUpdateList());
        }
        if (CollectionUtils.isEmpty(sepData.getRemoveList())) {
            List<Long> delIds = sepData.getRemoveList().stream().map(RealtyAlias::getId).collect(Collectors.toList());
            removeByIds(delIds);
        }
    }

    @Override
    public void batchUpdateAliasReadings(List<RealtyReadingsVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<RealtyReadingsVo> readings = list.stream().filter(x -> BooleanUtils.isTrue(x.getAlias())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(readings)) {
            return;
        }
        getBaseMapper().batchUpdateAliasReadings(readings);
    }

    @Override
    public List<RealtyAlias> listRealtyAlias(Long realtyId) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            return Collections.emptyList();
        }

        return list(new QueryWrapper<RealtyAlias>().lambda().eq(RealtyAlias::getRealtyId, realtyId));
    }

    /**
     * 批量新增
     * @param list
     */
    private void batchAddAlias(List<RealtyAlias> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 分段保存
        for (int index = 0; index < list.size(); index += RealtyConst.BATCH_SIZE_1000) {
            List<RealtyAlias> aliasList = list.stream().skip(index).limit(RealtyConst.BATCH_SIZE_1000).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(aliasList)) {
                getBaseMapper().batchAddAlias(aliasList);
            }
        }
    }

    /**
     * 批量更新
     * @param list
     */
    private void batchUpdateAlias(List<RealtyAlias> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 分段保存
        for (int index = 0; index < list.size(); index += RealtyConst.BATCH_SIZE_1000) {
            List<RealtyAlias> aliasList = list.stream().skip(index).limit(RealtyConst.BATCH_SIZE_1000).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(aliasList)) {
                getBaseMapper().batchUpdateAlias(aliasList);
            }
        }
    }

    private DataSepDto<RealtyAlias> compareAndSepData(List<RealtyAlias> srcList, List<RealtyAlias> targetList) {
        List<RealtyAlias> addList = new ArrayList<>(targetList.size());
        List<RealtyAlias> updateList = new ArrayList<>(srcList.size());
        List<RealtyAlias> delList = new ArrayList<>(srcList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            // 原始记录为空，新增所有目标记录
            addList.addAll(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            // 目标记录为空，删掉所有原始记录
            delList.addAll(srcList);

        } else {
            for (RealtyAlias item : srcList) {
                RealtyAlias targetItem = targetList.stream().filter(x -> Objects.equals(x, item)).findFirst().orElse(null);
                if (targetItem != null) {
                    targetItem.setId(item.getId());
                    targetItem.setCreatorId(null);
                    targetItem.setModifierId(null);
                    updateList.add(targetItem);
                } else {
                    delList.add(item);
                }
            }

            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
        }

        return new DataSepDto<>(addList, updateList, delList);
    }
}
