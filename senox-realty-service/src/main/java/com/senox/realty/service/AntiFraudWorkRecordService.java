package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.realty.constant.AntiFraudWorkRecordMediaSource;
import com.senox.realty.convert.AntiFraudWorkRecordConvert;
import com.senox.realty.convert.AntiFraudWorkRecordMediaConvert;
import com.senox.realty.domain.AntiFraudWorkRecord;
import com.senox.realty.domain.AntiFraudWorkRecordMedia;
import com.senox.realty.mapper.AntiFraudWorkRecordMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class AntiFraudWorkRecordService extends ServiceImpl<AntiFraudWorkRecordMapper, AntiFraudWorkRecord> {
    private final AntiFraudWorkRecordMediaService mediaService;
    private final AntiFraudWorkRecordConvert recordConvert;
    private final AntiFraudWorkRecordMediaConvert recordMediaConvert;

    /**
     * 添加
     * @param workRecord 工作记录参数
     */
    public void add(AntiFraudWorkRecord workRecord) {
        ContextUtils.initEntityCreator(workRecord);
        ContextUtils.initEntityModifier(workRecord);
        workRecord.setCreateTime(LocalDateTime.now());
        workRecord.setModifiedTime(LocalDateTime.now());
        baseMapper.add(workRecord);
    }

    /**
     * 批量添加
     * @param workRecords 工作记录参数集
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<AntiFraudWorkRecordVo> workRecords) {
        if (CollectionUtils.isEmpty(workRecords)) {
            return;
        }
        for (AntiFraudWorkRecordVo workRecordVo : workRecords) {
            AntiFraudWorkRecord workRecord = recordConvert.toDo(workRecordVo);
            add(workRecord);
            if (WrapperClassUtils.biggerThanLong(workRecord.getId(), 0)) {
                mediaService.addBatch(getRecordMedia(workRecord.getId(), workRecordVo));
            }
        }
    }

    /**
     * 根据id批量更新
     * @param workRecords 工作记录参数集
     */
    public void updateByIds(List<AntiFraudWorkRecordVo> workRecords) {
        if (CollectionUtils.isEmpty(workRecords)) {
            return;
        }
        for (AntiFraudWorkRecordVo workRecordVo : workRecords) {
            AntiFraudWorkRecord workRecord = recordConvert.toDo(workRecordVo);
            ContextUtils.initEntityModifier(workRecord);
            workRecord.setModifiedTime(LocalDateTime.now());
            baseMapper.updateById(workRecord);
            mediaService.addOrDeleteBatch(getRecordMedia(workRecord.getId(), workRecordVo));
        }
    }

    /**
     * 根据id批量删除
     * @param ids id集
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        baseMapper.deleteByIds(ids);
        mediaService.deleteByProductIds(ids, Arrays.asList(
                AntiFraudWorkRecordMediaSource.STORE_IMAGE
                , AntiFraudWorkRecordMediaSource.PROMO_IMAGE
                , AntiFraudWorkRecordMediaSource.ANTI_FRAUD_GREEN_QR_CODE_IMAGE));
    }

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的数据
     */
    public AntiFraudWorkRecordVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        AntiFraudWorkRecordSearchVo search = new AntiFraudWorkRecordSearchVo();
        search.setPage(false);
        search.setId(id);
        List<AntiFraudWorkRecordVo> list = list(search);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(AntiFraudWorkRecordSearchVo search) {
        return baseMapper.countList(search);
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<AntiFraudWorkRecordVo> list(AntiFraudWorkRecordSearchVo search) {
        List<AntiFraudWorkRecordVo> list = baseMapper.list(search);
        AntiFraudWorkRecordMediaSearchVo mediaSearch = new AntiFraudWorkRecordMediaSearchVo();
        mediaSearch.setProductIds(list.stream().map(AntiFraudWorkRecordVo::getId).collect(Collectors.toList()));
        List<AntiFraudWorkRecordMedia> medias = mediaService.list(mediaSearch);
        Map<Long, List<AntiFraudWorkRecordMedia>> mediaMap = medias.stream().collect(Collectors.groupingBy(x -> x.getProductId() + x.getSource()));
        list.forEach(workRecord -> {
            workRecord.setStoreImages(recordMediaConvert.toV(mediaMap.get(workRecord.getId() + AntiFraudWorkRecordMediaSource.STORE_IMAGE.getNumber())));
            workRecord.setAntiFraudGreenQrCodeImages(recordMediaConvert.toV(mediaMap.get(workRecord.getId() + AntiFraudWorkRecordMediaSource.ANTI_FRAUD_GREEN_QR_CODE_IMAGE.getNumber())));
            workRecord.setPromoImages(recordMediaConvert.toV(mediaMap.get(workRecord.getId() + AntiFraudWorkRecordMediaSource.PROMO_IMAGE.getNumber())));
        });
        return list;
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计
     */
    public AntiFraudWorkRecordStatistics listStatistics(AntiFraudWorkRecordSearchVo search) {
        return baseMapper.listStatistics(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<AntiFraudWorkRecordVo, AntiFraudWorkRecordStatistics> pageList(AntiFraudWorkRecordSearchVo search) {
        return new PageStatisticsResult<>(PageUtils.commonPageResult(search
                , () -> countList(search)
                , () -> list(search))
                , listStatistics(search));
    }

    private List<AntiFraudWorkRecordMedia> getRecordMedia(Long wordRecordId, AntiFraudWorkRecordVo workRecordVo) {
        List<AntiFraudWorkRecordMedia> medias = new ArrayList<>();
        List<AntiFraudWorkRecordMediaVo> storeImages = workRecordVo.getStoreImages();
        List<AntiFraudWorkRecordMediaVo> promoImages = workRecordVo.getPromoImages();
        List<AntiFraudWorkRecordMediaVo> antiFraudGreenQrCodeImages = workRecordVo.getAntiFraudGreenQrCodeImages();
        medias.addAll(getRecordMedia(wordRecordId, AntiFraudWorkRecordMediaSource.STORE_IMAGE, storeImages));
        medias.addAll(getRecordMedia(wordRecordId, AntiFraudWorkRecordMediaSource.PROMO_IMAGE, promoImages));
        medias.addAll(getRecordMedia(wordRecordId, AntiFraudWorkRecordMediaSource.ANTI_FRAUD_GREEN_QR_CODE_IMAGE, antiFraudGreenQrCodeImages));
        return medias;
    }

    private List<AntiFraudWorkRecordMedia> getRecordMedia(Long wordRecordId, AntiFraudWorkRecordMediaSource source, List<AntiFraudWorkRecordMediaVo> storeMediaVos) {
        if (CollectionUtils.isEmpty(storeMediaVos)) {
            return Collections.emptyList();
        }

        return storeMediaVos.stream().map(x -> {
            AntiFraudWorkRecordMedia media = new AntiFraudWorkRecordMedia();
            media.setProductId(wordRecordId);
            media.setSource(source.getNumber());
            media.setFileName(x.getFileName());
            media.setFileUrl(x.getFileUrl());
            return media;
        }).collect(Collectors.toList());
    }
}
