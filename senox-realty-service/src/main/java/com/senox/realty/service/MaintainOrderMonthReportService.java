package com.senox.realty.service;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.realty.constant.MaintainOrderDepartment;
import com.senox.realty.constant.MaintainOrderStatus;
import com.senox.realty.domain.MaintainOrderMonthReport;
import com.senox.realty.mapper.MaintainOrderMonthReportMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15 13:44
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MaintainOrderMonthReportService extends ServiceImpl<MaintainOrderMonthReportMapper, MaintainOrderMonthReport> {

    private final MaintainOrderService maintainOrderService;

    /**
     * 生成月报表
     * @param year
     * @param month
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateMonthReport(Integer year, Integer month) {
        MaintainOrderDepartment[] departments = MaintainOrderDepartment.values();
        for (MaintainOrderDepartment department : departments) {
            log.info("【物维月报表】 开始生成 {} {}-{}报表", department.getDescription(), year, month);
            LocalDateTime startTime = LocalDateTime.of(year, month, 1, 0, 0, 0);
            LocalDateTime endTime = LocalDateTime.of(startTime.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate(), LocalTime.MAX);
            String yearMonth = DateUtils.formatYearMonth(startTime.toLocalDate(), "yyyy-MM");
            MaintainOrderMonthReport monthReport = findByReportYearMonth(yearMonth, department.getValue());
            MaintainOrderMonthReport report = new MaintainOrderMonthReport();
            if (monthReport != null) {
                log.info("【物维月报表】{} {}-{}报表已存在", department.getDescription(), year, month);
                report.setId(monthReport.getId());

            } else {
                report.setCreateTime(LocalDateTime.now());
                ContextUtils.initEntityCreator(report);
            }
            MaintainOrderSearchVo addSearchVo = prepareAddSearchVo(startTime, endTime, department.getValue(), Collections.emptyList());
            MaintainOrderSearchVo completeSearchVo = prepareCompleteSearchVo(startTime, endTime, department.getValue(), Lists.newArrayList(MaintainOrderStatus.DONE.getValue(), MaintainOrderStatus.INCAPABLE.getValue()));
            int addSingularNumbers = maintainOrderService.countMaintainOrder(addSearchVo);
            int completeSingularNumbers = maintainOrderService.countMaintainOrder(completeSearchVo);
            report.setReportYearMonth(yearMonth);
            report.setManagementDeptId(department.getValue());
            report.setManagementDeptName(department.getDescription());
            report.setAddSingularNumbers(addSingularNumbers);
            report.setCompleteSingularNumbers(completeSingularNumbers);
            report.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(report);
            saveOrUpdate(report);
        }
    }

    /**
     * 物维单新增查询参数
     * @param startTime
     * @param endTime
     * @param managementDeptId
     * @return
     */
    private MaintainOrderSearchVo prepareAddSearchVo(LocalDateTime startTime, LocalDateTime endTime, Long managementDeptId, List<Integer> statusList) {
        MaintainOrderSearchVo searchVo = new MaintainOrderSearchVo();
        searchVo.setDateStart(startTime);
        searchVo.setDateEnd(endTime);
        searchVo.setManagementDeptList(Collections.singletonList(managementDeptId));
        searchVo.setStatus(statusList);
        return searchVo;
    }

    /**
     * 物维单完成查询参数
     * @param startTime
     * @param endTime
     * @param managementDeptId
     * @return
     */
    private MaintainOrderSearchVo prepareCompleteSearchVo(LocalDateTime startTime, LocalDateTime endTime, Long managementDeptId, List<Integer> statusList) {
        MaintainOrderSearchVo searchVo = new MaintainOrderSearchVo();
        searchVo.setFinishStart(startTime);
        searchVo.setFinishEnd(endTime);
        searchVo.setManagementDeptList(Collections.singletonList(managementDeptId));
        searchVo.setStatus(statusList);
        return searchVo;
    }

    /**
     * 物维单月报表分页
     * @param search
     * @return
     */
    public PageStatisticsResult<MaintainOrderMonthReportVo, MaintainOrderMonthReportVo> listPage(MaintainOrderMonthReportSearchVo search) {
        PageResult<MaintainOrderMonthReportVo> pageResult = PageUtils.commonPageResult(search, () -> getBaseMapper().count(search), () -> getBaseMapper().list(search));
        MaintainOrderMonthReportVo monthReportVo = sum(search);
        return new PageStatisticsResult<>(pageResult, monthReportVo);
    }

    /**
     * 物维单月报表合计
     * @param searchVo
     * @return
     */
    public MaintainOrderMonthReportVo sum(MaintainOrderMonthReportSearchVo searchVo) {
        return getBaseMapper().sum(searchVo);
    }

    /**
     * 根据月报年月查询维修月报表
     * @param reportYearMonth
     * @param managementDeptId
     * @return
     */
    private MaintainOrderMonthReport findByReportYearMonth(String reportYearMonth, Long managementDeptId) {
        if (StringUtils.isBlank(reportYearMonth)) {
            return null;
        }
        return getOne(new QueryWrapper<MaintainOrderMonthReport>().lambda()
                .eq(MaintainOrderMonthReport::getReportYearMonth, reportYearMonth)
                .eq(MaintainOrderMonthReport::getManagementDeptId, managementDeptId));
    }
}
