package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.MaintainChargeItem;
import com.senox.realty.mapper.MaintainChargeItemMapper;
import com.senox.realty.vo.MaintainChargeItemVo;
import com.senox.realty.vo.MaintainChargeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/13 8:51
 */
@Service
@Slf4j
public class MaintainChargeItemService extends ServiceImpl<MaintainChargeItemMapper, MaintainChargeItem> {


    @Transactional(rollbackFor = Exception.class)
    public void saveMaintainChargeItem(Long chargeId, List<MaintainChargeItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //合并同类项
        list = mergeCollect(list);

        if (!WrapperClassUtils.biggerThanLong(chargeId, 0L)) {
            throw new BusinessException("收费单id有误");
        }
        list.forEach(x -> x.setChargeId(chargeId));
        DataSepDto<MaintainChargeItem> sepData = SeparateUtils.separateData(maintainChargeItemList(chargeId), list);
        log.info("转换后的结果:{}", JsonUtils.object2Json(sepData));
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(MaintainChargeItem::getId).collect(Collectors.toList()));
        }
    }

    private static List<MaintainChargeItem> mergeCollect(List<MaintainChargeItem> list) {
        //如果价格和商品都一样就合并
        return new ArrayList<>(list.stream().collect(Collectors.toMap(Function.identity(), x -> x, (item1, item2) -> {
            //单价一样，合并数量和总金额
            item1.setQuantity(item1.getQuantity() + item2.getQuantity());
            item1.setAmount(item1.getAmount().add(item2.getAmount()));
            return item1;
        })).values());
    }

    /**
     * 根据id物维收费单集合
     * @param ids
     * @return
     */
    public List<MaintainChargeVo> listMaintainChargeByIds(List<Long> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return getBaseMapper().listMaintainChargeByIds(ids);
    }

    /**
     * 物维收费id查询维修费用列表
     *
     * @param chargeId
     * @return
     */
    public List<MaintainChargeItem> maintainChargeItemList(Long chargeId) {
        LambdaQueryWrapper<MaintainChargeItem> wrapper = new LambdaQueryWrapper<>();
        if (!WrapperClassUtils.biggerThanLong(chargeId, 0L)) {
            return Collections.emptyList();
        }
        wrapper.eq(MaintainChargeItem::getChargeId, chargeId);
        wrapper.eq(MaintainChargeItem::getDisabled, Boolean.FALSE);
        return list(wrapper);
    }

    /**
     * 根据账单id删除明细
     * @param chargeId
     */
    public void removeByChargeId(Long chargeId) {
        if (!WrapperClassUtils.biggerThanLong(chargeId, 0L)){
            return;
        }
        remove(new QueryWrapper<MaintainChargeItem>().lambda().eq(MaintainChargeItem::getChargeId, chargeId));
    }

    /**
     * 根据物维收费id获取收费项
     * @param chargeId
     * @return
     */
    public List<MaintainChargeItemVo> listChargeItemByChargeId(Long chargeId){
        if (!WrapperClassUtils.biggerThanLong(chargeId, 0L)){
            return Collections.emptyList();
        }
        return getBaseMapper().listChargeItemByChargeId(chargeId);
    }

    /**
     * 根据派工id查询物维收费单集合
     * @param jobId
     * @return
     */
    public List<MaintainChargeItemVo> listChargeItemByJobId(Long jobId){
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            return Collections.emptyList();
        }
        return getBaseMapper().listChargeItemByJobId(jobId);
    }

    /**
     * 根据订单id查询物维收费单集合
     * @param orderId
     * @return
     */
    public List<MaintainChargeItemVo> listChargeItemByOrderId(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        return getBaseMapper().listChargeItemByOrderId(orderId);
    }
}
