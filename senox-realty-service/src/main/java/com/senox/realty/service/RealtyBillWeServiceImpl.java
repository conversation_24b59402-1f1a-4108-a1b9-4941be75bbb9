package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.realty.constant.ContractType;
import com.senox.realty.domain.RealtyBillWe;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.mapper.RealtyBillWeMapper;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.ContractVo;
import com.senox.realty.vo.RealtyBillWeSearchVo;
import com.senox.realty.vo.RealtyBillWeVo;
import com.senox.realty.vo.RealtyReadingsVo;
import com.senox.realty.vo.RealtyWeType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-7-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RealtyBillWeServiceImpl extends ServiceImpl<RealtyBillWeMapper, RealtyBillWe> implements RealtyBillWeService {

    private final RealtyService realtyService;
    private final RealtyWeService weService;

    /**
     * 生成水电账单
     * @param weTime
     * @param operator
     */
    @Override
    public void buildAndSaveWeBill(BillMonthVo weTime, AdminUserDto operator) {
        log.info("【水电账单】生成 {}-{} 水电账单开始...", weTime.getYear(), weTime.getMonth());
        long execTimeStart = System.currentTimeMillis();

        // 删除无水电数据的记录（不包括同步的水电账单）
        getBaseMapper().removeAbandonWeBill(weTime);
        
        // 未生成水电账单列表
        List<RealtyBillWe> weList = getBaseMapper().listGeneratingBill(weTime);
        // 未生成的别名水电列表
        List<RealtyBillWe> aliasList = getBaseMapper().listGeneratingAliasBill(weTime);
        // 待重新生成水电账单列表
        List<RealtyBillWe> rebuildList = getBaseMapper().listGeneratedBill(weTime);

        // 多个水电表的处理
        aliasList.forEach(x -> {
            boolean isShareIgnore = checkWeShareIgnore(x, weList) || checkWeShareIgnore(x, rebuildList);
            x.setIgnoreElectricShare(isShareIgnore);
            x.setIgnoreWaterShare(isShareIgnore);
        });

        // 多线程生成账单
        List<CompletableFuture<Void>> futures = new ArrayList<>(3);
        // 新增 主水电表数据
        futures.add(CompletableFuture.runAsync(() -> {
            log.info("【水电账单】生成 {}-{} 主水电表账单生成...", weTime.getYear(), weTime.getMonth());
            batchAddWeBill(weList, operator);
            updateReadingsCascade(weList, false);
        }).whenComplete((x, e) -> log.info("【水电账单】生成 {}-{} 主水电表账单完成。", weTime.getYear(), weTime.getMonth())));

        // 新增 副水电表数据
        futures.add(CompletableFuture.runAsync(() -> {
            log.info("【水电账单】生成 {}-{} 副水电表账单生成...", weTime.getYear(), weTime.getMonth());
            batchAddWeBill(aliasList, operator);
            updateReadingsCascade(aliasList, false);
        }).whenComplete((x, e) -> log.info("【水电账单】生成 {}-{} 副水电表账单完成。.", weTime.getYear(), weTime.getMonth())));

        // 更新
        if (!CollectionUtils.isEmpty(rebuildList)) {
            futures.add(CompletableFuture.runAsync(() -> {
                log.info("【水电账单】更新 {}-{} 电表账单生成...", weTime.getYear(), weTime.getMonth());
                rebuildList.forEach(x -> {
                    prepareBill(x);
                    prepareBillModifier(x, operator);
                });
                updateBatchById(rebuildList);
                updateReadingsCascade(rebuildList, false);
            }));
        }

        // 等待所有线程执行完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("【水电账单】生成 {}-{} 水电账单完成，耗时 {}", weTime.getYear(), weTime.getMonth(), System.currentTimeMillis() - execTimeStart);
    }

    /**
     * 根据合同、水电读数生成水电账单
     * @param realtyBillId
     * @param contract
     * @param we
     * @return
     */
    @Override
    public List<RealtyBillWe> saveContractWeBill(Long realtyBillId, ContractVo contract, List<RealtyWe> we) {
        if (CollectionUtils.isEmpty(we)) {
            return Collections.emptyList();
        }

        // 生成水电账单
        List<RealtyBillWe> resultList = we.stream().map(x -> buildWeBill(realtyBillId, contract, x)).collect(Collectors.toList());
        // 入库
        saveBatch(resultList);

        // 冗余更新物业读数
        updateReadingsCascade(resultList);
        return resultList;
    }

    @Override
    public void updateWeBillId(BillMonthVo weTime) {
        BillMonthVo billTime = calRealtyBillTime(weTime.getYear(), weTime.getMonth());
        // 按合同类型匹配物业账单、水电账单  租赁合同 > 物业合同
        getBaseMapper().updateWeBillIdByContractType(weTime, billTime, ContractType.LEASE.getValue());
        getBaseMapper().updateWeBillIdByContractType(weTime, billTime, ContractType.ESTATE.getValue());
    }

    @Override
    public void updateWeBill(RealtyBillWe bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            return;
        }

        RealtyBillWeVo weBill = findById(bill.getId());
        if (weBill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (weBill.isBillPaid()) {
            throw new BusinessException("账单已支付");
        }

        // 数据初始化
        prepareBill(bill);

        getBaseMapper().updateById(bill);
    }

    /**
     * 更新水电账单id
     * @param ids
     * @param billId
     */
    @Override
    public void updateWeBillId(List<Long> ids, Long billId) {
        if (CollectionUtils.isEmpty(ids) || !WrapperClassUtils.biggerThanLong(billId, 0L)) {
            return;
        }

        getBaseMapper().updateWeBillId(ids, billId);
    }

    /**
     * 解除水电物业账单关联
     * @param billId
     */
    @Override
    public void unlinkWeBill(Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            return;
        }

        getBaseMapper().unlinkWeBill(billId);
    }

    /**
     * 根据年月解除水电物业账单关联
     * @param year
     * @param month
     */
    @Override
    public void unlinkWeBill(Integer year, Integer month) {
        getBaseMapper().unlinkWeBillByYearMonth(year, month);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteWeBill(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        // 删除
        getBaseMapper().deleteById(id);
        weService.unlinkWeBill(Collections.singletonList(id));
    }

    @Override
    public RealtyBillWeVo findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getBaseMapper().findById(id) : null;
    }

    @Override
    public List<RealtyBillWe> listNonmatchWeBill(BillMonthVo billMonth) {
        LambdaQueryWrapper<RealtyBillWe> queryWrapper = new QueryWrapper<RealtyBillWe>().lambda()
                .eq(Objects.nonNull(billMonth.getYear()), RealtyBillWe::getBillYear, billMonth.getYear())
                .eq(Objects.nonNull(billMonth.getYear()), RealtyBillWe::getBillMonth, billMonth.getMonth())
                .eq(!StringUtils.isBlank(billMonth.getRealtySerial()), RealtyBillWe::getRealtySerial, billMonth.getRealtySerial())
                .eq(RealtyBillWe::getBillId, 0L)
                .eq(RealtyBillWe::getDisabled, 0L);
        return list(queryWrapper);
    }

    /**
     * 获取物业账单水电明细
     * @param billId
     * @return
     */
    @Override
    public List<RealtyBillWeVo> listWeBill(Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            return Collections.emptyList();
        }

        RealtyBillWeSearchVo search = new RealtyBillWeSearchVo();
        search.setBillId(billId);
        return listWeBill(search);
    }

    /**
     * 水电账单列表
     * @param search
     * @return
     */
    @Override
    public List<RealtyBillWeVo> listWeBill(RealtyBillWeSearchVo search) {
        return getBaseMapper().listWeBill(search);
    }

    /**
     * 水电账单合计
     * @param search
     * @return
     */
    @Override
    public RealtyBillWeVo sumWeBill(RealtyBillWeSearchVo search) {
        return getBaseMapper().sumWeBill(search);
    }

    /**
     * 水电账单列表页
     * @param search
     * @return
     */
    @Override
    public PageResult<RealtyBillWeVo> listWeBillPage(RealtyBillWeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        int totalSize = getBaseMapper().countWeBill(search);
        search.prepare();


        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<RealtyBillWeVo> resultList = getBaseMapper().listWeBill(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }


    /**
     * 通过水电读数生成账单
     * @param realtyBillId
     * @param contract
     * @param we
     * @return
     */
    private RealtyBillWe buildWeBill(Long realtyBillId, ContractVo contract, RealtyWe we) {
        RealtyBillWe result = new RealtyBillWe();
        result.setBillYear(we.getBillYear());
        result.setBillMonth(we.getBillMonth());
        result.setBillId(realtyBillId == null ? 0L : realtyBillId);
        result.setRealtySerial(contract.getRealtySerial());
        result.setRealtyAliasSerial(!Objects.equals(contract.getRealtySerial(), we.getRealtySerial()) ? StringUtils.EMPTY : we.getRealtySerial());
        result.setCustomerId(contract.getCustomerId());
        result.setLastRecordDate(we.getLastRecordDate());
        result.setRecordDate(we.getRecordDate());

        result.setCreatorId(we.getModifierId());
        result.setCreatorName(we.getModifierName());
        result.setCreateTime(LocalDateTime.now());
        result.setModifierId(we.getModifierId());
        result.setModifierName(we.getModifierName());
        result.setModifiedTime(LocalDateTime.now());

        // 水费
        result.setLastWaterReadings(we.getLastWaterReadings());
        result.setWaterReadings(we.getWaterReadings());
        result.setWaterCost(we.getWaterReadings() - we.getLastWaterReadings());
        result.setWaterPrice(contract.getWaterPrice() == null ? BigDecimal.ZERO : contract.getWaterPrice().getPrice());
        result.setWaterShare(we.getWaterBase() > result.getWaterCost() ? (int) (we.getWaterBase() - result.getWaterCost()) : 0);

        // 电费
        result.setLastElectricReadings(we.getLastElectricReadings());
        result.setElectricReadings(we.getElectricReadings());
        result.setElectricCost(we.getElectricReadings() - we.getLastElectricReadings());
        result.setElectricPrice(contract.getElectricPrice() == null ? BigDecimal.ZERO : contract.getElectricPrice().getPrice());
        result.setElectricShare(we.getElectricBase() > result.getElectricCost() ? (int) (we.getElectricBase() - result.getElectricCost()) : 0);

        // 水电金额
        prepareBill(result);
        return result;
    }


    /**
     * 批量添加水电账单
     * @param billList
     */
    private void batchAddWeBill(List<RealtyBillWe> billList, final AdminUserDto modifier) {
        if (CollectionUtils.isEmpty(billList)) {
            return;
        }

        // 数据初始化
        billList.forEach(x -> {
            // 公摊 和 费用计算
            prepareBill(x);
            // 操作人信息
            prepareBillModifier(x, modifier);
        });
        saveBatch(billList);

        // 更新读数账单id
        weService.batchUpdateBillBySerial(billList.stream().map(this::monthlyBill2We).collect(Collectors.toList()));
    }

    @Override
    public boolean saveBatch(Collection<RealtyBillWe> entityList) {
        if (CollectionUtils.isEmpty(entityList)){
            return false;
        }
        return super.saveBatch(entityList);
    }

    /**
     * 过滤并检查数据是否需要计算公摊
     * 有多个表的物业，只需要计算其中一个表的公摊就可以(只计算主表的公摊)
     * @param list
     */
    private boolean checkWeShareIgnore(RealtyBillWe data, List<RealtyBillWe> list) {
        return list.stream()
                // 同一物业
                .anyMatch(x -> Objects.equals(x.getRealtySerial(), data.getRealtySerial()) && StringUtils.isBlank(x.getRealtyAliasSerial()));
    }

    /**
     * 根据水电账单时间计算物业账单时间
     *
     * @param year
     * @param month
     * @return
     */
    private BillMonthVo calRealtyBillTime(Integer year, Integer month) {
        month++;
        if (month > Month.DECEMBER.getValue()) {
            year++;
            month = Month.JANUARY.getValue();
        }
        return new BillMonthVo(year, month);
    }

    /**
     * 账单初始化
     * @param bill
     */
    private void prepareBill(RealtyBillWe bill) {
        prepareBillShare(bill);
        prepareBillCost(bill);
        prepareBillAmount(bill);
    }

    /**
     * 计算公摊
     *
     * @param bill
     */
    private void prepareBillShare(RealtyBillWe bill) {
        if (BooleanUtils.isTrue(bill.getIgnoreWaterShare())) {
            bill.setWaterShare(0);
        }
        if (BooleanUtils.isTrue(bill.getIgnoreWaterShare())) {
            bill.setElectricShare(0);
        }
    }

    /**
     * 计算实耗
     * @param bill
     */
    private void prepareBillCost(RealtyBillWe bill) {
        bill.setWaterCost(Math.max(bill.getWaterReadings() - bill.getLastWaterReadings(), 0));
        bill.setElectricCost(Math.max(bill.getElectricReadings() - bill.getLastElectricReadings(), 0));
    }

    /**
     * 账单金额计算
     * @param bill
     */
    private void prepareBillAmount(RealtyBillWe bill) {
        // 水
        long waterMeter = bill.getWaterCost() + bill.getWaterShare();
        bill.setWaterAmount(DecimalUtils.multiple(bill.getWaterPrice(), BigDecimal.valueOf(waterMeter)).setScale(1, RoundingMode.HALF_UP));

        // 电
        long electricMeter = bill.getElectricCost() + bill.getElectricShare();
        bill.setElectricAmount(DecimalUtils.multiple(bill.getElectricPrice(), BigDecimal.valueOf(electricMeter)).setScale(1, RoundingMode.HALF_UP));

        // 合计
        bill.setTotalAmount(DecimalUtils.add(bill.getWaterAmount(), bill.getElectricAmount()));
    }

    /**
     * 账单处理人信息
     * @param bill
     * @param modifier
     */
    private void prepareBillModifier(RealtyBillWe bill, AdminUserDto modifier) {
        bill.setModifierId(modifier.getUserId());
        bill.setModifierName(modifier.getUsername());
        bill.setModifiedTime(LocalDateTime.now());

        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            bill.setCreatorId(bill.getModifierId());
            bill.setCreatorName(bill.getModifierName());
            bill.setCreateTime(LocalDateTime.now());
        }
    }

    /**
     * 水电账单转水电读数
     * @param bill
     * @return
     */
    private RealtyWe monthlyBill2We(RealtyBillWe bill) {
        RealtyWe result = new RealtyWe();
        result.setBillYear(bill.getBillYear());
        result.setBillMonth(bill.getBillMonth());
        result.setType(RealtyWeType.MONTH_PAYMENT_BILL.getValue());
        result.setRealtySerial(StringUtils.isBlank(bill.getRealtyAliasSerial()) ? bill.getRealtySerial() : bill.getRealtyAliasSerial());
        result.setWeBillId(bill.getId());
        return result;
    }

    /**
     * 冗余更新物业水电读数
     * @param billList
     */
    private void updateReadingsCascade(List<RealtyBillWe> billList) {
        updateReadingsCascade(billList, true);
    }

    /**
     * 冗余更新物业水电读数
     * @param billList
     * @param isUpdateChecked
     */
    private void updateReadingsCascade(List<RealtyBillWe> billList, boolean isUpdateChecked) {
        if (CollectionUtils.isEmpty(billList)) {
            return;
        }

        if (!isUpdateChecked) {
            // 是否需要冗余同步水电读数
            Integer year = billList.get(0).getBillYear();
            Integer month = billList.get(0).getBillMonth();
            String realtySerial = billList.get(0).getRealtySerial();
            RealtyBillWe latestBill = getBaseMapper().findLatestWeBill(realtySerial);
            if (DateUtils.parseDate(year, month, 1).isBefore(DateUtils.parseDate(latestBill.getBillYear(), latestBill.getBillMonth(), 1))) {
                log.info("不是最新得物业水电账单，无需更新水电读数");
                return;
            }
        }

        realtyService.updateRealtyReadings(billList.stream().map(this::weBill2Readings).collect(Collectors.toList()));
    }

    /**
     * 水电账单转冗余读数
     * @param bill
     * @return
     */
    private RealtyReadingsVo weBill2Readings(RealtyBillWe bill) {
        RealtyReadingsVo readings = new RealtyReadingsVo();
        boolean isAlias = !StringUtils.isBlank(bill.getRealtyAliasSerial());
        readings.setRealtySerial(isAlias ? bill.getRealtyAliasSerial() : bill.getRealtySerial());
        readings.setWaterReadings(bill.getWaterReadings());
        readings.setElectricReadings(bill.getElectricReadings());
        readings.setAlias(isAlias);
        readings.setModifierId(bill.getModifierId());
        readings.setModifierName(bill.getModifierName());
        return readings;
    }

}
