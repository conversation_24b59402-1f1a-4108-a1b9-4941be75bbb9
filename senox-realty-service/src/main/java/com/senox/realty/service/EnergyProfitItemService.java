package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.config.RealtyConfig;
import com.senox.realty.constant.EnergySource;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.domain.EnergyConsumeUnit;
import com.senox.realty.domain.EnergyProfit;
import com.senox.realty.domain.EnergyProfitItem;
import com.senox.realty.mapper.EnergyProfitItemMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/9/22 14:58
 */
@RequiredArgsConstructor
@Service
public class EnergyProfitItemService extends ServiceImpl<EnergyProfitItemMapper, EnergyProfitItem> {

    private final RealtyConfig realtyConfig;
    private final RealtyWeService realtyWeService;
    private final EnergyConsumeUnitService consumeUnitService;

    /**
     * 保存能源损耗明细
     * @param profit
     * @param profitItems
     * @param source
     */
    public void saveProfitItems(EnergyProfit profit, List<EnergyProfitItem> profitItems, EnergySource source) {
        profitItems.forEach(x -> x.setSource(source.getValue()));

        // db 中原始数据
        List<EnergyProfitItem> srcProfitItems = listByProfitIdAndSource(profit.getId(), source);

        DataSepDto<EnergyProfitItem> sepData = SeparateUtils.separateData(srcProfitItems, profitItems);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            batchAddProfitItems(profit, sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            batchUpdateProfitItems(profit, sepData.getUpdateList());
        }
    }


    /**
     * 添加能源损益明细
     * @param profit
     * @param list
     */
    public void batchAddProfitItems(EnergyProfit profit, List<EnergyProfitItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        list.forEach(x -> {
            x.setProfitId(profit.getId());
            x.setCreatorId(profit.getModifierId());
            x.setCreatorName(profit.getModifierName());
            x.setCreateTime(LocalDateTime.now());
            x.setModifierId(x.getCreatorId());
            x.setModifierName(x.getCreatorName());
            x.setModifiedTime(x.getCreateTime());
        });
        saveBatch(list);
    }

    /**
     * 更新能源损益明细
     * @param profit
     * @param list
     */
    public void batchUpdateProfitItems(EnergyProfit profit, List<EnergyProfitItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        list.forEach(x -> {
            x.setProfitId(profit.getId());
            x.setModifierId(profit.getModifierId());
            x.setModifierName(profit.getModifierName());
            x.setModifiedTime(LocalDateTime.now());
        });
        getBaseMapper().batchUpdateProfitItem(profit.getId(), list);
    }

    /**
     * 根据损益id获取损益明细
     * @param profitId
     * @return
     */
    public List<EnergyProfitItem> listByProfitId(Long profitId) {
        if (!WrapperClassUtils.biggerThanLong(profitId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<EnergyProfitItem> queryWrapper = new QueryWrapper<EnergyProfitItem>().lambda()
                .eq(EnergyProfitItem::getProfitId, profitId)
                .eq(EnergyProfitItem::getDisabled, Boolean.FALSE);
        return list(queryWrapper);
    }

    public List<EnergyProfitItem> listByProfitIdAndSource(Long profitId, EnergySource source) {
        if (!WrapperClassUtils.biggerThanLong(profitId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<EnergyProfitItem> queryWrapper = new QueryWrapper<EnergyProfitItem>().lambda()
                .eq(EnergyProfitItem::getProfitId, profitId)
                .eq(EnergyProfitItem::getSource, source.getValue())
                .eq(EnergyProfitItem::getDisabled, Boolean.FALSE);
        return list(queryWrapper);
    }

    /**
     * 系统损益明细
     * @param billTime
     * @param energyType
     * @return
     */
    public List<EnergyProfitItem> newSystemProfits(BillTimeVo billTime, EnergyType energyType) {
        // 消费单位
        List<EnergyConsumeUnit> unitList = consumeUnitService.listByType(energyType);
        // 消费单位实耗
        List<EnergyProfitItem> energyList = realtyWeService.listEnergyProfits(billTime, energyType);

        List<EnergyProfitItem> resultList = new ArrayList<>(unitList.size());
        for (EnergyConsumeUnit unit : unitList) {
            Optional<EnergyProfitItem> op = energyList.stream()
                    .filter(x -> Objects.equals(x.getUnitKey(), unit.getUnit()))
                    .findFirst();

            EnergyProfitItem item = op.orElseGet(() -> new EnergyProfitItem(unit.getUnit(), unit.getName()));
            item.setSource(EnergySource.SYSTEM.getValue());
            if (item.getBillYear() == null) {
                item.setBillYear(billTime.getYear());
            }
            if (item.getBillMonth() == null) {
                item.setBillMonth(billTime.getMonth());
            }
            resultList.add(item);
        }
        return resultList;
    }

    /**
     * 结算损益明细
     * @param billTime
     * @param energyType
     * @return
     */
    public List<EnergyProfitItem> newBalanceProfits(BillTimeVo billTime, EnergyType energyType) {
        List<String> units = realtyConfig.getEnergySources().get(energyType);

        List<EnergyProfitItem> resultList = new ArrayList<>(units.size());
        for (String unit : units) {
            EnergyProfitItem item = new EnergyProfitItem(StringUtils.EMPTY, unit);
            item.setSource(EnergySource.THIRD_PARTY.getValue());
            item.setBillYear(billTime.getYear());
            item.setBillMonth(billTime.getMonth());
            resultList.add(item);
        }
        return resultList;
    }

}
