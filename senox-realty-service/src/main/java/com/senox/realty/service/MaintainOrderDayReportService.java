package com.senox.realty.service;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.PageUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.realty.constant.MaintainOrderDepartment;
import com.senox.realty.constant.MaintainOrderStatus;
import com.senox.realty.domain.MaintainOrderDayReport;
import com.senox.realty.mapper.MaintainOrderDayReportMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.MaintainOrderDayReportSearchVo;
import com.senox.realty.vo.MaintainOrderDayReportVo;
import com.senox.realty.vo.MaintainOrderSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15 10:38
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MaintainOrderDayReportService extends ServiceImpl<MaintainOrderDayReportMapper, MaintainOrderDayReport> {

    private final MaintainOrderService maintainOrderService;

    /**
     * 生成日报表
     * @param year
     * @param month
     * @param day
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateDayReport(Integer year, Integer month, Integer day) {
        MaintainOrderDepartment[] departments = MaintainOrderDepartment.values();
        for (MaintainOrderDepartment department : departments) {
            log.info("【物维日报表】 开始生成 {} {}-{}-{}报表", department.getDescription(), year, month, day);
            LocalDateTime startTime = LocalDateTime.of(year, month, day, 0, 0, 0);
            LocalDateTime endTime = LocalDateTime.of(startTime.toLocalDate(), LocalTime.MAX);
            LocalDate reportDate = LocalDate.of(year, month, day);
            MaintainOrderDayReport orderDayReport = findByReportDate(reportDate, department.getValue());
            if (orderDayReport != null) {
                log.info("【物维日报表】 {} {}-{}-{}报表已存在", department.getDescription(), year, month, day);
                return;
            }
            MaintainOrderSearchVo completeSearchVo = prepareCompleteSearchVo(startTime, endTime, department.getValue(), Lists.newArrayList(MaintainOrderStatus.DONE.getValue(), MaintainOrderStatus.INCAPABLE.getValue()));
            MaintainOrderSearchVo addSearchVo = prepareAddSearchVo(startTime, endTime, department.getValue(), Collections.emptyList());

            int addSingularNumbers = maintainOrderService.countMaintainOrder(addSearchVo);
            int completeSingularNumbers = maintainOrderService.countMaintainOrder(completeSearchVo);
            MaintainOrderDayReport report = new MaintainOrderDayReport();
            report.setManagementDeptId(department.getValue());
            report.setManagementDeptName(department.getDescription());
            report.setReportDate(reportDate);
            report.setAddSingularNumbers(addSingularNumbers);
            report.setCompleteSingularNumbers(completeSingularNumbers);
            report.setCreateTime(LocalDateTime.now());
            report.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityCreator(report);
            ContextUtils.initEntityModifier(report);
            save(report);
        }
    }

    /**
     * 物维单新增查询参数
     * @param startTime
     * @param endTime
     * @param managementDeptId
     * @return
     */
    private MaintainOrderSearchVo prepareAddSearchVo(LocalDateTime startTime, LocalDateTime endTime, Long managementDeptId, List<Integer> statusList) {
        MaintainOrderSearchVo searchVo = new MaintainOrderSearchVo();
        searchVo.setDateStart(startTime);
        searchVo.setDateEnd(endTime);
        searchVo.setManagementDeptList(Collections.singletonList(managementDeptId));
        searchVo.setStatus(statusList);
        return searchVo;
    }

    /**
     * 物维单完成查询参数
     * @param startTime
     * @param endTime
     * @param managementDeptId
     * @return
     */
    private MaintainOrderSearchVo prepareCompleteSearchVo(LocalDateTime startTime, LocalDateTime endTime, Long managementDeptId, List<Integer> statusList) {
        MaintainOrderSearchVo searchVo = new MaintainOrderSearchVo();
        searchVo.setFinishStart(startTime);
        searchVo.setFinishEnd(endTime);
        searchVo.setManagementDeptList(Collections.singletonList(managementDeptId));
        searchVo.setStatus(statusList);
        return searchVo;
    }

    /**
     * 物维单日报表分页
     * @param search
     * @return
     */
    public PageStatisticsResult<MaintainOrderDayReportVo, MaintainOrderDayReportVo> listPage(MaintainOrderDayReportSearchVo search) {
        PageResult<MaintainOrderDayReportVo> result = PageUtils.commonPageResult(search, () -> getBaseMapper().count(search), () -> getBaseMapper().list(search));
        MaintainOrderDayReportVo dayReportVo = sum(search);
        return new PageStatisticsResult<>(result, dayReportVo);
    }

    /**
     * 物维单日报表合计
     * @param searchVo
     * @return
     */
    public MaintainOrderDayReportVo sum(MaintainOrderDayReportSearchVo searchVo) {
        return getBaseMapper().sum(searchVo);
    }

    /**
     * 根据报表日期查询
     * @param reportDate
     * @param managementDeptId
     * @return
     */
    private MaintainOrderDayReport findByReportDate(LocalDate reportDate, Long managementDeptId) {
        return getOne(new QueryWrapper<MaintainOrderDayReport>().lambda()
                .eq(MaintainOrderDayReport::getReportDate, reportDate)
                .eq(MaintainOrderDayReport::getManagementDeptId, managementDeptId));
    }
}
