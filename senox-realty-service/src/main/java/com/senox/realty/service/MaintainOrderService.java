package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.MaintainOrderStatus;
import com.senox.realty.constant.MaintainType;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.MaintainOrder;
import com.senox.realty.mapper.MaintainOrderMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.senox.realty.constant.RealtyConst.MQ.MQ_REALTY_AC_MAINTAIN_EVALUATE;

/**
 * <AUTHOR>
 * @date 2023/2/21 16:52
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MaintainOrderService extends ServiceImpl<MaintainOrderMapper, MaintainOrder> {

    @Value("${senox.maintain.serial.prefix}")
    private String orderNoPrefix;
    @Value("${senox.maintain.serial.length}")
    private Integer orderNoPostfixLength;
    @Value("${senox.maintain.serial.fill-char}")
    private Character fillChar;

    private final MaintainMediaService maintainMediaService;
    private final MaintainEvaluateMediaService evaluateMediaService;
    private final RabbitTemplate rabbitTemplate;

    /**
     * 新增维修单
     *
     * @param order
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addMaintainOrder(MaintainOrder order, List<String> urls) {
        // 信息初始化
        prepareOrder(order);

        getBaseMapper().addMaintainOrder(order);
        if (!CollectionUtils.isEmpty(urls)) {
            maintainMediaService.saveMaintainMedia(order.getId(), urls);
        }
        return order.getId();
    }

    /**
     * 更新维修单
     *
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMaintainOrder(MaintainOrder order, List<String> urls) {
        if (!WrapperClassUtils.biggerThanLong(order.getId(), 0L)) {
            return;
        }
        MaintainOrder dbItem = findById(order.getId());
        if (dbItem == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (MaintainOrderStatus.fromValue(dbItem.getStatus()) == MaintainOrderStatus.DONE) {
            throw new BusinessException("订单已完成，无法变更");
        }
        if (!CollectionUtils.isEmpty(urls)) {
            maintainMediaService.saveMaintainMedia(order.getId(), urls);
        } else {
            //替换已有媒体
            if (urls != null) {
                maintainMediaService.saveMaintainMedia(order.getId(), urls);
            }
        }
        order.setModifiedTime(LocalDateTime.now());
        updateById(order);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateMaintainOrder(MaintainOrder order) {
        MaintainOrder dbItem = findById(order.getId());
        if (dbItem == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (MaintainOrderStatus.fromValue(dbItem.getStatus()) == MaintainOrderStatus.DONE
                || MaintainOrderStatus.fromValue(dbItem.getStatus()) == MaintainOrderStatus.INCAPABLE
                || MaintainOrderStatus.fromValue(dbItem.getStatus()) == MaintainOrderStatus.CANCEL) {
            throw new BusinessException("订单已完成，无法变更");
        }
        if (Objects.equals(dbItem.getStatus(), order.getStatus())) {
            //更新的状态和db状态相同，不做更新
            order.setStatus(null);
        }
        if (MaintainOrderStatus.fromValue(order.getStatus()) == MaintainOrderStatus.DONE
                && !WrapperClassUtils.biggerThanInt(dbItem.getEvaluateRating(), 0)
                && !StringUtils.isBlank(dbItem.getCreateOpenid())) {
            sendEvaluateMessage(dbItem);
        }
        order.setModifiedTime(LocalDateTime.now());
        updateById(order);
    }

    /**
     * 发送评价通知
     * @param order
     */
    public void sendEvaluateMessage(MaintainOrder order) {
        MaintainEvaluateMessageVo messageVo = new MaintainEvaluateMessageVo();
        messageVo.setId(order.getId());
        messageVo.setOpenid(order.getCreateOpenid());
        messageVo.setOrderNo(order.getOrderNo());
        messageVo.setRegionName(order.getRegionName());
        messageVo.setStreetName(order.getStreetName());
        messageVo.setCustomerName(order.getCustomerName());
        messageVo.setAddress(order.getAddress());
        rabbitTemplate.convertAndSend(MQ_REALTY_AC_MAINTAIN_EVALUATE, messageVo);
    }

    /**
     * 根据id获取维修单
     *
     * @param id
     * @return
     */
    public MaintainOrder findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 查询记录
     *
     * @param searchVo
     * @return
     */
    public PageResult<MaintainOrderVo> pageMaintainOrder(MaintainOrderSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        // total
        int total = countMaintainOrder(searchVo);

        //开始分页查询
        searchVo.prepare();
        if (total <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }
        //查询分页结果
        List<MaintainOrderVo> maintainOrderVos = listMaintainOrder(searchVo);
        return PageUtils.resultPage(searchVo, total, maintainOrderVos);
    }

    /**
     * 维修单列表
     * @param searchVo
     * @return
     */
    public List<MaintainOrderVo> listMaintainOrder(MaintainOrderSearchVo searchVo) {
        return getBaseMapper().listMaintainOrder(searchVo);
    }

    /**
     * 维修单列表导出所有处理节点
     * @param searchVo
     * @return
     */
    public List<MaintainOrderVo> exportListMaintainOrder(MaintainOrderSearchVo searchVo) {
        searchVo.prepare();
        return getBaseMapper().exportListMaintainOrder(searchVo);
    }

    /**
     * 维修单数量
     * @param searchVo
     * @return
     */
    public int countMaintainOrder(MaintainOrderSearchVo searchVo) {
        return getBaseMapper().count(searchVo);
    }

    /**
     * 维修单费用统计分页
     * @param searchVo
     * @return
     */
    public PageResult<MaintainOrderStatisticVo> pageOrderStatistic(MaintainOrderSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countOrderStatistic(searchVo), () -> getBaseMapper().listOrderStatistic(searchVo));
    }

    /**
     * 维修单费用合计
     * @param searchVo
     * @return
     */
    public MaintainOrderStatisticVo sumOrderStatistic(MaintainOrderSearchVo searchVo) {
        return getBaseMapper().sumOrderStatistic(searchVo);
    }

    /**
     * 维修单信息初始化
     *
     * @param order
     */
    private void prepareOrder(MaintainOrder order) {
        // 订单号
        if (StringUtils.isBlank(order.getOrderNo())) {
            String prefix = orderNoPrefix.concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
            order.setOrderNo(prepareOrderNo(prefix, orderNoPostfixLength, fillChar));
        }
        //订单状态初始化
        order.setStatus(MaintainOrderStatus.INIT.getValue());

        // 维修类型
        if (MaintainType.fromValue(order.getMaintainType()) == null) {
            order.setMaintainType(MaintainType.CUSTOMER_OTHERS.getValue());
        }
        //不是公共维修类型就赋初始值
        if (order.getMaintainType() == MaintainType.CUSTOMER_OTHERS.getValue()
        || order.getMaintainType() == MaintainType.CUSTOMER_POWER.getValue()
        || order.getMaintainType() == MaintainType.CUSTOMER_CONSTRUCTION.getValue()) {
            order.setHandlerDeptId(0L);
            order.setHandlerDeptName(null);
        }
        if (order.getManagementDeptId() == null) {
            order.setManagementDeptId(0L);
        }
        log.info("维修订单初始化完成:{}", JsonUtils.object2Json(order));
    }

    /**
     * 删除维修单
     * @param id
     */
    public void deleteMaintainOrder(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        MaintainOrder order = findById(id);
        if (order != null && MaintainOrderStatus.INIT == MaintainOrderStatus.fromValue(order.getStatus())) {
            //只可删除初始化的维修单
            removeById(id);
        }
    }

    /**
     * 维修单评价
     * @param evaluateVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void evaluateOrder(MaintainOrderEvaluateVo evaluateVo) {
        MaintainOrder order = findById(evaluateVo.getId());
        if (order == null || order.getEvaluateRating() > 0) {
            throw new BusinessException("该维修单已评价！");
        }
        order.setEvaluate(evaluateVo.getEvaluate());
        order.setEvaluateTime(LocalDateTime.now());
        order.setEvaluateRating(evaluateVo.getEvaluateRating());
        order.setEvaluateOpenid(evaluateVo.getEvaluateOpenid());
        ContextUtils.initEntityModifier(order);
        order.setModifiedTime(LocalDateTime.now());
        updateById(order);
        if (!CollectionUtils.isEmpty(evaluateVo.getEvaluateMedias())) {
            evaluateMediaService.saveMedias(evaluateVo.getId(), evaluateVo.getEvaluateMedias());
        }
    }

    /**
     * 重置维修单评价
     * @param orderId
     */
    public void resetEvaluate(Long orderId) {
        MaintainOrder order = findById(orderId);
        if (order == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "维修单记录不存在！");
        }
        order.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(order);
        order.setEvaluate(StringUtils.EMPTY);
        order.setEvaluateOpenid(StringUtils.EMPTY);
        order.setEvaluateRating(0);
        updateById(order);
        evaluateMediaService.batchRemoveMedias(Collections.singletonList(orderId));
    }

    /**
     * 维修单评价分页
     * @param searchVo
     * @return
     */
    public PageResult<MaintainOrderVo> evaluateOrderPage(MaintainOrderEvaluateSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().evaluateOrderCount(searchVo), () -> getBaseMapper().evaluateOrderList(searchVo));
    }

    /**
     * 订单号初始化
     *
     * @param prefix
     * @param length
     * @param fillChar
     * @return
     */
    private String prepareOrderNo(String prefix, int length, char fillChar) {
        // get serial from cache
        String cacheKey = String.format(RealtyConst.Cache.KEY_MAINTAIN_ORDER_NO, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, RealtyConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxOrderSerial(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, RealtyConst.Cache.TTL_2D);
        }

        return prefix.concat(StringUtils.fixLength(String.valueOf(result), length, fillChar));
    }

    /**
     * 获取最大订单序号
     *
     * @param prefix
     * @return
     */
    private Long findMaxOrderSerial(String prefix) {
        String maxOrderNo = getBaseMapper().findMaxOrderNo(prefix);
        return (StringUtils.isBlank(maxOrderNo) ? 0L : NumberUtils.parseLong(maxOrderNo.substring(prefix.length()), 0L));
    }
}
