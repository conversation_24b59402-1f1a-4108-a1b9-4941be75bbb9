package com.senox.realty.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillCancelVo;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillPenaltyIgnoreItem;
import com.senox.common.vo.BillPenaltyIgnoreVo;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.vo.OrderCancelVo;
import com.senox.pm.vo.OrderVo;
import com.senox.realty.component.OrderComponent;
import com.senox.realty.config.RealtyConfig;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.RealtyFee;
import com.senox.realty.domain.Contract;
import com.senox.realty.domain.Fee;
import com.senox.realty.domain.Realty;
import com.senox.realty.domain.RealtyBill;
import com.senox.realty.domain.RealtyBillItem;
import com.senox.realty.mapper.RealtyBillMapper;
import com.senox.realty.vo.BillFeeChangeVo;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyContractSuspendDto;
import com.senox.realty.vo.RealtyBillBatchVo;
import com.senox.realty.vo.RealtyBillSearchVo;
import com.senox.realty.vo.RealtyBillSendVo;
import com.senox.realty.vo.RealtyBillVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/9 8:49
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class RealtyBillService {

    private final RealtyConfig realtyConfig;
    private final RealtyBillMapper billMapper;
    private final FeeService feeService;
    private final ContractService contractService;
    private final RealtyService realtyService;
    private final RealtyBillWeService billWeService;
    private final OrderComponent orderComponent;

    /**
     * 保存应收账单
     * @param billVo
     * @param fees
     * @param modifier
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBill(RealtyBillVo billVo, List<Fee> fees, AdminUserDto modifier) {
        // 数据格式化
        RealtyBill bill = buildBill(billVo, modifier);
        List<RealtyBillItem> billItems = Arrays.asList(
                buildBillItem(billVo.getRentAmount(), getFee(fees, RealtyFee.RENT)),
                buildBillItem(billVo.getManageAmount(), getFee(fees, RealtyFee.MANAGE)),
                buildBillItem(billVo.getElectricAmount(), getFee(fees, RealtyFee.ELECTRIC), billVo.getElectricStartDate(), billVo.getElectricEndDate()),
                buildBillItem(billVo.getWaterAmount(), getFee(fees, RealtyFee.WATER), billVo.getWaterStartDate(), billVo.getWaterEndDate())
        );


        // 查找已生成的账单
        RealtyBill dbBill = findMonthlyBill(billVo);
        if (dbBill == null) {
            addBill(bill, billItems);
        } else {
            BillStatus billStatus = BillStatus.fromStatus(dbBill.getStatus());
            if (BillStatus.PAID == billStatus) {
                log.info("账单已支付，不更新账单 {}。", dbBill.getContractNo());
                return;
            }
            updateBill(bill, billItems);
        }
    }

    /**
     * 添加应收账单
     * @param bill
     * @param billItems
     * @return
     */
    public void addBill(RealtyBill bill, List<RealtyBillItem> billItems) {
        // 根据应收账单费项明细，计算总金额
        bill.setAmount(checkAndSumBillItems(billItems));
        if (!DecimalUtils.isPositive(bill.getAmount())) {
            log.info("账单合计金额为0，忽略处理");
            return;
        }

        // 物业账单初始化
        prepareBill(bill);
        int result = billMapper.addBill(bill);

        if (billItems != null && result > 0) {
            billItems = prepareBillItems(bill, billItems, feeService.listFeeMap());
            billMapper.batchAddBillItems(billItems);
        }
    }

    /**
     * 更新物业账单
     *
     * @param bill
     * @param billItems
     */
    public void updateBill(RealtyBill bill, List<RealtyBillItem> billItems) {
        // 计算账单总额
        bill.setAmount(checkAndSumBillItems(billItems));
        prepareBill(bill);

        // 更新费项明细
        List<RealtyBillItem> dbBillItems = billMapper.listBillItems(bill.getId());
        billItems = prepareBillItems(bill, billItems, feeService.listFeeMap());
        DataSepDto<RealtyBillItem> sepData = compareAndSeparateBillItem(dbBillItems, billItems);

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            billMapper.batchAddBillItems(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            billMapper.batchUpdateBillItems(sepData.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            List<Long> delItemIds = sepData.getRemoveList().stream().map(RealtyBillItem::getId).collect(Collectors.toList());
            billMapper.batchDelBillItems(bill.getId(), delItemIds);
        }

        updateBill(bill);
    }

    /**
     * 更新物业账单
     *
     * @param bill
     */
    public void updateBill(RealtyBill bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            return;
        }
        billMapper.updateBill(bill);
    }

    /**
     * 改变费项金额
     * @param changeVo
     */
    public void changeFeeBill(BillFeeChangeVo changeVo) {
        if (!WrapperClassUtils.biggerThanLong(changeVo.getBillId(), 0L)
                || !WrapperClassUtils.biggerThanLong(changeVo.getFeeId(), 0L)
                || DecimalUtils.equals(changeVo.getChangeAmount(), BigDecimal.ZERO)) {
            return;
        }
        billMapper.changeBillFee(changeVo);
    }

    /**
     * 更新账单金额
     *
     * @param billTime
     */
    public void updateBillAmount(BillMonthVo billTime) {
        billMapper.updateBillAmount(billTime);
    }


    /**
     * 更新物业账单水电
     *
     * @param billTime
     */
    public void updateBillWeAmount(BillMonthVo billTime) {
        billMapper.updateBillWeAmount(billTime);
    }

    /**
     * 更新水电日期
     *
     * @param billTime
     * @param weTime
     */
    public void updateBillWeDefaultAttr(BillMonthVo billTime, BillMonthVo weTime) {
        LocalDate defaultAttr1 = DateUtils.parseDate(weTime.getYear(), weTime.getMonth() - 1, realtyConfig.getRecordDay(), true);
        LocalDate defaultAttr2 = DateUtils.parseDate(weTime.getYear(), weTime.getMonth(), realtyConfig.getRecordDay() - 1);
        billMapper.updateBillWeAttr(billTime, defaultAttr1.toString(), defaultAttr2.toString());
    }

    /**
     * 减免滞纳金
     * @param penaltyIgnore
     */
    public void updateRealtyBillPenaltyIgnore(BillPenaltyIgnoreVo penaltyIgnore) {
        if (CollectionUtils.isEmpty(penaltyIgnore.getBillIds())) {
            return;
        }

        // 减免滞纳金
        if (BooleanUtils.isTrue(penaltyIgnore.getPenaltyIgnore())) {
            // 减免明细计算
            List<RealtyBillVo> bills = listBillById(penaltyIgnore.getBillIds(), false);
            if (CollectionUtils.isEmpty(bills)) {
                return;
            }
            BigDecimal penaltyTotal = bills.stream().map(RealtyBillVo::getPenaltyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (penaltyTotal.compareTo(penaltyIgnore.getPenaltyIgnoreAmount()) < 0) {
                throw new BusinessException("减免金额大于滞纳金总额");
            }

            // 明细计算
            List<BillPenaltyIgnoreItem> ignoreItems = prepareBillPenaltyIgnoreDetail(bills, penaltyIgnore.getPenaltyIgnoreAmount());
            penaltyIgnore.setIgnoreDetails(ignoreItems);
            penaltyIgnore.setBillIds(ignoreItems.stream().map(BillPenaltyIgnoreItem::getId).collect(Collectors.toList()));

            if (CollectionUtils.isEmpty(penaltyIgnore.getIgnoreDetails())) {
                log.warn("无减免滞纳金明细");
                return;
            }
            log.info("减免滞纳金明细 {}", JsonUtils.object2Json(penaltyIgnore.getIgnoreDetails()));
        }
        billMapper.updateBillPenaltyIgnore(penaltyIgnore);
    }

    /**
     * 发票
     *
     * @param batchBills
     * @param repeat     是否更新账单费项发票状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBillReceipt(RealtyBillBatchVo batchBills, boolean repeat) {
        if (CollectionUtils.isEmpty(batchBills.getIds()) || batchBills.getReceipt() == null) {
            return;
        }
        List<RealtyBillItem> updateBillItemList = new ArrayList<>();
        if (BooleanUtils.isTrue(batchBills.getReceipt())) {
            List<Long> ids = batchBills.getIds().stream().distinct().collect(Collectors.toList());
            if (ids.size() != billMapper.countPaidBills(ids)) {
                throw new BusinessException("已支付账单才能开票");
            }
            batchBills.setIds(ids);
        }

        if (repeat) {
            for (Long billId : batchBills.getIds()) {
                List<RealtyBillItem> realtyBillItemList = billMapper.listBillItems(billId);
                realtyBillItemList.forEach(item -> {
                    item.setReceiptSerialNo("");
                    item.setReceiptStatus(BooleanUtils.isTrue(batchBills.getReceipt()) ? ReceiptStatus.ISSUED.getStatus() : ReceiptStatus.PENDING_ISSUE.getStatus());
                });
                updateBillItemList.addAll(realtyBillItemList);
            }
            billMapper.batchUpdateBillItemReceipt(updateBillItemList);
        }
        billMapper.updateBillReceipt(batchBills);
    }

    /**
     * 更新物业账单状态
     *
     * @param billPaid
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBillStatus(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return;
        }
        // 加载该次支付关联的账单
        List<RealtyBillVo> paidBills = null;
        if (BooleanUtils.isTrue(billPaid.getPaid())) {
            if (!CollectionUtils.isEmpty(billPaid.getBillIds())) {
                // 支付成功的不一定是最新的支付订单，有可能订单状态未来得及更新，用户重新下单
                paidBills = listBillById(billPaid.getBillIds(), false);
            } else {
                paidBills = listRealtyBillByOrderId(billPaid.getOrderId(), BooleanUtils.isTrue(billPaid.getRefund()));
            }
        }

        // 更新订单支付结果
        if (!CollectionUtils.isEmpty(billPaid.getBillIds())) {
            log.info("根据物业账单id更新物业账单状态 {}", JsonUtils.object2Json(billPaid));
            updateBillPaidById(billPaid, BooleanUtils.isTrue(billPaid.getRefund()));
        } else {
            log.info("根据支付订单id更新物业账单状态 {}", JsonUtils.object2Json(billPaid));
            updateBillPaidByOrder(billPaid, BooleanUtils.isTrue(billPaid.getRefund()));
        }

        // 支付成功 & 更新支付时间
        if (!CollectionUtils.isEmpty(paidBills)) {
            // 更新子订单
            List<Long> paidBillIds = paidBills.stream().map(RealtyBillVo::getId).distinct().collect(Collectors.toList());
            updateBillItemStatus(paidBillIds, billPaid.getPaidTime(), BooleanUtils.isTrue(billPaid.getRefund()));
        }
    }

    /**
     * 下发物业账单
     *
     * @param sendVo
     */
    public void sendBill(RealtyBillSendVo sendVo) {
        if ((sendVo.getBillYear() == null || sendVo.getBillMonth() == null)
                && CollectionUtils.isEmpty(sendVo.getBillIds())) {
            log.warn("未明下发的物业账单");
            return;
        }
        billMapper.sendBill(sendVo);
    }

    public void deleteZeroBill(BillMonthVo billMonth) {
        billMapper.deleteZeroBill(billMonth);
    }

    /**
     * 修改停用账单
     *
     * @param suspendDto
     */
    public void suspendRealtyBill(RealtyContractSuspendDto suspendDto) {
        // 将该合同本月账单的管理费、租金置0，停用合同的使用已重新计算本月的管理费、租金
        billMapper.suspendBill(suspendDto);

        BillMonthVo billMonth = new BillMonthVo(suspendDto.getYear(), suspendDto.getMonth());
        billMonth.setContractNo(suspendDto.getContractNo());
        deleteZeroBill(billMonth);
    }

    /**
     * 撤销订单
     *
     * @param cancelBill
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelBill(BillCancelVo cancelBill) {
        RealtyBillVo bill = findById(cancelBill.getBillId());
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BillStatus.fromStatus(bill.getStatus()) != BillStatus.PAID) {
            throw new BusinessException("订单未支付");
        }

        // 订单状态
        billMapper.cancelBillPaid(cancelBill);
        billMapper.cancelBillItemPaid(cancelBill);

        // 撤销订单
        OrderCancelVo cancelVo = new OrderCancelVo();
        cancelVo.setOrderId(bill.getRemoteOrderId());
        cancelVo.setProductIds(Collections.singletonList(cancelBill.getBillId()));
        cancelVo.setRemark(cancelVo.getRemark());
        orderComponent.cancelOrder(cancelVo);
    }

    /**
     * 下发应收账单
     *
     * @param billMonth
     */
    private void sendBill(BillMonthVo billMonth) {
        if (!isMonthlyBillSend(billMonth.getYear(), billMonth.getMonth())) {
            return;
        }
        RealtyBillSendVo send = new RealtyBillSendVo();
        send.setBillYear(billMonth.getYear());
        send.setBillMonth(billMonth.getMonth());
        sendBill(send);
    }

    /**
     * 删除应收账单
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBill(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        RealtyBillVo bill = findDetailById(id);
        if (bill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BillStatus.fromStatus(bill.getStatus()) == BillStatus.PAID || DecimalUtils.isPositive(bill.getPaidAmount())) {
            throw new BusinessException("订单已支付，无法删除");
        }

        int result = billMapper.deleteBillById(id);
        if (result > 0) {
            billMapper.deleteBillItems(id);
            billWeService.unlinkWeBill(id);
        }
    }

    /**
     * 删除应收账单
     *
     * @param year
     * @param month
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBill(Integer year, Integer month) {
        billWeService.unlinkWeBill(year, month);
        billMapper.deleteBillItemsByYearMonth(year, month);
        billMapper.deleteBillByYearMonth(year, month);
    }

    /**
     * 下发月账单
     *
     * @param year
     * @param month
     * @return
     */
    private boolean isMonthlyBillSend(Integer year, Integer month) {
        return WrapperClassUtils.biggerThanLong(billMapper.findMonthlyBillSend(year, month), 0L);
    }

    /**
     * 查找月账单
     *
     * @param bill
     * @return
     */
    private RealtyBill findMonthlyBill(RealtyBillVo bill) {
        return WrapperClassUtils.biggerThanLong(bill.getId(), 0L)
                ? findMonthBillById(bill.getId(), bill.getBillYear(), bill.getBillMonth())
                : findMonthlyBillByContractNo(bill.getContractNo(), bill.getBillYear(), bill.getBillMonth());
    }

    /**
     * 根据id查找物业月账单
     * @param id
     * @param year
     * @param month
     * @return
     */
    public RealtyBill findMonthBillById(Long id, Integer year, Integer month) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L) || year == null || month == null) {
            return null;
        }
        return billMapper.findMonthlyBillById(id, year, month);
    }

    /**
     * 根据合同号查找物业月账单
     * @param contractNo
     * @param year
     * @param month
     * @return
     */
    public RealtyBill findMonthlyBillByContractNo(String contractNo, Integer year, Integer month) {
        if (StringUtils.isBlank(contractNo) || year == null || month == null) {
            return null;
        }
        return billMapper.findMonthlyBillByContractNo(contractNo, year, month);
    }

    /**
     * 查找物业月账单列表
     * @param monthVo
     * @return
     */
    public List<RealtyBillVo> monthlyBillList(BillMonthVo monthVo) {
        if (StringUtils.isBlank(monthVo.getContractNo()) || monthVo.getYear() == null || monthVo.getMonth() == null) {
            return Collections.emptyList();
        }
        return billMapper.findMonthlyBillListByContractNo(monthVo.getContractNo(), monthVo.getYear(), monthVo.getMonth());
    }

    /**
     * 根据id获取账单
     *
     * @param id
     * @return
     */
    public RealtyBillVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return billMapper.findBillById(id);
    }

    /**
     * 根据id获取账单明细
     *
     * @param id
     * @return
     */
    public RealtyBillVo findDetailById(Long id) {
        RealtyBillVo result = findById(id);
        if (result != null) {
            // 费项明细
            List<RealtyBillItem> billItems = billMapper.listBillItems(id);
            if (!CollectionUtils.isEmpty(billItems)) {
                List<Fee> fees = feeService.listAll();
                prepareRentBill(result, findFeeInBill(billItems, getFee(fees, RealtyFee.RENT)));
                prepareManageBill(result, findFeeInBill(billItems, getFee(fees, RealtyFee.MANAGE)));
                prepareWaterBill(result, findFeeInBill(billItems, getFee(fees, RealtyFee.WATER)));
                prepareElectricBill(result, findFeeInBill(billItems, getFee(fees, RealtyFee.ELECTRIC)));
            }

            if (WrapperClassUtils.biggerThanLong(result.getRemoteOrderId(), 0L)) {
                OrderVo order = orderComponent.findById(result.getRemoteOrderId());
                if (order != null) {
                    result.setTradeNo(order.getTradeNo());
                    result.setPayWay(order.getPayWay().getValue());
                }
            }
        }
        return result;
    }

    /**
     * 根据id列表查找物业账单
     *
     * @param ids
     * @param isWithDetail
     * @return
     */
    public List<RealtyBillVo> listBillById(List<Long> ids, boolean isWithDetail) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return billMapper.listBillById(ids, isWithDetail);
    }

    /**
     * 带滞纳金费率的物业账单列表
     *
     * @param search
     * @return
     */
    public List<RealtyBillVo> listRealtyBillWithPenaltyRate(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return Collections.emptyList();
        }
        search.prepare();
        return billMapper.listBillWithPenaltyRate(search);
    }

    /**
     * 根据订单id查找物业账单
     *
     * @param orderId
     * @param isRefund
     * @return
     */
    public List<RealtyBillVo> listRealtyBillByOrderId(Long orderId, boolean isRefund) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        return isRefund ? billMapper.listBillByRefundOrderId(orderId) : billMapper.listBillByRemoteOrderId(orderId);
    }

    /**
     * 物业账单合计
     *
     * @param search
     * @return
     */
    public RealtyBillVo sumRealtyBill(RealtyBillSearchVo search) {
        return billMapper.sumBill(search);
    }

    /**
     * 物业账单数统计
     * @param search
     * @return
     */
    public int countRealtyBill(RealtyBillSearchVo search) {
        return billMapper.countRealtyBill(search);
    }

    /**
     * 物业账单详情列表
     * @param search
     * @return
     */
    public List<RealtyBillVo> listRealtyBillWithDetail(RealtyBillSearchVo search) {
        return billMapper.listBillWithDetail(search);
    }

    /**
     * 物业账单明细列表信息
     * @param searchVo
     * @return
     */
    public List<RealtyBillVo> listBillWithDetailInfo(RealtyBillSearchVo searchVo) {
        return billMapper.listBillWithDetailInfo(searchVo);
    }

    /**
     * 物业账单详情页信息
     *
     * @param search
     * @return
     */
    public PageResult<RealtyBillVo> listRealtyBillPageWithDetailInfo(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = billMapper.countRealtyBill(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<RealtyBillVo> resultList = billMapper.listBillWithDetailInfo(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 物业账单页
     *
     * @param search
     * @return
     */
    public PageResult<RealtyBillVo> listRealtyBillPage(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = countRealtyBill(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<RealtyBillVo> resultList = billMapper.listBill(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 已开票账单
     *
     * @param search
     * @return
     */
    public PageResult<RealtyBillVo> listReceiptBill(RealtyBillSearchVo search) {
        return PageUtils.commonPageResult(search, () -> billMapper.countReceiptRealtyBill(search), () -> billMapper.listReceiptBill(search));
    }

    /**
     * 已开票账单列表金额合计
     *
     * @param search 查询参数
     * @return
     */
    public RealtyBillVo sumReceiptBillAmount(RealtyBillSearchVo search) {
        RealtyBillVo billVo = billMapper.sumReceiptBillAmount(search);
        return null != billVo ? billVo : new RealtyBillVo();
    }

    /**
     * 物业账单详情合计
     *
     * @param search
     * @return
     */
    public RealtyBillVo sumRealtyBillWithDetail(RealtyBillSearchVo search) {
        return billMapper.sumBillWithDetail(search);
    }

    /**
     * 物业账单详情页
     *
     * @param search
     * @return
     */
    public PageResult<RealtyBillVo> listRealtyBillPageWithDetail(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = billMapper.countRealtyBill(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<RealtyBillVo> resultList = billMapper.listBillWithDetail(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 根据账单更新账单状态
     *
     * @param billPaid
     * @param isRefund
     * @return
     */
    private int updateBillPaidById(BillPaidVo billPaid, boolean isRefund) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            return 0;
        }

        return isRefund ? billMapper.updateBillRefundById(billPaid) : billMapper.updateBillPaidById(billPaid);
    }

    /**
     * 根据订单id更新账单状态
     *
     * @param billPaid
     * @param isRefund
     * @return
     */
    private int updateBillPaidByOrder(BillPaidVo billPaid, boolean isRefund) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return 0;
        }

        return isRefund ? billMapper.updateBillRefundByRemoteOrder(billPaid) : billMapper.updateBillPaidByRemoteOrder(billPaid);
    }

    /**
     * 更新订单明细状态
     *
     * @param billIds
     * @param paidTime
     * @param isRefund
     * @return
     */
    private int updateBillItemStatus(List<Long> billIds, LocalDateTime paidTime, boolean isRefund) {
        if (CollectionUtils.isEmpty(billIds)) {
            return 0;
        }

        return isRefund ? billMapper.refundBillItem(billIds) : billMapper.updateBillItemStatus(billIds, paidTime);
    }

    /**
     * 构建物业账单
     *
     * @param bill
     * @return
     */
    private RealtyBill buildBill(RealtyBillVo bill, AdminUserDto modifier) {
        RealtyBill result = new RealtyBill();
        result.setId(bill.getId());
        result.setBillYear(bill.getBillYear());
        result.setBillMonth(bill.getBillMonth());
        result.setBillYearMonth(DateUtils.formatYearMonth(bill.getBillYear(), bill.getBillMonth(), DateUtils.PATTERN_YEAR_MONTH));
        result.setContractNo(bill.getContractNo());
        result.setRemark(bill.getRemark());
        if (modifier != null) {
            result.setCreatorId(modifier.getUserId());
            result.setCreatorName(modifier.getUsername());
            result.setModifierId(modifier.getUserId());
            result.setModifierName(modifier.getUsername());
        }

        // 合同
        Contract contract = contractService.findByContractNo(result.getContractNo());
        if (contract == null) {
            log.warn("【应收账单】保存应收失败，合同 {} 不存在", result.getContractNo());
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "合同不存在");
        }
        result.setRealtyId(contract.getRealtyId());

        // 物业号校验
        if (!StringUtils.isBlank(bill.getRealtySerial())) {
            Realty realty = realtyService.findBySerial(bill.getRealtySerial());
            if (realty == null || !Objects.equals(realty.getId(), result.getRealtyId())) {
                log.warn("【应收账单】保存应收账单失败，合同 {} 与物业 {}不匹配", result.getContractNo(), bill.getRealtySerial());
                throw new BusinessException("合同与物业不匹配");
            }
        }
        return result;
    }

    /**
     * 构建物业账单费项
     *
     * @param amount
     * @param fee
     * @return
     */
    private RealtyBillItem buildBillItem(BigDecimal amount, Fee fee) {
        return buildBillItem(amount, fee, null, null);
    }

    /**
     * 构建物业账单费项
     *
     * @param amount
     * @param fee
     * @param attr1
     * @param attr2
     * @return
     */
    public RealtyBillItem buildBillItem(BigDecimal amount, Fee fee, String attr1, String attr2) {
        if (fee == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "无效的费项");
        }
        RealtyBillItem result = new RealtyBillItem();
        result.setAmount(amount == null ? BigDecimal.ZERO : amount);
        result.setFeeId(fee.getId());
        result.setFeeName(fee.getName());
        result.setAttr1(attr1);
        result.setAttr2(attr2);
        return result;
    }

    /**
     * 批量更新账单费项发票状态
     *
     * @param billItemList 账单费项列表
     */
    public void batchUpdateBillItemReceipt(List<RealtyBillItem> billItemList) {
        if (CollectionUtils.isEmpty(billItemList)) {
            return;
        }
        billMapper.batchUpdateBillItemReceipt(billItemList);
    }


    /**
     * 获取费项
     *
     * @param fees
     * @param fee
     * @return
     */
    private Fee getFee(List<Fee> fees, RealtyFee fee) {
        if (fee == null) {
            return null;
        }
        Fee result = CollectionUtils.isEmpty(fees) ? null
                : fees.stream().filter(x -> x.getAlias().equalsIgnoreCase(fee.name())).findFirst().orElse(null);
        if (result == null) {
            log.warn("找不到费项 {}", fee);
        }
        return result;
    }

    /**
     * 校验物业账单明细
     *
     * @param billItems
     * @return
     */
    public BigDecimal checkAndSumBillItems(List<RealtyBillItem> billItems) {
        if (CollectionUtils.isEmpty(billItems)) {
            return BigDecimal.ZERO;
        }
        if (billItems.stream().map(RealtyBillItem::getFeeId).distinct().count() != billItems.size()) {
            throw new InvalidParameterException("重复的费项");
        }
        return billItems.stream().map(RealtyBillItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 初始化物业账单
     *
     * @param bill
     */
    private void prepareBill(RealtyBill bill) {
        bill.setAmount(DecimalUtils.nullToZero(bill.getAmount()));
        bill.setTotalAmount(bill.getTotalAmount() == null ? DecimalUtils.add(bill.getAmount(), bill.getPenaltyAmount()) : bill.getTotalAmount());

        // 滞纳金日期
        prepareBillPenaltyDate(bill);
        // 同步的账单，需要补充添加滞纳金
        prepareBillPaidStillAmount(bill);
    }

    /**
     * 初始化滞纳金日期
     *
     * @param bill
     */
    private void prepareBillPenaltyDate(RealtyBill bill) {
        if (bill.getPenaltyDate() != null) {
            return;
        }
        bill.setPenaltyDate(DateUtils.parseDate(bill.getBillYear(), bill.getBillMonth(), realtyConfig.getDateOfPenalty()));
    }


    /**
     * 初始化账单待收金额
     *
     * @param bill
     */
    private void prepareBillPaidStillAmount(RealtyBill bill) {
        BillStatus billStatus = BillStatus.fromStatus(bill.getStatus());
        if (billStatus == null || BillStatus.INIT == billStatus) {
            // 新增的账单，待收金额初始化
            bill.setPaidStillAmount(DecimalUtils.add(bill.getTotalAmount(), bill.getPenaltyAmount()));
        }
    }

    /**
     * 初始化账单明细
     *
     * @param bill
     * @param billItems
     * @param feeMap
     */
    private List<RealtyBillItem> prepareBillItems(RealtyBill bill, List<RealtyBillItem> billItems, Map<Long, Fee> feeMap) {
        if (CollectionUtils.isEmpty(billItems)) {
            return Collections.emptyList();
        }

        List<RealtyBillItem> resultList = new ArrayList<>(billItems.size());
        for (RealtyBillItem billItem : billItems) {
            Fee fee = feeMap.get(billItem.getFeeId());
            // 找不到的费项或滞纳金 金额为0时，不添加
            if (!DecimalUtils.isPositive(billItem.getAmount())
                    && (fee == null || Objects.equals(fee.getAlias(), RealtyFee.PENALTY.name()))) {
                continue;
            }

            // 金额
            if (billItem.getAmount() == null) {
                billItem.setAmount(BigDecimal.ZERO);
            }
            // 状态
            if (billItem.getStatus() == null) {
                billItem.setStatus(BillStatus.INIT.getStatus());
            }
            // 费项名
            if (StringUtils.isBlank(billItem.getFeeName()) && fee != null) {
                billItem.setFeeName(fee.getName());
            }

            billItem.setBillId(bill.getId());
            billItem.setCreatorId(bill.getModifierId());
            billItem.setCreatorName(bill.getModifierName());
            billItem.setModifierId(bill.getModifierId());
            billItem.setModifierName(bill.getModifierName());
            resultList.add(billItem);
        }
        return resultList;
    }

    /**
     * 租金账单
     *
     * @param bill
     * @param rentBill
     */
    private void prepareRentBill(RealtyBillVo bill, RealtyBillItem rentBill) {
        bill.setRentAmount(rentBill == null ? BigDecimal.ZERO : rentBill.getAmount());
        bill.setRentPaidStatus(rentBill == null ? bill.getStatus() : rentBill.getStatus());
    }

    /**
     * 管理费账单
     *
     * @param bill
     * @param manageBill
     */
    private void prepareManageBill(RealtyBillVo bill, RealtyBillItem manageBill) {
        bill.setManageAmount(manageBill == null ? BigDecimal.ZERO : manageBill.getAmount());
        bill.setManagePaidStatus(manageBill == null ? bill.getStatus() : manageBill.getStatus());
    }

    /**
     * 水费账单
     *
     * @param bill
     * @param waterBill
     */
    private void prepareWaterBill(RealtyBillVo bill, RealtyBillItem waterBill) {
        bill.setWaterAmount(waterBill == null ? BigDecimal.ZERO : waterBill.getAmount());
        bill.setWaterStartDate(waterBill == null ? null : waterBill.getAttr1());
        bill.setWaterEndDate(waterBill == null ? null : waterBill.getAttr2());
        bill.setWaterPaidStatus(waterBill == null ? bill.getStatus() : waterBill.getStatus());
    }

    /**
     * 电费账单
     *
     * @param bill
     * @param electricBill
     */
    private void prepareElectricBill(RealtyBillVo bill, RealtyBillItem electricBill) {
        bill.setElectricAmount(electricBill == null ? BigDecimal.ZERO : electricBill.getAmount());
        bill.setElectricStartDate(electricBill == null ? null : electricBill.getAttr1());
        bill.setElectricEndDate(electricBill == null ? null : electricBill.getAttr2());
        bill.setElectricPaidStatus(electricBill == null ? bill.getStatus() : electricBill.getStatus());
    }

    /**
     * 对比物业账单明细
     *
     * @param srcItems
     * @param targetItems
     * @return
     */
    private DataSepDto<RealtyBillItem> compareAndSeparateBillItem(List<RealtyBillItem> srcItems, List<RealtyBillItem> targetItems) {
        // 比较结果
        List<RealtyBillItem> addItems = null;
        List<RealtyBillItem> updateItems = null;
        List<RealtyBillItem> delItems = null;

        if (CollectionUtils.isEmpty(targetItems)) {
            delItems = srcItems;

        } else if (CollectionUtils.isEmpty(srcItems)) {
            addItems = targetItems;

        } else {
            addItems = new ArrayList<>(targetItems.size());
            updateItems = new ArrayList<>(targetItems.size());
            for (RealtyBillItem targetItem : targetItems) {
                RealtyBillItem item = findBillItemInList(srcItems, targetItem);
                if (item != null) {
                    targetItem.setId(item.getId());
                    updateItems.add(targetItem);
                } else {
                    addItems.add(targetItem);
                }
            }

            delItems = srcItems.stream()
                    .filter(x -> findBillItemInList(targetItems, x) == null)
                    .collect(Collectors.toList());
        }

        DataSepDto<RealtyBillItem> result = new DataSepDto<>();
        result.setAddList(addItems);
        result.setUpdateList(updateItems);
        result.setRemoveList(delItems);
        return result;
    }


    /**
     * 在费项列中查找记录
     *
     * @param list
     * @param item
     * @return
     */
    private RealtyBillItem findBillItemInList(List<RealtyBillItem> list, RealtyBillItem item) {
        return list.stream()
                .filter(x -> Objects.equals(x.getBillId(), item.getBillId()) && Objects.equals(x.getFeeId(), item.getFeeId()))
                .findFirst().orElse(null);
    }

    /**
     * 在账单查找费项
     *
     * @param list
     * @param fee
     * @return
     */
    private RealtyBillItem findFeeInBill(List<RealtyBillItem> list, Fee fee) {
        if (CollectionUtils.isEmpty(list) || fee == null) {
            return null;
        }

        return list.stream().filter(x -> Objects.equals(x.getFeeId(), fee.getId())).findFirst().orElse(null);
    }

    /**
     * 计算减免明细
     * @param bills
     * @param ignoreTotal
     * @return
     */
    private List<BillPenaltyIgnoreItem> prepareBillPenaltyIgnoreDetail(List<RealtyBillVo> bills, BigDecimal ignoreTotal) {
        Map<Long, BillPenaltyIgnoreItem> resultMap = new HashMap<>(bills.size());
        // 按比例扣减
        BigDecimal penaltyTotal = bills.stream().map(RealtyBillVo::getPenaltyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal subtractTotal = BigDecimal.ZERO;
        for (RealtyBillVo bill : bills) {
            if (!DecimalUtils.isPositive(bill.getPenaltyAmount())) {
                break;
            }

            // 减免金额 （按比例）
            BigDecimal ignoreAmount = DecimalUtils.multiple(bill.getPenaltyAmount(), ignoreTotal).divide(penaltyTotal, 1, RoundingMode.HALF_UP);
            // 减免明细
            BigDecimal amount = DecimalUtils.subtract(bill.getPenaltyAmount(), ignoreAmount);
            if (DecimalUtils.isPositive(amount)) {
                resultMap.put(bill.getId(), new BillPenaltyIgnoreItem(bill.getId(), ignoreAmount));
                subtractTotal = DecimalUtils.add(subtractTotal, ignoreAmount);
            } else {
                resultMap.put(bill.getId(), new BillPenaltyIgnoreItem(bill.getId(), bill.getPenaltyAmount()));
                subtractTotal = DecimalUtils.add(subtractTotal, bill.getPenaltyAmount());
            }
        }

        // 修平按比例扣减的误差
        return roundIgnorePenalty(bills, resultMap, DecimalUtils.subtract(ignoreTotal, subtractTotal));
    }

    /**
     * 修平误差
     * @param bills
     * @param ignoreDetails
     * @param roundPenalty
     * @return
     */
    private List<BillPenaltyIgnoreItem> roundIgnorePenalty(List<RealtyBillVo> bills, Map<Long, BillPenaltyIgnoreItem> ignoreDetails, BigDecimal roundPenalty) {
        log.info("减免滞纳金，找平 {}, {}", roundPenalty, JsonUtils.object2Json(ignoreDetails));
        if (DecimalUtils.isPositive(roundPenalty)) {
            roundIgnorePenaltyPositive(bills, ignoreDetails, roundPenalty);

        } else if (DecimalUtils.isNegative(roundPenalty)) {
            roundIgnorePenaltyNegative(ignoreDetails, roundPenalty);
        }

        List<BillPenaltyIgnoreItem> resultList = new ArrayList<>(ignoreDetails.values());
        return resultList.stream().filter(x -> DecimalUtils.isPositive(x.getIgnoreAmount())).collect(Collectors.toList());
    }

    /**
     * 修正正误差
     * @param bills
     * @param ignoreDetails
     * @param roundPenalty
     */
    private void roundIgnorePenaltyPositive(List<RealtyBillVo> bills, Map<Long, BillPenaltyIgnoreItem> ignoreDetails, BigDecimal roundPenalty) {
        for (RealtyBillVo bill : bills) {
            BillPenaltyIgnoreItem ignoreItem = ignoreDetails.get(bill.getId());
            if (ignoreItem == null) {
                ignoreItem = new BillPenaltyIgnoreItem(bill.getId(), BigDecimal.ZERO);
                ignoreDetails.put(bill.getId(), ignoreItem);
            }

            BigDecimal amount = DecimalUtils.subtract(bill.getPenaltyAmount(), ignoreItem.getIgnoreAmount(), roundPenalty);
            if (!DecimalUtils.isNegative(amount)) {
                ignoreItem.setIgnoreAmount(DecimalUtils.add(ignoreItem.getIgnoreAmount(), roundPenalty));
                roundPenalty = BigDecimal.ZERO;

            } else {
                roundPenalty = DecimalUtils.subtract(roundPenalty, DecimalUtils.subtract(bill.getPenaltyAmount(), ignoreItem.getIgnoreAmount()));
                ignoreItem.setIgnoreAmount(bill.getPenaltyAmount());
            }

            if (!DecimalUtils.isPositive(roundPenalty)) {
                break;
            }
        }
        log.info("减免滞纳金找平结果 {}", JsonUtils.object2Json(ignoreDetails));
    }

    /**
     * 修正负误差
     * @param ignoreDetails
     * @param roundPenalty
     */
    private void roundIgnorePenaltyNegative(Map<Long, BillPenaltyIgnoreItem> ignoreDetails, BigDecimal roundPenalty) {
        for (Map.Entry<Long, BillPenaltyIgnoreItem> entry : ignoreDetails.entrySet()) {
            BillPenaltyIgnoreItem ignoreItem = entry.getValue();
            if (ignoreItem == null) {
                continue;
            }

            BigDecimal amount = DecimalUtils.add(ignoreItem.getIgnoreAmount(), roundPenalty);
            if (DecimalUtils.isPositive(amount)) {
                roundPenalty = BigDecimal.ZERO;
                ignoreItem.setIgnoreAmount(amount);

            } else {
                roundPenalty = amount;
                ignoreItem.setIgnoreAmount(BigDecimal.ZERO);
            }

            if (!DecimalUtils.isNegative(roundPenalty)) {
                break;
            }
        }
        log.info("减免滞纳金找平结果 {}", JsonUtils.object2Json(ignoreDetails));
    }
}
