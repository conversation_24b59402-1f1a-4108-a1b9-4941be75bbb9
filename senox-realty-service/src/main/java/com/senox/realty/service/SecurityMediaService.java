package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.SecurityJournal;
import com.senox.realty.domain.SecurityMedia;
import com.senox.realty.mapper.SecurityMediaMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/26 15:29
 */
@Service
public class SecurityMediaService extends ServiceImpl<SecurityMediaMapper, SecurityMedia> {

    /**
     * 保存安保多媒体资料
     * @param journal
     * @param medias
     */
    public void saveMedias(SecurityJournal journal, List<String> medias) {
        saveMedias(journal, 1, medias);
    }

    /**
     * 保存安保多媒体资料
     * @param journal
     * @param medias
     */
    public void saveMedias(SecurityJournal journal, int processTimes, List<String> medias) {
        // 传 null 不处理
        if (journal == null || !WrapperClassUtils.biggerThanLong(journal.getId(), 0L)
                || medias == null) {
            return;
        }

        // 要保存的多媒体信息
        List<SecurityMedia> mediaList = medias.stream().map(x -> buildSecurityMedia(journal, processTimes, x)).collect(Collectors.toList());
        // 原来保存的多媒体信息
        List<SecurityMedia> srcList = listByJournalIdAndTimes(journal.getId(), processTimes);

        // 比较
        DataSepDto<SecurityMedia> sepData = compareAndSeparateMedias(srcList, mediaList);

        // 添加
        addMedias(sepData.getAddList());
        // 删除
        removeMedias(journal.getId(), sepData.getRemoveList());
    }


    /**
     * 添加安保多媒体资料
     * @param medias
     */
    public void addMedias(List<SecurityMedia> medias) {
        if (CollectionUtils.isEmpty(medias)) {
            return;
        }

        saveBatch(medias);
    }

    /**
     * 删除安保多媒体资料
     * @param journalId
     */
    public void removeByJournalId(Long journalId) {
        if (!WrapperClassUtils.biggerThanLong(journalId, 0L)) {
            return;
        }

        LambdaQueryWrapper<SecurityMedia> queryWrapper = new QueryWrapper<SecurityMedia>().lambda()
                .eq(SecurityMedia::getJournalId, journalId);
        remove(queryWrapper);
    }

    /**
     * 删除安保多媒体资料
     * @param journalId
     * @param medias
     */
    public void removeMedias(Long journalId, List<SecurityMedia> medias) {
        if (CollectionUtils.isEmpty(medias)) {
            return;
        }

        removeMediasById(journalId, medias.stream().map(SecurityMedia::getId).collect(Collectors.toList()));
    }

    /**
     * 删除安保多媒体资料
     * @param journalId
     * @param ids
     */
    public void removeMediasById(Long journalId, List<Long> ids) {
        if (!WrapperClassUtils.biggerThanLong(journalId, 0L) || CollectionUtils.isEmpty(ids)) {
            return;
        }

        LambdaQueryWrapper<SecurityMedia> queryWrapper = new QueryWrapper<SecurityMedia>().lambda()
                .eq(SecurityMedia::getJournalId, journalId)
                .in(SecurityMedia::getId, ids);
        remove(queryWrapper);
    }

    /**
     * 根据日志id查找多媒体资料
     * @param journalId
     * @return
     */
    public List<SecurityMedia> listByJournalId(Long journalId) {
        if (!WrapperClassUtils.biggerThanLong(journalId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SecurityMedia> queryWrapper = new QueryWrapper<SecurityMedia>().lambda()
                .eq(SecurityMedia::getJournalId, journalId);
        return list(queryWrapper);
    }

    /**
     * 根据日志id查找多媒体资料
     * @param journalId
     * @param processTimes
     * @return
     */
    public List<SecurityMedia> listByJournalIdAndTimes(Long journalId, Integer processTimes) {
        if (!WrapperClassUtils.biggerThanLong(journalId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SecurityMedia> queryWrapper = new QueryWrapper<SecurityMedia>().lambda()
                .eq(SecurityMedia::getJournalId, journalId)
                .eq(SecurityMedia::getProcessTimes, processTimes);
        return list(queryWrapper);
    }

    /**
     * 构建安保多媒体资料实体
     * @param journal
     * @param media
     * @return
     */
    private SecurityMedia buildSecurityMedia(SecurityJournal journal, Integer processTimes, String media) {
        return SecurityMedia.builder()
                .journalId(journal.getId())
                .mediaUrl(media)
                .processTimes(processTimes)
                .modifierId(journal.getModifierId())
                .modifiedTime(LocalDateTime.now())
                .build();
    }

    /**
     * 对比分类数据
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<SecurityMedia> compareAndSeparateMedias(List<SecurityMedia> srcList, List<SecurityMedia> targetList) {
        DataSepDto<SecurityMedia> result = new DataSepDto<>();

        if (CollectionUtils.isEmpty(targetList)) {
            result.setRemoveList(srcList);

        } else if (CollectionUtils.isEmpty(srcList)) {
            result.setAddList(targetList);

        } else {
            result.setAddList(targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList()));
            result.setRemoveList(srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList()));
        }
        return result;
    }
}
