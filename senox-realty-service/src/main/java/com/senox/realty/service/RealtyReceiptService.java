package com.senox.realty.service;

import com.senox.common.vo.PageResult;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.realty.domain.RealtyReceipt;
import com.senox.realty.vo.RealtyBillReceiptApplyInfoVo;
import com.senox.realty.vo.RealtyReceiptMangerVo;
import com.senox.realty.vo.RealtyReceiptSearchVo;
import com.senox.realty.vo.RealtyReceiptVo;

import java.util.List;

/**
 * 物业发票服务
 *
 * <AUTHOR>
 * @date 2023-3-23
 */
public interface RealtyReceiptService {

    /**
     * 物业发票申请
     *
     * @param realtyReceiptManger 物业发票管理
     */
    void apply(RealtyReceiptMangerVo realtyReceiptManger);

    /**
     * 物业发票申请pc
     *
     * @param realtyReceiptManger
     */
    void pcApply(RealtyReceiptMangerVo realtyReceiptManger);

    /**
     * 根据id获取物业发票申请
     *
     * @param id 物业发票申请id
     * @return 物业发票申请
     */
    RealtyReceipt findById(Long id);

    /**
     * 更新物业发票申请
     *
     * @param realtyReceipt 物业发票申请
     */
    void update(RealtyReceipt realtyReceipt);

    /**
     * 批量更新物业发票申请
     *
     * @param realtyReceiptList 物业发票申请列表
     */
    void updateBatch(List<RealtyReceipt> realtyReceiptList);

    /**
     * 物业发票申请列表
     *
     * @param search 查询参数
     * @return 返回物业发票申请列表
     */
    List<RealtyReceiptVo> list(RealtyReceiptSearchVo search);

    /**
     * 物业发票申请分页列表
     *
     * @param search 查询参数
     * @return 分页后的物业发票申请列表
     */
    PageResult<RealtyReceiptVo> listPage(RealtyReceiptSearchVo search);

    /**
     * 审核
     *
     * @param id     物业发票申请id
     * @param status 状态
     * @param remark 审核备注
     * @param auditMan 审核人
     */
    void audit(Long id, ReceiptStatus status,String remark,Long auditMan);

    /**
     * 根据id获取发票单据号列表
     * @param ids 物业发票申请id列表
     * @return 发票单据号列表
     */
    List<String> receiptSerialNoListById(Long ids);

    /**
     * 根据单据号列表获取发票申请
     * @param serialNoList 单据号列表
     * @return 发票申请列表
     */
    List<RealtyReceipt> getBySerialNoList(List<String> serialNoList);


    /**
     * 更新账单费项发票状态
     *
     * @param applyList 发票申请列表
     */
    void updateBillItemReceiptStatus(List<ReceiptApplyVo> applyList);

    /**
     * 发票申请列表
     *
     * @param id       物业发票申请id
     * @param isDetail 是否详细
     * @return 申请列表
     */
    List<ReceiptApplyVo> receiptApplyListByRealtyReceiptId(Long id, Boolean isDetail);

    /**
     * 物业账单发票申请信息列表
     *
     * @param id 物业发票申请id
     * @return 账单发票申请信息列表
     */
    List<RealtyBillReceiptApplyInfoVo> applyBillInfoList(Long id);

    /**
     * 刷新物业账单发票状态
     * @param billIds 账单集
     */
    void refreshBillReceiptStatus(List<Long> billIds);
}
