package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.constant.MaintainJobStatus;
import com.senox.realty.domain.MaintainJobItem;
import com.senox.realty.mapper.MaintainJobItemMapper;
import com.senox.realty.utils.ContextUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/28 16:28
 */
@Service
@RequiredArgsConstructor
public class MaintainJobItemService extends ServiceImpl<MaintainJobItemMapper, MaintainJobItem> {

    /**
     * 添加派工人员
     * @param jobId
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMaintainJobItem(Long jobId, List<MaintainJobItem> list) {
        list = list.stream().distinct().collect(Collectors.toList());
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            throw new BusinessException("派工单号有误");
        }
        list.forEach(x -> {
            x.setJobId(jobId);
            ContextUtils.initEntityModifier(x);
            ContextUtils.initEntityCreator(x);
            x.setCreateTime(LocalDateTime.now());
            x.setModifiedTime(LocalDateTime.now());
        });
        DataSepDto<MaintainJobItem> sepData = compareAndSepData(maintainJobItemListByJobId(jobId, null), list);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            List<Long> removeIds = sepData.getRemoveList().stream().map(MaintainJobItem::getId).collect(Collectors.toList());
            removeByIds(removeIds);
        }
    }


    /**
     * 根据id获取派工人员
     *
     * @param id
     * @return
     */
    public MaintainJobItem findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 派工id和状态查询派工人员
     * @param jobId
     * @param status
     * @return
     */
    public List<MaintainJobItem> maintainJobItemListByJobId(Long jobId, MaintainJobStatus status) {
        LambdaQueryWrapper<MaintainJobItem> wrapper = new LambdaQueryWrapper<>();
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            return Collections.emptyList();
        }
        if (status != null) {
            wrapper.eq(MaintainJobItem::getHandlerStatus, status.getValue());
        }
        wrapper.eq(MaintainJobItem::getJobId, jobId);
        return list(wrapper);
    }

    private DataSepDto<MaintainJobItem> compareAndSepData(List<MaintainJobItem> srcList, List<MaintainJobItem> targetList) {
        List<MaintainJobItem> addList = new ArrayList<>(targetList.size());
        List<MaintainJobItem> updateList = new ArrayList<>(targetList.size());
        List<MaintainJobItem> delList = new ArrayList<>(targetList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            // 原始记录为空，新增所有目标记录
            addList.addAll(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            // 目标记录为空，删掉所有原始记录
            delList.addAll(srcList);

        } else {
            for (MaintainJobItem item : srcList) {
                MaintainJobItem targetItem = targetList.stream().filter(x -> Objects.equals(x, item)).findFirst().orElse(null);
                if (targetItem != null) {
                    targetItem.setId(item.getId());
                    targetItem.setCreatorId(null);
                    targetItem.setModifierId(null);
                    updateList.add(targetItem);
                } else {
                    delList.add(item);
                }
            }

            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
        }

        return new DataSepDto<>(addList, updateList, delList);
    }

    /**
     * 根据派工id和状态删除派工人员
     * @param jobId
     * @param status
     */
    public void deleteMaintainJobItemByJobId(Long jobId, MaintainJobStatus status) {
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            return;
        }
        List<MaintainJobItem> jobItems = maintainJobItemListByJobId(jobId, status);
        //删除派工人员
        removeByIds(jobItems.stream().map(MaintainJobItem::getId).collect(Collectors.toList()));
    }

    /**
     * 查询最近一次为审核节点的维修员
     * @param orderId
     * @return
     */
    public MaintainJobItem findLastExamineByOrderId(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return null;
        }
        return getBaseMapper().findLastExamineByOrderId(orderId);
    }
}
