package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.MaintainMedia;
import com.senox.realty.mapper.MaintainMediaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/23 16:24
 */
@Slf4j
@Service
public class MaintainMediaService extends ServiceImpl<MaintainMediaMapper, MaintainMedia> {

    /**
     * 保存维修单或派工单多媒体资源
     *
     * @param orderId
     * @param medias
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMaintainMedia(Long orderId, List<String> medias) {
        saveMaintainMedia(orderId, 0L, 0L, medias);
    }

    /**
     * 保存维修单或派工单多媒体资源
     *
     * @param orderId
     * @param jobId
     * @param medias
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMaintainMedia(Long orderId, Long jobId, Long jobItemId, List<String> medias) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            log.warn("Invalid Maintain media with unspecified orderId");
            return;
        }
        final Long jid = WrapperClassUtils.biggerThanLong(jobId, 0L) ? jobId : 0L;
        final Long itemId = WrapperClassUtils.biggerThanLong(jobItemId, 0L) ? jobItemId : 0L;

        List<MaintainMedia> list = medias.stream()
                .map(x -> newMaintainMedia(orderId, jid, itemId, x))
                .collect(Collectors.toList());
        DataSepDto<MaintainMedia> sepData = compareAndSeparateMaintainMedia(listByOrderIdAndJobId(orderId, jid, itemId), list);

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(MaintainMedia::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 根据订单id查找多媒体信息
     *
     * @param orderId
     * @return
     */
    public List<MaintainMedia> listByOrderId(Long orderId) {
        return listByOrderIdAndJobId(orderId, 0L, 0L);
    }

    /**
     * 根据订单id和派工单id和派工单子项id查找多媒体信息
     * @param orderId
     * @param jobId
     * @param jobItemId
     * @return
     */
    public List<MaintainMedia> listByOrderIdAndJobId(Long orderId, Long jobId, Long jobItemId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        jobId = WrapperClassUtils.biggerThanLong(jobId, 0L) ? jobId : 0L;
        jobItemId = WrapperClassUtils.biggerThanLong(jobItemId, 0L) ? jobItemId : 0L;

        Wrapper<MaintainMedia> wrapper = new QueryWrapper<MaintainMedia>().lambda()
                .eq(MaintainMedia::getOrderId, orderId)
                .eq(MaintainMedia::getJobId, jobId)
                .eq(MaintainMedia::getJobItemId, jobItemId);
        return getBaseMapper().selectList(wrapper);
    }

    /**
     * 比较区分维修单/派工单多媒体信息
     *
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<MaintainMedia> compareAndSeparateMaintainMedia(List<MaintainMedia> srcList, List<MaintainMedia> targetList) {
        List<MaintainMedia> addList = null;
        List<MaintainMedia> removeList = null;
        if (CollectionUtils.isEmpty(srcList)) {
            addList = targetList;

        } else if (CollectionUtils.isEmpty(targetList)) {
            removeList = srcList;

        } else {
            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
            removeList = srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList());
        }

        DataSepDto<MaintainMedia> result = new DataSepDto<>();
        result.setAddList(addList);
        result.setRemoveList(removeList);
        return result;
    }


    /**
     * 多媒体信息
     * @param orderId
     * @param jobId
     * @param jobItemId
     * @param media
     * @return
     */
    private MaintainMedia newMaintainMedia(Long orderId, Long jobId, Long jobItemId, String media) {
        MaintainMedia result = new MaintainMedia(orderId, jobId, jobItemId, media);
        result.setModifiedTime(LocalDateTime.now());
        return result;
    }
}
