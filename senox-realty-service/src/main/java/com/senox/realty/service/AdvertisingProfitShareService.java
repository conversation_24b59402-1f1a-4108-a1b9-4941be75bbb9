package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.AdvertisingProfitShare;
import com.senox.realty.mapper.AdvertisingProfitShareMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/25 14:13
 */
@Service
public class AdvertisingProfitShareService extends ServiceImpl<AdvertisingProfitShareMapper, AdvertisingProfitShare> {

    /**
     * 保存利润分成
     * @param contractId
     * @param shareList
     */
    public void batchSaveProfitShare(Long contractId, List<AdvertisingProfitShare> shareList) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L) || shareList == null) {
            return;
        }

        shareList.forEach(x -> {
            x.setContractId(contractId);
            x.setRealtySerial(StringUtils.trimToEmpty(x.getRealtySerial()));
            x.setRealtyName(StringUtils.trimToEmpty(x.getRealtyName()));
        });
        List<AdvertisingProfitShare> srcShareList = listByContractId(contractId);

        // 对比并更新数据
        DataSepDto<AdvertisingProfitShare> sepData = SeparateUtils.separateData(srcShareList, shareList,
                this::checkProfitShareDuplicated, x -> shareList.stream().noneMatch(y -> checkProfitShareDuplicated(x, y)));
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateProfitShare(sepData.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(AdvertisingProfitShare::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 批量更新广告分成
     * @param shareList
     */
    public void updateProfitShare(List<AdvertisingProfitShare> shareList) {
        shareList.forEach(x -> {
            x.setCreatorId(null);
            x.setCreatorName(null);
            x.setCreateTime(null);
        });
        updateBatchById(shareList);
    }

    /**
     * 根据合同查找分成
     * @param contractId
     * @return
     */
    public List<AdvertisingProfitShare> listByContractId(Long contractId) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AdvertisingProfitShare> queryWrapper = new QueryWrapper<AdvertisingProfitShare>().lambda()
                .eq(AdvertisingProfitShare::getContractId, contractId);
        return list(queryWrapper);
    }

    /**
     * 利益分成是否重复
     * @param share1
     * @param share2
     * @return
     */
    private boolean checkProfitShareDuplicated(AdvertisingProfitShare share1, AdvertisingProfitShare share2) {
        return Objects.equals(share1.getCustomerId(), share2.getCustomerId())
                && Objects.equals(share1.getRealtySerial(), share2.getRealtySerial())
                && Objects.equals(share1.getContractId(), share2.getContractId());
    }


}
