package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.pm.constant.PayWay;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.domain.AdvertisingPayoff;
import com.senox.realty.domain.AdvertisingProfitShare;
import com.senox.realty.mapper.AdvertisingPayoffMapper;
import com.senox.realty.vo.AdvertisingPayoffDetailVo;
import com.senox.realty.vo.AdvertisingPayoffSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/3 8:47
 */
@Service
@RequiredArgsConstructor
public class AdvertisingPayoffService extends ServiceImpl<AdvertisingPayoffMapper, AdvertisingPayoff> {

    private final AdvertisingProfitShareService profitShareService;


    /**
     * 保存广告合同应付账单
     * @param contract
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePayoff(AdvertisingContract contract) {
        List<AdvertisingPayoff> payoffList = buildPayoffByContract(contract);

        // 保存应付账单
        savePayoff(contract.getContractNo(), payoffList);
    }

    /**
     * 保存广告应付合同
     * @param contractNo
     * @param payoffList
     */
    public void savePayoff(String contractNo, List<AdvertisingPayoff> payoffList) {
        // dbData
        List<AdvertisingPayoff> dbPayoffList = listByContractNo(contractNo);

        // 对比并更新数据
        DataSepDto<AdvertisingPayoff> sepData = SeparateUtils.separateData(
                dbPayoffList,
                payoffList,
                this::checkPayoffExist,
                x -> payoffList.stream().noneMatch(y -> checkPayoffExist(x, y)),
                this::checkPayoffDuplicated);
        // 校验应付账单是否可以调整
        checkPayoffModifiable(sepData.getUpdateList(), sepData.getRemoveList());

        // 批量添加应付账单
        batchAddPayoff(sepData.getAddList());
        // 批量更新应付账单
        batchUpdatePayoff(sepData.getUpdateList());
        // 删除应付账单
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            batchDeletePayoff(sepData.getRemoveList().stream().map(AdvertisingPayoff::getId).collect(Collectors.toList()));
        }


    }

    /**
     * 批量添加应付账单
     * @param payoffList
     */
    private void batchAddPayoff(List<AdvertisingPayoff> payoffList) {
        if (CollectionUtils.isEmpty(payoffList)) {
            return;
        }
        payoffList.forEach(x -> x.setTollManId(0L));

        saveBatch(payoffList);
    }

    /**
     * 更新应付账单
     * @param payoff
     */
    public void updatePayoff(AdvertisingPayoff payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            return;
        }
        LambdaQueryWrapper<AdvertisingPayoff> queryWrapper =
                new QueryWrapper<AdvertisingPayoff>().lambda().eq(AdvertisingPayoff::getId, payoff.getId())
                        .eq(AdvertisingPayoff::getStatus, BillStatus.INIT.getStatus());

        payoff.setStatus(null);
        payoff.setTollManId(0L);
        payoff.setPaidTime(null);
        payoff.setCreatorId(null);
        payoff.setCreatorName(null);
        payoff.setCreateTime(null);
        update(payoff, queryWrapper);
    }

    /**
     * 批量更新应付账单
     * @param payoffList
     */
    private void batchUpdatePayoff(List<AdvertisingPayoff> payoffList) {
        if (CollectionUtils.isEmpty(payoffList)) {
            return;
        }

        for (AdvertisingPayoff item : payoffList) {
            // 不更新字段
            item.setStatus(null);
            item.setTollManId(0L);
            item.setPaidTime(null);
            item.setCreatorId(null);
            item.setCreatorName(null);
            item.setCreateTime(null);
        }
        updateBatchById(payoffList);
    }

    /**
     * 删除应付账单
     * @param id
     */
    public void deletePayoff(Long id) {
        AdvertisingPayoff payoff = findById(id);
        if (payoff == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BillStatus.PAID == BillStatus.fromStatus(payoff.getStatus())) {
            throw new BusinessException("账单已支付");
        }

        batchDeletePayoff(Collections.singletonList(id));
    }

    /**
     * 删除应付账单
     * @param idList
     */
    public void batchDeletePayoff(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        LambdaQueryWrapper<AdvertisingPayoff> queryWrapper =
                new QueryWrapper<AdvertisingPayoff>().lambda()
                        .in(AdvertisingPayoff::getId, idList)
                        .eq(AdvertisingPayoff::getStatus, BillStatus.INIT.getStatus());
        remove(queryWrapper);
    }

    /**
     * 更新应付账单状态
     * @param billPaid
     * @param payway
     * @param operator
     */
    public void updatePayoffStatus(BillPaidVo billPaid, PayWay payway, AdminUserDto operator) {
        AdvertisingPayoff payoff = new AdvertisingPayoff();
        payoff.setTollManId(billPaid.getTollMan());
        payoff.setStatus(BillStatus.PAID.getStatus());
        payoff.setPayway(payway.getValue());
        payoff.setPaidTime(billPaid.getPaidTime());
        payoff.setModifierId(operator.getUserId());
        payoff.setModifierName(operator.getUsername());
        payoff.setModifiedTime(LocalDateTime.now());

        LambdaQueryWrapper<AdvertisingPayoff> queryWrapper =
                new QueryWrapper<AdvertisingPayoff>().lambda()
                        .in(AdvertisingPayoff::getId, billPaid.getBillIds())
                        .eq(AdvertisingPayoff::getStatus, BillStatus.INIT.getStatus());
        update(payoff, queryWrapper);
    }

    /**
     * 获取应付账单
     * @param id
     * @return
     */
    public AdvertisingPayoff findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 根据合同号查找应付账单
     * @param contractNo
     * @return
     */
    public List<AdvertisingPayoff> listByContractNo(String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AdvertisingPayoff> queryWrapper =
                new QueryWrapper<AdvertisingPayoff>().lambda().eq(AdvertisingPayoff::getContractNo, contractNo);
        return list(queryWrapper);
    }

    /**
     * 根据合同号查找应付账单
     * @param contractNos
     * @return
     */
    public List<AdvertisingPayoff> listByContractNos(List<String> contractNos) {
        if (CollectionUtils.isEmpty(contractNos)) {
            return Collections.emptyList();
        }

        List<AdvertisingPayoff> resultList = new ArrayList<>(contractNos.size());
        for (int index = 0; index < contractNos.size(); index += RealtyConst.BATCH_SIZE_1000) {
            List<String> list = contractNos.stream().skip(index).limit(RealtyConst.BATCH_SIZE_1000).collect(Collectors.toList());

            LambdaQueryWrapper<AdvertisingPayoff> queryWrapper = new QueryWrapper<AdvertisingPayoff>().lambda().in(AdvertisingPayoff::getContractNo, list);
            resultList.addAll(list(queryWrapper));
        }
        return resultList;
    }

    /**
     * 广告应付合计
     * @param ids
     * @param paid
     * @return
     */
    private int countPayoff(List<Long> ids, boolean paid) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        LambdaQueryWrapper<AdvertisingPayoff> queryWrapper = new QueryWrapper<AdvertisingPayoff>().lambda()
                .eq(AdvertisingPayoff::getStatus, paid)
                .in(AdvertisingPayoff::getId, ids);
        return getBaseMapper().selectCount(queryWrapper);
    }

    /**
     * 应付账单合计
     * @param search
     * @return
     */
    public AdvertisingPayoffDetailVo sumPayoff(AdvertisingPayoffSearchVo search) {
        return getBaseMapper().sumPayoff(search);
    }

    /**
     * 应付账单列表
     * @param search
     * @return
     */
    public PageResult<AdvertisingPayoffDetailVo> listPayoffPage(AdvertisingPayoffSearchVo search) {
        return PageUtils.commonPageResult(search, () -> getBaseMapper().countPayoff(search), () -> getBaseMapper().listPayoff(search));
    }

    /**
     * 根据合同构建应付账单
     * @param contract
     * @return
     */
    public List<AdvertisingPayoff> buildPayoffByContract(AdvertisingContract contract) {
        List<AdvertisingProfitShare> shareList = profitShareService.listByContractId(contract.getId());
        return buildPayoff(contract, shareList);
    }

    /**
     * 构建合同应付账单
     * @param contract
     * @param shares
     * @return
     */
    public List<AdvertisingPayoff> buildPayoff(AdvertisingContract contract, List<AdvertisingProfitShare> shares) {
        if (CollectionUtils.isEmpty(shares)) {
            return Collections.emptyList();
        }

        List<AdvertisingPayoff> resultList = new ArrayList<>(shares.size());
        for (AdvertisingProfitShare item : shares) {
            if (!DecimalUtils.isPositive(item.getShareAmount())) {
                continue;
            }

            resultList.add(buildPayoff(contract, item));
        }
        return resultList;
    }

    /**
     * 应付账单能否调整
     * @param payoffs
     */
    private void checkPayoffModifiable(List<AdvertisingPayoff>... payoffs) {
        List<Long> modifiedIds = new ArrayList<>();
        for (List<AdvertisingPayoff> list : payoffs) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            modifiedIds.addAll(list.stream().map(AdvertisingPayoff::getId).collect(Collectors.toList()));
        }

        if (countPayoff(modifiedIds, true) > 0) {
            throw new BusinessException("分成已支付，无法修改");
        }
    }

    /**
     * 构建应付账单
     * @param contract
     * @param share
     * @return
     */
    private AdvertisingPayoff buildPayoff(AdvertisingContract contract, AdvertisingProfitShare share) {
        AdvertisingPayoff result = new AdvertisingPayoff();
        result.setBillYear(LocalDateTime.now().getYear());
        result.setBillMonth(LocalDateTime.now().getMonthValue());
        result.setSpaceId(contract.getSpaceId());
        result.setContractNo(contract.getContractNo());
        result.setCustomerId(share.getCustomerId());
        result.setCustomerName(share.getCustomerName());
        result.setRealtySerial(share.getRealtySerial());
        result.setRealtyName(share.getRealtyName());
        result.setAmount(share.getShareAmount());
        result.setPayway(0);
        result.setStatus(BillStatus.INIT.getStatus());
        result.setCreatorId(contract.getModifierId());
        result.setCreatorName(contract.getModifierName());
        result.setCreateTime(LocalDateTime.now());
        result.setModifierId(result.getCreatorId());
        result.setModifierName(result.getCreatorName());
        result.setModifiedTime(LocalDateTime.now());
        return result;
    }

    private boolean checkPayoffExist(AdvertisingPayoff payoff1, AdvertisingPayoff payoff2) {
        return Objects.equals(payoff1.getSpaceId(), payoff2.getSpaceId())
                && Objects.equals(payoff1.getContractNo(), payoff2.getContractNo())
                && Objects.equals(payoff1.getRealtySerial(), payoff2.getRealtySerial())
                && Objects.equals(payoff1.getCustomerId(), payoff2.getCustomerId());
    }

    private boolean checkPayoffDuplicated(AdvertisingPayoff payoff1, AdvertisingPayoff payoff2) {
        return checkPayoffExist(payoff1, payoff2) && DecimalUtils.equals(payoff1.getAmount(), payoff2.getAmount());
    }
}
