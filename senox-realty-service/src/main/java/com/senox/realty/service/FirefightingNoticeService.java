package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.constant.TemplateStatus;
import com.senox.realty.domain.FirefightingNotice;
import com.senox.realty.domain.FirefightingTemplate;
import com.senox.realty.event.InspectTaskDropEvent;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.mapper.FirefightingNoticeMapper;
import com.senox.realty.vo.FirefightingNoticeSearchVo;
import com.senox.realty.vo.FirefightingNoticeVo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 10:02
 */
@Service
@RequiredArgsConstructor
public class FirefightingNoticeService extends ServiceImpl<FirefightingNoticeMapper, FirefightingNotice> {

    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingTemplateService templateService;
    private final ApplicationEventPublisher publisher;

    /**
     * 添加告知单
     * @param taskId
     * @param notice
     * @param realtySerials
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addNotice(Long taskId, FirefightingNotice notice, List<String> realtySerials) {
        // 模板校验
        checkTemplateValid(notice.getTemplateCode(), notice.getTemplateVersion());

        if (notice.getNotifyDate() == null) {
            notice.setNotifyDate(LocalDate.now());
        }

        notice.setCreateTime(LocalDateTime.now());
        notice.setModifiedTime(LocalDateTime.now());
        save(notice);
        inspectRealtyService.saveInspectRealities(notice.getId(), InspectionType.NOTICE, realtySerials);

        // 巡检任务事件
        InspectTaskFulfilledEvent event = new InspectTaskFulfilledEvent(this, InspectionType.NOTICE, notice.getId());
        event.setTaskId(taskId);
        event.setEnterpriseId(notice.getEnterpriseId());
        event.setRealtySerials(realtySerials);
        publisher.publishEvent(event);
        return notice.getId();
    }

    /**
     * 更新告知单
     * @param notice
     * @param realtySerials
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateNotice(FirefightingNotice notice, List<String> realtySerials) {
        // 模板校验
        if (!StringUtils.isBlank(notice.getTemplateCode()) && notice.getTemplateVersion() != null) {
            checkTemplateValid(notice.getTemplateCode(), notice.getTemplateVersion());
        }

        notice.setCreatorId(null);
        notice.setCreatorName(null);
        notice.setCreateTime(null);
        notice.setModifiedTime(LocalDateTime.now());
        updateById(notice);
        inspectRealtyService.saveInspectRealities(notice.getId(), InspectionType.NOTICE, realtySerials);
    }

    /**
     * 删除告知单
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteNotice(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        removeById(id);
        inspectRealtyService.deleteByInspectId(id, InspectionType.NOTICE);
        // 触发移除事件
        publisher.publishEvent(new InspectTaskDropEvent(this, InspectionType.NOTICE, id));
    }

    /**
     * 获取告知单
     * @param id
     * @return
     */
    public FirefightingNotice findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 告知单统计
     * @param search
     * @return
     */
    public int countNotice(FirefightingNoticeSearchVo search) {
        return getBaseMapper().countNotice(search);
    }

    /**
     * 告知单列表
     * @param search
     * @return
     */
    public List<FirefightingNoticeVo> listNotice(FirefightingNoticeSearchVo search) {
        return getBaseMapper().listNotice(search);
    }

    /**
     * 模板有效性校验
     * @param code
     * @param version
     */
    private void checkTemplateValid(String code, Integer version) {
        FirefightingTemplate template = templateService.findTemplateByCodeAndVersion(code, version);
        if (template == null || TemplateStatus.fromStatus(template.getStatus()) != TemplateStatus.VALID) {
            throw new BusinessException("模板状态异常");
        }
    }
}
