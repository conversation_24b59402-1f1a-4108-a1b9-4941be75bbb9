package com.senox.realty.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.common.constant.device.DeviceState;
import com.senox.common.constant.device.EnergyType;
import com.senox.common.constant.device.PowerState;
import com.senox.dm.vo.EnergyPointRefreshResult;
import com.senox.realty.component.DeviceEnergyMeteringPointComponent;
import com.senox.realty.domain.EnergyMeteringPoint;
import com.senox.realty.mapper.EnergyMeteringPointMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.EnergyMeterPointSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class EnergyMeteringPointService {
    private final EnergyMeteringPointMapper meteringPointMapper;
    private final DeviceEnergyMeteringPointComponent meteringPointComponent;

    /**
     * 添加计量点
     *
     * @param point 计量点
     */
    public void add(EnergyMeteringPoint point) {
        addBatch(Collections.singletonList(point));
    }

    /**
     * 批量添加计量点
     *
     * @param pointList 计量点集
     */
    public void addBatch(List<EnergyMeteringPoint> pointList) {
        if (CollectionUtils.isEmpty(pointList)) {
            return;
        }
        pointList.forEach(point -> {
            if (null == EnergyType.fromValue(point.getType())) {
                point.setType(EnergyType.UNKNOWN.ordinal());
            }
            if (null == DeviceState.fromState(point.getStatus())) {
                point.setStatus(DeviceState.OFFLINE.getState());
            }
            if (null == PowerState.fromState(point.getPowerStatus())) {
                point.setPowerStatus(PowerState.UNKNOWN.getState());
            }
            ContextUtils.initEntityCreator(point);
            ContextUtils.initEntityModifier(point);
        });
        meteringPointMapper.addBatch(pointList);

    }

    /**
     * 根据id更新计量设备
     *
     * @param point 计量点
     */
    public void updateById(EnergyMeteringPoint point) {
        if (!WrapperClassUtils.biggerThanLong(point.getId(), 0L)) {
            return;
        }
        meteringPointMapper.updateById(point);
    }

    /**
     * 根据编码更新计量器
     *
     * @param point 计量点
     */
    public void updateByCode(EnergyMeteringPoint point) {
        updateBatchByCode(Collections.singletonList(point));
    }

    /**
     * 根据编码批量更新计量点
     *
     * @param pointList 计量点集
     */
    public void updateBatchByCode(List<EnergyMeteringPoint> pointList) {
        if (CollectionUtils.isEmpty(pointList)) {
            return;
        }
        pointList.forEach(point -> {
            if (StringUtils.isBlank(point.getCode())) {
                throw new BusinessException(ResultConst.INVALID_PARAMETER);
            }
            ContextUtils.initEntityModifier(point);
        });
        meteringPointMapper.updateBatchByCode(pointList);
    }

    /**
     * 更新状态
     * @param pointCode 计量点编码
     * @param state 状态
     */
    public void updateStatus(String pointCode, DeviceState state) {
        if (StringUtils.isBlank(pointCode)) {
            return;
        }
        EnergyMeteringPoint meteringPoint = findByCode(pointCode);
        if (null == meteringPoint) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (meteringPoint.getStatus().equals(state.getState())) {
            return;
        }
        EnergyMeteringPoint updateMeteringPoint = new EnergyMeteringPoint();
        updateMeteringPoint.setCode(pointCode);
        updateMeteringPoint.setStatus(state.getState());
        updateByCode(updateMeteringPoint);
    }

    /**
     * 根据计量点编码删除计量点
     * @param pointCode 计量点编码
     * @return 返回新的计量点编码
     */
    public String deleteByCode(String pointCode) {
        if (StringUtils.isBlank(pointCode)) {
            return null;
        }
        EnergyMeteringPoint meteringPoint = findByCode(pointCode);
        if (null == meteringPoint) {
            return null;
        }
        EnergyMeteringPoint updateMeteringPoint = new EnergyMeteringPoint();
        updateMeteringPoint.setId(meteringPoint.getId());
        updateMeteringPoint.setCode(pointCode.concat("_").concat(String.valueOf(meteringPoint.getId())));
        updateMeteringPoint.setDisabled(true);
        updateById(updateMeteringPoint);
        return updateMeteringPoint.getCode();
    }

    /**
     * 根据id查找
     *
     * @param pointId 计量点id
     * @return 返回查找到的计量点
     */
    public EnergyMeteringPoint findById(Long pointId) {
        if (!WrapperClassUtils.biggerThanLong(pointId, 0)) {
            return null;
        }
        return meteringPointMapper.findById(pointId);
    }

    /**
     * 根据计量点编码查找
     *
     * @param pointCode 计量点编码
     * @return 返回查找到的计量点
     */
    public EnergyMeteringPoint findByCode(String pointCode) {
        if (StringUtils.isBlank(pointCode)) {
            return null;
        }
        return meteringPointMapper.findByCode(pointCode);
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(EnergyMeterPointSearchVo search) {
        return meteringPointMapper.countList(search);
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<EnergyMeteringPoint> list(EnergyMeterPointSearchVo search) {
        if (null == search) {
            search = new EnergyMeterPointSearchVo();
            search.setPage(false);
        }
        return meteringPointMapper.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<EnergyMeteringPoint> pageList(EnergyMeterPointSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search, () -> countList(search), () -> list(search));
    }

    /**
     * 批量刷新计量点
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public EnergyPointRefreshResult batchRefresh(String meteringPointCode) {
        List<EnergyMeteringPoint> pointList = meteringPointComponent.remoteList(meteringPointCode);
        if (CollectionUtils.isEmpty(pointList)) {
            throw new BusinessException("未找到计量点");
        }
        EnergyPointRefreshResult result = new EnergyPointRefreshResult();
        List<EnergyMeteringPoint> dbPointList = list(null);
        DataSepDto<EnergyMeteringPoint> dataSepDto = compareEnergyMeterPoint(dbPointList, pointList);
        if (!CollectionUtils.isEmpty(dataSepDto.getAddList())) {
            addBatch(dataSepDto.getAddList());
            result.setNewCount(dataSepDto.getAddList().size());
        }
        if (!CollectionUtils.isEmpty(dataSepDto.getUpdateList())) {
            updateBatchByCode(dataSepDto.getUpdateList());
            result.setUpdateCount(dataSepDto.getUpdateList().size());
        }
        if (!CollectionUtils.isEmpty(dataSepDto.getRemoveList())) {
            //暂时不做删除处理
        }
        return result;
    }

    private DataSepDto<EnergyMeteringPoint> compareEnergyMeterPoint(List<EnergyMeteringPoint> srcList, List<EnergyMeteringPoint> targetList) {
        if (CollectionUtils.isEmpty(targetList)) {
            return new DataSepDto<>();
        }
        List<EnergyMeteringPoint> addList = new ArrayList<>(targetList.size());
        List<EnergyMeteringPoint> updateList = new ArrayList<>(srcList.size());
        if (CollectionUtils.isEmpty(srcList)) {
            addList.addAll(targetList);
        } else {
            Map<String, EnergyMeteringPoint> srcMap = srcList.stream().collect(Collectors.toMap(EnergyMeteringPoint::getCode, Function.identity()));
            targetList.forEach(target -> {
                EnergyMeteringPoint src = srcMap.get(target.getCode());
                if (null != src) {
                    if (!checkDataExist(src, target)) {
                        target.setId(src.getId());
                        updateList.add(target);
                    }
                } else {
                    addList.add(target);
                }
            });
        }

        return new DataSepDto<>(addList, updateList, null);
    }

    /**
     * 检查数据是否存在
     *
     * @param srcPoint    计量点1
     * @param targetPoint 计量点2
     * @return 存在返回true
     */
    private boolean checkDataExist(EnergyMeteringPoint srcPoint, EnergyMeteringPoint targetPoint) {
        return Objects.equals(srcPoint.getCode(), targetPoint.getCode())
                && Objects.equals(srcPoint.getName(), targetPoint.getName())
                && Objects.equals(srcPoint.getType(), targetPoint.getType())
                && Objects.equals(srcPoint.getStatus(), targetPoint.getStatus());
    }
}
