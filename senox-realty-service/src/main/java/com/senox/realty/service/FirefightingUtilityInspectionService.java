package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingUtilityInspection;
import com.senox.realty.event.InspectTaskDropEvent;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.mapper.FirefightingUtilityInspectionMapper;
import com.senox.realty.vo.FirefightingUtilityInspectionSearchVo;
import com.senox.realty.vo.FirefightingUtilityInspectionVo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13 16:40
 */
@Service
@RequiredArgsConstructor
public class FirefightingUtilityInspectionService
        extends ServiceImpl<FirefightingUtilityInspectionMapper, FirefightingUtilityInspection> {

    private final FirefightingInspectMediaService inspectMediaService;
    private final ApplicationEventPublisher publisher;

    /**
     * 添加公共消防设施巡检记录
     * @param taskId
     * @param inspection
     * @param medias
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addInspection(Long taskId, FirefightingUtilityInspection inspection, List<String> medias) {
        if (inspection.getInspectDate() == null) {
            inspection.setInspectDate(LocalDate.now());
        }

        inspection.setCreateTime(LocalDateTime.now());
        inspection.setModifiedTime(LocalDateTime.now());
        save(inspection);
        inspectMediaService.saveMedias(inspection.getId(), InspectionType.UTILITY, medias);

        // 发送巡检任务事件
        InspectTaskFulfilledEvent event = new InspectTaskFulfilledEvent(this, InspectionType.UTILITY, inspection.getId());
        event.setTaskId(taskId);
        event.setUtilityId(inspection.getUtilityId());
        publisher.publishEvent(event);
        return inspection.getId();
    }

    /**
     * 更新公共消防设施巡检记录
     * @param inspection
     * @param medias
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInspection(FirefightingUtilityInspection inspection, List<String> medias) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        inspection.setCreatorId(null);
        inspection.setCreatorName(null);
        inspection.setCreateTime(null);
        inspection.setModifiedTime(LocalDateTime.now());
        updateById(inspection);

        inspectMediaService.saveMedias(inspection.getId(), InspectionType.UTILITY, medias);
    }

    /**
     * 删除公共消防设施巡检记录
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInspection(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        removeById(id);
        inspectMediaService.deleteByInspectId(id, InspectionType.UTILITY);
        // 触发移除事件
        publisher.publishEvent(new InspectTaskDropEvent(this, InspectionType.UTILITY, id));
    }

    /**
     * 获取公共消防设施巡检记录
     * @param id
     * @return
     */
    public FirefightingUtilityInspection findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 公共消防设施巡检记录统计
     * @param search
     * @return
     */
    public int countInspection(FirefightingUtilityInspectionSearchVo search) {
        return getBaseMapper().countInspection(search);
    }

    /**
     * 公共消防设施巡检记录列表
     * @param search
     * @return
     */
    public List<FirefightingUtilityInspectionVo> listInspection(FirefightingUtilityInspectionSearchVo search) {
        return getBaseMapper().listInspection(search);
    }

}
