package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.realty.config.RealtyConfig;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.domain.EnergyProfitItem;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.mapper.RealtyWeMapper;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyEnergyVo;
import com.senox.realty.vo.RealtyWeSearchVo;
import com.senox.realty.vo.RealtyWeType;
import com.senox.realty.vo.RealtyWeVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-8-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RealtyWeServiceImpl extends ServiceImpl<RealtyWeMapper, RealtyWe> implements RealtyWeService {

    private final RealtyConfig realtyConfig;
    private final RealtyService realtyService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddWeData(List<RealtyWe> weList) {
        batchAddWeData(weList, true);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddWeData(List<RealtyWe> weList, boolean isOverWrite) {
        if (CollectionUtils.isEmpty(weList)) {
            return;
        }

        // 抄表时间处理
        prepareRecordDate(weList);
        // 消费单元
        prepareConsumeUnit(weList);

        // 保存
        batchSaveRealtyWe(weList, isOverWrite);
    }

    @Override
    public void update(RealtyWe we) {
        if (!WrapperClassUtils.biggerThanLong(we.getId(), 0L)) {
            return;
        }

        RealtyWeVo dbItem = findById(we.getId());
        if (dbItem == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (dbItem.isWeBillPaid()) {
            throw new BusinessException("该物业账单已支付");
        }

        updateById(we);
    }

    @Override
    public void batchUpdateBillById(List<RealtyWe> weList) {
        if (CollectionUtils.isEmpty(weList)) {
            return;
        }
        baseMapper.batchUpdateBillById(weList);
    }

    /**
     * 更新物业水电读数
     *
     * @param weList
     */
    @Override
    public void batchUpdateBillBySerial(List<RealtyWe> weList) {
        if (CollectionUtils.isEmpty(weList)) {
            return;
        }

        Integer year = weList.get(0).getBillYear();
        Integer month = weList.get(0).getBillMonth();
        Integer type = weList.get(0).getType();
        baseMapper.batchUpdateBillBySerial(year, month, type, weList);
    }

    @Override
    public void unlinkWeBill(List<Long> weBillIds) {
        if (CollectionUtils.isEmpty(weBillIds)) {
            return;
        }
        getBaseMapper().unlinkWeBill(weBillIds);
    }

    @Override
    public void delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<RealtyWeVo> list = listByIds(ids);
        if (list.stream().anyMatch(RealtyWeVo::isWeBillPaid)) {
            throw new BusinessException("部分水电账单已支付");
        }
        removeByIds(ids);
    }

    @Override
    public void deleteMonthReadings(BillMonthVo month) {
        baseMapper.deleteByYearMonth(month.getYear(), month.getMonth());
    }

    @Override
    public RealtyWeVo findById(Long weId) {
        List<RealtyWeVo> list = listByIds(Collections.singletonList(weId));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 根据日期检查是否生成过
     *
     * @param year             年
     * @param month            月
     * @return
     */
    @Override
    public boolean checkMonthReadings(Integer year, Integer month) {
        return count(new QueryWrapper<RealtyWe>()
                .lambda()
                .eq(RealtyWe::getBillYear, year)
                .eq(RealtyWe::getBillMonth, month)
                .eq(RealtyWe::getType, RealtyWeType.MONTH_PAYMENT_BILL.getValue())
                .select(RealtyWe::getRealtySerial)
        ) > 0;
    }

    /**
     * 合计读数
     * @param search
     * @return
     */
    @Override
    public RealtyWeVo sumReadings(RealtyWeSearchVo search) {
        return getBaseMapper().sumReadings(search);
    }

    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    @Override
    public PageResult<RealtyWeVo> listReadingsPage(RealtyWeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = getBaseMapper().countReadings(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<RealtyWeVo> list = getBaseMapper().listReadings(search);
        return PageUtils.resultPage(search, totalSize, list);
    }

    @Override
    public void prepareRealtyWe(RealtyWe we, Integer year, Integer month) {
        we.setBillYear(year);
        we.setBillMonth(month);
        we.setType(RealtyWeType.MONTH_PAYMENT_BILL.getValue());
        // 是否需要计算低消
        int waterBase = we.getLastWaterReadings() == null || we.getWaterReadings() == null ? 0 : realtyConfig.getWaterBase();
        int electricBase = we.getLastElectricReadings() == null || we.getElectricReadings() == null ? 0 : realtyConfig.getElectricBase();
        we.setWaterBase(Math.max(waterBase, we.getWaterBase() == null ? 0 : we.getWaterBase()));
        we.setElectricBase(Math.max(electricBase, we.getElectricBase() == null ? 0 : we.getElectricBase()));

        if (we.getLastWaterReadings() == null) {
            we.setLastWaterReadings(0L);
        }
        if (we.getWaterReadings() == null) {
            we.setWaterReadings(0L);
        }
        if (we.getLastElectricReadings() == null) {
            we.setLastElectricReadings(0L);
        }
        if (we.getElectricReadings() == null) {
            we.setElectricReadings(0L);
        }
        AdminUserDto user = AdminContext.getUser();
        we.setCreatorId(user.getUserId());
        we.setCreatorName(user.getUsername());
        we.setCreateTime(LocalDateTime.now());
        we.setModifierId(user.getUserId());
        we.setModifierName(user.getUsername());
        we.setModifiedTime(LocalDateTime.now());
    }

    /**
     * 能源消费信息
     * @param billTime
     * @param energyType
     * @return
     */
    @Override
    public List<EnergyProfitItem> listEnergyProfits(BillTimeVo billTime, EnergyType energyType) {
        switch (energyType) {
            case ELECTRIC:
                return getBaseMapper().lisElectricProfit(billTime);

            case WATER:
                return getBaseMapper().listWaterProfit(billTime);

            default:
                return Collections.emptyList();
        }
    }

    /**
     * 批量保存物业水电读数（只允许操作同一账期的）
     *
     * @param weList      待处理水电读数
     * @param isOverWrite 覆盖已存在数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveRealtyWe(List<RealtyWe> weList, boolean isOverWrite) {
        if (CollectionUtils.isEmpty(weList)) {
            log.warn("Empty batch save realty we data...");
            return;
        }

        Integer year = weList.get(0).getBillYear();
        Integer month = weList.get(0).getBillMonth();
        RealtyWeType weType = RealtyWeType.fromValue(weList.get(0).getType());

        // 当月数据是否有导入 (月水电数据处理才需要判断)
        List<RealtyWeVo> dbList = weType == RealtyWeType.MONTH_PAYMENT_BILL
                ? listMonthReadings(year, month, RealtyWeType.MONTH_PAYMENT_BILL) : Collections.emptyList();
        // 区分并筛选数据
        // 已缴费的数据不处理，差异数据不做删除
        // 重复数据根据需求做替换
        DataSepDto<RealtyWe> sepData = sepRealtyWeData(dbList, weList, isOverWrite);

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getUpdateList());
        }
    }


    /**
     * 水电读数抄表时间初始化
     *
     * @param weList
     */
    private void prepareRecordDate(List<RealtyWe> weList) {
        if (CollectionUtils.isEmpty(weList)) {
            return;
        }

        // 查询物业最新的水电读数，方便处理水电读数处理时间
        List<String> realtySerials = weList.stream().map(RealtyWe::getRealtySerial).collect(Collectors.toList());
        RealtyWeType weType = RealtyWeType.fromValue(weList.get(0).getType());
        Integer year = weList.get(0).getBillYear();
        Integer month = weList.get(0).getBillMonth();

        // 物业当前最新的水电 (如果是月水电数据，则防止重复导入，排除要导入月份的水电数据)
        List<RealtyWe> latestWe = listLatestReadings(realtySerials,
                weType == RealtyWeType.MONTH_PAYMENT_BILL ? year : null,
                weType == RealtyWeType.MONTH_PAYMENT_BILL ? month : null,
                weType);
        Map<String, RealtyWe> latestWeMap = latestWe.stream().collect(Collectors.toMap(RealtyWe::getRealtySerial, Function.identity()));

        for (RealtyWe we : weList) {
            RealtyWe lastWe = latestWeMap.get(we.getRealtySerial());
            LocalDate lastDate = lastWe == null ? null : lastWe.getLastRecordDate();
            lastDate = lastDate == null ? null : lastDate.plusDays(1L);
            LocalDate lastDateConfig = DateUtils.parseDate(we.getBillYear(), we.getBillMonth() - 1, realtyConfig.getRecordDay(), true);
            LocalDate recordDateConfig = DateUtils.parseDate(we.getBillYear(), we.getBillMonth(), realtyConfig.getRecordDay() - 1, true);

            // 上次抄表时间，取参数、上次抄表日期、默认配置上月抄表时间最大值
            log.info("{}-{} 水电读数 {} 上次抄表时间 {}, {}, {} 取最大值。", year, month, we.getRealtySerial(), we.getLastRecordDate(), lastDate, lastDateConfig);
            we.setLastRecordDate(DateUtils.getMaxDate(we.getLastRecordDate(), lastDate, lastDateConfig));

            // 本次抄表时间，取参数、默认配置抄表时间最小值
            log.info("{}-{} 水电读数 {} 抄表时间 {}, {}取最小值。", year, month, we.getRealtySerial(), we.getRecordDate(), recordDateConfig);
            we.setRecordDate(DateUtils.getMinDate(we.getRecordDate(), recordDateConfig));
        }
    }

    private void prepareConsumeUnit(List<RealtyWe> weList) {
        if (CollectionUtils.isEmpty(weList)) {
            return;
        }

        List<String> realtySerials = weList.stream().map(RealtyWe::getRealtySerial).distinct().collect(Collectors.toList());
        // 主表
        List<RealtyEnergyVo> energyList = realtyService.listEnergy(realtySerials);
        // 副表
        List<RealtyEnergyVo> subEnergyList = realtyService.listEnergyBySubSerials(realtySerials);
        Map<String, RealtyEnergyVo> energyMap = energyList.stream().collect(Collectors.toMap(RealtyEnergyVo::getRealtySerial, Function.identity()));
        Map<String, RealtyEnergyVo> subEnergyMap = subEnergyList.stream().collect(Collectors.toMap(RealtyEnergyVo::getRealtySerial, Function.identity()));
        for (RealtyWe we : weList) {
            RealtyEnergyVo energy = energyMap.get(we.getRealtySerial());
            RealtyEnergyVo subEnergy = subEnergyMap.get(we.getRealtySerial());
            if (energy == null && subEnergy == null) {
                continue;
            }

            we.setElectricConsumeUnit(energy == null ? subEnergy.getElectricConsumeUnit() : energy.getElectricConsumeUnit());
            we.setWaterConsumeUnit(energy == null ? subEnergy.getWaterConsumeUnit() : energy.getWaterConsumeUnit());
        }
    }


    /**
     * 对比拆分水电数据
     *
     * @param srcList
     * @param targetList
     * @param isOverWrite
     * @return
     */
    private DataSepDto<RealtyWe> sepRealtyWeData(List<RealtyWeVo> srcList, List<RealtyWe> targetList, boolean isOverWrite) {
        if (CollectionUtils.isEmpty(targetList)) {
            return new DataSepDto<>();
        }

        List<RealtyWe> addList = new ArrayList<>(targetList.size());
        List<RealtyWe> updateList = new ArrayList<>(srcList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            addList.addAll(targetList);

        } else {
            // 更新及删除数据
            for (RealtyWeVo item : srcList) {
                RealtyWe we = targetList.stream()
                        .filter(x -> Objects.equals(item.getRealtySerial(), x.getRealtySerial()))
                        .findFirst()
                        .orElse(null);

                if (we == null || !checkRealtyWeUpdatable(item, we, isOverWrite)) {
                    continue;
                }

                we.setId(item.getId());
                we.setCreatorId(null);
                we.setCreatorName(null);
                we.setCreateTime(null);
                updateList.add(we);
            }

            // 添加的数据
            addList.addAll(targetList.stream().filter(x -> !isRealtyWeContains(srcList, x.getRealtySerial())).collect(Collectors.toList()));
        }

        return new DataSepDto<>(addList, updateList, null);
    }

    /**
     * 水电读数是否可以更新
     * @param srcWe
     * @param targetWe
     * @param isOverWrite
     * @return
     */
    private boolean checkRealtyWeUpdatable(RealtyWeVo srcWe, RealtyWe targetWe, boolean isOverWrite) {
        if (srcWe.isWeBillPaid()) {
            log.warn("水电账单已缴费，不允许调整。 {}", JsonUtils.object2Json(srcWe));
            return false;
        }

        if (targetWe != null && !isOverWrite) {
            log.warn("水电账单不允许覆盖， {}", JsonUtils.object2Json(srcWe));
            return false;
        }

        return true;
    }

    /**
     * 物业水电是否存在
     * @param weList
     * @param realtySerial
     * @return
     */
    private boolean isRealtyWeContains(List<RealtyWeVo> weList, String realtySerial) {
        return weList.stream().anyMatch(x -> Objects.equals(x.getRealtySerial(), realtySerial));
    }

    /**
     * 根据id查找物业水电
     * @param ids
     * @return
     */
    private List<RealtyWeVo> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return baseMapper.listByIds(ids);
    }

    /**
     * 月物业水电读数
     *
     * @param year
     * @param month
     * @param weType
     * @return
     */
    private List<RealtyWeVo> listMonthReadings(Integer year, Integer month, RealtyWeType weType) {
        return baseMapper.listMonthReadings(year, month, weType == null ? null : weType.getValue());
    }

    /**
     * 物业最新水电读数
     *
     * @param realtySerials
     * @return
     */
    private List<RealtyWe> listLatestReadings(List<String> realtySerials,
                                              Integer excludeYear,
                                              Integer excludeMonth,
                                              RealtyWeType excludeType) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return Collections.emptyList();
        }

        RealtyWeSearchVo search = new RealtyWeSearchVo();
        search.setRealtySerials(realtySerials);
        search.setExcludeYear(excludeYear);
        search.setExcludeMonth(excludeMonth);
        search.setExcludeWeType(excludeType);
        return getBaseMapper().listWeLatestData(search);
    }

}
