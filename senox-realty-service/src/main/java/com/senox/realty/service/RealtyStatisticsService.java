package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.ContractType;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.RealtyStatistics;
import com.senox.realty.mapper.RealtyBillMapper;
import com.senox.realty.mapper.RealtyStatisticsMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.ContractSearchVo;
import com.senox.realty.vo.StatisticsGenerateVo;
import com.senox.realty.vo.StatisticsSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/24 9:06
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RealtyStatisticsService extends ServiceImpl<RealtyStatisticsMapper, RealtyStatistics> {

    private final RealtyService realtyService;
    private final RealtyBillMapper realtyBillMapper;
    private final ContractService contractService;

    /**
     * 生成物业统计报表
     * @param generateVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateRealtyStatistics(StatisticsGenerateVo generateVo) {
        final String buildLock = String.format(RealtyConst.Cache.KEY_REALTY_STATISTICS_BUILD, generateVo.getYear(), generateVo.getMonth(), generateVo.getDay());
        if (!RedisUtils.lock(buildLock, RealtyConst.Cache.TTL_1H)) {
            throw new BusinessException("【生成物业统计】系统处理中，请稍侯。");
        }
        LocalDate localDate =LocalDate.of(generateVo.getYear(), generateVo.getMonth(), generateVo.getDay());
        try {
            log.info("开始生成【物业统计】--{}--{}--{}", generateVo.getYear(), generateVo.getMonth(), generateVo.getDay());

            RealtyStatistics statistics = realtyService.realtyStatistics();
            log.info("【公司物业数量】：{}", JsonUtils.object2Json(statistics));
            int realtyContractNum = contractService.countContract(contractSearch(ContractType.ESTATE.getValue()));
            int rentContractNum = contractService.countContract(contractSearch(ContractType.RENT_PROXY.getValue()));
            statistics.setRealtyContractNum(realtyContractNum);
            statistics.setRentContractNum(rentContractNum);

            RealtyStatistics realtyBillStatistics = realtyBillMapper.realtyBillStatistics(generateVo);
            log.info("【应收金额统计】：{}", JsonUtils.object2Json(realtyBillStatistics));
            statistics.setRentCollectNum(realtyBillStatistics == null ? 0 : realtyBillStatistics.getRentCollectNum());
            statistics.setRentCollectAmount(realtyBillStatistics == null ? BigDecimal.ZERO
                    : DecimalUtils.nullToZero(realtyBillStatistics.getRentCollectAmount()));
            statistics.setUnRentCollectNum(realtyBillStatistics == null ? 0 :realtyBillStatistics.getUnRentCollectNum());
            statistics.setUnRentCollectAmount(realtyBillStatistics == null ? BigDecimal.ZERO
                    : DecimalUtils.nullToZero(realtyBillStatistics.getUnRentCollectAmount()));
            statistics.setPenaltyAmount(realtyBillStatistics == null ? BigDecimal.ZERO
                    : DecimalUtils.nullToZero(realtyBillStatistics.getPenaltyAmount()));
            statistics.setStatisticsDate(localDate);

            log.info("初始化物业统计完毕，数据为：{}", JsonUtils.object2Json(statistics));
            ContextUtils.initEntityModifier(statistics);
            statistics.setModifiedTime(LocalDateTime.now());
            RealtyStatistics realtyStatistics = findRealtyStatisticsByDate(localDate);
            if (realtyStatistics != null) {
                log.info("【物业统计】--{}--{}--{} 存在数据，结果为:{}，update", generateVo.getYear(), generateVo.getMonth(), generateVo.getDay(), JsonUtils.object2Json(realtyStatistics));
                statistics.setId(realtyStatistics.getId());
            } else {
                ContextUtils.initEntityCreator(statistics);
                statistics.setCreateTime(LocalDateTime.now());
            }
            saveOrUpdate(statistics);
        } finally {
            RedisUtils.del(buildLock);
        }
    }

    /**
     * 合同数量查询参数
     * @return
     */
    private ContractSearchVo contractSearch(Integer type) {
        ContractSearchVo searchVo = new ContractSearchVo();
        searchVo.setType(type);
        searchVo.setStatus(ContractStatus.EFFECTIVE.getStatus());
        return searchVo;
    }

    /**
     * 物业统计分页
     * @param searchVo
     * @return
     */
    public PageResult<RealtyStatistics> realtyStatisticsPageResult(StatisticsSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countRealtyStatistics(searchVo), () -> getBaseMapper().listRealtyStatistics(searchVo));
    }

    /**
     * 根据统计日期获取物业统计记录
     * @param statisticsDate
     * @return
     */
    public RealtyStatistics findRealtyStatisticsByDate(LocalDate statisticsDate) {
        return getOne(new QueryWrapper<RealtyStatistics>().lambda().eq(RealtyStatistics::getStatisticsDate, statisticsDate));
    }
}
