package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.domain.SecurityJournal;
import com.senox.realty.mapper.SecurityJournalMapper;
import com.senox.realty.vo.SecurityJournalSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26 15:16
 */
@Service
@RequiredArgsConstructor
public class SecurityJournalService extends ServiceImpl<SecurityJournalMapper, SecurityJournal> {

    private final SecurityMediaService mediaService;

    /**
     * 添加安保工作日志
     * @param journal
     * @param medias
     * @param reprocessMedias
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addJournal(SecurityJournal journal, List<String> medias, List<String> reprocessMedias) {
        // 保存安防日志
        if (journal.getJournalDate() == null) {
            journal.setJournalDate(LocalDate.now());
        }
        if (journal.getEventTime() == null) {
            journal.setEventTime(LocalDateTime.now());
        }
        journal.setExpense(DecimalUtils.nullToZero(journal.getExpense()));
        journal.setCreateTime(LocalDateTime.now());
        journal.setModifiedTime(LocalDateTime.now());
        save(journal);

        // 保存安防多媒体
        mediaService.saveMedias(journal, medias);
        mediaService.saveMedias(journal, 2, reprocessMedias);
        return journal.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateJournal(SecurityJournal journal, List<String> medias, List<String> reprocessMedias) {
        if (!WrapperClassUtils.biggerThanLong(journal.getId(), 0L)) {
            return;
        }

        // 更新安防日志
        journal.setCreatorId(null);
        journal.setCreatorName(null);
        journal.setCreateTime(null);
        journal.setModifiedTime(LocalDateTime.now());
        updateById(journal);

        // 保存安防多媒体
        mediaService.saveMedias(journal, medias);
        mediaService.saveMedias(journal, 2, reprocessMedias);
    }

    /**
     * 删除安保工作日志
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteJournal(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        removeById(id);
        mediaService.removeByJournalId(id);
    }

    /**
     * 根据 id 查找安防日志
     * @param id
     * @return
     */
    public SecurityJournal findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 安保日志统计
     * @param search
     * @return
     */
    public int countJournal(SecurityJournalSearchVo search) {
        return getBaseMapper().countJournal(search);
    }

    /**
     * 安保日志列表
     * @param search
     * @return
     */
    public List<SecurityJournal> listJournal(SecurityJournalSearchVo search) {
        return getBaseMapper().listJournal(search);
    }
}
