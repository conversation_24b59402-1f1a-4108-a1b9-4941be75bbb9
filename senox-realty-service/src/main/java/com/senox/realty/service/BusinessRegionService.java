package com.senox.realty.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.BusinessRegion;
import com.senox.realty.mapper.BusinessRegionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/15 15:27
 */
@Service
public class BusinessRegionService {

    @Autowired
    private BusinessRegionMapper regionMapper;


    /**
     * 添加经营区域
     * @param region
     * @return
     */
    public Long addRegion(BusinessRegion region) {
        if (StringUtils.isBlank(region.getName())) {
            return 0L;
        }
        if (region.getOrderNum() == null) {
            region.setOrderNum(0);
        }
        long result = regionMapper.addRegion(region) > 0 ? region.getId() : 0L;

        // clear cache
        if (result > 0L) {
            RedisUtils.del(RealtyConst.Cache.KEY_BUSINESS_REGION);
        }
        return result;
    }

    /**
     * 更新经营区域
     * @param region
     * @return
     */
    public boolean updateRegion(BusinessRegion region) {
        if (region.getId() == null || region.getId() < 1L) {
            return false;
        }
        boolean result = regionMapper.updateRegion(region) > 0;

        // clear cache
        if (result) {
            RedisUtils.del(RealtyConst.Cache.KEY_BUSINESS_REGION);
        }
        return result;
    }

    /**
     * 查找经营区域
     * @param id
     * @return
     */
    public BusinessRegion findById(Long id) {
        if (id == null || id < 1L) {
            return null;
        }
        return regionMapper.findById(id);
    }

    /**
     * 经营区域列表
     * @return
     */
    public List<BusinessRegion> listAll() {
        List<BusinessRegion> resultList = null;

        // load from cache
        String cacheValue = RedisUtils.get(RealtyConst.Cache.KEY_BUSINESS_REGION);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<BusinessRegion>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = regionMapper.listAll();

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(RealtyConst.Cache.KEY_BUSINESS_REGION, JsonUtils.object2Json(resultList), RealtyConst.Cache.TTL_7D);
            }
        }

        return resultList;
    }
}
