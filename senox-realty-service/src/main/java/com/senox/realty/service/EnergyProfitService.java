package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.EnergySource;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.domain.EnergyProfit;
import com.senox.realty.domain.EnergyProfitItem;
import com.senox.realty.mapper.EnergyProfitMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.EnergyProfitSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 14:44
 */
@RequiredArgsConstructor
@Service
public class EnergyProfitService extends ServiceImpl<EnergyProfitMapper, EnergyProfit> {

    private final EnergyProfitItemService profitItemService;


    /**
     * 生成能源损益
     * @param billTime
     * @param energyType
     */
    @Transactional(rollbackFor = Exception.class)
    public EnergyProfit generateProfits(BillTimeVo billTime, EnergyType energyType) {
        EnergyProfit result = findByBillTime(billTime);
        if (result != null) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "能源损益表已生成");
        }

        // 新建损益表
        result = new EnergyProfit();
        result.setBillTime(StringUtils.buildYearMonthStr(billTime.getYear(), billTime.getMonth()));
        result.setEnergyType(energyType.getValue());

        // 系统损益
        List<EnergyProfitItem> systemProfits = profitItemService.newSystemProfits(billTime, energyType);
        // 结算单损益
        List<EnergyProfitItem> balanceProfits = profitItemService.newBalanceProfits(billTime, energyType);
        prepareRecordedProfits(result, systemProfits);
        prepareBalanceProfits(result, balanceProfits);
        prepareDiffProfits(result);

        // 新增损益表
        addProfit(result);
        // 明细
        profitItemService.batchAddProfitItems(result, systemProfits);
        profitItemService.batchAddProfitItems(result, balanceProfits);

        return result;
    }

    /**
     * 更新能源损益
     * @param profit
     * @param systemProfits
     * @param balanceProfits
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public EnergyProfit saveProfits(EnergyProfit profit, List<EnergyProfitItem> systemProfits, List<EnergyProfitItem> balanceProfits) {
        prepareRecordedProfits(profit, systemProfits);
        prepareBalanceProfits(profit, balanceProfits);
        prepareDiffProfits(profit);

        // 保存损益表
        updateById(profit);
        // 明细
        profitItemService.saveProfitItems(profit, systemProfits, EnergySource.SYSTEM);
        profitItemService.saveProfitItems(profit, balanceProfits, EnergySource.THIRD_PARTY);

        return profit;
    }

    /**
     * 添加能源损益表
     * @param profit
     */
    public void addProfit(EnergyProfit profit) {
        ContextUtils.initEntityCreator(profit);
        ContextUtils.initEntityModifier(profit);
        profit.setCreateTime(LocalDateTime.now());
        profit.setModifiedTime(profit.getCreateTime());

        save(profit);
    }

    /**
     * 根据id查找能源损益表
     * @param id
     * @return
     */
    public EnergyProfit findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 根据账单时间查找损益表
     * @param billTime
     * @return
     */
    public EnergyProfit findByBillTime(BillTimeVo billTime) {
        LambdaQueryWrapper<EnergyProfit> queryWrapper = new QueryWrapper<EnergyProfit>().lambda()
                .eq(EnergyProfit::getBillTime, StringUtils.buildYearMonthStr(billTime.getYear(), billTime.getMonth()));
        return getOne(queryWrapper);
    }

    /**
     * 能源收益页
     * @param search
     * @return
     */
    public PageResult<EnergyProfit> listPage(EnergyProfitSearchVo search) {
        return PageUtils.commonPageResult(search, () -> countProfit(search), () -> listProfit(search));
    }

    /**
     * 能源收益合计
     * @param search
     * @return
     */
    private int countProfit(EnergyProfitSearchVo search) {
        return getBaseMapper().countProfit(search);
    }

    /**
     * 能源收益列表
     * @param search
     * @return
     */
    private List<EnergyProfit> listProfit(EnergyProfitSearchVo search) {
        return getBaseMapper().listProfit(search);
    }

    /**
     * 初始化系统记录的损益表
     * @param profit
     * @param list
     */
    private void prepareRecordedProfits(EnergyProfit profit, List<EnergyProfitItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        if (profit.getRecordedStartDate() == null) {
            profit.setRecordedStartDate(list.stream().map(EnergyProfitItem::getLastRecordDate).min(LocalDate::compareTo).orElse(null));
        }
        if (profit.getRecordedEndDate() == null) {
            profit.setRecordedEndDate(list.stream().map(EnergyProfitItem::getRecordDate).max(LocalDate::compareTo).orElse(null));
        }
        if (profit.getRecordedStartDate() != null && profit.getRecordedEndDate() != null) {
            profit.setRecordedDays((int) DateUtils.getDaysBetween(profit.getRecordedStartDate(), profit.getRecordedEndDate()));
        }
        profit.setRecordedCost(list.stream().mapToInt(EnergyProfitItem::getCost).sum());
    }

    /**
     * 根据账单信息初始化损益表
     * @param profit
     * @param list
     */
    private void prepareBalanceProfits(EnergyProfit profit, List<EnergyProfitItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        profit.setBalanceCost(list.stream().mapToInt(EnergyProfitItem::getCost).sum());
    }

    /**
     * 损耗分析
     * @param profit
     */
    private void prepareDiffProfits(EnergyProfit profit) {
        // 抄表差异天数（结算间隔天数 - 实用间隔天数）
        profit.setDiffDays(profit.getBalanceDays() - profit.getRecordedDays());
        // 抄表差异度数（结算度数 / 结算抄表天数 * 抄表差异天数）
        profit.setDiffCost(profit.getBalanceCost() * profit.getDiffDays() / profit.getDiffDays());
        // 总损耗度数（结算度数 - 抄表差异度数 - 实用度数）
        profit.setProfitsCost(profit.getBalanceCost() - profit.getDiffCost() - profit.getRecordedCost());
        // 损耗率（总损耗度数 / 结算读数）
        profit.setProfitsRate(new BigDecimal(profit.getProfitsCost()).multiply(new BigDecimal(100)).divide(new BigDecimal(profit.getBalanceCost()), 2, RoundingMode.HALF_UP));
    }

}
