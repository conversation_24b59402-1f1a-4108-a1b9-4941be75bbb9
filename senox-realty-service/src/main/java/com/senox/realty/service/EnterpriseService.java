package com.senox.realty.service;

import com.senox.realty.component.EnterpriseComponent;
import com.senox.user.vo.EnterpriseRealtyVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/18 11:17
 */
@Service
@RequiredArgsConstructor
public class EnterpriseService {

    private final EnterpriseComponent enterpriseComponent;

    /**
     * 经营户列表
     * @param search
     * @return
     */
    public List<EnterpriseViewVo> listEnterprise(EnterpriseSearchVo search) {
        search.setPage(false);
        return enterpriseComponent.listEnterprise(search);
    }


    /**
     * 经营户物业列表
     * @param enterpriseIds
     * @return
     */
    public List<EnterpriseRealtyVo> listEnterpriseRealty(List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return Collections.emptyList();
        }

        return enterpriseComponent.listEnterpriseRealty(enterpriseIds);
    }
}
