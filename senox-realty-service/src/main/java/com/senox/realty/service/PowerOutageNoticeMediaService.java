package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.realty.constant.PowerOutageNoticeMediaType;
import com.senox.realty.convert.PowerOutageNoticeMediaConvert;
import com.senox.realty.domain.PowerOutageNoticeMedia;
import com.senox.realty.mapper.PowerOutageNoticeMediaMapper;
import com.senox.realty.vo.PowerOutageNoticeMediaVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-08
 **/
@Service
@RequiredArgsConstructor
public class PowerOutageNoticeMediaService extends ServiceImpl<PowerOutageNoticeMediaMapper, PowerOutageNoticeMedia> {
    private final PowerOutageNoticeMediaConvert mediaConvert;

    /**
     * 批量添加
     * @param noticeId 停电通知id
     * @param mediaType 媒体资源类型
     * @param mediaVoList 媒体资源集
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(long noticeId, PowerOutageNoticeMediaType mediaType, List<PowerOutageNoticeMediaVo> mediaVoList) {
        if (noticeId < 1 || null == mediaType || CollectionUtils.isEmpty(mediaVoList)) {
            return;
        }
        List<PowerOutageNoticeMedia> mediaList = mediaVoList.stream().map(x -> {
            PowerOutageNoticeMedia media = mediaConvert.toDo(x);
            media.setPowerOutageNoticeId(noticeId);
            media.setType(mediaType.getNumber());
            return media;
        }).collect(Collectors.toList());
        saveBatch(mediaList);
    }

    /**
     * 根据停电通知id删除
     * @param ids 停电通知id集
     */
    public void deleteByNoticeIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        remove(new QueryWrapper<PowerOutageNoticeMedia>().lambda().in(PowerOutageNoticeMedia::getPowerOutageNoticeId, ids));
    }
}
