package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.EnergyConsumeUnit;
import com.senox.realty.mapper.EnergyConsumeUnitMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/31 11:03
 */
@Service
public class EnergyConsumeUnitService extends ServiceImpl<EnergyConsumeUnitMapper, EnergyConsumeUnit> {

    /**
     * 添加能源消费单元
     * @param unit
     * @return
     */
    public Long addUnit(EnergyConsumeUnit unit) {
        // 能源类别
        EnergyType energyType = EnergyType.fromValue(unit.getType());
        if (energyType == null) {
            throw new InvalidParameterException();
        }

        if (checkUnitExist(unit)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR);
        }

        // init
        unit.setCreateTime(LocalDateTime.now());
        unit.setModifierId(unit.getCreatorId());
        unit.setModifierName(unit.getCreatorName());
        unit.setModifiedTime(unit.getCreateTime());

        save(unit);
        RedisUtils.hdel(RealtyConst.Cache.KEY_ENERGY_CONSUME_UNIT, energyType.name());
        return unit.getId();
    }

    /**
     * 更新能源消费单元
     * @param unit
     */
    public void updateUnit(EnergyConsumeUnit unit) {
        if (!WrapperClassUtils.biggerThanLong(unit.getId(), 0L)) {
            return;
        }

        // 能源类别
        EnergyType energyType = EnergyType.fromValue(unit.getType());
        if (energyType == null) {
            throw new InvalidParameterException();
        }

        if (checkUnitExist(unit)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR);
        }

        unit.setModifiedTime(LocalDateTime.now());
        updateById(unit);

        RedisUtils.hdel(RealtyConst.Cache.KEY_ENERGY_CONSUME_UNIT, energyType.name());
    }


    /**
     * 根据id查找能源消费单元
     * @param id
     * @return
     */
    public EnergyConsumeUnit findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 根据单元查找能源消费单元
     * @param unit
     * @return
     */
    public EnergyConsumeUnit findByUnit(String unit) {
        if (StringUtils.isBlank(unit)) {
            return null;
        }

        LambdaQueryWrapper<EnergyConsumeUnit> queryWrapper = new QueryWrapper<EnergyConsumeUnit>().lambda().eq(EnergyConsumeUnit::getUnit, unit);
        return getOne(queryWrapper);
    }

    /**
     * 根据能源类型查找消费单元
     * @param energyType
     * @return
     */
    public List<EnergyConsumeUnit> listByType(EnergyType energyType) {
        if (energyType == null) {
            return Collections.emptyList();
        }

        List<EnergyConsumeUnit> resultList = null;
        // load from cache
        String cachedValue = RedisUtils.hget(RealtyConst.Cache.KEY_ENERGY_CONSUME_UNIT, energyType.name());
        if (!StringUtils.isBlank(cachedValue)) {
            resultList = JsonUtils.json2GenericObject(cachedValue, new TypeReference<List<EnergyConsumeUnit>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            LambdaQueryWrapper<EnergyConsumeUnit> queryWrapper = new QueryWrapper<EnergyConsumeUnit>().lambda()
                    .eq(EnergyConsumeUnit::getType, energyType.getValue())
                    .eq(EnergyConsumeUnit::getDisabled, Boolean.FALSE)
                    .select(EnergyConsumeUnit::getId, EnergyConsumeUnit::getUnit, EnergyConsumeUnit::getType, EnergyConsumeUnit::getName);
            resultList = list(queryWrapper);

            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.hset(RealtyConst.Cache.KEY_ENERGY_CONSUME_UNIT, energyType.name(), JsonUtils.object2Json(resultList));
                RedisUtils.expire(RealtyConst.Cache.KEY_ENERGY_CONSUME_UNIT, RealtyConst.Cache.TTL_7D);
            }
        }
        return resultList;
    }

    /**
     * 消费单元是否已存在
     * @param unit
     * @return
     */
    public boolean checkUnitExist(EnergyConsumeUnit unit) {
        EnergyConsumeUnit dbUnit = findByUnit(unit.getUnit());
        return dbUnit != null && !Objects.equals(unit.getId(), dbUnit.getId());
    }
}
