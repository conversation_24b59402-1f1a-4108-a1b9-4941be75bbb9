package com.senox.realty.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.Street;
import com.senox.realty.mapper.StreetMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/15 15:27
 */
@Service
public class StreetService {

    @Autowired
    private StreetMapper streetMapper;


    /**
     * 添加街道
     * @param street
     * @return
     */
    public Long addStreet(Street street) {
        if (StringUtils.isBlank(street.getName())) {
            return 0L;
        }
        if (street.getRegionId() == null) {
            street.setRegionId(0L);
        }
        if (street.getOrderNum() == null) {
            street.setOrderNum(0);
        }
        long result = streetMapper.addStreet(street) > 0 ? street.getId() : 0L;

        // clear cache
        if (result > 0L) {
            RedisUtils.del(RealtyConst.Cache.KEY_STREET);
            RedisUtils.del(String.format(RealtyConst.Cache.KEY_REGION_STREET, street.getRegionId()));
        }
        return result;
    }

    /**
     * 更新街道
     * @param street
     * @return
     */
    public boolean updateStreet(Street street) {
        if (!WrapperClassUtils.biggerThanLong(street.getId(), 0L)) {
            return false;
        }
        Street dbStreet = findById(street.getId());
        boolean result = streetMapper.updateStreet(street) > 0;

        // clear cache
        if (result) {
            RedisUtils.del(RealtyConst.Cache.KEY_STREET);
            if (dbStreet != null && WrapperClassUtils.biggerThanLong(dbStreet.getRegionId(), 0L)) {
                RedisUtils.del(String.format(RealtyConst.Cache.KEY_REGION_STREET, dbStreet.getRegionId()));
            }
            if (WrapperClassUtils.biggerThanLong(street.getRegionId(), 0L)) {
                RedisUtils.del(String.format(RealtyConst.Cache.KEY_REGION_STREET, street.getRegionId()));
            }
        }
        return result;
    }

    /**
     * 查找街道
     * @param id
     * @return
     */
    public Street findById(Long id) {
        if (id == null || id < 1L) {
            return null;
        }
        return streetMapper.findById(id);
    }

    public List<Street> listRegionStreet(Long regionId) {
        if (!WrapperClassUtils.biggerThanLong(regionId, 0L)) {
            return Collections.emptyList();
        }

        List<Street> resultList = null;

        // load from cache
        String cacheKey = String.format(RealtyConst.Cache.KEY_REGION_STREET, regionId);
        String cacheValue = RedisUtils.get(cacheKey);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<Street>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = streetMapper.listRegionStreet(regionId);

            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(cacheKey, JsonUtils.object2Json(resultList), RealtyConst.Cache.TTL_7D);
            }
        }
        return resultList;
    }

    /**
     * 街道列表
     * @return
     */
    public List<Street> listAll() {
        List<Street> resultList = null;

        // load from cache
        String cacheValue = RedisUtils.get(RealtyConst.Cache.KEY_STREET);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<Street>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = streetMapper.listAll();

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(RealtyConst.Cache.KEY_STREET, JsonUtils.object2Json(resultList), RealtyConst.Cache.TTL_7D);
            }
        }

        return resultList;
    }
}
