package com.senox.realty.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.WaterElectricPriceType;
import com.senox.realty.mapper.WaterElectricPriceTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/9 14:26
 */
@Service
public class WaterElectricPriceTypeService {

    @Autowired
    private WaterElectricPriceTypeMapper priceTypeMapper;

    /**
     * 添加水电价格类别
     * @param priceType
     * @return
     */
    public Long addPriceType(WaterElectricPriceType priceType) {
        int result = priceTypeMapper.addPriceType(priceType);
        if (result > 0) {
            RedisUtils.del(RealtyConst.Cache.KEY_WE_PRICE_TYPE);
        }
        return result > 0 ? priceType.getId() : 0L;
    }

    /**
     * 更新水电价格类别
     * @param priceType
     * @return
     */
    public boolean updatePriceType(WaterElectricPriceType priceType) {
        if (!WrapperClassUtils.biggerThanLong(priceType.getId(), 0L)) {
            return false;
        }
        int result = priceTypeMapper.updatePriceType(priceType);
        if (result > 0) {
            RedisUtils.del(RealtyConst.Cache.KEY_WE_PRICE_TYPE);
        }
        return result > 0;
    }

    /**
     * 根据id查找水电价类别
     * @param id
     * @return
     */
    public WaterElectricPriceType findById(Long id) {
        return !WrapperClassUtils.biggerThanLong(id, 0L) ? null : priceTypeMapper.findById(id);
    }

    /**
     * 水电价列表
     * @return
     */
    public List<WaterElectricPriceType> listAll() {
        List<WaterElectricPriceType> resultList = null;

        // load from cache
        String cacheVal = RedisUtils.get(RealtyConst.Cache.KEY_WE_PRICE_TYPE);
        if (!StringUtils.isBlank(cacheVal)) {
            resultList = JsonUtils.json2GenericObject(cacheVal, new TypeReference<List<WaterElectricPriceType>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = priceTypeMapper.listAll();

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(RealtyConst.Cache.KEY_WE_PRICE_TYPE, JsonUtils.object2Json(resultList));
            }
        }
        return resultList;
    }
}
