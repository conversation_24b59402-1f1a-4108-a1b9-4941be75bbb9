package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.realty.domain.RealtyBillWe;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-7-18
 */
public interface RealtyBillWeService extends IService<RealtyBillWe> {

    /**
     * 生成物业账单
     * @param weMonth
     * @param modifier
     */
    void buildAndSaveWeBill(BillMonthVo weMonth, AdminUserDto modifier);

    /**
     * 根据合同、水电读数生成水电账单
     * @param realtyBillId
     * @param contract
     * @param we
     * @return
     */
    List<RealtyBillWe> saveContractWeBill(Long realtyBillId, ContractVo contract, List<RealtyWe> we);


    /**
     * 更新水电账单
     * @param bill
     * @return
     */
    void updateWeBill(RealtyBillWe bill);

    /**
     * 关联水电账单及物业账单
     * @param weTime
     */
    void updateWeBillId(BillMonthVo weTime);

    /**
     * 更新水电账单id
     * @param ids
     * @param billId
     */
    void updateWeBillId(List<Long> ids, Long billId);

    /**
     * 解除水电物业账单关联
     * @param billId
     */
    void unlinkWeBill(Long billId);

    /**
     * 根据年月解除水电物业账单关联
     * @param year
     * @param month
     */
    void unlinkWeBill(Integer year, Integer month);

    /**
     * 删除水电账单
     * @param id
     */
    void deleteWeBill(Long id);

    /**
     * 根据id获取水电涨啊电脑
     * @param id
     * @return
     */
    RealtyBillWeVo findById(Long id);

    /**
     * 未匹配账单的水电账单
     * @param billMonth
     * @return
     */
    List<RealtyBillWe> listNonmatchWeBill(BillMonthVo billMonth);

    /**
     * 获取物业账单水电明细
     *
     * @param billId
     * @return
     */
    List<RealtyBillWeVo> listWeBill(Long billId);

    /**
     * 物业水电账单
     *
     * @param search
     * @return
     */
    List<RealtyBillWeVo> listWeBill(RealtyBillWeSearchVo search);

    /**
     * 水电账单合计
     * @param search
     * @return
     */
    RealtyBillWeVo sumWeBill(RealtyBillWeSearchVo search);

    /**
     * 物业水电账单分页
     *
     * @param search
     * @return
     */
    PageResult<RealtyBillWeVo> listWeBillPage(RealtyBillWeSearchVo search);

}
