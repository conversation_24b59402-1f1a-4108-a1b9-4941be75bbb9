package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.MaintainChargeStatus;
import com.senox.realty.domain.MaintainCharge;
import com.senox.realty.domain.MaintainChargeItem;
import com.senox.realty.mapper.MaintainChargeMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/6 11:54
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MaintainChargeService extends ServiceImpl<MaintainChargeMapper, MaintainCharge> {

    @Value("${senox.maintainCharge.serial.prefix:CN}")
    private String chargeNoPrefix;
    @Value("${senox.maintainCharge.serial.length:5}")
    private Integer chargeNoPostfixLength;
    @Value("${senox.maintainCharge.serial.fill-char:0}")
    private Character fillChar;

    private final MaintainChargeItemService maintainChargeItemService;

    /**
     * 添加维修费用
     *
     * @param maintainCharge
     * @param chargeItems
     */
    @Transactional(rollbackFor = Exception.class)
    public void addMaintainCharge(MaintainCharge maintainCharge, List<MaintainChargeItem> chargeItems) {
        //查询db是否已经创建过收费单
        MaintainCharge dbItem = findChargeByOrderIdAndJobId(maintainCharge.getOrderId(), maintainCharge.getJobId());
        if (dbItem != null && dbItem.getStatus() == MaintainChargeStatus.PAID.getStatus()) {
            throw new BusinessException("账单已支付不能更新账单");
        }
        if (dbItem != null && CollectionUtils.isEmpty(chargeItems)) {
            log.info("【账单明细为空，删除账单】");
            removeById(dbItem.getId());
            maintainChargeItemService.removeByChargeId(dbItem.getId());
        }
        // 信息初始化
        prepareCharge(maintainCharge, dbItem, chargeItems);
        //如果金额为0则设置实收已收费
        if (DecimalUtils.equals(maintainCharge.getTotalAmount(), BigDecimal.ZERO)) {
            maintainCharge.setStatus(MaintainChargeStatus.PAID.getStatus());
        }
        maintainCharge.setModifiedTime(LocalDateTime.now());
        if (dbItem != null) {
            maintainCharge.setId(dbItem.getId());
            updateById(maintainCharge);
        } else {
            maintainCharge.setCreateTime(LocalDateTime.now());
            save(maintainCharge);
        }

        maintainChargeItemService.saveMaintainChargeItem(maintainCharge.getId(), chargeItems);
    }

    /**
     * 收费单信息初始化
     *
     * @param maintainCharge
     * @param dbItem
     * @param chargeItems
     */
    private void prepareCharge(MaintainCharge maintainCharge, MaintainCharge dbItem, List<MaintainChargeItem> chargeItems) {
        maintainCharge.setChargeYear(LocalDateTime.now().getYear());
        maintainCharge.setChargeMonth(LocalDateTime.now().getMonthValue());
        maintainCharge.setTotalAmount(BigDecimal.ZERO);
        if (!CollectionUtils.isEmpty(chargeItems)){
            if (!chargeItems.stream().allMatch(x -> DecimalUtils.isPositive(x.getAmount()))) {
                throw new BusinessException("账单费项金额不能为0");
            }
            maintainCharge.setTotalAmount(chargeItems.stream().map(MaintainChargeItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (DecimalUtils.equals(BigDecimal.ZERO, maintainCharge.getTotalAmount())) {
            throw new BusinessException("账单总金额不能为0");
        }
        // 订单号
        if (StringUtils.isBlank(maintainCharge.getChargeNo()) && dbItem == null) {
            prepareChargeNo(maintainCharge);
        }
    }

    /**
     * 构建收费单号
     * @param maintainCharge
     */
    private void prepareChargeNo(MaintainCharge maintainCharge) {
        String prefix = chargeNoPrefix.concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        maintainCharge.setChargeNo(prepareChargeNo(prefix, chargeNoPostfixLength, fillChar));
    }

    /**
     * 收费单号初始化
     *
     * @param prefix
     * @param length
     * @param fillChar
     * @return
     */
    private String prepareChargeNo(String prefix, Integer length, Character fillChar) {

        Long dbSerial = findMaxChargeSerial(prefix);

        return prefix.concat(StringUtils.fixLength(String.valueOf(++dbSerial), length, fillChar));
    }

    /**
     * 获取最大收费单序号
     *
     * @param prefix
     * @return
     */
    private Long findMaxChargeSerial(String prefix) {
        String maxChargeNo = getBaseMapper().findMaxChargeNo(prefix);
        return (StringUtils.isBlank(maxChargeNo) ? 0L : NumberUtils.parseLong(maxChargeNo.substring(prefix.length()), 0L));
    }

    /**
     * 根据订单id和派工id查询收费单
     *
     * @param orderId
     * @param jobId
     * @return
     */
    private MaintainCharge findChargeByOrderIdAndJobId(Long orderId, Long jobId) {
        return getOne(new QueryWrapper<MaintainCharge>().lambda().eq(MaintainCharge::getOrderId, orderId)
                .eq(MaintainCharge::getJobId, jobId));
    }

    /**
     * 根据派工id查询收费单
     *
     * @param jobId
     * @return
     */
    public MaintainCharge findChargeByJobId(Long jobId) {
        return getOne(new QueryWrapper<MaintainCharge>().lambda().eq(MaintainCharge::getJobId, jobId));
    }

    /**
     * 根据订单id查询账单
     * @param orderId
     * @return
     */
    public List<MaintainCharge> listChargeByOrderId(Long orderId) {
        return list(new QueryWrapper<MaintainCharge>().lambda().eq(MaintainCharge::getOrderId, orderId));
    }

    /**
     * 删除账单明细
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaintainChargeItem(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        MaintainChargeItem chargeItem = maintainChargeItemService.getById(id);
        if (chargeItem == null) {
            log.info("账单明细未找到....");
            return;
        }
        MaintainCharge charge = getById(chargeItem.getChargeId());
        if (charge == null || charge.getStatus() == MaintainChargeStatus.PAID.getStatus()) {
            throw new BusinessException("账单未找到或账单已支付");
        }
        maintainChargeItemService.removeById(id);
        List<MaintainChargeItem> items = maintainChargeItemService.maintainChargeItemList(chargeItem.getChargeId());
        if (CollectionUtils.isEmpty(items)) {
            log.info("账单明细为空，移除账单...");
            removeById(chargeItem.getChargeId());
            return;
        }
        BigDecimal totalAmount = items.stream().map(MaintainChargeItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        charge.setTotalAmount(totalAmount);
        charge.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(charge);
        updateById(charge);
    }

    /**
     * 删除物维账单
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaintainCharge(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        MaintainCharge charge = getById(id);
        if (charge == null || charge.getStatus() == MaintainChargeStatus.PAID.getStatus()) {
            throw new BusinessException("账单未找到或账单已支付");
        }
        removeById(id);
        //删除明细
        maintainChargeItemService.removeByChargeId(id);
    }

    /**
     * 物维账单列表
     *
     * @param search
     * @return
     */
    public PageResult<MaintainChargeDataVo> listMaintainCharge(MaintainChargeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = getBaseMapper().countMaintainCharge(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<MaintainChargeDataVo> resultList = getBaseMapper().listMaintainCharge(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 根据id获取账单信息
     *
     * @param id
     * @return
     */
    public MaintainChargeDataVo chargeDataVoById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return getBaseMapper().chargeDataVoById(id);
    }

    /**
     * 根据派工id获取账单信息
     *
     * @param jobId
     * @return
     */
    public MaintainChargeDataVo chargeDataVoByJobId(Long jobId) {
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            return null;
        }
        return getBaseMapper().chargeDataVoByJobId(jobId);
    }

    /**
     * 根据订单id获取账单信息
     * @param orderId
     * @return
     */
    public List<MaintainChargeDataVo> listChargeDataVoByOrderId(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        return getBaseMapper().listChargeDataVoByOrderId(orderId);
    }

    /**
     * 物维账单合计
     *
     * @param searchVo
     * @return
     */
    public MaintainChargeDataVo sumMaintainCharge(MaintainChargeSearchVo searchVo) {
        return getBaseMapper().sumMaintainCharge(searchVo);
    }

    /**
     * 更新收费账单信息
     *
     * @param billPaid
     */
    public void updateChargeStatus(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return;
        }

        // 更新订单支付结果
        if (!CollectionUtils.isEmpty(billPaid.getBillIds())) {
            log.info("根据物维收费账单id更新账单状态 {}", JsonUtils.object2Json(billPaid));
            getBaseMapper().updateChargeStatusById(billPaid);
        } else {
            log.info("根据支付订单id更新物维账单状态-{}", JsonUtils.object2Json(billPaid));
            getBaseMapper().updateChargeStatusByOrderId(billPaid);
        }
    }

    /**
     * 更新票据号
     *
     * @param chargeSerial
     */
    public void updateChargeSerial(MaintainChargeSerialVo chargeSerial) {
        if (!WrapperClassUtils.biggerThanLong(chargeSerial.getChargeId(), 0L)) {
            return;
        }

        getBaseMapper().updateChargeSerial(chargeSerial);
    }

    /**
     * 更新账单备注
     * @param chargeRemarkList
     */
    public void updateChargeRemark(List<MaintainChargeRemarkVo> chargeRemarkList) {
        if (CollectionUtils.isEmpty(chargeRemarkList)) {
            return;
        }
        getBaseMapper().updateChargeRemark(chargeRemarkList);
    }

    /**
     * 批量插入账单
     * @param chargeMap
     */
    public void batchSaveCharge(Map<MaintainCharge, List<MaintainChargeItem>> chargeMap) {
        if (CollectionUtils.isEmpty(chargeMap)) {
            return;
        }
        for (Map.Entry<MaintainCharge, List<MaintainChargeItem>> entry : chargeMap.entrySet()) {
            MaintainCharge charge = entry.getKey();
            List<MaintainChargeItem> chargeItems = entry.getValue();
            MaintainCharge dbCharge = findChargeByJobId(charge.getJobId());
            if (dbCharge != null) {
                if (dbCharge.getStatus() == MaintainChargeStatus.PAID.getStatus()) {
                    log.info("账单已支付不能更新账单, 单号：{}", JsonUtils.object2Json(dbCharge));
                    throw new BusinessException("账单已支付不能更新账单, 单号: " + dbCharge.getChargeNo());
                }
                if (CollectionUtils.isEmpty(chargeItems)) {
                    log.info("【账单明细为空，删除账单】");
                    removeById(dbCharge.getId());
                    maintainChargeItemService.removeByChargeId(dbCharge.getId());
                    continue;
                }
                charge.setId(dbCharge.getId());
                charge.setChargeNo(dbCharge.getChargeNo());
            } else {
                prepareChargeNo(charge);
            }
            if (!CollectionUtils.isEmpty(chargeItems)){
                if (!chargeItems.stream().allMatch(x -> DecimalUtils.isPositive(x.getAmount()))) {
                    log.info("账单费项金额不能为0");
                    throw new BusinessException("账单费项金额不能为0");
                }
                charge.setTotalAmount(chargeItems.stream().map(MaintainChargeItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                if (!DecimalUtils.isPositive(charge.getTotalAmount())) {
                    log.info("账单总金额不能为0");
                    throw new BusinessException("账单总金额不能为0");
                }
            }
            saveOrUpdate(charge);
            maintainChargeItemService.saveMaintainChargeItem(charge.getId(), chargeItems);
        }
    }

    public MaintainCharge buildCharge(Long jobId, Long orderId) {
        MaintainCharge charge = new MaintainCharge();
        charge.setOrderId(orderId);
        charge.setJobId(jobId);
        charge.setCreateTime(LocalDateTime.now());
        charge.setModifiedTime(LocalDateTime.now());
        charge.setChargeYear(LocalDateTime.now().getYear());
        charge.setChargeMonth(LocalDateTime.now().getMonthValue());
        ContextUtils.initEntityModifier(charge);
        ContextUtils.initEntityModifier(charge);
        return charge;
    }

    public void initChargeItem(List<MaintainChargeItem> chargeItems) {
        if (CollectionUtils.isEmpty(chargeItems)) {
            return;
        }
        chargeItems.forEach(item -> {
            ContextUtils.initEntityCreator(item);
            ContextUtils.initEntityModifier(item);
            item.setCreateTime(LocalDateTime.now());
            item.setModifiedTime(LocalDateTime.now());
        });
    }
}
