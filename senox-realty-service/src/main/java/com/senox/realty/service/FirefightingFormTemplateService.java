package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.FirefightingFormTemplate;
import com.senox.realty.mapper.FirefightingFormTemplateMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/28 14:21
 */
@Service
@RequiredArgsConstructor
public class FirefightingFormTemplateService extends ServiceImpl<FirefightingFormTemplateMapper, FirefightingFormTemplate> {

    /**
     * 添加表单模板
     * @param formTemplate
     * @return
     */
    public Long addFormTemplate(FirefightingFormTemplate formTemplate) {
        if (checkFormTemplateExist(formTemplate)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "表单属性已设置模板");
        }

        // save
        formTemplate.setCreateTime(LocalDateTime.now());
        formTemplate.setModifiedTime(LocalDateTime.now());
        save(formTemplate);

        // reset cache
        RedisUtils.hdel(RealtyConst.Cache.KEY_FIREFIGHTING_FORM_TEMPLATE, formTemplate.getForm());
        return formTemplate.getId();
    }

    /**
     * 修改表单模板
     * @param formTemplate
     */
    public void updateFormTemplate(FirefightingFormTemplate formTemplate) {
        if (!WrapperClassUtils.biggerThanLong(formTemplate.getId(), 0L)) {
            return;
        }

        if (checkFormTemplateExist(formTemplate)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "表单属性已设置模板");
        }

        // save
        formTemplate.setCreatorId(null);
        formTemplate.setCreatorName(null);
        formTemplate.setCreateTime(null);
        formTemplate.setModifiedTime(LocalDateTime.now());
        updateById(formTemplate);

        // reset cache
        RedisUtils.hdel(RealtyConst.Cache.KEY_FIREFIGHTING_FORM_TEMPLATE, formTemplate.getForm());
    }

    /**
     * 删除表单模板
     * @param id
     */
    public void deleteFormTemplate(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        FirefightingFormTemplate template = findById(id);
        if (template != null) {
            removeById(id);

            RedisUtils.hdel(RealtyConst.Cache.KEY_FIREFIGHTING_FORM_TEMPLATE, template.getForm());
        }
    }

    /**
     * 获取表单属性模板
     * @param id
     * @return
     */
    public FirefightingFormTemplate findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return getById(id);
    }

    /**
     * 获取表单属性模板
     * @param form
     * @param formAttr
     * @return
     */
    public FirefightingFormTemplate findByFormAndAttr(String form, String formAttr) {
        if (StringUtils.isBlank(form) || StringUtils.isBlank(formAttr)) {
            return null;
        }

        LambdaQueryWrapper<FirefightingFormTemplate> queryWrapper = new QueryWrapper<FirefightingFormTemplate>().lambda()
                .eq(FirefightingFormTemplate::getForm, form)
                .eq(FirefightingFormTemplate::getFormAttr, formAttr);
        return getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 根据表单获取表单模板
     * @param form
     * @return
     */
    public List<FirefightingFormTemplate> listByForm(String form) {
        if (StringUtils.isBlank(form)) {
            return Collections.emptyList();
        }

        List<FirefightingFormTemplate> resultList = null;
        // load from cache
        String cachedVal = RedisUtils.hget(RealtyConst.Cache.KEY_FIREFIGHTING_FORM_TEMPLATE, form);
        if (!StringUtils.isBlank(cachedVal)) {
            resultList = JsonUtils.json2GenericObject(cachedVal, new TypeReference<List<FirefightingFormTemplate>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            LambdaQueryWrapper<FirefightingFormTemplate> queryWrapper = new QueryWrapper<FirefightingFormTemplate>().lambda()
                    .eq(FirefightingFormTemplate::getForm, form);
            resultList = getBaseMapper().selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.hset(RealtyConst.Cache.KEY_FIREFIGHTING_FORM_TEMPLATE, form, JsonUtils.object2Json(resultList));
            }
        }
        return resultList;
    }

    /**
     * 表单模板是否已设置
     * @param formTemplate
     * @return
     */
    private boolean checkFormTemplateExist(FirefightingFormTemplate formTemplate) {
        FirefightingFormTemplate dbItem = findByFormAndAttr(formTemplate.getForm(), formTemplate.getFormAttr());
        return dbItem != null
                && (!WrapperClassUtils.biggerThanLong(formTemplate.getId(), 0L) || !Objects.equals(formTemplate.getId(), dbItem.getId()));
    }
}
