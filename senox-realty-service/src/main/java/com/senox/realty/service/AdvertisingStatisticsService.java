package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.AdvertisingStatistics;
import com.senox.realty.mapper.AdvertisingBillMapper;
import com.senox.realty.mapper.AdvertisingSpaceMapper;
import com.senox.realty.mapper.AdvertisingStatisticsMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.StatisticsGenerateVo;
import com.senox.realty.vo.StatisticsSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/25 10:29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdvertisingStatisticsService extends ServiceImpl<AdvertisingStatisticsMapper, AdvertisingStatistics> {

    private final AdvertisingBillMapper billMapper;
    private final AdvertisingSpaceMapper spaceMapper;

    /**
     * 生成广告位统计
     * @param generateVo
     */
    public void generateAdvertisingStatistics(StatisticsGenerateVo generateVo) {
        final String buildLock = String.format(RealtyConst.Cache.KEY_ADVERTISING_STATISTICS_BUILD, generateVo.getYear(), generateVo.getMonth(), generateVo.getDay());
        if (!RedisUtils.lock(buildLock, RealtyConst.Cache.TTL_1H)) {
            throw new BusinessException("【生成广告位统计】系统处理中，请稍侯。");
        }
        LocalDate localDate =LocalDate.of(generateVo.getYear(), generateVo.getMonth(), generateVo.getDay());
        try {
            log.info("开始生成【广告位统计】--{}--{}--{}", generateVo.getYear(), generateVo.getMonth(), generateVo.getDay());
            AdvertisingStatistics statistics = spaceMapper.advertisingStatistics();
            log.info("【广告位数量】：{}", JsonUtils.object2Json(statistics));
            AdvertisingStatistics advertisingBillStatistics = billMapper.advertisingBillStatistics(generateVo);
            log.info("【应收金额统计】：{}", JsonUtils.object2Json(advertisingBillStatistics));
            statistics.setAdvertisingNum(statistics.getRentAdvertisingNum() + statistics.getUnRentAdvertisingNum());
            statistics.setUnRentCollectNum(advertisingBillStatistics == null ? 0 : advertisingBillStatistics.getUnRentCollectNum());
            statistics.setUnRentCollectAmount(advertisingBillStatistics == null ? BigDecimal.ZERO
                    : DecimalUtils.nullToZero(advertisingBillStatistics.getUnRentCollectAmount()));
            statistics.setRentCollectNum(advertisingBillStatistics == null ? 0 : advertisingBillStatistics.getRentCollectNum());
            statistics.setRentCollectAmount(advertisingBillStatistics == null ? BigDecimal.ZERO
                    : DecimalUtils.nullToZero(advertisingBillStatistics.getRentCollectAmount()));
            statistics.setStatisticsDate(localDate);
            log.info("初始化广告位统计完毕，数据为：{}", JsonUtils.object2Json(statistics));
            ContextUtils.initEntityModifier(statistics);
            statistics.setModifiedTime(LocalDateTime.now());
            AdvertisingStatistics advertisingStatistics = findAdvertisingStatisticsByDate(localDate);
            if (advertisingStatistics != null) {
                log.info("【广告位统计】--{}--{}--{} 存在数据，结果为:{}，update", generateVo.getYear(), generateVo.getMonth(), generateVo.getDay(), JsonUtils.object2Json(advertisingStatistics));
                statistics.setId(advertisingStatistics.getId());
            } else {
                ContextUtils.initEntityCreator(statistics);
                statistics.setCreateTime(LocalDateTime.now());
            }
            saveOrUpdate(statistics);
        } finally {
            RedisUtils.del(buildLock);
        }
    }

    /**
     * 广告位统计分页
     * @param searchVo
     * @return
     */
    public PageResult<AdvertisingStatistics> advertisingStatisticsPageResult(StatisticsSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countAdvertisingStatistics(searchVo), () -> getBaseMapper().listAdvertisingStatistics(searchVo));
    }

    /**
     * 根据统计日期获取广告位统计记录
     * @param statisticsDate
     * @return
     */
    public AdvertisingStatistics findAdvertisingStatisticsByDate(LocalDate statisticsDate) {
        return getOne(new QueryWrapper<AdvertisingStatistics>().lambda().eq(AdvertisingStatistics::getStatisticsDate, statisticsDate));
    }
}
