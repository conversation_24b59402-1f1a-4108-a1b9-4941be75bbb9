package com.senox.realty.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.domain.FirefightingInspectTaskRealty;
import com.senox.realty.mapper.FirefightingInspectTaskRealtyMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/18 14:03
 */
@Service
@RequiredArgsConstructor
public class FirefightingInspectTaskRealtyService {

    private final FirefightingInspectTaskRealtyMapper inspectTaskRealtyMapper;

    /**
     * 添加巡检任务物业
     * @param list
     */
    public void batchAddInspectTaskRealty(List<FirefightingInspectTaskRealty> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        inspectTaskRealtyMapper.batchAddInspectTaskRealty(list);
    }

    /**
     * 删除巡检任务物业
     * @param taskId
     * @param taskItemIds
     */
    public void deleteInspectTaskRealtyByTask(Long taskId, List<Long> taskItemIds) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0L) || CollectionUtils.isEmpty(taskItemIds)) {
            return;
        }

        inspectTaskRealtyMapper.deleteInspectTaskRealtyByTask(taskId, taskItemIds);
    }
}
