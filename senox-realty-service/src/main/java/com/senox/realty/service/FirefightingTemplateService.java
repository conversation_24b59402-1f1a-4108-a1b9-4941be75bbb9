package com.senox.realty.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.TemplateStatus;
import com.senox.realty.domain.FirefightingTemplate;
import com.senox.realty.domain.FirefightingTemplateVariables;
import com.senox.realty.mapper.FirefightingTemplateMapper;
import com.senox.realty.vo.FireFightingTemplateSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/23 9:16
 */
@Service
@RequiredArgsConstructor
public class FirefightingTemplateService extends ServiceImpl<FirefightingTemplateMapper, FirefightingTemplate> {

    private final FirefightingTemplateVariablesService templateVariablesService;

    /**
     * 添加模板及模板变量
     * @param template
     * @param attrs
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addTemplate(FirefightingTemplate template, List<FirefightingTemplateVariables> attrs) {
        Long result = addTemplate(template);
        if (result > 0) {
            templateVariablesService.saveVariables(template.getCode(), template.getVersion(), attrs);
        }
        return result;
    }

    /**
     * 更新模板及模板变量
     * @param template
     * @param attrs
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplate(FirefightingTemplate template, List<FirefightingTemplateVariables> attrs) {
        if (!WrapperClassUtils.biggerThanLong(template.getId(), 0L)) {
            return;
        }

        updateTemplate(template);
        if (!StringUtils.isBlank(template.getCode()) && template.getVersion() != null) {
            templateVariablesService.saveVariables(template.getCode(), template.getVersion(), attrs);
        }
    }

    /**
     * 添加模板
     * @param template
     * @return
     */
    public Long addTemplate(FirefightingTemplate template) {
        if (template.getStatus() == null) {
            template.setStatus(TemplateStatus.INIT.getValue());
        }
        if (template.getVersion() == null) {
            FirefightingTemplate dbItem = findLatestTemplateByCode(template.getCode());
            template.setVersion(dbItem == null || dbItem.getVersion() == null ? 1 : dbItem.getVersion() + 1);
        }

        if (checkTemplateVersionExist(template)) {
            throw new BusinessException("模板版本已存在");
        }

        template.setValidDate(null);
        template.setInvalidDate(null);
        template.setCreateTime(LocalDateTime.now());
        template.setModifiedTime(LocalDateTime.now());
        save(template);
        return template.getId();
    }

    /**
     * 更新模板
     * @param template
     */
    public void updateTemplate(FirefightingTemplate template) {
        FirefightingTemplate dbTemplate = findTemplateById(template.getId());
        if (dbTemplate == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        TemplateStatus status = TemplateStatus.fromStatus(dbTemplate.getStatus());
        if (template.getStatus() == null && status != TemplateStatus.INIT) {
            throw new BusinessException("只允许修改未生效的模板");
        }

        if (checkTemplateVersionExist(template)) {
            throw new BusinessException("模板版本已存在");
        }

        // 不允许更新版本号
        Integer version = template.getVersion();
        template.setVersion(null);
        template.setCreatorId(null);
        template.setCreatorName(null);
        template.setCreateTime(null);
        template.setModifiedTime(LocalDateTime.now());
        updateById(template);

        // 回设版本（防丢处理）
        template.setVersion(version);
    }

    /**
     * 删除模板
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTemplate(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        LambdaQueryWrapper<FirefightingTemplate> queryWrapper = new QueryWrapper<FirefightingTemplate>().lambda()
                .eq(FirefightingTemplate::getId, id)
                .eq(FirefightingTemplate::getStatus, TemplateStatus.INIT.getValue());
        boolean result = remove(queryWrapper);
        if (result) {
            templateVariablesService.deleteVariables(id);
        }
    }


    /**
     * 根据id获取告知单模板
     * @param id
     * @return
     */
    public FirefightingTemplate findTemplateById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 根据模板编码和版本获取告知单模板
     * @param code
     * @param version
     * @return
     */
    public FirefightingTemplate findTemplateByCodeAndVersion(String code, Integer version) {
        LambdaQueryWrapper<FirefightingTemplate> queryWrapper = new QueryWrapper<FirefightingTemplate>().lambda()
                .eq(FirefightingTemplate::getCode, code)
                .eq(FirefightingTemplate::getVersion, version);
        return getOne(queryWrapper);
    }

    /**
     * 根据编码取最新一个模板
     * @param code
     * @return
     */
    private FirefightingTemplate findLatestTemplateByCode(String code) {
        FireFightingTemplateSearchVo search = new FireFightingTemplateSearchVo();
        search.setCode(code);

        return findLatestTemplateByCode(search);
    }

    /**
     * 根据编码取最新一个模板
     * @param search
     * @return
     */
    public FirefightingTemplate findLatestTemplateByCode(FireFightingTemplateSearchVo search) {
        if (StringUtils.isBlank(search.getCode())) {
            return null;
        }

        TemplateStatus templateStatus = TemplateStatus.fromStatus(search.getStatus());
        LambdaQueryWrapper<FirefightingTemplate> queryWrapper = new QueryWrapper<FirefightingTemplate>().lambda()
                .eq(!StringUtils.isBlank(search.getCode()), FirefightingTemplate::getCode, search.getCode())
                .eq(templateStatus != null, FirefightingTemplate::getStatus, search.getStatus())
                .ge(search.getVersionGe() != null, FirefightingTemplate::getVersion, search.getVersionGe())
                .le(search.getVersionLe() != null, FirefightingTemplate::getVersion, search.getVersionLe())
                .orderByDesc(FirefightingTemplate::getVersion)
                .last("LIMIT 1");
        return getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 店铺消防安全告知单模板统计
     * @param search
     * @return
     */
    public int countTemplate(FireFightingTemplateSearchVo search) {
        return getBaseMapper().countTemplate(search);
    }

    /**
     * 店铺消防安全告知单模板列表
     * @param search
     * @return
     */
    public List<FirefightingTemplate> listTemplate(FireFightingTemplateSearchVo search) {
        return getBaseMapper().listTemplate(search);
    }

    /**
     * 模板版本是否存在
     * @param template
     * @return
     */
    public boolean checkTemplateVersionExist(FirefightingTemplate template) {
        FirefightingTemplate dbItem = findTemplateByCodeAndVersion(template.getCode(), template.getVersion());

        return dbItem != null && (template.getId() == null || !Objects.equals(template.getId(), dbItem.getId()));
    }

}
