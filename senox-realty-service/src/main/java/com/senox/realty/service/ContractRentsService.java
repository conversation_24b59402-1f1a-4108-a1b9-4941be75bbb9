package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.ContractRents;
import com.senox.realty.mapper.ContractRentsMapper;
import com.senox.realty.vo.ContractRentsSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合同租赁信息冗余
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractRentsService extends ServiceImpl<ContractRentsMapper, ContractRents>  {

    private final ContractRentsMapper contractRentsMapper;
    private final RealtyDepositService realtyDepositService;

    /**
     * 整合查询需要新增或者更新的赁合同的租金，管理费，押金，押金状态，代租合同号，业主等
     * @param contractRents
     * @return
     * */
    public List<ContractRents> contractRentsList(ContractRentsSearchVo contractRents){
        return contractRentsMapper.contractRentsList(contractRents);
    }

    /**
     * 根据合同编号查询
     * @param contracts
     * @return
     */
    public List<ContractRents> listByContractNos(List<String> contracts) {
        if (CollectionUtils.isEmpty(contracts)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<ContractRents>().lambda()
                .in(ContractRents::getContractNo, contracts));
    }

    /**
     * 保存
     * @param rentsList
     */
    public void saveContractRents(List<ContractRents> rentsList) {
        if (CollectionUtils.isEmpty(rentsList)) {
            return;
        }
        List<ContractRents> dbRents = listByContractNos(rentsList.stream().map(ContractRents::getContractNo).collect(Collectors.toList()));
        DataSepDto<ContractRents> sepDto = separateData(dbRents, rentsList);
        log.info("数据对比后的sepDto值：{}", JsonUtils.object2Json(sepDto));
        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            saveBatch(sepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getUpdateList())) {
            updateBatchById(sepDto.getUpdateList());
        }
    }

    /**
     * 数据对比
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<ContractRents> separateData(List<ContractRents> srcList, List<ContractRents> targetList) {
        DataSepDto<ContractRents> sepDto = new DataSepDto<>();
        List<ContractRents> addList = new ArrayList<>(targetList.size());
        List<ContractRents> updateList = new ArrayList<>(targetList.size());
        if (CollectionUtils.isEmpty(srcList)) {
            targetList.forEach(item -> {
                item.setCreateTime(LocalDateTime.now());
                item.setModifiedTime(LocalDateTime.now());
            });
            addList = targetList;
        } else {
            Map<String, ContractRents> srcMap = srcList.stream()
                    .collect(Collectors.toMap(ContractRents::getContractNo, Function.identity()));

            for (ContractRents item : targetList) {
                ContractRents srcItem = srcMap.get(item.getContractNo());
                if (srcItem == null) {
                    item.setCreateTime(LocalDateTime.now());
                    item.setModifiedTime(LocalDateTime.now());
                    // 新增
                    addList.add(item);

                } else {
                    item.setModifiedTime(LocalDateTime.now());
                    updateList.add(item);
                }
            }
        }
        sepDto.setAddList(addList);
        sepDto.setUpdateList(updateList);
        return sepDto;
    }

    /**
     * 通过账单id或者支付订单id更新updateContractRent账单状态
     * @param billPaid
     * @return
     */
    public void updateContractRent(BillPaidVo billPaid){
        List<String> contractNoList = realtyDepositService.getContractNoList(billPaid);
        log.info("所更改的合同号:{}",JsonUtils.object2Json(contractNoList));
        if (!CollectionUtils.isEmpty(contractNoList)) {
            ContractRentsSearchVo contractRentsSearchVo = new ContractRentsSearchVo();
            contractRentsSearchVo.setContractNoList(contractNoList);
            List<ContractRents> contractRentsList = contractRentsList(contractRentsSearchVo);
            saveContractRents(contractRentsList);
        }
    }
}
