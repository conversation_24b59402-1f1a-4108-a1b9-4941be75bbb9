package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.InspectResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingFile;
import com.senox.realty.domain.FirefightingFileDetail;
import com.senox.realty.event.InspectTaskDropEvent;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.mapper.FirefightingFileMapper;
import com.senox.realty.vo.FirefightingFileBriefVo;
import com.senox.realty.vo.FirefightingFileSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 9:59
 */
@Service
@RequiredArgsConstructor
public class FirefightingFileService extends ServiceImpl<FirefightingFileMapper, FirefightingFile> {

    private final FirefightingFileDetailService detailService;
    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final ApplicationEventPublisher publisher;

    /**
     * 添加消防安全档案
     * @param taskId
     * @param file
     * @param detail
     * @param realtySerials
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addFile(Long taskId,
                        FirefightingFile file,
                        FirefightingFileDetail detail,
                        List<String> realtySerials,
                        List<String> reinspectMedias) {
        file.setBusinessLicenseIssued(BooleanUtils.isTrue(file.getBusinessLicenseIssued()));
        file.setFireAlarmsDisposed(BooleanUtils.isTrue(file.getFireAlarmsDisposed()));
        file.setFireReelsDisposed(BooleanUtils.isTrue(file.getFireReelsDisposed()));
        file.setSimpleSprinklersDisposed(BooleanUtils.isTrue(file.getSimpleSprinklersDisposed()));
        file.setEmergencyLightsDisposed(BooleanUtils.isTrue(file.getEmergencyLightsDisposed()));
        file.setRoomSeparated(BooleanUtils.isTrue(file.getRoomSeparated()));
        file.setEscapeHatchDisposed(BooleanUtils.isTrue(file.getEscapeHatchDisposed()));
        file.setCreateTime(LocalDateTime.now());
        file.setModifiedTime(LocalDateTime.now());
        if (file.getInspectDate() == null) {
            file.setInspectDate(LocalDate.now());
        }

        InspectResult inspectResult = InspectResult.fromValue(file.getInspectResult());
        inspectResult = inspectResult == null ? InspectResult.INIT : inspectResult;
        file.setInspectResult(inspectResult.getValue());
        // 保存消防档案
        save(file);

        // 消防档案明细
        detail.setFileId(file.getId());
        detailService.saveDetail(detail);
        inspectRealtyService.saveInspectRealities(file.getId(), InspectionType.FILE, realtySerials);

        // 复检的图片
        inspectMediaService.saveMedias(file.getId(), InspectionType.FILE, 2, reinspectMedias);

        // 发送巡检任务事件
        InspectTaskFulfilledEvent event = new InspectTaskFulfilledEvent(this, InspectionType.FILE, file.getId());
        event.setTaskId(taskId);
        event.setEnterpriseId(file.getEnterpriseId());
        event.setRealtySerials(realtySerials);
        publisher.publishEvent(event);
        return file.getId();
    }

    /**
     * 更新消防安全档案
     * @param file
     * @param detail
     * @param realtySerials
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFile(FirefightingFile file,
                           FirefightingFileDetail detail,
                           List<String> realtySerials,
                           List<String> reinspectMedias) {
        file.setCreatorId(null);
        file.setCreatorName(null);
        file.setCreateTime(null);
        file.setModifiedTime(LocalDateTime.now());

        updateById(file);

        if (detail != null && detail.getVenueDescription() != null &&  detail.getFollowUp() != null) {
            detailService.saveDetail(detail);
        }
        inspectRealtyService.saveInspectRealities(file.getId(), InspectionType.FILE, realtySerials);

        // 复检图片
        inspectMediaService.saveMedias(file.getId(), InspectionType.FILE, 2, reinspectMedias);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        removeById(id);
        detailService.deleteDetail(id);
        inspectRealtyService.deleteByInspectId(id, InspectionType.FILE);
        inspectMediaService.deleteByInspectId(id, InspectionType.FILE);

        // 触发移除事件
        publisher.publishEvent(new InspectTaskDropEvent(this, InspectionType.FILE, id));
    }

    /**
     * 根据id获取消防安全档案
     * @param id
     * @return
     */
    public FirefightingFile findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 店铺消防档案合计
     * @param search
     * @return
     */
    public int countFile(FirefightingFileSearchVo search) {
        return getBaseMapper().countFile(search);
    }

    /**
     * 店铺消防档案列表
     * @param search
     * @return
     */
    public List<FirefightingFileBriefVo> listFile(FirefightingFileSearchVo search) {
        return getBaseMapper().listFile(search);
    }


}
