package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminUserDto;
import com.senox.realty.domain.FirefightingUtility;
import com.senox.realty.mapper.FirefightingUtilityMapper;
import com.senox.realty.vo.FirefightingUtilitySearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/13 9:25
 */
@Service
@RequiredArgsConstructor
public class FirefightingUtilityService extends ServiceImpl<FirefightingUtilityMapper, FirefightingUtility> {

    /**
     * 添加公共消防设施
     * @param utility
     * @return
     */
    public Long addUtility(FirefightingUtility utility) {
        if (checkUtilityExist(utility)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR);
        }

        utility.setCreateTime(LocalDateTime.now());
        utility.setModifiedTime(LocalDateTime.now());
        save(utility);
        return utility.getId();
    }

    /**
     * 更新公共消防设施
     * @param utility
     */
    public void updateUtility(FirefightingUtility utility) {
        if (!WrapperClassUtils.biggerThanLong(utility.getId(), 0L)) {
            return;
        }
        if (checkUtilityExist(utility)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR);
        }

        utility.setCreatorId(null);
        utility.setCreatorName(null);
        utility.setCreateTime(null);
        utility.setModifiedTime(LocalDateTime.now());
        updateById(utility);
    }

    /**
     * 批量保存公共消防设施
     * @param list
     */
    public void batchSaveUtility(List<FirefightingUtility> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<FirefightingUtility> dataList = listByLocation(list);
        if (!CollectionUtils.isEmpty(dataList)) {
            list = list.stream().filter(x -> !dataList.contains(x)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        saveBatch(list);
    }

    /**
     * 删除公共消防设施
     * @param id
     * @param operator
     */
    public void deleteUtility(Long id, AdminUserDto operator) {
        FirefightingUtility utility = findById(id);
        if (utility == null) {
            return;
        }

        FirefightingUtility deleteItem = new FirefightingUtility();
        deleteItem.setId(id);
        deleteItem.setLocation(utility.getLocation() + "_"  + id);
        deleteItem.setModifierId(operator.getUserId());
        deleteItem.setModifierName(operator.getUsername());
        deleteItem.setModifiedTime(LocalDateTime.now());
        updateById(deleteItem);
    }

    /**
     * 获取公共消防设施
     * @param id
     * @return
     */
    public FirefightingUtility findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 公共消防设施统计
     * @param search
     * @return
     */
    public int countUtility(FirefightingUtilitySearchVo search) {
        return getBaseMapper().countUtility(search);
    }

    /**
     * 公共消防设施列表
     * @param search
     * @return
     */
    public List<FirefightingUtility> listUtility(FirefightingUtilitySearchVo search) {
        return getBaseMapper().listUtility(search);
    }

    /**
     * 消防设备是否存在
     * @param utility
     * @return
     */
    public boolean checkUtilityExist(FirefightingUtility utility) {
        FirefightingUtility item = findByLocation(utility);
        return item != null
                && (WrapperClassUtils.biggerThanLong(utility.getId(), 0L) || !Objects.equals(utility.getId(), item.getId()));
    }

    /**
     * 根据位置查找公共消防
     * @param utility
     * @return
     */
    private FirefightingUtility findByLocation(FirefightingUtility utility) {
        List<FirefightingUtility> list = listByLocation(Collections.singletonList(utility));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 根据位置查找公共消防
     * @param list
     * @return
     */
    private List<FirefightingUtility> listByLocation(List<FirefightingUtility> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return getBaseMapper().listByLocation(list);
    }
}
