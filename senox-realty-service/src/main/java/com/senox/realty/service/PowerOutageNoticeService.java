package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.PowerOutageNoticeMediaType;
import com.senox.realty.convert.PowerOutageNoticeConvert;
import com.senox.realty.domain.PowerOutageNotice;
import com.senox.realty.mapper.PowerOutageNoticeMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.PowerOutageNoticeMediaVo;
import com.senox.realty.vo.PowerOutageNoticeSearchVo;
import com.senox.realty.vo.PowerOutageNoticeVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-08
 **/
@Service
@RequiredArgsConstructor
public class PowerOutageNoticeService extends ServiceImpl<PowerOutageNoticeMapper, PowerOutageNotice> {
    private final PowerOutageNoticeMediaService mediaService;
    private final PowerOutageNoticeConvert noticeConvert;

    /**
     * 批量添加
     * @param noticeList 停电通知集
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<PowerOutageNoticeVo> noticeList) {
        if (CollectionUtils.isEmpty(noticeList)) {
            return;
        }
        noticeList.forEach(x -> {
            PowerOutageNotice notice = noticeConvert.toDo(x);
            long noticeId = add(notice);
            if (noticeId > 0) {
                List<PowerOutageNoticeMediaVo> preMediaList = x.getPreMedias();
                List<PowerOutageNoticeMediaVo> postMediaList = x.getPostMedias();
                mediaService.addBatch(noticeId, PowerOutageNoticeMediaType.RECTIFICATION_PRE, preMediaList);
                mediaService.addBatch(noticeId, PowerOutageNoticeMediaType.RECTIFICATION_POST, postMediaList);
            }
        });
    }

    /**
     * 添加
     * @param notice 停电通知
     */
    public long add(PowerOutageNotice notice) {
        ContextUtils.initEntityCreator(notice);
        ContextUtils.initEntityModifier(notice);
        notice.setCreateTime(LocalDateTime.now());
        notice.setModifiedTime(LocalDateTime.now());
        save(notice);
        return null != notice.getId() ? notice.getId() : 0;
    }

    /**
     * 根据id查找
     * @param id id
     * @return 返回查询到的数据
     */
    public PowerOutageNoticeVo findById(Long id) {
        return baseMapper.findById(id);
    }

    /**
     * 根据id批量删除
     * @param ids 停电通知id集
     */
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        if (removeByIds(ids)) {
            mediaService.deleteByNoticeIds(ids);
        }
    }

    /**
     * 根据id批量更新
     * @param noticeList 停电通知集
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchById(List<PowerOutageNoticeVo> noticeList) {
        if (CollectionUtils.isEmpty(noticeList)) {
            return;
        }
        noticeList.forEach(x -> {
            PowerOutageNotice notice = noticeConvert.toDo(x);
            ContextUtils.initEntityModifier(notice);
            notice.setModifiedTime(LocalDateTime.now());
            if (update(getUpdateWrapper(notice))) {
                List<PowerOutageNoticeMediaVo> preMediaList = x.getPreMedias();
                List<PowerOutageNoticeMediaVo> postMediaList = x.getPostMedias();
                //删除原来的资源
                mediaService.deleteByNoticeIds(Collections.singletonList(notice.getId()));
                //新增
                mediaService.addBatch(notice.getId(), PowerOutageNoticeMediaType.RECTIFICATION_PRE, preMediaList);
                mediaService.addBatch(notice.getId(), PowerOutageNoticeMediaType.RECTIFICATION_POST, postMediaList);
            }
        });
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(PowerOutageNoticeSearchVo search) {
        return baseMapper.countList(search);
    }

    /**
     * 列表
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<PowerOutageNoticeVo> list(PowerOutageNoticeSearchVo search) {
        return baseMapper.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<PowerOutageNoticeVo> pageList(PowerOutageNoticeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search, () -> countList(search), () -> list(search));
    }

    /**
     * 获取更新wrapper
     * @param notice 参数
     * @return 返回构建后的wrapper
     */
    private LambdaUpdateWrapper<PowerOutageNotice> getUpdateWrapper(PowerOutageNotice notice) {
        return new UpdateWrapper<PowerOutageNotice>().lambda()
                .set(WrapperClassUtils.biggerThanLong(notice.getRegionId(), 0), PowerOutageNotice::getRegionId, notice.getRegionId())
                .set(WrapperClassUtils.biggerThanLong(notice.getStreetId(), 0), PowerOutageNotice::getStreetId, notice.getStreetId())
                .set(!StringUtils.isBlank(notice.getStallAddress()), PowerOutageNotice::getStallAddress, notice.getStallAddress())
                .set(!StringUtils.isBlank(notice.getPoserOutageReason()), PowerOutageNotice::getPoserOutageReason, notice.getPoserOutageReason())
                .set(null != notice.getPreTime(), PowerOutageNotice::getStallAddress, notice.getStallAddress())
                .set(!StringUtils.isBlank(notice.getPreInspectorSign()), PowerOutageNotice::getPreInspectorSign, notice.getPreInspectorSign())
                .set(!StringUtils.isBlank(notice.getPreOwnerSign()), PowerOutageNotice::getPreOwnerSign, notice.getPreOwnerSign())
                .set(null != notice.getPostTime(), PowerOutageNotice::getPostTime, notice.getPostTime())
                .set(!StringUtils.isBlank(notice.getPostInspectorSign()), PowerOutageNotice::getPostInspectorSign, notice.getPostInspectorSign())
                .set(!StringUtils.isBlank(notice.getPostOwnerSign()), PowerOutageNotice::getPostOwnerSign, notice.getPostOwnerSign())
                .set(PowerOutageNotice::getModifierId, notice.getModifierId())
                .set(PowerOutageNotice::getModifierName, notice.getModifierName())
                .set(PowerOutageNotice::getModifiedTime, notice.getModifiedTime())
                .eq(PowerOutageNotice::getId, notice.getId());
    }
}
