package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.FirefightingTemplateVariables;
import com.senox.realty.mapper.FirefightingTemplateVariablesMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/28 8:42
 */
@Service
@RequiredArgsConstructor
public class FirefightingTemplateVariablesService extends ServiceImpl<FirefightingTemplateVariablesMapper, FirefightingTemplateVariables> {

    /**
     * 保存变量
     * @param code
     * @param version
     * @param variables
     */
    public void saveVariables(String code, Integer version, List<FirefightingTemplateVariables> variables) {
        if (!CollectionUtils.isEmpty(variables)) {
            for (FirefightingTemplateVariables attr : variables) {
                attr.setCode(code);
                attr.setVersion(version);
                attr.setModifiedTime(LocalDateTime.now());
            }
        }

        // variables in db
        List<FirefightingTemplateVariables> dbVariables = listVariablesByCodeAndVersion(code, version);
        // separate data
        DataSepDto<FirefightingTemplateVariables> sepDto = separateVariables(dbVariables, variables);
        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            saveBatch(sepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getUpdateList())) {
            updateVariables(code, version, sepDto.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
            deleteVariables(code, version, sepDto.getRemoveList());
        }
    }

    /**
     * 更新模板变量
     * @param list
     */
    public void updateVariables(String code, Integer version, List<FirefightingTemplateVariables> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        getBaseMapper().updateVariables(code, version, list);
    }

    /**
     * 删除变量
     * @param id
     */
    public void deleteVariables(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        getBaseMapper().deleteVariablesByTemplateId(id);
    }

    /**
     * 删除变量
     * @param code
     * @param version
     * @param list
     */
    public void deleteVariables(String code, Integer version, List<FirefightingTemplateVariables> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        getBaseMapper().deleteVariables(code, version, list);
    }

    /**
     * 获取模板变量
     * @param code
     * @param version
     * @return
     */
    public List<FirefightingTemplateVariables> listVariablesByCodeAndVersion(String code, Integer version) {
        LambdaQueryWrapper<FirefightingTemplateVariables> queryWrapper = new QueryWrapper<FirefightingTemplateVariables>().lambda()
                .eq(FirefightingTemplateVariables::getCode, code)
                .eq(FirefightingTemplateVariables::getVersion, version);

        return getBaseMapper().selectList(queryWrapper);
    }

    /**
     * 数据比较
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<FirefightingTemplateVariables> separateVariables(List<FirefightingTemplateVariables> srcList, List<FirefightingTemplateVariables> targetList) {
        DataSepDto<FirefightingTemplateVariables> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(srcList)) {
            result.setAddList(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            result.setRemoveList(srcList);

        } else {
            result.setAddList(targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList()));
            result.setUpdateList(targetList.stream().filter(srcList::contains).collect(Collectors.toList()));
            result.setRemoveList(srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList()));
        }
        return result;
    }
}
