package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.AdvertisingSpacePosition;
import com.senox.realty.mapper.AdvertisingSpacePositionMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/3 14:11
 */
@Service
public class AdvertisingSpacePositionService extends ServiceImpl<AdvertisingSpacePositionMapper, AdvertisingSpacePosition> {

    /**
     * 保存广告位区域信息
     * @param spaceId
     * @param positionList
     */
    public void saveAdvertisingSpacePosition(Long spaceId, List<AdvertisingSpacePosition> positionList) {
        if (!WrapperClassUtils.biggerThanLong(spaceId, 0L) || positionList == null) {
            return;
        }
        positionList.forEach(x -> x.setSpaceId(spaceId));

        // 数据
        List<AdvertisingSpacePosition> dbPositionList = listBySpaceId(spaceId);

        // 对比更新数据
        DataSepDto<AdvertisingSpacePosition> sepData = SeparateUtils.separateData(dbPositionList, positionList);
        // 添加广告位区域信息
        batchAddPosition(sepData.getAddList());
        // 更新广告位区域信息
        batchUpdatePosition(sepData.getUpdateList());
        // 删除广告位区域信息
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            batchDeletePosition(sepData.getRemoveList().stream().map(AdvertisingSpacePosition::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 批量添加广告位区域信息
     * @param positionList
     */
    private void batchAddPosition(List<AdvertisingSpacePosition> positionList) {
        if (CollectionUtils.isEmpty(positionList)) {
            return;
        }

        saveBatch(positionList);
    }

    /**
     * 批量更新广告位区域信息
     * @param positionList
     */
    public void batchUpdatePosition(List<AdvertisingSpacePosition> positionList) {
        if (CollectionUtils.isEmpty(positionList)) {
            return;
        }

        positionList.forEach(x -> {
            x.setCreatorId(null);
            x.setCreatorName(null);
            x.setCreateTime(null);
        });
        updateBatchById(positionList);
    }

    /**
     * 批量删除广告区域信息
     * @param idList
     */
    public void batchDeletePosition(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        removeByIds(idList);
    }


    public List<AdvertisingSpacePosition> listBySpaceId(Long spaceId) {
        if (!WrapperClassUtils.biggerThanLong(spaceId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AdvertisingSpacePosition> queryWrapper =
                new QueryWrapper<AdvertisingSpacePosition>().lambda().eq(AdvertisingSpacePosition::getSpaceId, spaceId);
        return list(queryWrapper);
    }

}
