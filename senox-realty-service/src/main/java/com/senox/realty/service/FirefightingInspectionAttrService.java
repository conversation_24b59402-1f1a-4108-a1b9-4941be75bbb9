package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingInspectionAttr;
import com.senox.realty.mapper.FirefightingInspectionAttrMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/29 15:32
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FirefightingInspectionAttrService
        extends ServiceImpl<FirefightingInspectionAttrMapper, FirefightingInspectionAttr> {

    /**
     * 保存巡检模板变量
     * @param inspectionId
     * @param attrs
     */
    public void saveInspectionAttrs(InspectionType inspectionType, Long inspectionId, List<FirefightingInspectionAttr> attrs) {
        if (CollectionUtils.isEmpty(attrs) || !WrapperClassUtils.biggerThanLong(inspectionId, 0L)) {
            return;
        }

        Map<String, List<FirefightingInspectionAttr>> attrMap = attrs
                .stream()
                .collect(Collectors.groupingBy(FirefightingInspectionAttr::getTemplateCode));
        for (Map.Entry<String, List<FirefightingInspectionAttr>> entry : attrMap.entrySet()) {
            List<FirefightingInspectionAttr> list = entry.getValue();
            saveInspectionAttrsOfTemplate(inspectionType.name(), inspectionId, entry.getKey(), list.get(0).getTemplateVersion(), list);
        }
    }

    /**
     * 保存巡检模板变量
     * @param inspectionType
     * @param inspectionId
     * @param attrs
     */
    public void saveInspectionAttrsOfTemplate(String inspectionType, Long inspectionId, String code, Integer version,
                                              List<FirefightingInspectionAttr> attrs) {
        if (!WrapperClassUtils.biggerThanLong(inspectionId, 0L)) {
            return;
        }

        // 巡检变量属性初始化
        if (!CollectionUtils.isEmpty(attrs)) {
            for (FirefightingInspectionAttr attr : attrs) {
                attr.setInspectionType(inspectionType);
                attr.setInspectionId(inspectionId);
                attr.setTemplateCode(code);
                attr.setTemplateVersion(version);
                attr.setModifiedTime(LocalDateTime.now());
            }
        }

        // data in db
        List<FirefightingInspectionAttr> dbAttrs = listInspectionAttrs(inspectionType, inspectionId, code, version);
        // separate data
        DataSepDto<FirefightingInspectionAttr> sepDto = separateInspectionAttrs(dbAttrs, attrs);
        log.info("inspect attrs: {}", JsonUtils.object2Json(sepDto));
        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            saveBatch(sepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getUpdateList())) {
            updateInspectionAttrs(sepDto.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
            getBaseMapper().deleteInspectionAttrs(inspectionType, inspectionId, code, version, sepDto.getRemoveList());
        }
    }

    /**
     * 更新巡检模板变量
     * @param attrs
     */
    public void updateInspectionAttrs(List<FirefightingInspectionAttr> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return;
        }

        FirefightingInspectionAttr attr = attrs.get(0);
        getBaseMapper().updateInspectionAttrs(attr.getInspectionType(), attr.getInspectionId(), attr.getTemplateCode(), attr.getTemplateVersion(), attrs);
    }

    /**
     * 删除巡检模板变量
     * @param inspectionType
     * @param inspectionId
     */
    public void deleteInspectionAttrs(InspectionType inspectionType, Long inspectionId) {
        if (inspectionType == null || !WrapperClassUtils.biggerThanLong(inspectionId, 0L)) {
            return;
        }

        LambdaQueryWrapper<FirefightingInspectionAttr> queryWrapper = new QueryWrapper<FirefightingInspectionAttr>()
                .lambda()
                .eq(FirefightingInspectionAttr::getInspectionType, inspectionType.name())
                .eq(FirefightingInspectionAttr::getInspectionId, inspectionId);
        remove(queryWrapper);
    }

    /**
     * 获取巡检模板变量
     * @param inspectionType
     * @param inspectionId
     * @return
     */
    public List<FirefightingInspectionAttr> listInspectionAttrs(InspectionType inspectionType, Long inspectionId) {
        return listInspectionAttrs(inspectionType.name(), inspectionId, null, null);
    }

    /**
     * 获取巡检模板变量
     * @param inspectionType
     * @param inspectionId
     * @param code
     * @param version
     * @return
     */
    public List<FirefightingInspectionAttr> listInspectionAttrs(String inspectionType, Long inspectionId, String code, Integer version) {
        if (StringUtils.isBlank(inspectionType) || !WrapperClassUtils.biggerThanLong(inspectionId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<FirefightingInspectionAttr> queryWrapper = new QueryWrapper<FirefightingInspectionAttr>()
                .lambda()
                .eq(FirefightingInspectionAttr::getInspectionType, inspectionType)
                .eq(FirefightingInspectionAttr::getInspectionId, inspectionId)
                .eq(!StringUtils.isBlank(code), FirefightingInspectionAttr::getTemplateCode, code)
                .eq(WrapperClassUtils.biggerThanInt(version, 0), FirefightingInspectionAttr::getTemplateVersion, version);
        return getBaseMapper().selectList(queryWrapper);
    }

    /**
     * 巡检模板数据比较
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<FirefightingInspectionAttr> separateInspectionAttrs(List<FirefightingInspectionAttr> srcList, List<FirefightingInspectionAttr> targetList) {
        DataSepDto<FirefightingInspectionAttr> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(srcList)) {
            result.setAddList(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            result.setRemoveList(srcList);

        } else {
            result.setAddList(targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList()));
            result.setUpdateList(targetList.stream().filter(srcList::contains).collect(Collectors.toList()));
            result.setRemoveList(srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList()));
        }
        return result;
    }
}
