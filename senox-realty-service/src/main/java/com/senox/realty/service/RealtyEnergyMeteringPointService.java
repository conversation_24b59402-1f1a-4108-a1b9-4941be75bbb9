package com.senox.realty.service;

import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.mapper.RealtyEnergyPointMapper;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 华立集抄-设备物业服务实现
 *
 * <AUTHOR>
 * @date 2022-11-2
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RealtyEnergyMeteringPointService {
    private final RealtyEnergyPointMapper realtyEnergyPointMapper;
    private final RealtyWeService realtyWeService;
    private final RealtyService realtyService;

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int meteringPointToRealtyCountList(RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyPointMapper.meteringPointToRealtyCountList(search);
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<RealtyToEnergyMeteringPointVo> meteringPointToRealtyList(RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyPointMapper.meteringPointToRealtyList(search);
    }

    /**
     * 根据编码查询计量点列表
     *
     * @param pointCodes 计量点编码集
     * @return 返回查询到的数据
     */
    public List<RealtyToEnergyMeteringPointVo> meteringPointToRealtyListByCode(List<String> pointCodes) {
        if (CollectionUtils.isEmpty(pointCodes)) {
            return Collections.emptyList();
        }
        RealtyToEnergyMeteringPointSearchVo search = new RealtyToEnergyMeteringPointSearchVo();
        search.setPage(false);
        search.setCodes(pointCodes);
        return meteringPointToRealtyList(search);
    }

    /**
     * 根据计量点编码查找物业计量点
     * @param meteringPointCode 计量点编码
     * @return 返回查找到的物业计量点
     */
    public RealtyToEnergyMeteringPointVo meteringPointToRealtyFindByPointCode(String meteringPointCode) {
        if (StringUtils.isBlank(meteringPointCode)) {
            return null;
        }
        RealtyToEnergyMeteringPointSearchVo search = new RealtyToEnergyMeteringPointSearchVo();
        search.setPage(false);
        search.setCode(meteringPointCode);
        List<RealtyToEnergyMeteringPointVo> meteringPointList = meteringPointToRealtyList(search);
        return CollectionUtils.isEmpty(meteringPointList) ? null : meteringPointList.get(0);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<RealtyToEnergyMeteringPointVo> meteringPointToRealtyPageList(RealtyToEnergyMeteringPointSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search,
                () -> meteringPointToRealtyCountList(search),
                () -> meteringPointToRealtyList(search));
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int meteringPointReadingsToRealtyCountList(RealtyToEnergyMeteringPointReadingsSearchVo search) {
        return realtyEnergyPointMapper.meteringPointReadingsToRealtyCountList(search);
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<RealtyToEnergyMeteringPointReadingsVo> meteringPointReadingsToRealtyList(RealtyToEnergyMeteringPointReadingsSearchVo search) {
        return realtyEnergyPointMapper.meteringPointReadingsToRealtyList(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<RealtyToEnergyMeteringPointReadingsVo> meteringPointReadingsToRealtyPageList(RealtyToEnergyMeteringPointReadingsSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search,
                () -> meteringPointReadingsToRealtyCountList(search),
                () -> meteringPointReadingsToRealtyList(search));
    }


    /**
     * 同步计量点读数
     *
     * @param realtyWeBatch 参数
     */
    public void syncMeteringPoint(RealtyWeBatchVo realtyWeBatch) {
        //获取最新的读数
        RealtyToEnergyMeteringPointReadingsSearchVo search = new RealtyToEnergyMeteringPointReadingsSearchVo();
        search.setPage(false);
        search.setRealTime(true);
        List<RealtyToEnergyMeteringPointReadingsVo> meteringPoints = meteringPointReadingsToRealtyList(search);
        //获取列表并过滤没绑定物业的
        List<RealtyToEnergyMeteringPointReadingsVo> meteringPointList = meteringPoints.stream().filter(pm -> !StringUtils.isBlank(pm.getRealtySerialNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(meteringPointList)) {
            return;
        }
        //合并相同物业的水电
        Map<String, List<RealtyToEnergyMeteringPointReadingsVo>> meteringPointMap = meteringPointList.stream().collect(Collectors.groupingBy(RealtyToEnergyMeteringPointReadingsVo::getRealtySerialNo));
        //获取物业上最新的读数
        List<RealtyReadingsVo> newWeList = realtyService.listReadings(new ArrayList<>(meteringPointMap.keySet()));
        Map<String, RealtyReadingsVo> newWeMap = newWeList.stream().collect(Collectors.toMap(RealtyReadingsVo::getRealtySerial, Function.identity()));
        List<RealtyWe> realtyWeList = new ArrayList<>();
        meteringPointMap.forEach((realtySerialNo, meters) -> {
            RealtyReadingsVo newWe = newWeMap.get(realtySerialNo);
            RealtyWe we = new RealtyWe();
            we.setRealtySerial(realtySerialNo);
            if (null != newWe) {
                we.setLastWaterReadings(newWe.getWaterReadings());
                we.setLastElectricReadings(newWe.getElectricReadings());
            }
            Supplier<Stream<RealtyToEnergyMeteringPointReadingsVo>> wrMeterStreamSupplier = () -> meters.stream().filter(m -> EnergyType.fromValue(m.getPointType()) == EnergyType.WATER);
            Supplier<Stream<RealtyToEnergyMeteringPointReadingsVo>> erMeterStreamSupplier = () -> meters.stream().filter(m -> EnergyType.fromValue(m.getPointType()) == EnergyType.ELECTRIC);
            BigDecimal wrMeter = !wrMeterStreamSupplier.get().findAny().isPresent() ? null : wrMeterStreamSupplier.get().map(r -> !DecimalUtils.isPositive(r.getPointRate()) ? r.getReadings() : r.getReadings().multiply(r.getPointRate())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal erMeter = !erMeterStreamSupplier.get().findAny().isPresent() ? null : erMeterStreamSupplier.get().map(r -> !DecimalUtils.isPositive(r.getPointRate()) ? r.getReadings() : r.getReadings().multiply(r.getPointRate())).reduce(BigDecimal.ZERO, BigDecimal::add);
            //设置水电读数，采用银行家舍入法(四舍六入五成双)
            we.setWaterReadings(null != wrMeter ? wrMeter.setScale(0, RoundingMode.HALF_EVEN).longValue() : null);
            we.setElectricReadings(null != erMeter ? erMeter.setScale(0, RoundingMode.HALF_EVEN).longValue() : null);
            realtyWeService.prepareRealtyWe(we, realtyWeBatch.getYear(), realtyWeBatch.getMonth());
            realtyWeList.add(we);
        });
        realtyWeService.batchAddWeData(realtyWeList);
    }

    /**
     * 检查是否已绑定计量点设备
     *
     * @param realtyBindPoint 绑定vo
     * @param energyPointType 计量点类型
     * @return >0:包含
     */
    public int checkPointDeviceBind(RealtyBindEnergyMeteringPointVo realtyBindPoint, Integer energyPointType) {
        return realtyEnergyPointMapper.checkPointDeviceBind(realtyBindPoint, energyPointType);
    }

    /**
     * 绑定计量点设备
     *
     * @param realtyBindPoint 绑定vo
     * @param energyPointType 计量点类型
     */
    public void bindPointDevice(RealtyBindEnergyMeteringPointVo realtyBindPoint, Integer energyPointType) {
        realtyEnergyPointMapper.bindPointDevice(realtyBindPoint, energyPointType);
    }

    /**
     * 物业解绑计量设备
     *
     * @param meteringPointCode 计量点编码
     * @return true:解绑成功,false:解绑失败
     */
    public boolean unBindPointDevice(String meteringPointCode) {
        if (StringUtils.isBlank(meteringPointCode)) {
            return false;
        }

        return realtyEnergyPointMapper.unBindPointDevice(meteringPointCode);
    }
}
