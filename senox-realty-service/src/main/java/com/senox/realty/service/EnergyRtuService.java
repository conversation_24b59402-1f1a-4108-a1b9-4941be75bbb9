package com.senox.realty.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.dm.vo.EnergyRtuSearchVo;
import com.senox.realty.component.DeviceEnergyRtuComponent;
import com.senox.realty.domain.EnergyRtu;
import com.senox.realty.mapper.EnergyRtuMapper;
import com.senox.realty.utils.ContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-11-08
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class EnergyRtuService {
    private final EnergyRtuMapper rtuMapper;
    private final DeviceEnergyRtuComponent rtuComponent;

    /**
     * 添加集中器
     *
     * @param rtu 集中
     * @return 返回集中器id
     */
    public Long add(EnergyRtu rtu) {
        if (checkRtuExist(rtu)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "终端设备已存在");
        }
        rtuMapper.add(rtu);
        return rtu.getId();
    }

    /**
     * 批量添加集中器
     * @param rtuList 集中器集
     */
    public void addBatch(List<EnergyRtu> rtuList) {
        if (CollectionUtils.isEmpty(rtuList)) {
            return;
        }
        rtuList.forEach(rtu -> {
            ContextUtils.initEntityCreator(rtu);
            ContextUtils.initEntityModifier(rtu);
        });
        rtuMapper.addBatch(rtuList);
    }

    /**
     * 批量添加或更新集中器
     * @param rtuList 集中器集
     */
    public void addOrUpdateBatch(List<EnergyRtu> rtuList) {
        if (CollectionUtils.isEmpty(rtuList)) {
            return;
        }
        EnergyRtuSearchVo rtuSearch = new EnergyRtuSearchVo();
        rtuSearch.setPage(false);
        List<EnergyRtu> energyRtuDbList = rtuMapper.list(rtuSearch);
        DataSepDto<EnergyRtu> sepData = compareEnergy(energyRtuDbList, rtuList);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            addBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchByCode(sepData.getUpdateList());
        }
    }

    /**
     * 根据id更新集中器
     *
     * @param rtu 集中年起
     */
    public void updateById(EnergyRtu rtu) {
        if (!WrapperClassUtils.biggerThanLong(rtu.getId(), 0L)) {
            return;
        }
        // 校验设备是否存在
        if (checkRtuExist(rtu)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "终端设备已存在");
        }

        // 删除设备
        if (BooleanUtils.isTrue(rtu.getDisabled())) {
            EnergyRtu dbItem = findById(rtu.getId());
            // 逻辑删除 避免编码重复
            rtu.setCode(dbItem.getCode().concat("_").concat(String.valueOf(dbItem.getId())));
        }
        rtuMapper.updateById(rtu);
    }

    /**
     * 根据编码更新集中器
     *
     * @param rtu 集中器
     */
    public void updateByCode(EnergyRtu rtu) {
        if (StringUtils.isBlank(rtu.getCode())) {
            return;
        }
        rtuMapper.updateByCode(rtu);
    }

    /**
     * 根据编码批量更新集中器
     *
     * @param rtuList 集中器集
     */
    public void updateBatchByCode(List<EnergyRtu> rtuList) {
        if (CollectionUtils.isEmpty(rtuList)) {
            return;
        }
        rtuList.forEach(rtu -> {
            ContextUtils.initEntityModifier(rtu);
            rtuMapper.updateByCode(rtu);
        });
    }

    /**
     * 根据id查找集中器
     *
     * @param id 集中器id
     * @return 返回查找到的集中器
     */
    public EnergyRtu findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return rtuMapper.findById(id);
    }

    /**
     * 根据编码查找集中器
     *
     * @param code 集中器编码
     * @return 返回查找到的集中器
     */
    public EnergyRtu findByCode(String code) {
        return StringUtils.isBlank(code) ? null : rtuMapper.findByCode(code);
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(EnergyRtuSearchVo search) {
        return rtuMapper.countList(search);
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<EnergyRtu> list(EnergyRtuSearchVo search) {
        if (null == search){
            search = new EnergyRtuSearchVo();
            search.setPage(false);
        }
        return rtuMapper.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<EnergyRtu> pageList(EnergyRtuSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search, () -> countList(search), () -> list(search));
    }

    /**
     * 集中器同步
     */
    public void rtuSync() {
        List<EnergyRtu> rtuList = rtuComponent.remoteList();
        addOrUpdateBatch(rtuList);
    }

    /**
     * 检查集中器是否存在
     *
     * @param rtu 集中器
     * @return 返回结果
     */
    private boolean checkRtuExist(EnergyRtu rtu) {
        EnergyRtu dbItem = findByCode(rtu.getCode());
        return dbItem != null && (!WrapperClassUtils.biggerThanLong(rtu.getId(), 0L) || !Objects.equals(rtu.getId(), dbItem.getId()));
    }

    /**
     * 比较集中器
     * @param srcList 源集
     * @param targetList 目标集
     * @return 返回比较结果
     */
    private DataSepDto<EnergyRtu> compareEnergy(List<EnergyRtu> srcList, List<EnergyRtu> targetList) {
        if (CollectionUtils.isEmpty(targetList)) {
            return new DataSepDto<>();
        }
        List<EnergyRtu> addList = new ArrayList<>(targetList.size());
        List<EnergyRtu> updateList = new ArrayList<>(srcList.size());
        Map<String, EnergyRtu> srcMap = srcList.stream().collect(Collectors.toMap(EnergyRtu::getCode, Function.identity()));
        if (CollectionUtils.isEmpty(srcList)) {
            addList.addAll(targetList);
        } else {
            targetList.forEach(target -> {
                EnergyRtu rtu = srcMap.get(target.getCode());
                if (null != rtu) {
                    ContextUtils.initEntityModifier(rtu);
                    updateList.add(rtu);
                } else {
                    addList.add(target);
                }

            });
        }
        return new DataSepDto<>(addList, updateList, null);
    }
}
