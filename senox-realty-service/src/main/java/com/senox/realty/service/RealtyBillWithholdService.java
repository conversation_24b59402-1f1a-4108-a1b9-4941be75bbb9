package com.senox.realty.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import com.senox.realty.component.OrderComponent;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.BankWithhold;
import com.senox.realty.domain.RealtyBillItem;
import com.senox.realty.mapper.BankWithholdMapper;
import com.senox.realty.mapper.RealtyBillMapper;
import com.senox.realty.mapper.RealtyBillWithholdMapper;
import com.senox.realty.vo.BankOfferRealtyBillVo;
import com.senox.realty.vo.RealtyBillSearchVo;
import com.senox.realty.vo.WithholdBackVo;
import com.senox.realty.vo.WithholdPaidVo;
import com.senox.realty.vo.WithholdSumVo;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/17 9:49
 */
@AllArgsConstructor
@Service
public class RealtyBillWithholdService {

    private static final Logger logger = LoggerFactory.getLogger(RealtyBillWithholdService.class);

    private final BankWithholdMapper bankWithholdMapper;
    private final RealtyBillWithholdMapper billWithholdMapper;
    private final RealtyBillMapper billMapper;
    private final OrderComponent orderComponent;
    private final RealtyBillPenaltyService penaltyService;


    /**
     * 银行托收报盘
     * @param withhold
     */
    @Transactional(rollbackFor = Exception.class)
    public void applyBillBankWithhold(BankWithhold withhold) {
        BankWithhold recordWithhold = findByYearMonth(withhold.getBillYear(), withhold.getBillMonth());
        if (isWithholdSubmitted(recordWithhold)) {
            throw new BusinessException("报盘失败，该月已报盘。");
        }
        if (isWithholdBack(recordWithhold)) {
            throw new BusinessException("报盘失败，该月已回盘。");
        }

        // 初始化报盘信息
        withhold.setBillDate(LocalDate.of(withhold.getBillYear(), withhold.getBillMonth(), 1));
        withhold.setOffer(Boolean.TRUE);
        withhold.setOfferTime(LocalDateTime.now());

        // 添加银行报盘记录
        if (Objects.isNull(recordWithhold)) {
            bankWithholdMapper.addWithhold(withhold);
        } else {
            bankWithholdMapper.updateWithholdOffer(withhold);
        }

        // 账单报盘信息持久化(报盘数据若有滞纳金，先免滞纳金)
        billWithholdMapper.updateBillBankWithholdIgnorePenalty(withhold);
        billWithholdMapper.addBillBankWithhold(withhold);

        // clear cache
        RedisUtils.del(String.format(RealtyConst.Cache.KEY_BANK_WITHHOLD, withhold.getBillYear(), withhold.getBillMonth()));
    }

    /**
     * 取消银行托收报盘
     * @param withhold
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelBillBankWithhold(BankWithhold withhold) {
        BankWithhold recordWithhold = findByYearMonth(withhold.getBillYear(), withhold.getBillMonth());
        if (!isWithholdSubmitted(recordWithhold)) {
            throw new BusinessException("取消报盘失败，该月未报盘。");
        }
        if (isWithholdBack(recordWithhold)) {
            throw new BusinessException("取消报盘失败，该月已回盘。");
        }
        // 初始化报盘信息
        withhold.setOffer(Boolean.FALSE);
        withhold.setOfferTime(null);

        // 取消银行报盘记录
        bankWithholdMapper.updateWithholdOffer(withhold);

        // 删除账单报盘信息
        billWithholdMapper.deleteBillBankWithhold(withhold.getBillYear(), withhold.getBillMonth());

        // clear cache
        RedisUtils.del(String.format(RealtyConst.Cache.KEY_BANK_WITHHOLD, withhold.getBillYear(), withhold.getBillMonth()));
    }

    /**
     * 回盘
     * @param withhold
     */
    public void backBillBankWithhold(BankWithhold withhold) {
        // 回盘校验
        BankWithhold recordWithhold = findByYearMonth(withhold.getBillYear(), withhold.getBillMonth());
        if (!isWithholdSubmitted(recordWithhold)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "回盘失败，该月未报盘。");
        }

        if (isWithholdBack(recordWithhold)) {
            throw new BusinessException("回盘失败，该月已回盘。");
        }

        withhold.setBack(Boolean.TRUE);
        withhold.setBackTime(LocalDateTime.now());
        bankWithholdMapper.updateWithholdBack(withhold);

        // clear cache
        RedisUtils.del(String.format(RealtyConst.Cache.KEY_BANK_WITHHOLD, withhold.getBillYear(), withhold.getBillMonth()));

        // 计算滞纳金
        logger.info("回盘计算滞纳金...");
        penaltyService.calBillPenaltyAsync();
    }

    /**
     * 回盘支付账单
     * @param back
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderResultVo withholdPayBill(WithholdBackVo back) {
        // 代扣已报盘账单
        BankOfferRealtyBillVo bill = findWithholdPayingBill(back);
        if (bill == null) {
            throw new BusinessException("找不到账单，账单未报盘或已支付");
        }
        // 锁
        if (!RedisUtils.lock(buildBillPayingLockKey(bill), RealtyConst.Cache.TTL_10S)) {
            throw new BusinessException("物业账单缴费操作太频繁，请稍后尝试");
        }

        OrderResultVo result = null;
        try {
            // 账单标题
            OrderVo order = new OrderVo();
            order.setTitle(String.format(RealtyConst.TITLE_REALTY_BILL, bill.getRealtyName(), buildBillYearMonth(back.getBillYear(), back.getBillMonth())));
            order.setOrderType(OrderType.REALTY);
            order.setPayWay(PayWay.WITHHOLD);
            order.setCreateIp(back.getRequestIp());
            order.setItems(Collections.singletonList(withholdBill2OrderItem(bill, order.getTitle())));
            result = orderComponent.addOrder(order);

            if (result == null) {
                throw new BusinessException("回盘失败");
            }
            logger.info("代扣回盘支付订单 {}, 结果 {}。", JsonUtils.object2Json(order), JsonUtils.object2Json(result));

            // 更新远程订单号
            if (WrapperClassUtils.biggerThanLong(result.getOrderId(), 0L)) {
                // 更新账单结果
                WithholdPaidVo withholdPaid = new WithholdPaidVo();
                withholdPaid.setBillId(bill.getBillId());
                withholdPaid.setRemoteOrderId(result.getOrderId());
                withholdPaid.setPaid(result.getStatus() == OrderStatus.PAID.getStatus());
                withholdPaid.setAmount(bill.getAmount());
                withholdPaid.setAccountNo(back.getAccountNo());
                withholdPaid.setAccountName(back.getAccountName());
                withholdPaid.setPaidTime(result.getOrderTime());
                withholdPaid.setTollMan(back.getTollMan());
                billWithholdMapper.updateBillBankWithholdBack(withholdPaid);
            }

        } finally {
            RedisUtils.del(buildBillPayingLockKey(bill));
        }
        return result;
    }

    /**
     * 按年月查找托收记录
     * @param year
     * @param month
     * @return
     */
    public BankWithhold findByYearMonth(Integer year, Integer month) {
        String cacheKey = String.format(RealtyConst.Cache.KEY_BANK_WITHHOLD, year, month);

        BankWithhold result = null;

        // load from cache
        String cacheVal = RedisUtils.get(cacheKey);
        if (!StringUtils.isBlank(cacheVal)) {
            result = JsonUtils.json2GenericObject(cacheVal, new TypeReference<BankWithhold>() {});
            return result;
        }

        // load from db
        result = bankWithholdMapper.findByYearMonth(year, month);
        if (result != null) {
            RedisUtils.set(cacheKey, JsonUtils.object2Json(result), RealtyConst.Cache.TTL_1H);
        }
        return result;
    }

    /**
     * 银行托收物业账单列表(待申请)
     * @param search
     * @return
     */
    public PageResult<BankOfferRealtyBillVo> listBankOfferBillApplying(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        search.setBillDate(LocalDate.of(search.getBillYear(), search.getBillMonth(), 1));
        int totalSize = billWithholdMapper.countBankOfferBillApplying(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<BankOfferRealtyBillVo> resultList = billWithholdMapper.listBankOfferBillApplying(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 银行托收物业账单列表(待申请)(不分页)
     * @param search
     * @return
     */
    public List<BankOfferRealtyBillVo> listBankOfferBillApplyingAll(RealtyBillSearchVo search) {
        search.setPage(false);
        search.setBillDate(LocalDate.of(search.getBillYear(), search.getBillMonth(), 1));
        return billWithholdMapper.listBankOfferBillApplying(search);
    }

    /**
     * 银行托收物业账单合计(待申请)
     * @param search
     * @return
     */
    public WithholdSumVo sumBankOfferBillApplying(RealtyBillSearchVo search) {
        search.setBillDate(LocalDate.of(search.getBillYear(), search.getBillMonth(), 1));
        return billWithholdMapper.sumBankOfferBillApplying(search);
    }

    /**
     * 行托收物业账单列表（已提交）
     * @param search
     * @return
     */
    public PageResult<BankOfferRealtyBillVo> listBankOfferBillSubmitted(RealtyBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = billWithholdMapper.countBankOfferBillSubmitted(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<BankOfferRealtyBillVo> resultList = billWithholdMapper.listBankOfferBillSubmitted(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 行托收物业账单列表（已提交）（不分页）
     * @param search
     * @return
     */
    public List<BankOfferRealtyBillVo> listBankOfferBillSubmittedAll(RealtyBillSearchVo search) {
        search.setPage(false);
        return billWithholdMapper.listBankOfferBillSubmitted(search);
    }

    /**
     * 合计银行托收物业账单金额（已提交）
     * @param search
     * @return
     */
    public WithholdSumVo sumBankOfferBillSubmitted(RealtyBillSearchVo search) {
        return billWithholdMapper.sumBankOfferBillSubmitted(search);
    }

    /**
     * 判断是否已托收报盘
     * @param withhold
     * @return
     */
    public boolean isWithholdSubmitted(BankWithhold withhold) {
        return withhold != null && BooleanUtils.isTrue(withhold.getOffer());
    }

    /**
     * 判断是否已托收回盘
     * @param withhold
     * @return
     */
    private boolean isWithholdBack(BankWithhold withhold) {
        return withhold != null && BooleanUtils.isTrue(withhold.getBack());
    }

    /**
     * 查找回盘支付得账单
     * @param back
     * @return
     */
    private BankOfferRealtyBillVo findWithholdPayingBill(WithholdBackVo back) {
        List<BankOfferRealtyBillVo> list = billWithholdMapper.listRealtyBankOfferBill(back.getBillYear(), back.getBillMonth(), back.getRealtySerial());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        // 账号和金额对应得上得记录
        Optional<BankOfferRealtyBillVo> op = list.stream()
                .filter(x -> BooleanUtils.isTrue(x.getOffer()))
                .filter(x -> DecimalUtils.equals(x.getAmount(), back.getAmount()) && Objects.equals(x.getBankAccountNo(), back.getAccountNo()))
                .filter(x -> BillStatus.fromStatus(x.getStatus()) != BillStatus.PAID)
                .findFirst();
        return op.orElse(null);
    }

    /**
     * 构建账单支付分布式锁
     * @param bill
     * @return
     */
    private String buildBillPayingLockKey(BankOfferRealtyBillVo bill) {
        return String.format(RealtyConst.Cache.KEY_REALTY_BILL_PAY, bill.getContractNo(),
                bill.getBillYear() + "-" + bill.getBillMonth());
    }

    /**
     * 账单年月
     * @param billYear
     * @param billMonth
     * @return
     */
    private String buildBillYearMonth(Integer billYear, Integer billMonth) {
        return billYear + StringUtils.fixLength(String.valueOf(billMonth), 2, '0');
    }

    /**
     * 代扣账单转支付账单
     * @param bill
     * @param orderTitle
     * @return
     */
    private OrderItemVo withholdBill2OrderItem(BankOfferRealtyBillVo bill, String orderTitle) {
        OrderItemVo result = new OrderItemVo();
        result.setProductId(bill.getBillId());
        result.setProductName(orderTitle);
        result.setQuantity(1);
        result.setPrice(bill.getAmount());
        result.setTotalAmount(bill.getAmount());


        List<RealtyBillItem> billDetails = billMapper.listBillItems(bill.getBillId());
        result.setDetails(billDetails.stream().map(this::realtyBillItem2OrderItemDetail).collect(Collectors.toList()));
        return result;
    }

    /**
     * 代扣账单详情转支付账单详情
     * @param billItem
     * @return
     */
    private OrderItemDetailVo realtyBillItem2OrderItemDetail(RealtyBillItem billItem) {
        OrderItemDetailVo result = new OrderItemDetailVo();
        result.setFeeId(billItem.getFeeId());
        result.setFeeName(billItem.getFeeName());
        result.setPrice(billItem.getAmount());
        result.setQuantity(1);
        result.setTotalAmount(result.getPrice());
        return result;
    }

}
