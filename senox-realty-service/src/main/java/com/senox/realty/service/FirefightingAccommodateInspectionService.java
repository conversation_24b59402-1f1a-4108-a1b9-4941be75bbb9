package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.InspectResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingAccommodateInspection;
import com.senox.realty.domain.FirefightingInspectionAttr;
import com.senox.realty.event.InspectTaskDropEvent;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.mapper.FirefightingAccommodateInspectionMapper;
import com.senox.realty.vo.FirefightingAccommodateInspectionBriefVo;
import com.senox.realty.vo.FirefightingAccommodateInspectionSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/8 15:46
 */
@Service
@RequiredArgsConstructor
public class FirefightingAccommodateInspectionService
        extends ServiceImpl<FirefightingAccommodateInspectionMapper, FirefightingAccommodateInspection> {

    private final FirefightingInspectionAttrService inspectionAttrService;
    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final ApplicationEventPublisher publisher;

    /**
     * 添加巡检记录
     * @param taskId
     * @param inspection
     * @param attrs
     * @param realtySerials
     * @param medias
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addInspection(Long taskId,
                              FirefightingAccommodateInspection inspection,
                              List<FirefightingInspectionAttr> attrs,
                              List<String> realtySerials,
                              List<String> medias,
                              List<String> reinspectMedias) {
        // 保存巡检记录
        Long result = addInspection(inspection);
        // 物业信息
        inspectRealtyService.saveInspectRealities(inspection.getId(), InspectionType.ACCOMMODATE, realtySerials);
        // 保存巡检变量
        inspectionAttrService.saveInspectionAttrs(InspectionType.ACCOMMODATE, result, attrs);
        // 保存多媒体资料
        inspectMediaService.saveMedias(inspection.getId(), InspectionType.ACCOMMODATE, medias);
        inspectMediaService.saveMedias(inspection.getId(), InspectionType.ACCOMMODATE, 2, reinspectMedias);

        // 发送巡检任务事件
        InspectTaskFulfilledEvent event = new InspectTaskFulfilledEvent(this, InspectionType.ACCOMMODATE, inspection.getId());
        event.setTaskId(taskId);
        event.setEnterpriseId(inspection.getEnterpriseId());
        event.setRealtySerials(realtySerials);
        publisher.publishEvent(event);
        return result;
    }

    /**
     * 更新巡检记录
     * @param inspection
     * @param attrs
     * @param medias
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInspection(FirefightingAccommodateInspection inspection,
                                 List<FirefightingInspectionAttr> attrs,
                                 List<String> realtySerials,
                                 List<String> medias,
                                 List<String> reinspectMedias) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        // 保存巡检记录
        updateInspection(inspection);
        // 物业
        inspectRealtyService.saveInspectRealities(inspection.getId(), InspectionType.ACCOMMODATE, realtySerials);
        // 保存巡检变量
        inspectionAttrService.saveInspectionAttrs(InspectionType.ACCOMMODATE, inspection.getId(), attrs);
        // 保存多媒体资料
        inspectMediaService.saveMedias(inspection.getId(), InspectionType.ACCOMMODATE, medias);
        inspectMediaService.saveMedias(inspection.getId(), InspectionType.ACCOMMODATE, 2, reinspectMedias);
    }

    /**
     * 删除巡检记录
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInspection(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        if (removeById(id)) {
            inspectRealtyService.deleteByInspectId(id, InspectionType.ACCOMMODATE);
            inspectionAttrService.deleteInspectionAttrs(InspectionType.ACCOMMODATE, id);
            inspectMediaService.deleteByInspectId(id, InspectionType.ACCOMMODATE);
            // 触发移除事件
            publisher.publishEvent(new InspectTaskDropEvent(this, InspectionType.ACCOMMODATE, id));
        }
    }


    /**
     * 根据id获取巡检记录
     * @param id
     * @return
     */
    public FirefightingAccommodateInspection findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 违规住人统计
     * @param search
     * @return
     */
    public int countInspection(FirefightingAccommodateInspectionSearchVo search) {
        return getBaseMapper().countInspection(search);
    }

    /**
     * 违规住人列表
     * @param search
     * @return
     */
    public List<FirefightingAccommodateInspectionBriefVo> listInspection(FirefightingAccommodateInspectionSearchVo search) {
        return getBaseMapper().listInspection(search);
    }

    /**
     * 添加巡检记录
     * @param inspection
     * @return
     */
    private Long addInspection(FirefightingAccommodateInspection inspection) {
        if (inspection.getEnterpriseId() == null) {
            inspection.setEnterpriseId(0L);
        }
        if (inspection.getAccommodatedCount() == null) {
            inspection.setAccommodatedCount(0);
        }
        if (inspection.getInspectDate() == null) {
            inspection.setInspectDate(LocalDate.now());
        }

        InspectResult inspectResult = InspectResult.fromValue(inspection.getInspectResult());
        inspectResult = inspectResult == null ? InspectResult.INIT : inspectResult;
        inspection.setInspectResult(inspectResult.getValue());
        inspection.setCreateTime(LocalDateTime.now());
        inspection.setModifiedTime(LocalDateTime.now());
        save(inspection);
        return inspection.getId();
    }

    /**
     * 更新巡检记录
     * @param inspection
     */
    private void updateInspection(FirefightingAccommodateInspection inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        inspection.setCreatorId(null);
        inspection.setCreatorName(null);
        inspection.setCreateTime(null);
        inspection.setModifiedTime(LocalDateTime.now());
        updateById(inspection);
    }
}
