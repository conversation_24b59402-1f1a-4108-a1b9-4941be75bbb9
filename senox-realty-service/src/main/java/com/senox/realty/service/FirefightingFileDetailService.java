package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.domain.FirefightingFileDetail;
import com.senox.realty.mapper.FirefightingFileDetailMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/19 10:03
 */
@Service
public class FirefightingFileDetailService extends ServiceImpl<FirefightingFileDetailMapper, FirefightingFileDetail> {

    /**
     * 添加档案详情
     * @param detail
     */
    public void saveDetail(FirefightingFileDetail detail) {
        detail.setModifiedTime(LocalDateTime.now());
        saveOrUpdate(detail);
    }

    /**
     * 删除档案详情
     * @param fileId
     */
    public void deleteDetail(Long fileId) {
        if (!WrapperClassUtils.biggerThanLong(fileId, 0L)) {
            return;
        }

        LambdaQueryWrapper<FirefightingFileDetail> queryWrapper = new QueryWrapper<FirefightingFileDetail>()
                .lambda()
                .eq(FirefightingFileDetail::getFileId, fileId);
        remove(queryWrapper);
    }

    /**
     * 根据id获取档案详情
     * @param id
     */
    public FirefightingFileDetail findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }


}
