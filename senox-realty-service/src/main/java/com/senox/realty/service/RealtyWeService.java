package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.PageResult;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.domain.EnergyProfitItem;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.RealtyWeSearchVo;
import com.senox.realty.vo.RealtyWeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-8-11
 */
public interface RealtyWeService extends IService<RealtyWe> {


    /**
     * 批量添加水电读数
     * @param weList
     */
    void batchAddWeData(List<RealtyWe> weList);

    /**
     * 批量添加水电读数
     *
     * @param weList
     * @param isOverWrite
     */
    void batchAddWeData(List<RealtyWe> weList, boolean isOverWrite);

    /**
     * 修改物业水电
     *
     * @param we
     */
    void update(RealtyWe we);

    /**
     * 根据id批量更新账单id
     *
     * @param weList
     */
    void batchUpdateBillById(List<RealtyWe> weList);


    /**
     * 根据编号批量更新账单id
     *
     * @param weList
     */
    void batchUpdateBillBySerial(List<RealtyWe> weList);

    /**
     * 取消关联水电读数
     * @param weBillIds
     */
    void unlinkWeBill(List<Long> weBillIds);


    /**
     * 删除水电读数
     * @param ids
     */
    void delete(List<Long> ids);

    /**
     * 删除月水电读数
     * @param month
     */
    void deleteMonthReadings(BillMonthVo month);

    /**
     * 根据id获取物业水电读数
     *
     * @param weId
     * @return
     */
    RealtyWeVo findById(Long weId);

    /**
     * 根据日期检查是否生成过
     *
     * @param year             年
     * @param month            月
     * @return
     */
    boolean checkMonthReadings(Integer year, Integer month);

    /**
     * 合计读数
     * @param search
     * @return
     */
    RealtyWeVo sumReadings(RealtyWeSearchVo search);

    /**
     * 分页查询
     *
     * @param searchVo
     * @return
     */
    PageResult<RealtyWeVo> listReadingsPage(RealtyWeSearchVo searchVo);

    /**
     * 初始化水电
     * @param we 水电实体
     * @param year 年
     * @param month 月
     */
     void prepareRealtyWe(RealtyWe we, Integer year, Integer month);

    /**
     * 能源消费信息
     * @param billTime
     * @param energyType
     * @return
     */
     List<EnergyProfitItem> listEnergyProfits(BillTimeVo billTime, EnergyType energyType);
}
