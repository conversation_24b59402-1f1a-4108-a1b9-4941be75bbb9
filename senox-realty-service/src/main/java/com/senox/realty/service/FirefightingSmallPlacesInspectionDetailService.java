package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.domain.FirefightingSmallPlacesInspectionDetail;
import com.senox.realty.mapper.FirefightingSmallPlacesInspectionDetailMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/29 17:11
 */
@Service
public class FirefightingSmallPlacesInspectionDetailService
        extends ServiceImpl<FirefightingSmallPlacesInspectionDetailMapper, FirefightingSmallPlacesInspectionDetail> {

    /**
     * 保存巡检明细
     * @param detail
     */
    public void saveInspectionDetail(FirefightingSmallPlacesInspectionDetail detail) {
        if (!WrapperClassUtils.biggerThanLong(detail.getInspectionId(), 0L)) {
            return;
        }
        detail.setModifiedTime(LocalDateTime.now());
        saveOrUpdate(detail);
    }

    /**
     * 删除巡检明细
     * @param inspectionId
     */
    public void deleteInspectionDetail(Long inspectionId) {
        if (!WrapperClassUtils.biggerThanLong(inspectionId, 0L)) {
            return;
        }

        removeById(inspectionId);
    }

    /**
     * 获取巡检明细
     * @param inspectionId
     * @return
     */
    public FirefightingSmallPlacesInspectionDetail findDetailByInspectionId(Long inspectionId) {
        return WrapperClassUtils.biggerThanLong(inspectionId, 0L) ? getById(inspectionId) : null;
    }

}
