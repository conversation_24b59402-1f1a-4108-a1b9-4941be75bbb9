package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.convert.MaintainMaterialItemConvertor;
import com.senox.realty.domain.MaintainJob;
import com.senox.realty.domain.MaintainMaterial;
import com.senox.realty.domain.MaintainMaterialItem;
import com.senox.realty.mapper.MaintainJobMapper;
import com.senox.realty.mapper.MaintainMaterialMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.MaintainMaterialDataVo;
import com.senox.realty.vo.MaintainMaterialItemVo;
import com.senox.realty.vo.MaintainMaterialOutNoVo;
import com.senox.realty.vo.MaintainMaterialSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/6 14:41
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MaintainMaterialService extends ServiceImpl<MaintainMaterialMapper, MaintainMaterial> {

    private final MaintainJobMapper maintainJobMapper;

    private final MaintainMaterialItemService maintainMaterialItemService;

    private final MaintainMaterialItemConvertor itemConvertor;
    /**
     * 保存维修所需的物料
     *
     * @param material
     * @param materialItems
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveMaintainMaterial(MaintainMaterial material, List<MaintainMaterialItem> materialItems) {
        if (material.getOrderId() == null || material.getJobId() == null) {
            throw new BusinessException("订单未存在或未派单");
        }
        MaintainJob maintainJob = maintainJobMapper.selectById(material.getJobId());
        if (maintainJob == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        //查询db是否已经创建过物料单
        MaintainMaterial dbItem = findMaterialByMaterialIdAndJobId(material.getId(), material.getJobId());
        material.setCreateTime(LocalDateTime.now());
        material.setModifiedTime(LocalDateTime.now());
        if (dbItem != null) {
            if (dbItem.getOutNo() == null && CollectionUtils.isEmpty(materialItems)) {
                log.info("【物料明细为空且未领料， 删除物料】");
                removeById(dbItem.getId());
                maintainMaterialItemService.removeByMaterialId(dbItem.getId());
                return 0L;
            }
            //如果已经领料则不能更新物料
            if (dbItem.getOutNo() != null) {
                throw new BusinessException("已领料，不能更改物料");
            }
            material.setId(dbItem.getId());
        }
        saveOrUpdate(material);
        maintainMaterialItemService.saveMaintainMaterialItem(material.getId(), materialItems);
        return material.getId();
    }

    /**
     * 删除维修物料
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaintainMaterial(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        MaintainMaterialItem maintainMaterialItem = maintainMaterialItemService.getById(id);
        if (maintainMaterialItem == null) {
            return;
        }
        maintainMaterialItemService.deleteMaintainMaterialItem(id);
        //无物料子项则删除物料父项
        List<MaintainMaterialItem> items = maintainMaterialItemService.maintainMaterialItemList(maintainMaterialItem.getMaterialId());
        if (CollectionUtils.isEmpty(items)) {
            removeById(maintainMaterialItem.getMaterialId());
        }
    }

    /**
     * 批量删除物料及明细
     * @param ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteMaterial(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        boolean result = removeByIds(ids);
        if (result) {
            maintainMaterialItemService.batchRemoveByMaterialIds(ids);
        }
    }

    /**
     * 获取维修所需物料
     *
     * @param id
     * @return
     */
    public MaintainMaterial findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 维修物料列表
     *
     * @param search
     * @return
     */
    public PageResult<MaintainMaterialDataVo> listMaintainMaterial(MaintainMaterialSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = getBaseMapper().countMaintainMaterial(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<MaintainMaterialDataVo> resultList = listMaintainMaterialDataVo(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 维修物料列表
     * @param searchVo
     * @return
     */
    public List<MaintainMaterialDataVo> listMaintainMaterialDataVo(MaintainMaterialSearchVo searchVo) {
        List<MaintainMaterialDataVo> resultList = getBaseMapper().listMaintainMaterial(searchVo);
        if (!CollectionUtils.isEmpty(resultList)) {
            List<Long> ids = resultList.stream().map(MaintainMaterialDataVo::getId).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(ids)) {
                List<MaintainMaterialItemVo> materialItemVos = itemConvertor.toV(maintainMaterialItemService.maintainMaterialItemList(ids));
                for (MaintainMaterialDataVo materialDataVo : resultList) {
                    materialDataVo.setMaterialItemVos(materialItemVos.stream().filter(x -> Objects.equals(materialDataVo.getId(), x.getMaterialId())).collect(Collectors.toList()));
                }
            }
        }
        return resultList;
    }

    /**
     * 更新出库单号
     *
     * @param outNoVo
     */
    public void updateOutNo(MaintainMaterialOutNoVo outNoVo) {
        if (!WrapperClassUtils.biggerThanLong(outNoVo.getMaterialId(), 0L)) {
            return;
        }
        getBaseMapper().updateOutNo(outNoVo);
    }

    /**
     * 撤销出库
     * @param outNo
     */
    public void cancelOutBound(String outNo) {
        if (StringUtils.isBlank(outNo)) {
            return;
        }
        getBaseMapper().cancelOutBound(outNo);
    }

    /**
     * 更新维修物料单
     * @param material
     */
    public void updateMaintainMaterial(MaintainMaterial material) {
        if (!WrapperClassUtils.biggerThanLong(material.getId(), 0L)) {
            return;
        }
        updateById(material);
    }

    /**
     * 根据物料id和派工id查询维修物料
     *
     * @param materialId
     * @param jobId
     * @return
     */
    public MaintainMaterial findMaterialByMaterialIdAndJobId(Long materialId, Long jobId) {
        return getOne(new QueryWrapper<MaintainMaterial>().lambda().eq(MaintainMaterial::getId, materialId)
                .eq(MaintainMaterial::getJobId, jobId));
    }


    /**
     * 根据派工id查询维修物料
     *
     * @param jobId
     * @return
     */
    public List<MaintainMaterial> findMaterialByJobId(Long jobId) {
        return list(new QueryWrapper<MaintainMaterial>().lambda().eq(MaintainMaterial::getJobId, jobId));
    }

    /**
     * 批量插入物料
     * @param materialMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveMaterial(Map<MaintainMaterial, List<MaintainMaterialItem>> materialMap) {
        if (CollectionUtils.isEmpty(materialMap)) {
            return;
        }
        for (Map.Entry<MaintainMaterial, List<MaintainMaterialItem>> entry : materialMap.entrySet()) {
            MaintainMaterial material = entry.getKey();
            List<MaintainMaterialItem> materialItems = entry.getValue();
            MaintainMaterial dbMaterial = null;
            //查找最后一个物料如果未领料则拼接，并且现在是新增
            if (!WrapperClassUtils.biggerThanLong(material.getId(), 0L)) {
                List<MaintainMaterial> materialList = findMaterialByJobId(material.getJobId());
                if (!CollectionUtils.isEmpty(materialList)) {
                    MaintainMaterial maintainMaterial = materialList.get(materialList.size() - 1);
                    if (maintainMaterial.getOutNo() == null) {
                        List<MaintainMaterialItem> dbMaterialItemList = maintainMaterialItemService.maintainMaterialItemList(maintainMaterial.getId());
                        //之前的单未出库，可拼接
                        material.setId(maintainMaterial.getId());
                        materialItems.addAll(dbMaterialItemList);
                    }
                }
            }
            log.info("【物料明细】拼接的物料结果为：{}", JsonUtils.object2Json(materialItems));
            dbMaterial = findMaterialByMaterialIdAndJobId(material.getId(), material.getJobId());
            if (dbMaterial != null) {
                if (dbMaterial.getOutNo() != null) {
                    log.info("【已领料不做更新】单号, {}", JsonUtils.object2Json(dbMaterial));
                    continue;
                }
                if (CollectionUtils.isEmpty(materialItems)) {
                    log.info("【物料明细为空且未领料， 删除物料】");
                    removeById(dbMaterial.getId());
                    maintainMaterialItemService.removeByMaterialId(dbMaterial.getId());
                    continue;
                }
                material.setId(dbMaterial.getId());
            }
            saveOrUpdate(material);
            maintainMaterialItemService.saveMaintainMaterialItem(material.getId(), materialItems);
        }
    }

    public MaintainMaterial buildMaterial(Long materialId, Long jobId, Long orderId) {
        MaintainMaterial material = new MaintainMaterial();
        material.setId(materialId);
        material.setOrderId(orderId);
        material.setJobId(jobId);
        material.setCreateTime(LocalDateTime.now());
        material.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(material);
        ContextUtils.initEntityModifier(material);
        return material;
    }

    public void initMaterialItem(List<MaintainMaterialItem> materialItems) {
        if (CollectionUtils.isEmpty(materialItems)) {
            return;
        }
        materialItems.forEach(item -> {
            ContextUtils.initEntityCreator(item);
            ContextUtils.initEntityModifier(item);
            item.setCreateTime(LocalDateTime.now());
            item.setModifiedTime(LocalDateTime.now());
        });
    }
}
