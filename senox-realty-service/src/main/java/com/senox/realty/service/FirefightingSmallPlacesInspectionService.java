package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.InspectResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingInspectionAttr;
import com.senox.realty.domain.FirefightingSmallPlacesInspection;
import com.senox.realty.domain.FirefightingSmallPlacesInspectionDetail;
import com.senox.realty.event.InspectTaskDropEvent;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.mapper.FirefightingSmallPlacesInspectionMapper;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionBriefVo;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/29 17:18
 */
@Service
@RequiredArgsConstructor
public class FirefightingSmallPlacesInspectionService
        extends ServiceImpl<FirefightingSmallPlacesInspectionMapper, FirefightingSmallPlacesInspection> {

    private final FirefightingSmallPlacesInspectionDetailService inspectionDetailService;
    private final FirefightingInspectionAttrService inspectionAttrService;
    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final ApplicationEventPublisher publisher;

    /**
     * 添加巡检记录
     * @param inspection
     * @param detail
     * @param attrs
     * @param realtySerials
     * @param medias
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addInspection(Long taskId,
                              FirefightingSmallPlacesInspection inspection,
                              FirefightingSmallPlacesInspectionDetail detail,
                              List<FirefightingInspectionAttr> attrs,
                              List<String> realtySerials,
                              List<String> medias) {
        // 保存巡检记录
        Long result = addInspection(inspection);
        // 保存巡检明细
        detail.setInspectionId(result);
        inspectionDetailService.saveInspectionDetail(detail);
        inspectRealtyService.saveInspectRealities(result, InspectionType.SMALL_PLACES, realtySerials);
        inspectionAttrService.saveInspectionAttrs(InspectionType.SMALL_PLACES, result, attrs);
        inspectMediaService.saveMedias(result, InspectionType.SMALL_PLACES, medias);

        // 发送巡检任务事件
        InspectTaskFulfilledEvent event = new InspectTaskFulfilledEvent(this, InspectionType.SMALL_PLACES, inspection.getId());
        event.setTaskId(taskId);
        event.setEnterpriseId(inspection.getEnterpriseId());
        event.setRealtySerials(realtySerials);
        publisher.publishEvent(event);
        return result;
    }

    /**
     * 更新巡检记录
     * @param inspection
     * @param detail
     * @param attrs
     * @param realtySerials
     * @param medias
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInspection(FirefightingSmallPlacesInspection inspection,
                                 FirefightingSmallPlacesInspectionDetail detail,
                                 List<FirefightingInspectionAttr> attrs,
                                 List<String> realtySerials,
                                 List<String> medias) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        // 保存巡检记录
        updateInspection(inspection);
        // 保存巡检明细
        detail.setInspectionId(inspection.getId());
        inspectionDetailService.saveInspectionDetail(detail);
        inspectRealtyService.saveInspectRealities(inspection.getId(), InspectionType.SMALL_PLACES, realtySerials);
        inspectionAttrService.saveInspectionAttrs(InspectionType.SMALL_PLACES, inspection.getId(), attrs);
        inspectMediaService.saveMedias(inspection.getId(), InspectionType.SMALL_PLACES, medias);

    }

    /**
     * 删除巡检记录
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInspection(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }
        if (removeById(id)) {
            inspectionDetailService.deleteInspectionDetail(id);
            inspectRealtyService.deleteByInspectId(id, InspectionType.SMALL_PLACES);
            inspectionAttrService.deleteInspectionAttrs(InspectionType.SMALL_PLACES, id);
            inspectMediaService.deleteByInspectId(id, InspectionType.SMALL_PLACES);

            // 触发移除事件
            publisher.publishEvent(new InspectTaskDropEvent(this, InspectionType.SMALL_PLACES, id));
        }
    }

    /**
     * 根据id获取巡检记录
     * @param id
     * @return
     */
    public FirefightingSmallPlacesInspection findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 巡检合计
     * @param search
     * @return
     */
    public int countInspection(FirefightingSmallPlacesInspectionSearchVo search) {
        return getBaseMapper().countSmallPlacesInspection(search);
    }

    /**
     * 巡检列表
     * @param search
     * @return
     */
    public List<FirefightingSmallPlacesInspectionBriefVo> listInspection(FirefightingSmallPlacesInspectionSearchVo search) {
        return getBaseMapper().listSmallPlacesInspection(search);
    }

    /**
     * 添加巡检记录
     * @param inspection
     * @return
     */
    private Long addInspection(FirefightingSmallPlacesInspection inspection) {
        if (inspection.getInspectDate() == null) {
            inspection.setInspectDate(LocalDate.now());
        }

        InspectResult inspectResult = InspectResult.fromValue(inspection.getInspectResult());
        inspectResult = inspectResult == null ? InspectResult.INIT : inspectResult;
        inspection.setInspectResult(inspectResult.getValue());
        inspection.setCreateTime(LocalDateTime.now());
        inspection.setModifiedTime(LocalDateTime.now());
        save(inspection);
        return inspection.getId();
    }

    /**
     * 更新巡检记录
     * @param inspection
     */
    private void updateInspection(FirefightingSmallPlacesInspection inspection) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        inspection.setCreatorId(null);
        inspection.setCreatorName(null);
        inspection.setCreateTime(null);
        inspection.setModifiedTime(LocalDateTime.now());
        updateById(inspection);
    }
}
