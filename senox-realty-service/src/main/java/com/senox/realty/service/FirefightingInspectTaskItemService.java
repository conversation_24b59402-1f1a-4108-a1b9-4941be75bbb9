package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingInspectTaskItem;
import com.senox.realty.domain.FirefightingInspectTaskRealty;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.mapper.FirefightingInspectTaskItemMapper;
import com.senox.realty.vo.FirefightingInspectPropertyTaskVo;
import com.senox.realty.vo.FirefightingInspectTaskItemSearchVo;
import com.senox.realty.vo.FirefightingTaskItemDropVo;
import com.senox.user.vo.EnterpriseRealtyVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/14 13:38
 */
@Service
@RequiredArgsConstructor
public class FirefightingInspectTaskItemService
        extends ServiceImpl<FirefightingInspectTaskItemMapper, FirefightingInspectTaskItem> {

    private final EnterpriseService enterpriseService;
    private final FirefightingNoticeService noticeService;
    private final FirefightingFileService fileService;
    private final FirefightingUtilityInspectionService utilityInspectionService;
    private final FirefightingStoreInspectionService storeInspectionService;
    private final FirefightingSmallPlacesInspectionService smallPlacesInspectionService;
    private final FirefightingAccommodateInspectionService accommodateInspectionService;
    private final FirefightingInspectTaskRealtyService inspectTaskRealtyService;


    /**
     * 批量新增子任务
     * @param taskId
     * @param list
     */
    public void batchSaveTaskItem(Long taskId, List<FirefightingInspectTaskItem> list) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0L) || CollectionUtils.isEmpty(list)) {
            return;
        }

        // list to deal
        list.forEach(x -> {
            prepareTaskItem(x);
            x.setTaskId(taskId);
            x.setModifiedTime(LocalDateTime.now());
        });
        // enterpriseTask
        List<FirefightingInspectTaskItem> enterpriseTasks = list.stream()
                .filter(x -> WrapperClassUtils.biggerThanLong(x.getEnterpriseId(), 0L))
                .collect(Collectors.toList());

        // list task in db
        List<FirefightingInspectTaskItem> dbList = listItemByTaskId(taskId);
        // 处理非经营户任务
        list = list.stream()
                .filter(x -> x.getEnterpriseId() == null || x.getEnterpriseId() == 0L)
                .filter(x -> CollectionUtils.isEmpty(dbList) || !dbList.contains(x))
                .collect(Collectors.toList());
        saveNotEnterpriseTaskItem(list);

        // 处理经营户任务
        enterpriseTasks = enterpriseTasks.stream()
                .filter(x -> dbList.stream().noneMatch(y -> Objects.equals(x.getEnterpriseId(), y.getEnterpriseId()) && Objects.equals(x.getInspectType(), y.getInspectType())))
                .collect(Collectors.toList());
        saveEnterpriseTaskItem(enterpriseTasks);
    }

    /**
     * 保存非经营户巡检任务
     * @param list
     */
    public void saveNotEnterpriseTaskItem(List<FirefightingInspectTaskItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        saveBatch(list);

        // 物业任务还需处理
        List<FirefightingInspectTaskRealty> realtyTasks = new ArrayList<>(list.size());
        for (FirefightingInspectTaskItem taskItem : list) {
            if (StringUtils.isBlank(taskItem.getRealtySerial())) {
                continue;
            }

            realtyTasks.add(new FirefightingInspectTaskRealty(taskItem.getTaskId(), taskItem.getId(), taskItem.getRealtySerial()));
        }

        inspectTaskRealtyService.batchAddInspectTaskRealty(realtyTasks);
    }

    /**
     * 保存经营户巡检任务
     * @param list
     */
    public void saveEnterpriseTaskItem(List<FirefightingInspectTaskItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 企业物业列表
        List<Long> enterpriseIds = list.stream().map(FirefightingInspectTaskItem::getEnterpriseId).collect(Collectors.toList());
        List<EnterpriseRealtyVo> enterpriseRealities = enterpriseService.listEnterpriseRealty(enterpriseIds);
        Map<Long, List<String>> enterpriseRealtyMap = enterpriseRealities.stream()
                .collect(Collectors.groupingBy(EnterpriseRealtyVo::getEnterpriseId, Collectors.mapping(EnterpriseRealtyVo::getRealtySerial, Collectors.toList())));

        for (FirefightingInspectTaskItem taskItem : list) {
            List<String> realities = enterpriseRealtyMap.get(taskItem.getEnterpriseId());
            taskItem.setRealtySerial(CollectionUtils.isEmpty(realities) ? StringUtils.EMPTY : realities.get(0));
        }
        // 保存任务
        saveBatch(list);

        // 保存物业
        List<FirefightingInspectTaskRealty> realtyTasks = new ArrayList<>(enterpriseRealities.size());
        for (FirefightingInspectTaskItem taskItem : list) {
            List<String> realities = enterpriseRealtyMap.get(taskItem.getEnterpriseId());
            for (String realty : realities) {
                realtyTasks.add(new FirefightingInspectTaskRealty(taskItem.getTaskId(), taskItem.getId(), realty));
            }
        }
        inspectTaskRealtyService.batchAddInspectTaskRealty(realtyTasks);
    }

    /**
     * 完成巡检任务
     * @param taskItemIds
     * @param inspectId
     */
    public void fulfillInspectTaskItem(List<Long> taskItemIds, Long inspectId) {
        if (CollectionUtils.isEmpty(taskItemIds) || !WrapperClassUtils.biggerThanLong(inspectId, 0L)) {
            return;
        }

        LambdaQueryWrapper<FirefightingInspectTaskItem> queryWrapper = new QueryWrapper<FirefightingInspectTaskItem>()
                .lambda()
                .in(FirefightingInspectTaskItem::getId, taskItemIds)
                .eq(FirefightingInspectTaskItem::getInspectId, 0L);

        FirefightingInspectTaskItem updateItem = new FirefightingInspectTaskItem();
        updateItem.setInspectId(inspectId);
        updateItem.setModifiedTime(LocalDateTime.now());

        update(updateItem, queryWrapper);
    }

    /**
     * 取消巡检任务
     * @param inspectId
     * @param inspectType
     */
    public void dropInspectTaskItem(Long inspectId, InspectionType inspectType) {
        LambdaQueryWrapper<FirefightingInspectTaskItem> queryWrapper = new QueryWrapper<FirefightingInspectTaskItem>()
                .lambda()
                .eq(FirefightingInspectTaskItem::getInspectType, inspectType)
                .eq(FirefightingInspectTaskItem::getInspectId, inspectId);

        FirefightingInspectTaskItem updateItem = new FirefightingInspectTaskItem();
        updateItem.setInspectId(0L);
        updateItem.setModifiedTime(LocalDateTime.now());

        update(updateItem, queryWrapper);
    }

    /**
     * 删除子任务和巡检表
     * @param drop
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTaskItemAndInspection(FirefightingTaskItemDropVo drop) {
        List<FirefightingInspectTaskItem> taskItems = listItemByTaskId(drop.getTaskId(), drop.getTaskItems());
        if (CollectionUtils.isEmpty(taskItems)) {
            return;
        }

        // 删除子任务
        deleteTaskItem(drop.getTaskId(), drop.getTaskItems());
        if (drop.isDeleteInspection()) {
            for (FirefightingInspectTaskItem item : taskItems) {
                deleteTaskInspection(item);
            }
        }
    }

    /**
     * 删除子任务
     * @param taskId
     * @param ids
     */
    private void deleteTaskItem(Long taskId, List<Long> ids) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0L) || CollectionUtils.isEmpty(ids)) {
            return;
        }

        LambdaQueryWrapper<FirefightingInspectTaskItem> queryWrapper = new QueryWrapper<FirefightingInspectTaskItem>()
                .lambda()
                .eq(FirefightingInspectTaskItem::getTaskId, taskId)
                .in(FirefightingInspectTaskItem::getId, ids);
        remove(queryWrapper);

        // 巡检任务物业
        inspectTaskRealtyService.deleteInspectTaskRealtyByTask(taskId, ids);
    }

    /**
     * 删除巡检
     * @param taskItem
     */
    private void deleteTaskInspection(FirefightingInspectTaskItem taskItem) {
        if (taskItem == null || taskItem.getInspectType() == null) {
            return;
        }

        switch (taskItem.getInspectType()) {
            case UTILITY:
                utilityInspectionService.deleteInspection(taskItem.getInspectId());
                break;

            case NOTICE:
                noticeService.deleteNotice(taskItem.getInspectId());
                break;

            case FILE:
                fileService.deleteFile(taskItem.getInspectId());
                break;

            case STORE:
                storeInspectionService.deleteInspection(taskItem.getInspectId());
                break;

            case SMALL_PLACES:
                smallPlacesInspectionService.deleteInspection(taskItem.getInspectId());
                break;

            case ACCOMMODATE:
                accommodateInspectionService.deleteInspection(taskItem.getInspectId());
                break;

            default:
                break;
        }
    }

    /**
     * 获取任务明细
     * @param id
     * @return
     */
    public FirefightingInspectTaskItem findItemById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return getById(id);
    }

    /**
     * 获取任务明细
     * @param taskId
     * @return
     */
    public List<FirefightingInspectTaskItem> listItemByTaskId(Long taskId) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<FirefightingInspectTaskItem> queryWrapper = new QueryWrapper<FirefightingInspectTaskItem>()
                .lambda()
                .eq(FirefightingInspectTaskItem::getTaskId, taskId);
        return list(queryWrapper);
    }

    /**
     * 获取任务明细
     * @param taskId
     * @param itemIds
     * @return
     */
    public List<FirefightingInspectTaskItem> listItemByTaskId(Long taskId, List<Long> itemIds) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0L) || CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<FirefightingInspectTaskItem> queryWrapper = new QueryWrapper<FirefightingInspectTaskItem>()
                .lambda()
                .eq(FirefightingInspectTaskItem::getTaskId, taskId)
                .in(FirefightingInspectTaskItem::getId, itemIds);
        return list(queryWrapper);
    }

    /**
     * 根据履行的任务查找巡检任务
     * @param fulfilledEvent
     * @return
     */
    public List<FirefightingInspectTaskItem> listItemByFulfilledEvent(InspectTaskFulfilledEvent fulfilledEvent) {
        return getBaseMapper().listTaskItemByFulfilledEvent(fulfilledEvent);
    }

    /**
     * 任务明细合计
     * @param search
     * @return
     */
    public int countTaskItem(FirefightingInspectTaskItemSearchVo search) {
        return getBaseMapper().countTaskItem(search);
    }

    /**
     * 任务明细列表
     * @param search
     * @return
     */
    public List<FirefightingInspectPropertyTaskVo> listTaskItem(FirefightingInspectTaskItemSearchVo search) {
        return getBaseMapper().listTaskItem(search);
    }

    /**
     * 查找任务
     * @param utilityId
     * @param realtySerials
     * @param inspectionType
     * @return
     */
    public List<FirefightingInspectTaskItem> listTaskItem(Long utilityId,
                                                          List<String> realtySerials,
                                                          InspectionType inspectionType) {
        LambdaQueryWrapper<FirefightingInspectTaskItem> queryWrapper = new QueryWrapper<FirefightingInspectTaskItem>()
                .lambda()
                .eq(FirefightingInspectTaskItem::getUtilityId, utilityId == null ? 0 : utilityId)
                .in(!CollectionUtils.isEmpty(realtySerials), FirefightingInspectTaskItem::getRealtySerial, realtySerials)
                .eq(FirefightingInspectTaskItem::getInspectType, inspectionType)
                .eq(FirefightingInspectTaskItem::getInspectId, 0L)
                .orderByAsc(FirefightingInspectTaskItem::getId);
        return list(queryWrapper);
    }

    /**
     * 子任务初始化
     * @param taskItem
     */
    private void prepareTaskItem(FirefightingInspectTaskItem taskItem) {
        if (taskItem.getUtilityId() == null) {
            taskItem.setUtilityId(0L);
        }
        if (taskItem.getEnterpriseId() == null) {
            taskItem.setEnterpriseId(0L);
        }
        if (taskItem.getInspectId() == null) {
            taskItem.setInspectId(0L);
        }
        taskItem.setRealtySerial(StringUtils.trimToEmpty(taskItem.getRealtySerial()));
    }

}
