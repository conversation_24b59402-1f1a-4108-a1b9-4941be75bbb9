package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.senox.realty.domain.RealtyAlias;
import com.senox.realty.vo.RealtyReadingsVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-8-12
 */
public interface RealtyAliasService extends IService<RealtyAlias> {

    /**
     * 批量保存物业别名
     * @param realtyId
     * @param aliasList
     */
    void batchSaveRealtyAlias(Long realtyId, List<RealtyAlias> aliasList);

    /**
     * 批量更新物业水电读数
     * @param list
     */
    void batchUpdateAliasReadings(List<RealtyReadingsVo> list);

    /**
     * 查找物业别名
     * @param realtyId
     * @return
     */
    List<RealtyAlias> listRealtyAlias(Long realtyId);
}
