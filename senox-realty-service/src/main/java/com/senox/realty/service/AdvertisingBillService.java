package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.BillReceiptBriefVo;
import com.senox.common.vo.TollSerialVo;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.domain.AdvertisingBill;
import com.senox.realty.domain.AdvertisingBillPaidEvent;
import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.mapper.AdvertisingBillMapper;
import com.senox.realty.vo.AdvertisingBillSearchVo;
import com.senox.realty.vo.AdvertisingBillVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/25 16:23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvertisingBillService extends ServiceImpl<AdvertisingBillMapper, AdvertisingBill> {

    private final ApplicationEventPublisher eventPublisher;

    /**
     * 保存广告合同应收账单
     * @param contract
     */
    public void saveBill(AdvertisingContract contract) {
        AdvertisingBill bill = buildBillByContract(contract);

        AdvertisingBill dbBill = findByContractNo(contract.getContractNo());
        if (dbBill == null) {
            // 新增账单
            addBill(bill);

        } else {
            // 更新账单
            bill.setId(dbBill.getId());
            updateBill(bill);
        }
    }

    /**
     * 添加广告合同应收账单
     * @param bill
     * @return
     */
    private void addBill(AdvertisingBill bill) {
        if (bill.getReceipt() == null) {
            bill.setReceipt(Boolean.FALSE);
        }
        getBaseMapper().addBill(bill);
    }

    /**
     * 更新广告合同应收账单
     * @param bill
     */
    private void updateBill(AdvertisingBill bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            return;
        }

        bill.setCreatorId(null);
        bill.setCreatorName(null);
        bill.setCreateTime(null);

        LambdaQueryWrapper<AdvertisingBill> queryWrapper = new QueryWrapper<AdvertisingBill>().lambda()
                .eq(AdvertisingBill::getId, bill.getId())
                .eq(AdvertisingBill::getStatus, BillStatus.INIT.getStatus());
        update(bill, queryWrapper);
    }

    /**
     * 更新账单状态
     * @param billPaid
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBillStatus(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return;
        }

        int result= 0;
        // 更新订单支付结果
        if (!CollectionUtils.isEmpty(billPaid.getBillIds())) {
            log.info("根据广告账单id更新账单状态 {}", JsonUtils.object2Json(billPaid));
            result = updateBillPaidById(billPaid);

        } else {
            log.info("根据广告支付订单id更新账单状态 {}", JsonUtils.object2Json(billPaid));
            result = updateBillPaidByRemoteOrder(billPaid);
        }

        // 支付成功生成应付账单
        if (result > 0 && BooleanUtils.isTrue(billPaid.getPaid())) {
            eventPublisher.publishEvent(new AdvertisingBillPaidEvent(this, billPaid));
        }
    }

    /**
     * 更新账单票据号
     * @param serial
     */
    public void updateBillTollSerial(TollSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getBillId(), 0L)) {
            return;
        }

        AdvertisingBill bill = new AdvertisingBill();
        bill.setTollSerial(serial.getBillSerial());
        bill.setModifiedTime(LocalDateTime.now());
        if (WrapperClassUtils.biggerThanLong(serial.getOperatorId(), 0L)) {
            bill.setModifierId(serial.getBillId());
        }

        LambdaQueryWrapper<AdvertisingBill> queryWrapper = new QueryWrapper<AdvertisingBill>().lambda()
                .eq(AdvertisingBill::getId, serial.getBillId())
                .eq(AdvertisingBill::getStatus, BillStatus.PAID.getStatus());
        update(bill, queryWrapper);
    }

    /**
     * 更新开票信息
     * @param receipt
     */
    public void updateBillReceipt(BillReceiptBriefVo receipt) {
        if (CollectionUtils.isEmpty(receipt.getBillIds())) {
            return;
        }
        getBaseMapper().updateBillReceipt(receipt);
    }

    /**
     * 根据id删除应收账单
     * @param id
     */
    public void deleteBill(Long id) {
        AdvertisingBill bill = findById(id);
        if (bill == null) {
            return;
        }

        if (BillStatus.fromStatus(bill.getStatus()) != BillStatus.INIT) {
            throw new BusinessException("合同账单已支付");
        }
        removeById(id);
    }

    /**
     * 根据id朝朝应收账单
     * @param id
     * @return
     */
    public AdvertisingBill findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return getById(id);
    }

    /**
     * 根据合同查找应收账单
     * @param contractNo
     * @return
     */
    public AdvertisingBill findByContractNo(String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            return null;
        }

        LambdaQueryWrapper<AdvertisingBill> queryWrapper = new QueryWrapper<AdvertisingBill>().lambda()
                .eq(AdvertisingBill::getContractNo, contractNo);
        return getOne(queryWrapper);
    }

    /**
     * 根据id查找账单
     * @param ids
     * @return
     */
    public List<AdvertisingBill> listById(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AdvertisingBill> queryWrapper = new QueryWrapper<AdvertisingBill>().lambda().in(AdvertisingBill::getId, ids);
        return list(queryWrapper);
    }

    /**
     * 根据支付订单id查找账单
     * @param remoteOrderId
     * @return
     */
    public List<AdvertisingBill> listByRemoteOrderId(Long remoteOrderId) {
        if (!WrapperClassUtils.biggerThanLong(remoteOrderId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AdvertisingBill> queryWrapper = new QueryWrapper<AdvertisingBill>().lambda().eq(AdvertisingBill::getRemoteOrderId, remoteOrderId);
        return list(queryWrapper);
    }

    /**
     * 根据id获取应收账单详情
     * @param id
     * @return
     */
    public AdvertisingBillVo findDetailById(Long id) {
        List<AdvertisingBillVo> resultList = listDetailByIds(Collections.singletonList(id));
        return CollectionUtils.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * 根据id列表获取应收账单详情
     * @param ids
     * @return
     */
    public List<AdvertisingBillVo> listDetailByIds(List<Long> ids) {
        return CollectionUtils.isEmpty(ids) ? Collections.emptyList() : getBaseMapper().listByIds(ids);
    }

    /**
     * 广告合同应收账单合计
     * @param search
     * @return
     */
    public AdvertisingBill sumBill(AdvertisingBillSearchVo search) {
        return getBaseMapper().sumBill(search);
    }

    /**
     * 广告合同应收账单统计
     * @param search
     * @return
     */
    public int countBill(AdvertisingBillSearchVo search) {
        return getBaseMapper().countBill(search);
    }

    /**
     * 广告合同应收账单列表
     * @param search
     * @return
     */
    public List<AdvertisingBillVo> listBill(AdvertisingBillSearchVo search) {
        return getBaseMapper().listBill(search);
    }

    /**
     * 根据合同生成账单
     * @param contract
     * @return
     */
    private AdvertisingBill buildBillByContract(AdvertisingContract contract) {
        AdvertisingBill result = new AdvertisingBill();
        if (contract.getSignDate() != null) {
            result.setBillYear(contract.getSignDate().getYear());
            result.setBillMonth(contract.getSignDate().getMonthValue());
        }
        result.setSpaceId(contract.getSpaceId());
        result.setContractNo(contract.getContractNo());
        result.setAmount(contract.getAmount());
        result.setCreatorId(contract.getModifierId());
        result.setCreatorName(contract.getModifierName());
        result.setCreateTime(LocalDateTime.now());
        result.setModifierId(contract.getModifierId());
        result.setModifierName(contract.getModifierName());
        result.setModifiedTime(LocalDateTime.now());
        return result;
    }


    /**
     * 根据账单id更新订单支付结果
     * @param billPaid
     */
    private int updateBillPaidById(BillPaidVo billPaid) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            return 0;
        }

        return getBaseMapper().updateBillPaidById(billPaid);
    }

    /**
     * 根据支付订单id更新账单支付结果
     * @param billPaid
     */
    private int updateBillPaidByRemoteOrder(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return 0;
        }

        return getBaseMapper().updateBillPaidByRemoteOrder(billPaid);
    }

}
