package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.domain.AdvertisingMedia;
import com.senox.realty.mapper.AdvertisingMediaMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/25 9:06
 */
@Service
public class AdvertisingMediaService extends ServiceImpl<AdvertisingMediaMapper, AdvertisingMedia> {

    /**
     * 保存广告媒体
     * @param contractId
     * @param mediaList
     */
    public void batchSaveMedia(Long contractId, List<AdvertisingMedia> mediaList) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L) || mediaList == null) {
            return;
        }

        mediaList.forEach(x -> x.setContractId(contractId));
        List<AdvertisingMedia> srcMediaList = listByContractId(contractId);

        // 对比并更新数据
        DataSepDto<AdvertisingMedia> sepData = SeparateUtils.separateData(srcMediaList, mediaList);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(AdvertisingMedia::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 根据合同id获取广告媒体内容
     * @param contractId
     * @return
     */
    public List<AdvertisingMedia> listByContractId(Long contractId) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AdvertisingMedia> queryWrapper = new QueryWrapper<AdvertisingMedia>().lambda()
                .eq(AdvertisingMedia::getContractId, contractId);
        return list(queryWrapper);
    }

}
