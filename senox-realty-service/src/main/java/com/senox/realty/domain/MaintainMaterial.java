package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <AUTHOR>
 * @date 2023/4/6 13:52
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_material")
public class MaintainMaterial extends TableIdEntity {

    @TableId(
            type = IdType.AUTO
    )
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 派单id
     */
    private Long jobId;
    /**
     *出库单号
     */
    private String outNo;
    /**
     * 备注
     */
    private String remark;
}
