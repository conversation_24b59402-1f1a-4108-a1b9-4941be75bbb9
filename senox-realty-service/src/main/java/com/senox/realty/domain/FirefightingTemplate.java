package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/23 9:09
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_template")
public class FirefightingTemplate extends TableIdEntity {

    /**
     * 模板编码
     */
    private String code;
    /**
     * 模板版本号
     */
    private Integer version;
    /**
     * 模板状态
     */
    private Integer status;
    /**
     * 生效日期
     */
    private LocalDate validDate;
    /**
     * 失效日期
     */
    private LocalDate invalidDate;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
}
