package com.senox.realty.domain;

import com.senox.common.vo.BillPaidVo;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2023/8/3 11:11
 */
@Getter
@Setter
public class AdvertisingBillPaidEvent extends ApplicationEvent {

    private static final long serialVersionUID = 5688680702814420842L;

    /**
     * 支付信息
     */
    private BillPaidVo billPaid;

    public AdvertisingBillPaidEvent(Object source, BillPaidVo billPaid) {
        super(source);
        this.billPaid = billPaid;
    }
}
