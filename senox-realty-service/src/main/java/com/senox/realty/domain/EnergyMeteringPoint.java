package com.senox.realty.domain;

import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Getter
@Setter
public class EnergyMeteringPoint extends TableIdEntity {

    /**
     * 终端编码
     */
    private String rtuCode;

    /**
     * 终端名称
     */
    private String rtuName;

    /**
     * 设备编码
     */
    private String code;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 能源类型：1.电 2.水
     */
    private Integer type;

    /**
     * 倍率
     */
    private BigDecimal rate;

    /**
     * 状态：0.停用1.运行
     */
    private Integer status;

    /**
     * 状态：-1.未知 0.合闸 1.拉闸
     */
    private Integer powerStatus;
}
