package com.senox.realty.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022-7-15
 */
@Getter
@Setter
@ToString
@TableName("r_realty_bill_we")
public class RealtyBillWe extends TableIdEntity {
    /**
     * 年份
     */
    private Integer billYear;
    /**
     * 月份
     */
    private Integer billMonth;
    /**
     * 账单id
     */
    private Long billId;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 副档编号
     */
    private String realtyAliasSerial;
    /**
     * 客户编号
     */
    private Long customerId;
    /**
     * 上次水读数
     */
    private Long lastWaterReadings = 0L;
    /**
     * 本次水读数
     */
    private Long waterReadings = 0L;
    /**
     * 水公摊
     */
    private Integer waterShare = 0;
    /**
     * 用水量
     */
    private Long waterCost = 0L;
    /**
     * 水价格
     */
    private BigDecimal waterPrice = BigDecimal.ZERO;
    /**
     * 水费
     */
    private BigDecimal waterAmount = BigDecimal.ZERO;
    /**
     * 上次电读数
     */
    private Long lastElectricReadings = 0L;
    /**
     * 本次电读数
     */
    private Long electricReadings = 0L;
    /**
     * 电公摊
     */
    private Integer electricShare = 0;
    /**
     * 用电量
     */
    private Long electricCost = 0L;
    /**
     * 电价格
     */
    private BigDecimal electricPrice = BigDecimal.ZERO;
    /**
     * 电费
     */
    private BigDecimal electricAmount = BigDecimal.ZERO;
    /**
     * 总金额
     */
    private BigDecimal totalAmount = BigDecimal.ZERO;
    /**
     * 上次抄表日期
     */
    private LocalDate lastRecordDate;
    /**
     * 抄表日期
     */
    private LocalDate recordDate;

    @TableField(exist = false)
    private Long znwId;

    @TableField(exist = false)
    private Boolean ignoreElectricShare;
    
    @TableField(exist = false)
    private Boolean ignoreWaterShare;


}
