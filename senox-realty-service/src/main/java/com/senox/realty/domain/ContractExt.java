package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import com.senox.common.utils.DecimalUtils;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/2/19 10:35
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class ContractExt extends BaseEntity {

    private static final long serialVersionUID = 9216137051306065176L;
    /**
     * 合同id
     */
    private Long contractId;
    /**
     * 交款方式
     * @see com.senox.realty.constant.CostType
     */
    private Integer costType;
    /**
     * 银行名
     */
    private String bankName;
    /**
     * 银行账号
     */
    private String bankAccountNo;
    /**
     * 银行账户名
     */
    private String bankAccountName;
    /**
     * 银行账户身份证
     */
    private String bankAccountIdcard;
    /**
     * 首月手续费率
     */
    private BigDecimal firstRate;
    /**
     * 每月手续费率
     */
    private BigDecimal monthlyRate;
    /**
     * 每月手续费绝对值
     */
    private BigDecimal monthlyFeeAbs;
    /**
     * 水价类别
     */
    private Integer waterPriceType;
    /**
     * 电价类别
     */
    private Integer electricPriceType;
    /**
     * 滞纳金开始日期
     */
    private Integer penaltyStartDate;
    /**
     * 滞纳金比例
     */
    private BigDecimal penaltyRate;
    /**
     * 归档文件地址
     */
    private String archiveUrl;
    /**
     * 备注
     */
    private String remark;

}
