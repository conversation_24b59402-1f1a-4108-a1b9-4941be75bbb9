package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/24 8:51
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_notice")
public class FirefightingNotice extends TableIdEntity {

    /**
     * 模板编码
     */
    private String templateCode;
    /**
     * 模板版本号
     */
    private Integer templateVersion;
    /**
     * 负责人
     */
    private String storeKeyman;
    /**
     * 负责人联系方式
     */
    private String storeContact;
    /**
     * 单位地址
     */
    private String store;
    /**
     * 经营户id
     */
    private Long enterpriseId;
    /**
     * 巡检员
     */
    private String inspector;
    /**
     * 通知日期
     */
    private LocalDate notifyDate;
}
