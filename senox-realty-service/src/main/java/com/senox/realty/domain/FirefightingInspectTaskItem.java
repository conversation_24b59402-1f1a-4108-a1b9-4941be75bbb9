package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.realty.constant.InspectionType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/14 11:17
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_inspect_task_item")
public class FirefightingInspectTaskItem {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 公共消防设施id
     */
    private Long utilityId;
    /**
     * 经营户id
     */
    private Long enterpriseId;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 巡检类型
     */
    private InspectionType inspectType;
    /**
     * 巡检id
     */
    private Long inspectId;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FirefightingInspectTaskItem that = (FirefightingInspectTaskItem) o;
        return Objects.equals(taskId, that.taskId)
                && Objects.equals(utilityId, that.utilityId)
                && Objects.equals(enterpriseId, that.enterpriseId)
                && Objects.equals(realtySerial, that.realtySerial)
                && inspectType == that.inspectType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(taskId, utilityId, enterpriseId, realtySerial, inspectType);
    }
}
