package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/25 10:10
 */
@TableName("r_advertising_statistics")
@Getter
@Setter
public class AdvertisingStatistics extends TableIdEntity {

    /**
     * 统计日期
     */
    private LocalDate statisticsDate;

    /**
     * 广告位数量
     */
    private Integer advertisingNum;

    /**
     * 未租赁广告位数量
     */
    private Integer unRentAdvertisingNum;

    /**
     * 已租赁广告位数量
     */
    private Integer rentAdvertisingNum;

    /**
     * 已收租数量
     */
    private Integer rentCollectNum;

    /**
     * 已收租金额
     */
    private BigDecimal rentCollectAmount;

    /**
     * 未已收租数量
     */
    private Integer unRentCollectNum;

    /**
     * 未收租金额
     */
    private BigDecimal unRentCollectAmount;
}
