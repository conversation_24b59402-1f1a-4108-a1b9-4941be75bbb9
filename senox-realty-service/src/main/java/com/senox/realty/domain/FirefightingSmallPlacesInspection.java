package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/29 14:50
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_small_places_inspection")
public class FirefightingSmallPlacesInspection extends TableIdEntity {
    /**
     * 受检单位
     */
    private String inspectedPlace;
    /**
     * 经营户id
     */
    private Long enterpriseId;
    /**
     * 负责人
     */
    private String keyman;
    /**
     * 地址
     */
    private String address;
    /**
     * 联系方式
     */
    private String contact;
    /**
     * 场所类型
     */
    private String placeType;
    /**
     * 工商注册情况
     */
    private String businessLicense;
    /**
     * 建筑结构安全情况
     */
    private String buildingSafety;
    /**
     * 生产经营范围
     */
    private String businessScope;
    /**
     * 建筑物数量
     */
    private Integer buildingCount;
    /**
     * 最高建筑层数
     */
    private Integer buildingFloors;
    /**
     * 建筑占地面积
     */
    private BigDecimal buildingArea;
    /**
     * 建筑结构
     */
    private String buildingStructure;
    /**
     * 建筑结构其他
     */
    private String buildingStructureOther;
    /**
     * 建筑使用情况
     */
    private String buildingUsage;
    /**
     * 经营者使用面积
     */
    private BigDecimal runnerUsageArea;
    /**
     * 员工数量
     */
    private Integer runnerStaffs;
    /**
     * 疏散出口楼梯
     */
    private String evacurationStaris;
    /**
     * 疏散出口直通天面
     */
    private String evacurationRoofStraight;
    /**
     * 疏散安全出口
     */
    private String evacurationExit;
    /**
     * 疏散逃生窗口
     */
    private String evacurationEscapeWindow;
    /**
     * 灭火器
     */
    private Integer fireExtinguisher;
    /**
     * 应急照明
     */
    private Integer emergencyLights;
    /**
     * 消防卷盘
     */
    @TableField("is_fire_reels_disposed")
    private Boolean fireReelsDisposed;
    /**
     * 紧急逃生措施
     */
    @TableField("is_escape_facilities_disposed")
    private Boolean escapeFacilitiesDisposed;
    /**
     * 简易喷淋
     */
    @TableField("is_simple_sprinklers_disposed")
    private Boolean simpleSprinklersDisposed;
    /**
     * 火警报警器
     */
    @TableField("is_fire_alarms_disposed")
    private Boolean fireAlarmsDisposed;
    /**
     * 过期灭火器数量
     */
    private Integer expireFireExtinguisher;
    /**
     * 违规住人
     */
    private String unauthorizedResidence;
    /**
     * 整改灭火器情况
     */
    private String reFireExtinguisher;
    /**
     * 整改灭火器数量
     */
    private Integer reFireExtinguisherNum;
    /**
     * 整改应急灯情况
     */
    private String reEmergencyLights;
    /**
     * 整改消防报警器情况
     */
    private String reFireAlarmsDisposed;
    /**
     * 整改电线情况
     */
    private String reWire;
    /**
     * 整改违规住人情况
     */
    private String reUnauthorizedResidence;
    /**
     * 巡检结果
     */
    private Integer inspectResult;
    /**
     * 巡检人员
     */
    private String inspector;
    /**
     * 巡检日期
     */
    private LocalDate inspectDate;
    /**
     * 被检查场所负责人签名
     */
    private String keymanInspectedSignature;
    /**
     * 整改截止日期
     */
    private LocalDate rectificationDeadline;
    /**
     * 复查人员
     */
    private String reinspector;
    /**
     * 复查日期
     */
    private LocalDate reinspectDate;
    /**
     * 被检查场所复查负责人签名
     */
    private String keymanReinspectedSignature;
}
