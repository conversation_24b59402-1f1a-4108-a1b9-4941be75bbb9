package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物业账单导入任务
 * <AUTHOR>
 * @date 2021/6/11 10:03
 */
public class RealtyBillImportTask extends BaseEntity {

    /**
     * 账单年份
     */
    private Integer billYear;
    /**
     * 账单月份
     */
    private Integer billMonth;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 合同性质
     */
    private String contractType;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 物业名称
     */
    private String realtyName;
    /**
     * 档主编号
     */
    private String realtyOwnerSerial;
    /**
     * 档主名称
     */
    private String realtyOwnerName;
    /**
     * 租金
     */
    private BigDecimal rentAmount;
    /**
     * 管理费
     */
    private BigDecimal manageAmount;
    /**
     * 电费
     */
    private BigDecimal electricAmount;
    /**
     * 水费
     */
    private BigDecimal waterAmount;
    /**
     * 滞纳金
     */
    private BigDecimal penaltyAmount;
    /**
     * 实收滞纳金
     */
    private BigDecimal penaltyAmountActual;
    /**
     * 合计费用
     */
    private BigDecimal totalAmount;
    /**
     * 已支付费用
     */
    private BigDecimal paidAmount;
    /**
     * 已退费用
     */
    private BigDecimal refundAmount;
    /**
     * 支付状态
     */
    private String paidStatus;
    /**
     * 任务状态
     */
    private Integer taskStatus;
    /**
     * 任务结果
     */
    private String taskMessage;
    /**
     * 任务执行时间
     */
    private LocalDateTime taskExecTime;


    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getRealtySerial() {
        return realtySerial;
    }

    public void setRealtySerial(String realtySerial) {
        this.realtySerial = realtySerial;
    }

    public String getRealtyName() {
        return realtyName;
    }

    public void setRealtyName(String realtyName) {
        this.realtyName = realtyName;
    }

    public String getRealtyOwnerSerial() {
        return realtyOwnerSerial;
    }

    public void setRealtyOwnerSerial(String realtyOwnerSerial) {
        this.realtyOwnerSerial = realtyOwnerSerial;
    }

    public String getRealtyOwnerName() {
        return realtyOwnerName;
    }

    public void setRealtyOwnerName(String realtyOwnerName) {
        this.realtyOwnerName = realtyOwnerName;
    }

    public BigDecimal getRentAmount() {
        return rentAmount;
    }

    public void setRentAmount(BigDecimal rentAmount) {
        this.rentAmount = rentAmount;
    }

    public BigDecimal getManageAmount() {
        return manageAmount;
    }

    public void setManageAmount(BigDecimal manageAmount) {
        this.manageAmount = manageAmount;
    }

    public BigDecimal getElectricAmount() {
        return electricAmount;
    }

    public void setElectricAmount(BigDecimal electricAmount) {
        this.electricAmount = electricAmount;
    }

    public BigDecimal getWaterAmount() {
        return waterAmount;
    }

    public void setWaterAmount(BigDecimal waterAmount) {
        this.waterAmount = waterAmount;
    }

    public BigDecimal getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public BigDecimal getPenaltyAmountActual() {
        return penaltyAmountActual;
    }

    public void setPenaltyAmountActual(BigDecimal penaltyAmountActual) {
        this.penaltyAmountActual = penaltyAmountActual;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getPaidStatus() {
        return paidStatus;
    }

    public void setPaidStatus(String paidStatus) {
        this.paidStatus = paidStatus;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getTaskMessage() {
        return taskMessage;
    }

    public void setTaskMessage(String taskMessage) {
        this.taskMessage = taskMessage;
    }

    public LocalDateTime getTaskExecTime() {
        return taskExecTime;
    }

    public void setTaskExecTime(LocalDateTime taskExecTime) {
        this.taskExecTime = taskExecTime;
    }

    @Override
    public String toString() {
        return "RealtyBillImportTask{"
                + "billYear=" + billYear
                + ", billMonth=" + billMonth
                + ", contractNo='" + contractNo + '\''
                + ", contractType='" + contractType + '\''
                + ", realtySerial='" + realtySerial + '\''
                + ", realtyName='" + realtyName + '\''
                + ", realtyOwnerSerial='" + realtyOwnerSerial + '\''
                + ", realtyOwnerName='" + realtyOwnerName + '\''
                + ", rentAmount=" + rentAmount
                + ", manageAmount=" + manageAmount
                + ", electricAmount=" + electricAmount
                + ", waterAmount=" + waterAmount
                + ", penaltyAmount=" + penaltyAmount
                + ", penaltyAmountActual=" + penaltyAmountActual
                + ", totalAmount=" + totalAmount
                + ", paidAmount=" + paidAmount
                + ", refundAmount=" + refundAmount
                + ", paidStatus='" + paidStatus + '\''
                + ", taskStatus=" + taskStatus
                + ", taskMessage='" + taskMessage + '\''
                + ", taskExecTime=" + taskExecTime
                + '}';
    }
}
