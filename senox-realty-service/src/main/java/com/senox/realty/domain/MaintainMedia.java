package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/2/23 16:12
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_media")
public class MaintainMedia {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 派工单id
     */
    private Long jobId;
    /**
     * 派工单子项id
     */
    private Long jobItemId;
    /**
     * 多媒体路径
     */
    private String mediaUrl;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;


    public MaintainMedia() {
    }

    public MaintainMedia(Long orderId, Long jobId, Long jobItemId, String mediaUrl) {
        this.orderId = orderId;
        this.jobId = jobId;
        this.jobItemId = jobItemId;
        this.mediaUrl = mediaUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MaintainMedia that = (MaintainMedia) o;
        return Objects.equals(orderId, that.orderId)
                && Objects.equals(jobId, that.jobId)
                && Objects.equals(jobItemId, that.jobItemId)
                && Objects.equals(mediaUrl, that.mediaUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderId, jobId, jobItemId, mediaUrl);
    }
}
