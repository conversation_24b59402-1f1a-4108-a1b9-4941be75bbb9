package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/26 15:24
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("d_security_media")
public class SecurityMedia {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 日志id
     */
    private Long journalId;
    /**
     * 多媒体链接
     */
    private String mediaUrl;
    /**
     * 处理次数
     */
    private Integer processTimes;
    /**
     * 修改人
     */
    private Long modifierId;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SecurityMedia that = (SecurityMedia) o;
        return journalId.equals(that.journalId)
                && mediaUrl.equals(that.mediaUrl)
                && processTimes.equals(that.processTimes);
    }

    @Override
    public int hashCode() {
        return Objects.hash(journalId, mediaUrl);
    }

}
