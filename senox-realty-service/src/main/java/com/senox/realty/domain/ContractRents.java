package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("r_contract_rents")
public class ContractRents  {

    /**
     * 合同编号
     * */
    @TableId(type = IdType.NONE)
    private String contractNo;

    /**
     * 管理费
     * */
    private BigDecimal manageAmount;

    /**
     * 押金费
     * */
    private BigDecimal depositAmount;

    /**
     * 业主名
     * */
    private String ownerName;

    /**
     * 押金状态
     * */
    private Integer depositStatus;

    /**
     * 租金金额
     * */
    private BigDecimal rentAmount;

    /**
     * 代租合同号
     * */
    private String rentProxyContractNo;

    /**
     * 业主号码
     * */
    private String ownerContact;

    /**
     * 创建时间
     * */
    private LocalDateTime createTime;

    /**
     * 修改时间
     * */
    private LocalDateTime modifiedTime;
}
