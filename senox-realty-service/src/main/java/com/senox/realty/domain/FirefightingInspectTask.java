package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/14 11:13
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_inspect_task")
public class FirefightingInspectTask extends TableIdEntity {

    /**
     * 任务名
     */
    private String name;
    /**
     * 任务类型
     */
    private Integer category;
    /**
     * 开始时间
     */
    private LocalDate startDate;
    /**
     * 结束时间
     */
    private LocalDate endDate;
    /**
     * 备注
     */
    private String remark;
}
