package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 广告位
 * <AUTHOR>
 * @date 2023/7/18 9:05
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
@TableName("r_advertising_space")
public class AdvertisingSpace extends TableIdEntity {

    /**
     * 编号
     */
    private String serialNo;
    /**
     * 名称
     */
    private String name;
    /**
     * 区域
     */
    private String region;
    /**
     * 街道
     */
    private String street;
    /**
     * 地址位置
     */
    private String address;
    /**
     * 长
     */
    private BigDecimal length;
    /**
     * 宽
     */
    private BigDecimal width;
    /**
     * 面积
     */
    private BigDecimal size;
}
