package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/6 11:45
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_charge")
public class MaintainCharge extends TableIdEntity {
    @TableId(
            type = IdType.AUTO
    )
    private Long id;
    /**
     * 收费单号
     */
    private String chargeNo;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 任务id
     */
    private Long jobId;
    /**
     * 收费年份
     */
    private Integer chargeYear;
    /**
     * 收费月份
     */
    private Integer chargeMonth;
    /**
     * 合计
     */
    private BigDecimal totalAmount;
    /**
     * 收费状态：0初始化；1已支付
     */
    private Integer status;
    /**
     * 支付时间
     */
    private LocalDateTime paidTime;
    /**
     * 支付账单id
     */
    private Long remoteOrderId;
    /**
     * 票据号
     */
    private String tollSerial;
    /**
     * 收费员
     */
    private Long tollManId;
    /**
     * 备注
     */
    private String remark;

}
