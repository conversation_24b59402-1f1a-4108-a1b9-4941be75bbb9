package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/5 15:54
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_evaluate_media")
public class MaintainEvaluateMedia {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 多媒体路径
     */
    private String mediaUrl;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    public MaintainEvaluateMedia() {
    }

    public MaintainEvaluateMedia(Long orderId, String mediaUrl) {
        this.orderId = orderId;
        this.mediaUrl = mediaUrl;
    }
}
