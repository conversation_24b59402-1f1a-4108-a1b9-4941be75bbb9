package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/24 8:56
 */
@TableName("r_realty_statistics")
@Getter
@Setter
public class RealtyStatistics extends TableIdEntity {

    /**
     * 统计日期
     */
    private LocalDate statisticsDate;

    /**
     * 物业数量
     */
    private Integer realtyNum;

    /**
     * 未租赁公司物业数量
     */
    private Integer unRentCompanyRealtyNum;

    /**
     * 已租赁公司物业数量
     */
    private Integer rentCompanyRealtyNum;

    /**
     * 物业合同数量
     */
    private Integer realtyContractNum;

    /**
     * 代租合同数量
     */
    private Integer rentContractNum;

    /**
     * 已收租数量
     */
    private Integer rentCollectNum;

    /**
     * 已收租金额
     */
    private BigDecimal rentCollectAmount;

    /**
     * 未已收租数量
     */
    private Integer unRentCollectNum;

    /**
     * 未收租金额
     */
    private BigDecimal unRentCollectAmount;

    /**
     * 滞纳金
     */
    private BigDecimal penaltyAmount;
}
