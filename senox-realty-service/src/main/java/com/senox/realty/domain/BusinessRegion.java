package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;

/**
 * 经营区域
 * <AUTHOR>
 * @Date 2020/12/15 14:37
 */
public class BusinessRegion extends BaseEntity {

    /**
     * 经营区域
     */
    private String name;
    /**
     * 号牌前缀
     */
    private String cycleNo;

    /**
     * 排序
     */
    private Integer orderNum;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCycleNo() {
        return cycleNo;
    }

    public void setCycleNo(String cycleNo) {
        this.cycleNo = cycleNo;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BusinessRegion businessRegion = (BusinessRegion) o;
        return Objects.equals(name, businessRegion.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }

    @Override
    public String toString() {
        return "BusinessRegion{"
                + "name='" + name + '\''
                + ", cycleNo='" + cycleNo + '\''
                + '}';
    }
}
