package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/29 15:20
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_inspection_attr")
public class FirefightingInspectionAttr {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 巡检类型
     */
    private String inspectionType;
    /**
     * 巡检id
     */
    private Long inspectionId;
    /**
     * 模板编码
     */
    private String templateCode;
    /**
     * 模板版本
     */
    private Integer templateVersion;
    /**
     * 变量
     */
    private String attrName;
    /**
     * 变量类型
     */
    private String attrType;
    /**
     * 变量值
     */
    private String attrValue;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FirefightingInspectionAttr that = (FirefightingInspectionAttr) o;
        return Objects.equals(inspectionType, that.inspectionType)
                && Objects.equals(inspectionId, that.inspectionId)
                && Objects.equals(templateCode, that.templateCode)
                && Objects.equals(templateVersion, that.templateVersion)
                && Objects.equals(attrName, that.attrName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(inspectionType, inspectionId, templateCode, templateVersion, attrName);
    }
}
