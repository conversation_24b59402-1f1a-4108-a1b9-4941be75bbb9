package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 一次性收费项目
 * <AUTHOR>
 * @date 2021/10/11 16:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OneTimeFee extends BaseEntity {

    private static final long serialVersionUID = 6360880292662227230L;
    /**
     * 收费项目名
     */
    private String name;
    /**
     * 冷藏费项
     */
    private Boolean refrigeration;
    /**
     * 移动端显示(false:不显示,true:显示)
     */
    private boolean mobileEditable;
}
