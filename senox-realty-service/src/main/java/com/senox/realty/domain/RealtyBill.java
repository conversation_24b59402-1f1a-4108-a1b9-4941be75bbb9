package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import com.senox.realty.constant.BillStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 物业应收账单
 * <AUTHOR>
 * @date 2021/6/8 10:35
 */
@Getter
@Setter
@ToString
public class RealtyBill extends BaseEntity {

    private static final long serialVersionUID = -6442367134584835323L;

    /**
     * 账单年月
     */
    private String billYearMonth;
    /**
     * 账单年份
     */
    private Integer billYear;
    /**
     * 账单月份
     */
    private Integer billMonth;
    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 区域1
     */
    private String region1;
    /**
     * 区域2
     */
    private String region2;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 滞纳金
     */
    private BigDecimal penaltyAmount;
    /**
     * 滞纳金起算日期
     */
    private LocalDate penaltyDate;
    /**
     * 减免滞纳金
     */
    private Boolean penaltyIgnore;
    /**
     * 滞纳金减免金额
     */
    private BigDecimal penaltyIgnoreAmount;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * 支付金额
     */
    private BigDecimal paidAmount;
    /**
     * 待收费金额
     */
    private BigDecimal paidStillAmount;
    /**
     * 退费
     */
    private BigDecimal refundAmount;
    /**
     * 实付滞纳金
     */
    private BigDecimal penaltyPaidAmount;
    /**
     * 支付时间
     */
    private LocalDateTime paidTime;
    /**
     * 订单id
     */
    private Long remoteOrderId;
    /**
     * 订单状态
     * @see BillStatus
     */
    private Integer status;
    /**
     * 已下发
     */
    private Boolean send;
    /**
     * 下发时间
     */
    private LocalDateTime sendTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 收费员
     */
    private Long tollManId;
    /**
     * 票据号
     */
    private String billSerial;
    /**
     * 已开发票
     */
    private Boolean receipt;
    /**
     * 开票人
     */
    private Long receiptMan;
    /**
     * 开票备注
     */
    private String receiptRemark;
    /**
     * 开票时间
     */
    private LocalDateTime receiptTime;

}
