package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;


/**
 * 街道
 * <AUTHOR>
 * @Date 2021/1/19 17:12
 */
public class Street extends BaseEntity {

    /**
     * 街道
     */
    private String name;
    /**
     * 区域id
     */
    private Long regionId;

    /**
     * 排序
     */
    private Integer orderNum;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Street street = (Street) o;
        return Objects.equals(name, street.name) && Objects.equals(regionId, street.regionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }

    @Override
    public String toString() {
        return "Street{"
                + "name='" + name + '\''
                + ", regionId=" + regionId
                + '}';
    }
}
