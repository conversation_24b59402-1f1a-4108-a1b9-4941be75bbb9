package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.OperateEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/28 16:11
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_job_item")
public class MaintainJobItem extends OperateEntity {

    @TableId(
            type = IdType.AUTO
    )
    private Long id;

    /**
     * 派工id
     */
    private Long jobId;


    /**
     * 处理人id
     */
    private Long handlerId;
    /**
     * 处理人
     */
    private String handlerName;

    /**
     * 状态
     */
    private Integer handlerStatus;

    /**
     * 备注
     */
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MaintainJobItem item = (MaintainJobItem) o;
        return  Objects.equals(jobId, item.getJobId())
                && Objects.equals(handlerId, item.handlerId)
                && Objects.equals(handlerName, item.handlerName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(jobId, handlerId, handlerName);
    }
}
