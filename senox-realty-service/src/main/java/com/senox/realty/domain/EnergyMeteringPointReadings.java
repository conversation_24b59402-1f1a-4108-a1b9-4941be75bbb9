package com.senox.realty.domain;

import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Getter
@Setter
public class EnergyMeteringPointReadings extends TableIdEntity {

    /**
     * id
     */
    private Long id;

    /**
     * 终端编码
     */
    private String rtuCode;

    /**
     * 设备编码
     */
    private String pointCode;

    /**
     * 设备倍率
     */
    private BigDecimal pointRate;

    /**
     * 设备类型
     */
    private Integer pointType;

    /**
     * 读数
     */
    private BigDecimal readings;

    /**
     * 数据时间
     */
    private LocalDateTime dataTime;

    /**
     * 抓取时间
     */
    private LocalDateTime grabTime;

    /**
     * 物业编号
     */
    private String realtySerialNo;

    /**
     * 物业名称
     */
    private String realtyName;
}
