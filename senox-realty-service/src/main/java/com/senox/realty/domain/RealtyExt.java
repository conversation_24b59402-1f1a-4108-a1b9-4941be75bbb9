package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/3/1 9:39
 */
@Getter
@Setter
@ToString
public class RealtyExt extends BaseEntity {

    private static final long serialVersionUID = 3735550788398907560L;
    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 水表读数
     */
    private Integer waterReadings;
    /**
     * 电表读数
     */
    private Integer electricReadings;
    /**
     * 水单价
     */
    private BigDecimal waterPrice;
    /**
     * 电单价
     */
    private BigDecimal electricPrice;
    /**
     * 水消费单元
     */
    private Integer waterConsumeUnit;
    /**
     * 电消费单元
     */
    private Integer electricConsumeUnit;
    /**
     * 租金税收编码
     */
    private String rentTaxCode;
    /**
     * 担保开始日期
     */
    private LocalDate guaranteeStartDate;
    /**
     * 担保结束日期
     */
    private LocalDate guaranteeEndDate;
    /**
     * 备注
     */
    private String remark;

}
