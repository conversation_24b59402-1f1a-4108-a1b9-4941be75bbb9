package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 广告收益分成
 * <AUTHOR>
 * @date 2023/7/25 14:05
 */
@Getter
@Setter
@ToString
@TableName("r_advertising_profit_share")
public class AdvertisingProfitShare extends TableIdEntity {

    /**
     * 合同id
     */
    private Long contractId;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 物业名
     */
    private String realtyName;
    /**
     * 分成金额
     */
    private BigDecimal shareAmount;
    /**
     * 备注
     */
    private String remark;

}
