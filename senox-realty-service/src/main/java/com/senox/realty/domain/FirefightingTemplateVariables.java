package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/23 9:09
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_template_variables")
public class FirefightingTemplateVariables {

    /**
     * 模板编码
     */
    private String code;
    /**
     * 模板版本号
     */
    private Integer version;
    /**
     * 模板变量名
     */
    private String attrName;
    /**
     * 模板变量类型
     */
    private String attrType;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FirefightingTemplateVariables that = (FirefightingTemplateVariables) o;
        return Objects.equals(code, that.code)
                && Objects.equals(version, that.version)
                && Objects.equals(attrName, that.attrName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, version, attrName);
    }
}
