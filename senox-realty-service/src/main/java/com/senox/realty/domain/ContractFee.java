package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import com.senox.common.utils.DecimalUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * 合同费项及费用
 * <AUTHOR>
 * @date 2021/2/22 9:11
 */
@Getter
@Setter
@ToString
public class ContractFee extends BaseEntity {

    /**
     * 合同id
     */
    private Long contractId;
    /**
     * 费项id
     */
    private Long feeId;
    /**
     * 类别
     * @see com.senox.realty.constant.ContractFeeCategory
     */
    private Integer category;
    /**
     * 期间
     */
    private Integer period;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 免租期
     */
    private Integer rentFreePeriod;
    /**
     * 开始时间
     */
    private LocalDate startDate;
    /**
     * 结束时间
     */
    private LocalDate endDate;


    public ContractFee() {
    }

    public ContractFee(Long contractId, Long feeId, Integer category, BigDecimal amount) {
        this.contractId = contractId;
        this.feeId = feeId;
        this.category = category;
        this.amount = amount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ContractFee that = (ContractFee) o;
        return Objects.equals(contractId, that.contractId)
                && Objects.equals(feeId, that.feeId)
                && Objects.equals(category, that.category)
                && Objects.equals(period, that.period)
                && DecimalUtils.equals(amount, that.amount)
                && Objects.equals(rentFreePeriod, that.rentFreePeriod)
                && Objects.equals(startDate, that.startDate)
                && Objects.equals(endDate, that.endDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contractId, feeId, category, period, amount, rentFreePeriod, startDate, endDate);
    }

    @Override
    public String toString() {
        return "ContractFee{"
                + "contractId=" + contractId
                + ", feeId=" + feeId
                + ", category=" + category
                + ", period=" + period
                + ", amount=" + amount
                + ", rentFreePeriod=" + rentFreePeriod
                + ", startDate=" + startDate
                + ", endDate=" + endDate
                + '}';
    }
}
