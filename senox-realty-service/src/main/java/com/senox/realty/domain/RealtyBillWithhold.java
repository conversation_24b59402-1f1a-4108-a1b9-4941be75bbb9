package com.senox.realty.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物业账单托收
 * <AUTHOR>
 * @date 2022/1/17 11:50
 */
public class RealtyBillWithhold {

    /**
     * id
     */
    private Long id;
    /**
     * 账单id
     */
    private Long billId;
    /**
     * 报盘
     */
    private Boolean offer;
    /**
     * 银行名
     */
    private String offerBank;
    /**
     * 银行账号
     */
    private String offerAccountNo;
    /**
     * 开户名
     */
    private String offerAccountName;
    /**
     * 开户人身份证
     */
    private String offerAccountIdcard;
    /**
     * 报盘时间
     */
    private LocalDateTime offerTime;
    /**
     * 是否回盘
     */
    private Boolean back;
    /**
     * 回盘金额
     */
    private BigDecimal backAmount;
    /**
     * 回盘账号
     */
    private String backAccountNo;
    /**
     * 回盘户名
     */
    private String backAccountName;
    /**
     * 回盘时间
     */
    private LocalDateTime backTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public Boolean getOffer() {
        return offer;
    }

    public void setOffer(Boolean offer) {
        this.offer = offer;
    }

    public String getOfferBank() {
        return offerBank;
    }

    public void setOfferBank(String offerBank) {
        this.offerBank = offerBank;
    }

    public String getOfferAccountNo() {
        return offerAccountNo;
    }

    public void setOfferAccountNo(String offerAccountNo) {
        this.offerAccountNo = offerAccountNo;
    }

    public String getOfferAccountName() {
        return offerAccountName;
    }

    public void setOfferAccountName(String offerAccountName) {
        this.offerAccountName = offerAccountName;
    }

    public String getOfferAccountIdcard() {
        return offerAccountIdcard;
    }

    public void setOfferAccountIdcard(String offerAccountIdcard) {
        this.offerAccountIdcard = offerAccountIdcard;
    }

    public LocalDateTime getOfferTime() {
        return offerTime;
    }

    public void setOfferTime(LocalDateTime offerTime) {
        this.offerTime = offerTime;
    }

    public Boolean getBack() {
        return back;
    }

    public void setBack(Boolean back) {
        this.back = back;
    }

    public BigDecimal getBackAmount() {
        return backAmount;
    }

    public void setBackAmount(BigDecimal backAmount) {
        this.backAmount = backAmount;
    }

    public String getBackAccountNo() {
        return backAccountNo;
    }

    public void setBackAccountNo(String backAccountNo) {
        this.backAccountNo = backAccountNo;
    }

    public String getBackAccountName() {
        return backAccountName;
    }

    public void setBackAccountName(String backAccountName) {
        this.backAccountName = backAccountName;
    }

    public LocalDateTime getBackTime() {
        return backTime;
    }

    public void setBackTime(LocalDateTime backTime) {
        this.backTime = backTime;
    }
}
