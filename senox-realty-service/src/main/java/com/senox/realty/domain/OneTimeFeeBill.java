package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 一次性收费账单
 * <AUTHOR>
 * @date 2021/10/13 15:42
 */
@Setter
@Getter
@ToString
public class OneTimeFeeBill extends BaseEntity {

    private static final long serialVersionUID = -8840523356515858232L;
    /**
     * 单号
     */
    private String billNo;
    /**
     * 年份
     */
    private Integer billYear;
    /**
     * 月份
     */
    private Integer billMonth;
    /**
     * 费项id
     */
    private Long feeId;
    /**
     * 费项名
     */
    private String feeName;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 部门id
     */
    private Long departmentId;
    /**
     * 部门名
     */
    private String departmentName;
    /**
     * 客户编号
     */
    private String customerSerial;
    /**
     * 客户
     */
    private String customer;
    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     * @see com.senox.realty.constant.BillStatus
     */
    private Integer status;
    /**
     * 经手人
     */
    private Long operateBy;
    /**
     * 操作时间
     */
    private LocalDate operateDate;
    /**
     * 收费票据好
     */
    private String tollSerial;
    /**
     * 收费人
     */
    private Long tollBy;
    /**
     * 收费时间
     */
    private LocalDateTime tollTime;
    /**
     * 支付订单id
     */
    private Long remoteOrderId;
    /**
     * 退费订单id
     */
    private Long refundOrderId;
    /**
     * 退费金额
     */
    private BigDecimal refundAmount;
    /**
     * 退费票据号
     */
    private String refundSerial;
    /**
     * 退费人
     */
    private Long refundBy;
    /**
     * 退费时间
     */
    private LocalDateTime refundTime;
    /**
     * 微信端创建人openid
     */
    private String wechatCreatorOpenid;
    /**
     * openid
     */
    private String openid;
}
