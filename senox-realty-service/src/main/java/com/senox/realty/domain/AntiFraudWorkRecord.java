package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-03-28
 **/
@Getter
@Setter
@TableName("r_anti_fraud_work_record")
public class AntiFraudWorkRecord extends TableIdEntity {

    /**
     * 区域名
     */
    private String regionName;

    /**
     * 街道名
     */
    private String streetName;

    /**
     * 详细地址
     */
    private String fullAddress;

    /**
     * 完成人数
     */
    private Integer completedPeople;

    /**
     * 剩余人数
     */
    private Integer remainingPeople;

    /**
     * 总人数
     */
    private Integer totalPeople;

    /**
     * 处理情况描述
     */
    private String description;

    /**
     * 是否配合
     */
    @TableField("is_cooperative")
    private Boolean cooperative;

    /**
     * 配合备注
     */
    private String cooperativeNotes;

    /**
     * 登记人
     */
    private String registrant;

    /**
     * 登记日期
     */
    private LocalDateTime registrationTime;

    /**
     * openid
     */
    private String openid;
}
