package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/25 10:25
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_store_inspection")
public class FirefightingStoreInspection extends TableIdEntity {

    /**
     * 检查单位
     */
    private String inspectAgency;
    /**
     * 经营户id
     */
    private Long enterpriseId;
    /**
     * 商铺
     */
    private String store;
    /**
     * 商铺经营类别
     */
    private String storeBusinessType;
    /**
     * 商铺业主
     */
    private String storeOwner;
    /**
     * 商铺业主联系方式
     */
    private String storeOwnerContact;
    /**
     * 商铺经营者
     */
    private String storeRunner;
    /**
     * 商铺经营者联系方式
     */
    private String storeRunnerContact;
    /**
     * 违规住人
     */
    @TableField("is_store_accommodated")
    private Boolean storeAccommodated;
    /**
     * 居住人数
     */
    private Integer storeAccommodatedCount;
    /**
     * 消防设施配齐
     */
    @TableField("is_firefighting_facilities_satisfied")
    private Boolean firefightingFacilitiesSatisfied;
    /**
     * 砖墙保护
     */
    @TableField("is_firefighting_wall_satisfied")
    private Boolean firefightingWallSatisfied;
    /**
     * 楼梯保护
     */
    @TableField("is_firefighting_stairs_satisfied")
    private Boolean firefightingStairsSatisfied;
    /**
     * 配电线路保护
     */
    @TableField("is_firefighting_lines_satisfied")
    private Boolean firefightingLinesSatisfied;
    /**
     * 火灾报警器配齐
     */
    @TableField("is_firefighting_alarm_satisfied")
    private Boolean firefightingAlarmSatisfied;
    /**
     * 安全出口保护
     */
    @TableField("is_firefighting_exit_satisfied")
    private Boolean firefightingExitSatisfied;
    /**
     * 巡检结果
     * @see com.senox.realty.constant.InspectResult
     */
    private Integer inspectResult;
    /**
     * 巡检意见
     */
    private String inspectOpinions;
    /**
     * 整改期限
     */
    private Integer rectificationTimeLimit;
    /**
     * 整改截止日期
     */
    private LocalDate rectificationDeadline;
    /**
     * 巡检日期
     */
    private LocalDate inspectDate;
    /**
     * 被巡检单位负责人签名
     */
    private String inspectedSignature;
    /**
     * 巡查人员
     */
    private String inspector;
    /**
     * 巡查服务人员
     */
    private String inspectorAssistant;
    /**
     * 被巡察场所负责人签名
     */
    private String storeKeymanSignature;
    /**
     * 复查意见
     */
    private String reinspectOpinions;
    /**
     * 复查人员
     */
    private String reinspector;
    /**
     * 复查日期
     */
    private LocalDate reinspectDate;

}
