package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/25 10:28
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_material_item")
public class MaintainMaterialItem extends TableIdEntity {

    @TableId(
            type = IdType.AUTO
    )
    private Long id;
    /**
     * 维修物料id
     */
    private Long materialId;
    /**
     * 物料编码
     */
    private Long materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料成本单价
     */
    private BigDecimal price;
    /**
     * 物料数量
     */
    private Integer quantity;
    /**
     * 物料金额
     */
    private BigDecimal amount;
    /**
     * 备注
     */
    private String remark;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        MaintainMaterialItem materialItem = (MaintainMaterialItem) obj;
        return Objects.equals(materialId, materialItem.getMaterialId())
                && Objects.equals(materialCode, materialItem.getMaterialCode())
                && Objects.equals(price, materialItem.getPrice());
    }

    @Override
    public int hashCode() {
        return Objects.hash(materialId, materialCode, price);
    }
}
