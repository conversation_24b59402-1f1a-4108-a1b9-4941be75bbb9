package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import com.senox.realty.constant.BillStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物业应收账单明细
 * <AUTHOR>
 * @date 2022/11/23 13:50
 */
@Getter
@Setter
@ToString
public class RealtyPayoffItem extends BaseEntity {

    private static final long serialVersionUID = 5136885503216787530L;

    /**
     * 账单id
     */
    private Long billId;
    /**
     * 费项id
     */
    private Long feeId;
    /**
     * 费项名
     */
    private String feeName;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 支付状态
     * @see BillStatus
     */
    private Integer status;
    /**
     * 支付时间
     */
    private LocalDateTime paidTime;
}
