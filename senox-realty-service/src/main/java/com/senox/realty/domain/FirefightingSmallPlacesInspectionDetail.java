package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/29 15:06
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_small_places_inspection_detail")
public class FirefightingSmallPlacesInspectionDetail {

    /**
     * 巡检id
     */
    @TableId(type = IdType.INPUT)
    private Long inspectionId;
    /**
     * 1层用途
     */
    private String floor1Usage;
    /**
     * 2层用途
     */
    private String floor2Usage;
    /**
     * 3层用途
     */
    private String floor3Usage;
    /**
     * 4层用途
     */
    private String floor4Usage;
    /**
     * 5层用途
     */
    private String floor5Usage;
    /**
     * 6层用途
     */
    private String floor6Usage;
    /**
     * 其他层用途
     */
    private String floorOthersUsage;
    /**
     * 经营者1层用途
     */
    private String floor1RunnerUsage;
    /**
     * 经营者2层用途
     */
    private String floor2RunnerUsage;
    /**
     * 经营者3层用途
     */
    private String floor3RunnerUsage;
    /**
     * 经营者4层用途
     */
    private String floor4RunnerUsage;
    /**
     * 经营者5层用途
     */
    private String floor5RunnerUsage;
    /**
     * 经营者6层用途
     */
    private String floor6RunnerUsage;
    /**
     * 经营者其他层用途
     */
    private String floorOthersRunnerUsage;
    /**
     * 备注
     */
    private String remark;
    /**
     * 检查意见
     */
    private String inspectOpinion;
    /**
     * 检查意见模板
     */
    private String inspectOpinionCode;
    /**
     * 检查意见模板版本
     */
    private Integer inspectOpinionVersion;
    /**
     * 复查意见
     */
    private String reinspectOpinion;
    /**
     * 复查意见模板
     */
    private String reinspectOpinionCode;
    /**
     * 检查意见模板版本
     */
    private Integer reinspectOpinionVersion;
    /**
     * 小商铺整改意见
     */
    private String storeRectification;
    /**
     * 小商铺整改意见模板
     */
    private String storeRectificationCode;
    /**
     * 小商铺整改意见模板版本
     */
    private Integer storeRectificationVersion;
    /**
     * 小作坊整改意见
     */
    private String workshopRectification;
    /**
     * 小作坊整改意见模板
     */
    private String workshopRectificationCode;
    /**
     * 小作坊整改意见模板版本
     */
    private String workshopRectificationVersion;
    /**
     * 出租屋整改意见
     */
    private String rentalRectification;
    /**
     * 出租屋整改意见模板
     */
    private String rentalRectificationCode;
    /**
     * 出租屋整改意见模板版本
     */
    private Integer rentalRectificationVersion;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}
