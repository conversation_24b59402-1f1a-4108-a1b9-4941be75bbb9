package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/7/24 14:41
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
@TableName("r_advertising_media")
public class AdvertisingMedia extends TableIdEntity {

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 广告媒体链接
     */
    private String mediaUrl;

    public AdvertisingMedia() {
    }

    public AdvertisingMedia(Long contractId, String mediaUrl) {
        this.contractId = contractId;
        this.mediaUrl = mediaUrl;
    }
}
