package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/12/9 15:02
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_order")
public class MaintainOrder extends TableIdEntity {

    /**
     * 维修订单号
     */
    private String orderNo;
    /**
     * 维修类型
     * @see com.senox.realty.constant.MaintainType
     */
    private Integer maintainType;
    /**
     * 客户名
     */
    private String customerName;
    /**
     * 联系方式
     */
    private String contact;
    /**
     * 区域名
     */
    private String regionName;
    /**
     * 街道名
     */
    private String streetName;
    /**
     * 地址
     */
    private String address;
    /**
     * 问题描述
     */
    private String problem;
    /**
     * 派工单
     */
    private String jobNo;
    /**
     * 处理人
     */
    private String handlerName;
    /**
     * 处理人部门id
     */
    private Long handlerDeptId;
    /**
     * 处理人部门
     */
    private String handlerDeptName;
    /**
     * 管理所属部门id
     */
    private Long managementDeptId;
    /**
     * 管理所属部门
     */
    private String managementDeptName;
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 用户确认
     */
    private Boolean userConfirm;
    /**
     * 评价星级
     */
    private Integer evaluateRating;
    /**
     * 评价时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime evaluateTime;
    /**
     * 评价人openid
     */
    private String evaluateOpenid;
    /**
     * 评价内容
     */
    private String evaluate;
    /**
     * 微信单创建用户
     */
    private String createOpenid;


}
