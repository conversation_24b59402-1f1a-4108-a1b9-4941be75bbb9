package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合同
 * <AUTHOR>
 * @date 2021/2/8 14:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Contract extends BaseEntity {

    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 手工合同编号
     */
    private String orderNo;
    /**
     * 合同类型
     */
    private Integer type;

    /**
     * 合同类别
     */
    private Integer category;

    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 物业名
     */
    private String realtyName;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 客户名
     */
    private String customerName;
    /**
     * 签约日期
     */
    private LocalDate signDate;
    /**
     * 开始日期
     */
    private LocalDate startDate;
    /**
     * 结束日期
     */
    private LocalDate endDate;
    /**
     * 合同状态
     */
    private Integer status;
    /**
     * 停用人id
     */
    private Long stopBy;
    /**
     * 停用时间
     */
    private LocalDateTime stopTime;
    /**
     * IC卡号
     */
    private String icCardNo;
    /**
     * 临租
     */
    private Boolean temporaryRent;
    /**
     * 转名
     */
    private Boolean renamed;

    /**
     * 营业执照名称
     */
    private String businessLicenseName;

    /**
     * 税号
     */
    private String taxSerial;
}
