package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 费项
 * <AUTHOR>
 * @Date 2020/12/15 11:44
 */
public class Fee extends BaseEntity {

    /**
     * 名称
     */
    private String name;
    /**
     * 别名
     */
    private String alias;
    /**
     * 费项类别
     */
    private Integer category;
    /**
     * 金额
     */
    private BigDecimal amount;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Fee fee = (Fee) o;
        return Objects.equals(name, fee.name)
                && Objects.equals(alias, fee.alias)
                && Objects.equals(category, fee.category);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, alias, category);
    }

    @Override
    public String toString() {
        return "Fee{"
                + "name='" + name + '\''
                + ", alias='" + alias + '\''
                + ", category=" + category
                + '}';
    }
}
