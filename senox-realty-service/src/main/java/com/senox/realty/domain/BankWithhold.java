package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 银行托收报盘记录
 * <AUTHOR>
 * @date 2022/1/14 15:07
 */
public class BankWithhold extends BaseEntity {

    private static final long serialVersionUID = 8564336802701463720L;

    /**
     * 账单年份
     */
    private Integer billYear;
    /**
     * 账单月份
     */
    private Integer billMonth;
    /**
     * 账单年月日
     */
    private LocalDate billDate;
    /**
     * 报盘
     */
    private Boolean offer;
    /**
     * 报盘时间
     */
    private LocalDateTime offerTime;
    /**
     * 回盘
     */
    private Boolean back;
    /**
     * 回盘时间
     */
    private LocalDateTime backTime;


    public Integer getBillYear() {
        return billYear;
    }

    public void setBillYear(Integer billYear) {
        this.billYear = billYear;
    }

    public Integer getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(Integer billMonth) {
        this.billMonth = billMonth;
    }

    public LocalDate getBillDate() {
        return billDate;
    }

    public void setBillDate(LocalDate billDate) {
        this.billDate = billDate;
    }

    public Boolean getOffer() {
        return offer;
    }

    public void setOffer(Boolean offer) {
        this.offer = offer;
    }

    public LocalDateTime getOfferTime() {
        return offerTime;
    }

    public void setOfferTime(LocalDateTime offerTime) {
        this.offerTime = offerTime;
    }

    public Boolean getBack() {
        return back;
    }

    public void setBack(Boolean back) {
        this.back = back;
    }

    public LocalDateTime getBackTime() {
        return backTime;
    }

    public void setBackTime(LocalDateTime backTime) {
        this.backTime = backTime;
    }
}
