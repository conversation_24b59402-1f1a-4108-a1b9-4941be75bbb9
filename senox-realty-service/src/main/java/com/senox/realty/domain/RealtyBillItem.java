package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import com.senox.realty.constant.BillStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物业应付账单明细
 * <AUTHOR>
 * @date 2021/6/8 11:34
 */
@Getter
@Setter
@ToString
public class RealtyBillItem extends BaseEntity {

    private static final long serialVersionUID = 8384587519878473061L;

    /**
     * 账单id
     */
    private Long billId;
    /**
     * 费项id
     */
    private Long feeId;
    /**
     * 费项名
     */
    private String feeName;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 保留字段1
     */
    private String attr1;
    /**
     * 保留字段2
     */
    private String attr2;
    /**
     * 支付状态
     * @see BillStatus
     */
    private Integer status;
    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    /**
     * 发票单据编号
     */
    private String receiptSerialNo;

    /**
     * 开票状态(0:待开票;1:开票中;2:已开具)
     */
    private Integer receiptStatus;

}
