package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.realty.constant.InspectionType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/12 15:00
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_inspect_realty")
public class FirefightingInspectRealty {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 巡检id
     */
    private Long inspectId;
    /**
     * 巡检类型
     */
    private InspectionType inspectType;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    public FirefightingInspectRealty() {
    }

    public FirefightingInspectRealty(Long inspectId, InspectionType inspectType, String realtySerial) {
        this.inspectId = inspectId;
        this.inspectType = inspectType;
        this.realtySerial = realtySerial;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FirefightingInspectRealty that = (FirefightingInspectRealty) o;
        return Objects.equals(inspectId, that.inspectId)
                && inspectType == that.inspectType
                && Objects.equals(realtySerial, that.realtySerial);
    }

    @Override
    public int hashCode() {
        return Objects.hash(inspectId, inspectType, realtySerial);
    }
}
