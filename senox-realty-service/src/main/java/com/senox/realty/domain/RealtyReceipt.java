package com.senox.realty.domain;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023-3-23
 */
@Getter
@Setter
public class RealtyReceipt {
    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 抬头类型
     */
    private Integer headerCategory;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请状态
     */
    private Integer applyStatus;

    /**
     * 禁用
     */
    private Boolean isDisabled;

    /**
     * 申请人
     */
    private String applyMan;

    /**
     * 申请人名称
     */
    private String applyUserName;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 审核人
     */
    private Long auditMan;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 下发
     */
    private Boolean send;
}
