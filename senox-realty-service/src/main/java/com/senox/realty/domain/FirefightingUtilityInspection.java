package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/13 16:12
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_utility_inspection")
public class FirefightingUtilityInspection extends TableIdEntity {

    /**
     * 公共消防设施id
     */
    private Long utilityId;
    /**
     * 巡检日期
     */
    private LocalDate inspectDate;
    /**
     * 消防栓
     */
    private String fireHydrant;
    /**
     * 消防水带
     */
    private String fireHose;
    /**
     * 灭火器
     */
    private String fireExtinguisher;
    /**
     * 水枪
     */
    private String waterGun;
    /**
     * 阀门
     */
    private String valve;
    /**
     * 消防通道
     */
    private String fireExits;
    /**
     * 灭火器数量
     */
    private Integer fireExtinguisherNum;
    /**
     * 过期灭火器数量
     */
    private Integer expireFireExtinguisher;
    /**
     * 备注
     */
    private String remark;
}
