package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.common.utils.DecimalUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/7/19 13:50
 */
@Getter
@Setter
@ToString
@TableName("r_advertising_contract")
public class AdvertisingContract extends TableIdEntity {

    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 广告位id
     */
    private Long spaceId;
    /**
     * 客户名
     */
    private String customerName;
    /**
     * 客户联系人
     */
    private String customerUser;
    /**
     * 客户联系方式
     */
    private String customerContact;
    /**
     * 赠送月份数
     */
    private Integer presentMonths;
    /**
     * 租赁月份数
     */
    private Integer rentMonths;
    /**
     * 签约日期
     */
    private LocalDate signDate;
    /**
     * 开始日期
     */
    private LocalDate startDate;
    /**
     * 结束日期
     */
    private LocalDate endDate;
    /**
     * 合同金额
     */
    private BigDecimal amount;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 合同状态
     * @see com.senox.realty.constant.ContractStatus
     */
    private Integer status;
    /**
     * 停用人
     */
    private Long stopBy;
    /**
     * 停用时间
     */
    private LocalDateTime stopTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 已支付
     */
    @TableField("is_paid")
    private Boolean paid;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AdvertisingContract contract = (AdvertisingContract) o;
        return Objects.equals(contractNo, contract.contractNo)
                && Objects.equals(spaceId, contract.spaceId)
                && Objects.equals(customerName, contract.customerName)
                && Objects.equals(customerUser, contract.customerUser)
                && Objects.equals(customerContact, contract.customerContact)
                && Objects.equals(presentMonths, contract.presentMonths)
                && Objects.equals(rentMonths, contract.rentMonths)
                && Objects.equals(signDate, contract.signDate)
                && Objects.equals(startDate, contract.startDate)
                && Objects.equals(endDate, contract.endDate)
                && DecimalUtils.equals(amount, contract.amount)
                && Objects.equals(status, contract.status)
                && Objects.equals(paid, contract.paid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contractNo, spaceId, customerName, customerUser, customerContact,
                presentMonths, rentMonths, signDate, startDate, endDate, amount, status, paid);
    }
}
