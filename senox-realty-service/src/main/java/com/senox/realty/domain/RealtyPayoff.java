package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物业应付账单
 * <AUTHOR>
 * @date 2022/11/22 14:33
 */
@Getter
@Setter
@ToString
public class RealtyPayoff extends BaseEntity {

    private static final long serialVersionUID = 1831672025445915406L;

    /**
     * 账单年月
     */
    private String billYearMonth;
    /**
     * 账单年份
     */
    private Integer billYear;
    /**
     * 账单月份
     */
    private Integer billMonth;
    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 订单状态
     * @see com.senox.realty.constant.BillStatus
     */
    private Integer status;
    /**
     * 支付时间
     */
    private LocalDateTime paidTime;
    /**
     * 备注
     */
    private String remark;
}
