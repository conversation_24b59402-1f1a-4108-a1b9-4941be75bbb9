package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.realty.constant.SecurityEvent;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 安保日志
 * <AUTHOR>
 * @date 2025/3/26 15:07
 */
@Data
@TableName("d_security_journal")
public class SecurityJournal extends TableIdEntity {

    /**
     * 日期
     */
    private LocalDate journalDate;
    /**
     * 事件类型
     */
    private SecurityEvent eventType;
    /**
     * 发生时间
     */
    private LocalDateTime eventTime;
    /**
     * 设备
     */
    private String equipment;
    /**
     * 简述
     */
    private String description;
    /**
     * 费用
     */
    private BigDecimal expense;
    /**
     * 处理结果
     */
    private String processingResult;
    /**
     * 复查结果
     */
    private String reprocessingResult;
    /**
     * 登记人
     */
    private String recorder;

}
