package com.senox.realty.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/17 15:29
 */
@Getter
@Setter
@ToString
public class FirefightingInspectTaskRealty {

    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 子任务id
     */
    private Long taskItemId;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    public FirefightingInspectTaskRealty() {
    }

    public FirefightingInspectTaskRealty(Long taskId, Long taskItemId, String realtySerial) {
        this.taskId = taskId;
        this.taskItemId = taskItemId;
        this.realtySerial = realtySerial;
    }
}
