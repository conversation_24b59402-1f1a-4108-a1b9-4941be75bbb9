package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import com.senox.common.utils.DecimalUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 水电价类别
 * <AUTHOR>
 * @date 2021/3/9 13:56
 */
public class WaterElectricPriceType extends BaseEntity {

    /**
     * 类型名
     */
    private String name;
    /**
     * 类别
     */
    private Integer type;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 备注
     */
    private String remark;
    /**
     * 禁用
     */
    private Boolean disabled;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public Boolean getDisabled() {
        return disabled;
    }

    @Override
    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WaterElectricPriceType that = (WaterElectricPriceType) o;
        return Objects.equals(name, that.name)
                && Objects.equals(type, that.type)
                && DecimalUtils.equals(price, that.price)
                && Objects.equals(remark, that.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, type, price, remark);
    }

    @Override
    public String toString() {
        return "WaterElectricPriceType{"
                + "name='" + name + '\''
                + ", type=" + type
                + ", price=" + price
                + ", remark='" + remark + '\''
                + '}';
    }

}
