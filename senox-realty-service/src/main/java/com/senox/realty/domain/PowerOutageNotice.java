package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-05-08
 **/
@Getter
@Setter
@TableName("r_power_outage_notice")
public class PowerOutageNotice extends TableIdEntity {

    /**
     * 区域id
     */
    private Long regionId;

    /**
     * 区域名
     */
    private String regionName;

    /**
     * 街道id
     */
    private Long streetId;

    /**
     * 街道名
     */
    private String streetName;

    /**
     * 档口地址
     */
    private String stallAddress;

    /**
     * 停电原因
     */
    private String poserOutageReason;

    /**
     * 整改前时间
     */
    private LocalDateTime preTime;

    /**
     * 整改前检查人签名
     */
    private String preInspectorSign;

    /**
     * 整改前档口负责人签名
     */
    private String preOwnerSign;

    /**
     * 整改后时间
     */
    private LocalDateTime postTime;

    /**
     * 整改后检查人签名
     */
    private String postInspectorSign;

    /**
     * 整改后档口负责人签名
     */
    private String postOwnerSign;

    /**
     * 复查状态
     */
    private Boolean reviewStatus;
}
