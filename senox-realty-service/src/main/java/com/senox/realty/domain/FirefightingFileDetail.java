package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/19 9:44
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_file_detail")
public class FirefightingFileDetail {

    /**
     * 档案id
     */
    @TableId(type = IdType.INPUT)
    private Long fileId;
    /**
     * 场所说明
     */
    private String venueDescription;
    /**
     * 情况跟踪
     */
    private String followUp;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}
