package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/22 14:32
 */
@Getter
@Setter
@ToString
@TableName("r_energy_profit_item")
public class EnergyProfitItem extends TableIdEntity {

    /**
     * 损益id
     */
    private Long profitId;
    /**
     * 年份
     */
    private Integer billYear;
    /**
     * 月份
     */
    private Integer billMonth;
    /**
     * 损益数据来源
     */
    private Integer source;
    /**
     * 能源消耗单位key
     */
    private String unitKey;
    /**
     * 能源消耗单位名
     */
    private String unitName;
    /**
     * 抄表时间起
     */
    @TableField(exist = false)
    private LocalDate lastRecordDate;
    /**
     * 抄表时间止
     */
    @TableField(exist = false)
    private LocalDate recordDate;
    /**
     * 能源消耗量
     */
    private Integer cost = 0;

    public EnergyProfitItem() {
    }

    public EnergyProfitItem(String unitKey, String unitName) {
        this.unitKey = unitKey;
        this.unitName = unitName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EnergyProfitItem that = (EnergyProfitItem) o;
        return Objects.equals(profitId, that.profitId)
                && Objects.equals(source, that.source)
                && Objects.equals(unitKey, that.unitKey)
                && Objects.equals(unitName, that.unitName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(profitId, source, unitKey, unitName);
    }
}
