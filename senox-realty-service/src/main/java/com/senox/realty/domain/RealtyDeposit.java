package com.senox.realty.domain;

import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 物业押金
 * <AUTHOR>
 * @date 2021/11/2 17:22
 */
@Getter
@Setter
@ToString
public class RealtyDeposit extends BaseEntity {

    private static final long serialVersionUID = -2150926825626814859L;

    /**
     * 合同id
     */
    private Long contractId;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 客户名
     */
    private String customerName;
    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 费项id
     */
    private Long feeId;
    /**
     * 费项名
     */
    private String feeName;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     * @see com.senox.realty.constant.BillStatus
     */
    private Integer status;
    /**
     * 操作日期
     */
    private LocalDate operateDate;
    /**
     * 收费票据号
     */
    private String tollSerial;
    /**
     * 收费人
     */
    private Long tollBy;
    /**
     * 收费时间
     */
    private LocalDateTime tollTime;
    /**
     * 退费金额
     */
    private BigDecimal refundAmount;
    /**
     * 退费票据号
     */
    private String refundSerial;
    /**
     * 退费人
     */
    private Long refundBy;
    /**
     * 退费时间
     */
    private LocalDateTime refundTime;

}
