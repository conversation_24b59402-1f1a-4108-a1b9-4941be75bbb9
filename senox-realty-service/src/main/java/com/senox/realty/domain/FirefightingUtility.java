package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/13 9:15
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_utility")
public class FirefightingUtility extends TableIdEntity {

    /**
     * 区域id
     */
    private Long regionId;
    /**
     * 区域名
     */
    private String regionName;
    /**
     * 街道id
     */
    private Long streetId;
    /**
     * 街道名
     */
    private String streetName;
    /**
     * 位置
     */
    private String location;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FirefightingUtility utility = (FirefightingUtility) o;
        return Objects.equals(regionName, utility.regionName)
                && Objects.equals(streetName, utility.streetName)
                && Objects.equals(location, utility.location);
    }

    @Override
    public int hashCode() {
        return Objects.hash(regionName, streetName, location);
    }
}
