package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/7/15 10:08
 */
@TableName("r_maintain_order_day_report")
@Getter
@Setter
public class MaintainOrderDayReport extends TableIdEntity {

    /**
     * 报表日期
     */
    private LocalDate reportDate;

    /**
     * 管理所属部门id
     */
    private Long managementDeptId;
    /**
     * 管理所属部门
     */
    private String managementDeptName;

    /**
     * 新增单数
     */
    private Integer addSingularNumbers;

    /**
     * 完成单数
     */
    private Integer completeSingularNumbers;
}
