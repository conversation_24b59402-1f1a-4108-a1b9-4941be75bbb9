package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-03-27
 **/
@Getter
@Setter
@TableName("r_anti_fraud_work_record_media")
public class AntiFraudWorkRecordMedia extends TableIdEntity {

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件地址
     */
    private String fileUrl;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AntiFraudWorkRecordMedia that = (AntiFraudWorkRecordMedia) o;
        return Objects.equals(productId, that.productId)
                && Objects.equals(source, that.source)
                && Objects.equals(fileName, that.fileName)
                && Objects.equals(fileUrl, that.fileUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(productId, source, fileName, fileUrl);
    }
}
