package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/3 14:08
 */
@Getter
@Setter
@ToString
@TableName("r_advertising_space_position")
public class AdvertisingSpacePosition extends TableIdEntity {

    /**
     * 广告位id
     */
    private Long spaceId;
    /**
     * 区域id
     */
    private Long regionId;
    /**
     * 区域名
     */
    private String regionName;
    /**
     * 街道id
     */
    private Long streetId;
    /**
     * 街道名
     */
    private String streetName;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AdvertisingSpacePosition that = (AdvertisingSpacePosition) o;
        return Objects.equals(spaceId, that.spaceId)
                && Objects.equals(regionId, that.regionId)
                && Objects.equals(streetId, that.streetId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(spaceId, regionId, streetId);
    }
}
