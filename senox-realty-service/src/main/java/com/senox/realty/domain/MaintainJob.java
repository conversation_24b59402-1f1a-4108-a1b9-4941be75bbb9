package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.OperateEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/4/3 8:59
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_job")
public class MaintainJob extends OperateEntity {

    @TableId(
            type = IdType.AUTO
    )
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 派工单号
     */
    private String jobNo;
    /**
     * 维修类型
     * @see com.senox.realty.constant.MaintainType
     */
    private Integer maintainType;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 派工类别 0：处理，1：审核
     */
    private Integer dispatchType;
    /**
     * 是否需要多个完成
     */
    private Boolean multipleComplete;
}
