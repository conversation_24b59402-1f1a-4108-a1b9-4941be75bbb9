package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 广告应付账单
 * <AUTHOR>
 * @date 2023/8/2 11:53
 */
@Getter
@Setter
@ToString
@TableName("r_advertising_payoff")
public class AdvertisingPayoff extends TableIdEntity {

    /**
     * 账单年
     */
    private Integer billYear;
    /**
     * 账单月
     */
    private Integer billMonth;
    /**
     * 广告位id
     */
    private Long spaceId;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 客户名
     */
    private String customerName;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 物业名称
     */
    private String realtyName;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 订单状态
     * @see com.senox.realty.constant.BillStatus
     */
    private Integer status;
    /**
     * 收费员
     */
    private Long tollManId;
    /**
     * 支付方式
     */
    private Integer payway;
    /**
     * 支付时间
     */
    private LocalDateTime paidTime;
    /**
     * 备注
     */
    private String remark;
}
