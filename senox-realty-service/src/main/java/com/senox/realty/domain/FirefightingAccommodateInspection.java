package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 违规住人整改登记表
 * <AUTHOR>
 * @date 2024/5/8 15:06
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_accommodate_inspection")
public class FirefightingAccommodateInspection extends TableIdEntity {

    /**
     * 地址
     */
    private String address;
    /**
     * 经营类别
     */
    private String businessType;
    /**
     * 经营户id
     */
    private Long enterpriseId;
    /**
     * 违规住人情况
     */
    private Boolean accommodated;
    /**
     * 居住人数
     */
    private Integer accommodatedCount;
    /**
     * 经营者
     */
    private String runner;
    /**
     * 联系方式
     */
    private String runnerContact;
    /**
     * 无物理分割，无甲级防火门，无安装防火门、闭门器的
     */
    @TableField("is_fire_doors_disqualified")
    private Boolean fireDoorsDisqualified;
    /**
     * 无逃生窗口，无逃生梯的
     */
    @TableField("is_evacuration_disqualified")
    private Boolean evacurationDisqualified;
    /**
     * 无应急灯、无消防器材、有消防器材但过期没有更换的
     */
    @TableField("is_fire_facilities_disqualified")
    private Boolean fireFacilitiesDisqualified;
    /**
     * 无滑防通道的
     */
    @TableField("is_fire_exit_disqualified")
    private Boolean fireExitDisqualified;
    /**
     * 电线乱接乱拉的
     */
    @TableField("is_electric_lines_disqualified")
    private Boolean electricLinesDisqualified;
    /**
     * 无烟感检测器
     */
    @TableField("is_smoke_detector_disqualified")
    private Boolean smokeDetectorDisqualified;
    /**
     * 无自动喷淋的
     */
    @TableField("is_sprinkler_disqualified")
    private Boolean sprinklerDisqualified;
    /**
     * 巡检意见
     */
    private String inspectOpinions;
    /**
     * 巡检意见模板
     */
    private String inspectOpinionCode;
    /**
     * 巡检意见版本
     */
    private Integer inspectOpinionVersion;
    /**
     * 巡检结果
     */
    private Integer inspectResult;
    /**
     * 巡查人员
     */
    private String inspector;
    /**
     * 巡检日期
     */
    private LocalDate inspectDate;
    /**
     * 巡检单位
     */
    private String inspectUnit;
    /**
     * 被巡检场所负责人签名
     */
    private String inspectedSignature;
    /**
     * 整改截止日期
     */
    private LocalDate rectificationDeadline;
    /**
     * 整改意见
     */
    private String rectification;
    /**
     * 整改意见模板
     */
    private String rectificationCode;
    /**
     * 整改意见版本
     */
    private Integer rectificationVersion;
    /**
     * 整改监督人
     */
    private String rectificationSupervisor;
    /**
     * 被整改负责人签名
     */
    private String rectificationSignature;
    /**
     * 复查意见
     */
    private String reinspectOpinions;
    /**
     * 复查人
     */
    private String reinspector;
    /**
     * 整改日期
     */
    private LocalDate reinspectDate;
}
