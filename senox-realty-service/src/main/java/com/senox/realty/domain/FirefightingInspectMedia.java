package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.realty.constant.InspectionType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/6 10:50
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_inspect_media")
public class FirefightingInspectMedia {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 巡检id
     */
    private Long inspectId;
    /**
     * 巡检类型
     */
    private InspectionType inspectType;
    /**
     * 巡检次数 1 初检；2 复检
     */
    private Integer inspectTimes;
    /**
     * 媒体url
     */
    private String mediaUrl;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;


    public FirefightingInspectMedia() {
    }

    public FirefightingInspectMedia(Long inspectId, InspectionType inspectType, String mediaUrl) {
        this(inspectId, inspectType, 1, mediaUrl);
    }

    public FirefightingInspectMedia(Long inspectId, InspectionType inspectType, Integer inspectTimes, String mediaUrl) {
        this.inspectId = inspectId;
        this.inspectType = inspectType;
        this.inspectTimes = inspectTimes;
        this.mediaUrl = mediaUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FirefightingInspectMedia that = (FirefightingInspectMedia) o;
        return Objects.equals(inspectId, that.inspectId)
                && inspectType == that.inspectType
                && Objects.equals(inspectTimes, that.inspectTimes)
                && Objects.equals(mediaUrl, that.mediaUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(inspectId, inspectType, inspectTimes, mediaUrl);
    }
}
