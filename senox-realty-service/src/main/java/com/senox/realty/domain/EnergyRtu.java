package com.senox.realty.domain;

import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 
 * @date 2024-11-11
 **/
@Getter
@Setter
public class EnergyRtu extends TableIdEntity {

    /**
     * 终端编码
     */
    private String code;

    /**
     * 终端名称
     */
    private String name;

    /**
     * 状态：0.离线1.在线
     */
    private Integer status;

    /**
     * 最后在线时间(yyyy-MM-dd HH:mm:ss)
     */
    private LocalDateTime onLineTime;
    /**
     * 离线时间(yyyy-MM-dd HH:mm:ss)
     */
    private LocalDateTime offLineTime;
}
