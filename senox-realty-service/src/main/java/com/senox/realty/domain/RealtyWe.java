package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022-8-12
 */
@Getter
@Setter
@ToString
@TableName("r_realty_we")
public class RealtyWe extends TableIdEntity {

    /**
     * 年
     */
    private Integer billYear;
    /**
     * 月
     */
    private Integer billMonth;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 上次水读数
     */
    private Long lastWaterReadings;
    /**
     * 本次水读数
     */
    private Long waterReadings;
    /**
     * 水消费单位
     */
    private Integer waterConsumeUnit;
    /**
     * 水低消
     */
    private Integer waterBase;
    /**
     * 上次电读数
     */
    private Long lastElectricReadings;
    /**
     * 本次电读数
     */
    private Long electricReadings;
    /**
     * 电消费单位
     */
    private Integer electricConsumeUnit;
    /**
     * 电低消
     */
    private Integer electricBase;
    /**
     * 上次抄表日期
     */
    private LocalDate lastRecordDate;
    /**
     * 抄表日期
     */
    private LocalDate recordDate;
    /**
     * 水电账单id
     */
    private Long weBillId = 0L;
    /**
     * 读数类型(1:月缴费账单读数)
     */
    private Integer type;
    /**
     * 是否人工产生
     */
    @TableField("is_man_made")
    private Boolean manMade = false;
}
