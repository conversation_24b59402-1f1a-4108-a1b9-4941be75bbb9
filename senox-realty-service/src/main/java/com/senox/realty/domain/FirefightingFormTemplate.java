package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 表单模板配置
 * <AUTHOR>
 * @date 2024/4/28 14:14
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_form_template")
public class FirefightingFormTemplate extends TableIdEntity {

    /**
     * 表单
     */
    private String form;
    /**
     * 属性
     */
    private String formAttr;
    /**
     * 模板
     */
    private String templateCode;
    /**
     * 模板版本
     */
    private Integer templateVersion;
}
