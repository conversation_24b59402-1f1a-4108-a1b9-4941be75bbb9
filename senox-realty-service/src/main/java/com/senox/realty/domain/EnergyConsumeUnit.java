package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.common.constant.device.EnergyType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/30 11:21
 */
@Getter
@Setter
@ToString
@TableName("r_energy_consume_unit")
public class EnergyConsumeUnit extends TableIdEntity {

    /**
     * 能源消费单元
     */
    private String unit;
    /**
     * 能源类别
     * @see EnergyType
     */
    private Integer type;
    /**
     * 能源消费单位名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EnergyConsumeUnit unit1 = (EnergyConsumeUnit) o;
        return Objects.equals(unit, unit1.unit) && Objects.equals(type, unit1.type) && Objects.equals(name, unit1.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(unit, type, name);
    }
}
