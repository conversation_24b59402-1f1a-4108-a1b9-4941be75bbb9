package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/22 14:36
 */
@Getter
@Setter
@ToString
@TableName("r_energy_profit")
public class EnergyProfit extends TableIdEntity {

    /**
     * 账单年月
     */
    private String billTime;
    /**
     * 能源类别，1水；2电
     */
    private Integer energyType;
    /**
     * 记录抄表时间起
     */
    private LocalDate recordedStartDate;
    /**
     * 记录抄表时间止
     */
    private LocalDate recordedEndDate;
    /**
     * 记录间隔天数
     */
    private Integer recordedDays;
    /**
     * 记录能源实耗
     */
    private Integer recordedCost;
    /**
     * 结算抄表时间起
     */
    private LocalDate balanceStartDate;
    /**
     * 结算抄表时间止
     */
    private LocalDate balanceEndDate;
    /**
     * 结算间隔天数
     */
    private Integer balanceDays;
    /**
     * 结算能源实耗
     */
    private Integer balanceCost;
    /**
     * 抄表差异天数
     */
    private Integer diffDays;
    /**
     * 抄表差异耗费
     */
    private Integer diffCost;
    /**
     * 能源损益量
     */
    private Integer profitsCost;
    /**
     * 能源损益率
     */
    private BigDecimal profitsRate;
}
