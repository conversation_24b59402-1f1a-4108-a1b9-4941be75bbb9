package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 广告应收账单
 * <AUTHOR>
 * @date 2023/7/25 15:45
 */
@Getter
@Setter
@ToString
@TableName("r_advertising_bill")
public class AdvertisingBill extends TableIdEntity {

    /**
     * 账单年份
     */
    private Integer billYear;
    /**
     * 账单月份
     */
    private Integer billMonth;
    /**
     * 广告位id
     */
    private Long spaceId;
    /**
     * 广告合同号
     */
    private String contractNo;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 实收金额
     */
    private BigDecimal paidAmount;
    /**
     * 支付时间
     */
    private LocalDateTime paidTime;
    /**
     * 缴费订单id
     */
    private Long remoteOrderId;
    /**
     * 账单状态
     */
    private Integer status;
    /**
     * 收费员id
     */
    private Long tollManId;
    /**
     * 收费票据号
     */
    private String tollSerial;
    /**
     * 备注
     */
    private String remark;
    /**
     * 开票
     */
    @TableField("is_receipt")
    private Boolean receipt;
    /**
     * 开票抬头
     */
    private String receiptTitle;
    /**
     * 开票时间
     */
    private LocalDateTime receiptTime;
}
