package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-8-12
 */
@Getter
@Setter
@ToString
@TableName("r_realty_alias")
public class RealtyAlias extends TableIdEntity {

    /**
     * 物业id
     */
    private Long realtyId;
    /**
     * 档位编号
     */
    private String serialNo;
    /**
     * 档位名称
     */
    private String name;
    /**
     * 水费读数
     */
    private Long waterReadings;
    /**
     * 电费读数
     */
    private Long electricReadings;


    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        RealtyAlias alias = (RealtyAlias) obj;
        return Objects.equals(realtyId, alias.realtyId)
                && Objects.equals(serialNo, alias.serialNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(realtyId, serialNo);
    }
}
