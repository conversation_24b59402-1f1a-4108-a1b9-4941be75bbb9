package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/13 8:46
 */
@Getter
@Setter
@ToString
@TableName("r_maintain_charge_item")
public class MaintainChargeItem extends TableIdEntity {

    @TableId(
            type = IdType.AUTO
    )
    private Long id;
    /**
     * 物维收费id
     */
    private Long chargeId;
    /**
     * 费用类别
     */
    private Long feeId;
    /**
     * 费用名
     */
    private String feeTitle;
    /**
     * 费项编码
     */
    private Long feeItemCode;
    /**
     * 费项名称
     */
    private String feeItemName;
    /**
     * 物料单价
     */
    private BigDecimal price;
    /**
     * 物料数量
     */
    private Integer quantity;
    /**
     * 物理金额
     */
    private BigDecimal amount;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        MaintainChargeItem charge = (MaintainChargeItem) obj;
        return Objects.equals(chargeId, charge.getChargeId())
                && Objects.equals(feeId, charge.getFeeId())
                && Objects.equals(feeItemCode, charge.getFeeItemCode())
                && Objects.equals(price, charge.getPrice());
    }

    @Override
    public int hashCode() {
        return Objects.hash(chargeId, feeId, feeItemCode, price);
    }

}
