package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/19 9:18
 */
@Getter
@Setter
@ToString
@TableName("r_firefighting_file")
public class FirefightingFile extends TableIdEntity {

    /**
     * 店铺名
     */
    private String store;
    /**
     * 就能够瀛湖id
     */
    private Long enterpriseId;
    /**
     * 店铺负责人
     */
    private String storeKeyman;
    /**
     * 店铺联系电话
     */
    private String storeContact;
    /**
     * 店铺地址
     */
    private String storeAddress;
    /**
     * 店铺建筑面积
     */
    private BigDecimal buildingArea;
    /**
     * 店铺出租面积
     */
    private BigDecimal rentingArea;
    /**
     * 店铺出租层数
     */
    private Integer rentingFloor;
    /**
     * 店铺生产性质
     */
    private String productType;
    /**
     * 工商营业执照办理情况
     */
    @TableField("is_business_license_issued")
    private Boolean businessLicenseIssued;
    /**
     * 住人情况
     */
    private String accommodated;
    /**
     * 明火煮食
     */
    private String flameCooking;
    /**
     * 热水器
     */
    private String heaterEquiped;
    /**
     * 灭火器
     */
    private Integer fireExtinguisher;
    /**
     * 火警报警器
     */
    @TableField("is_fire_alarms_disposed")
    private Boolean fireAlarmsDisposed;
    /**
     * 消防卷盘
     */
    @TableField("is_fire_reels_disposed")
    private Boolean fireReelsDisposed;
    /**
     * 简易喷淋
     */
    @TableField("is_simple_sprinklers_disposed")
    private Boolean simpleSprinklersDisposed;
    /**
     * 应急灯
     */
    @TableField("is_emergency_lights_disposed")
    private Boolean emergencyLightsDisposed;
    /**
     * 疏散楼梯
     */
    private String evacurationStairs;
    /**
     * 房间分隔
     */
    @TableField("is_room_separated")
    private Boolean roomSeparated;
    /**
     * 逃生出口
     */
    @TableField("is_escape_hatch_disposed")
    private Boolean escapeHatchDisposed;
    /**
     * 配电线路
     */
    private String distributionLines;
    /**
     * 其他消防环境
     */
    private String otherSurroundings;
    /**
     * 逃生梯
     */
    private String emergencyStairs;
    /**
     * 过期灭火器数量
     */
    private Integer expireFireExtinguisher;
    /**
     * 煤气管
     */
    private String gasPipe;
    /**
     * 阀门
     */
    private String valve;
    /**
     * 巡检结果
     */
    private Integer inspectResult;
    /**
     * 巡检日期
     */
    private LocalDate inspectDate;
    /**
     * 整改截止日期
     */
    private LocalDate rectificationDeadline;
    /**
     * 整改违规住人情况
     */
    private String reUnauthorizedResidence;
    /**
     * 整改灭火器情况
     */
    private String reFireExtinguisher;
    /**
     * 整改灭火器数量
     */
    private Integer reFireExtinguisherNum;
    /**
     * 整改应急灯情况
     */
    private String reEmergencyLights;
    /**
     * 整改消防通道情况
     */
    private String reThoroughfare;
    /**
     * 整改电线情况
     */
    private String reWire;
    /**
     * 整改热水器情况
     */
    private String reHeaterEquiped;
    /**
     * 整改逃生梯情况
     */
    private String reEmergencyStairs;
    /**
     * 整改烟感器情况
     */
    private String reSmokeDetector;
    /**
     * 整改煤气管情况
     */
    private String reGasPipe;
    /**
     * 整改阀门情况
     */
    private String reValve;
    /**
     * 复查意见
     */
    private String reinspectOpinion;
    /**
     * 复查人员签名
     */
    private String reinspector;
    /**
     * 复查日期
     */
    private LocalDate reinspectDate;
}
