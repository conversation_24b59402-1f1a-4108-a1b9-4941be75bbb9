package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.RealtyDeposit;
import com.senox.realty.vo.RealtyDepositVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/6 16:50
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyDepositConvertor extends BaseConvert<RealtyDeposit, RealtyDepositVo> {
}
