package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingStoreInspection;
import com.senox.realty.vo.FirefightingStoreInspectionBriefVo;
import com.senox.realty.vo.FirefightingStoreInspectionVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 15:49
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingStoreInspectionConvertor {

    /**
     * 商铺消防巡检记录视图转实体
     * @param vo
     * @return
     */
    FirefightingStoreInspection toDo(FirefightingStoreInspectionVo vo);


    /**
     * 商铺消防巡检记录实体转视图
     * @param domain
     * @return
     */
    FirefightingStoreInspectionVo toVo(FirefightingStoreInspection domain);

    /**
     * 商铺消防巡检记录实体转简要视图
     * @param list
     * @return
     */
    List<FirefightingStoreInspectionBriefVo> toBriefVo(List<FirefightingStoreInspection> list);


}
