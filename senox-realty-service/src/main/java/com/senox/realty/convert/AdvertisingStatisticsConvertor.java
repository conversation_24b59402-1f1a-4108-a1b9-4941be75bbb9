package com.senox.realty.convert;

import com.senox.realty.domain.AdvertisingStatistics;
import com.senox.realty.vo.AdvertisingStatisticsVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/4/25 10:24
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdvertisingStatisticsConvertor {

    /**
     * do 转 vo
     * @param advertisingStatistics
     * @return
     */
    AdvertisingStatisticsVo toVo(AdvertisingStatistics advertisingStatistics);
}
