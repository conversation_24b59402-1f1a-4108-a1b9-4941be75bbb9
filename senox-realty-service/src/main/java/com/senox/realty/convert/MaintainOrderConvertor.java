package com.senox.realty.convert;

import com.senox.realty.domain.MaintainOrder;
import com.senox.realty.vo.MaintainOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MaintainOrderConvertor{

    /**
     * 视图转 domain 对象
     * @param vo
     * @return
     */
    MaintainOrder toDo(MaintainOrderVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    MaintainOrderVo toVo(MaintainOrder domain);
}
