package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.MaintainMaterialItem;
import com.senox.realty.vo.MaintainMaterialItemVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/6/25 10:36
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MaintainMaterialItemConvertor extends BaseConvert<MaintainMaterialItem, MaintainMaterialItemVo> {

}
