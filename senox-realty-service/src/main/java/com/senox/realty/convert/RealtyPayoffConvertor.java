package com.senox.realty.convert;

import com.senox.realty.domain.RealtyPayoff;
import com.senox.realty.vo.RealtyPayoffVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/11/23 15:18
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyPayoffConvertor {

    /**
     * 应付账单视图对象转 domain 对象
     * @param vo
     * @return
     */
    RealtyPayoff vo2Do(RealtyPayoffVo vo);
}
