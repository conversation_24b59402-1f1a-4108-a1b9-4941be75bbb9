package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingInspectTask;
import com.senox.realty.vo.FirefightingInspectTaskVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/5/14 15:50
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingInspectTaskConvertor {

    /**
     * 巡检任务视图转实体
     * @param vo
     * @return
     */
    FirefightingInspectTask toDo(FirefightingInspectTaskVo vo);

    /**
     * 巡检任务实体转视图
     * @param domain
     * @return
     */
    FirefightingInspectTaskVo toVo(FirefightingInspectTask domain);
}
