package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingAccommodateInspection;
import com.senox.realty.vo.FirefightingAccommodateInspectionVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/5/9 14:54
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingAccommodateInspectionConvertor {

    /**
     * 违规住人消防安全巡检视图转实体
     * @param vo
     * @return
     */
    FirefightingAccommodateInspection toDo(FirefightingAccommodateInspectionVo vo);

    /**
     * 违规住人消防安全巡检实体转视图
     * @param domain
     * @return
     */
    FirefightingAccommodateInspectionVo toVo(FirefightingAccommodateInspection domain);
}
