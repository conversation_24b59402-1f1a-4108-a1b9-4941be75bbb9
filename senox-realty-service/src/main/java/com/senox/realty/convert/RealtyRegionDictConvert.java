package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.RealtyRegionDict;
import com.senox.realty.vo.RealtyRegionDictVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyRegionDictConvert extends BaseConvert<RealtyRegionDict, RealtyRegionDictVo> {
}
