package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.AntiFraudWorkRecord;
import com.senox.realty.vo.AntiFraudWorkRecordVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025-03-28
 **/
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AntiFraudWorkRecordConvert extends BaseConvert<AntiFraudWorkRecord, AntiFraudWorkRecordVo> {
}
