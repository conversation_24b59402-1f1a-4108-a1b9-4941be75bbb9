package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.AntiFraudWorkRecordMedia;
import com.senox.realty.vo.AntiFraudWorkRecordMediaVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025-03-31
 **/
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AntiFraudWorkRecordMediaConvert extends BaseConvert<AntiFraudWorkRecordMedia, AntiFraudWorkRecordMediaVo> {
}
