package com.senox.realty.convert;

import com.senox.realty.domain.EnergyProfit;
import com.senox.realty.vo.EnergyProfitEditVo;
import com.senox.realty.vo.EnergyProfitVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/9/25 16:11
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface EnergyProfitConvertor {

    /**
     * 能源损益实体转视图
     * @param domain
     * @return
     */
    @Mapping(target = "energyType", expression = "java(com.senox.common.constant.device.EnergyType.fromValue(domain.getEnergyType()))")
    EnergyProfitVo toVo(EnergyProfit domain);

    /**
     * 编辑视图转实体对象
     * @param editVo
     * @return
     */
    EnergyProfit editVoToDo(EnergyProfitEditVo editVo);
}
