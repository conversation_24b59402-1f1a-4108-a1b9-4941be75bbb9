package com.senox.realty.convert;

import com.senox.realty.domain.AdvertisingSpace;
import com.senox.realty.vo.AdvertisingSpaceVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/7/18 13:41
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdvertisingSpaceConvertor {

    /**
     * 视图对象转实体
     * @param spaceVo
     * @return
     */
    AdvertisingSpace toDo(AdvertisingSpaceVo spaceVo);

    /**
     * 实体转视图对象
     * @param space
     * @return
     */
    AdvertisingSpaceVo toVo(AdvertisingSpace space);
}
