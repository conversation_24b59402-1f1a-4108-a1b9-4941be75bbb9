package com.senox.realty.convert;

import com.senox.realty.domain.RealtyStatistics;
import com.senox.realty.vo.RealtyStatisticsVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/4/24 11:38
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyStatisticsConvertor {

    /**
     * do 转 vo
     * @param realtyStatistics
     * @return
     */
    RealtyStatisticsVo toVo(RealtyStatistics realtyStatistics);
}
