package com.senox.realty.convert;

import com.senox.realty.domain.Contract;
import com.senox.realty.domain.ContractExt;
import com.senox.realty.vo.ContractVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/10/11 11:44
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface ContractConvertor {


    /**
     * 合同实体转合同视图对象
     * @param domain
     * @return
     */
    ContractVo toVo(Contract domain);

    /**
     * 合同视图对象转合同实体对象
     * @param vo
     * @return
     */
    Contract toDo(ContractVo vo);

    /**
     * 合同视图对象转合同扩展实体
     * @param vo
     * @return
     */
    @Mapping(source = "id", target = "contractId")
    ContractExt voToExtDo(ContractVo vo);
}
