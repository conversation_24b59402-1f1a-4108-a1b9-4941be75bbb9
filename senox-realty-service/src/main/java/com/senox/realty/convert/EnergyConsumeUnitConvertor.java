package com.senox.realty.convert;

import com.senox.realty.domain.EnergyConsumeUnit;
import com.senox.realty.vo.EnergyConsumeUnitVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/31 14:50
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface EnergyConsumeUnitConvertor {

    /**
     * 实体对象转视图对象
     * @param domain
     * @return
     */
    EnergyConsumeUnitVo toVo(EnergyConsumeUnit domain);

    /**
     * 实体对象转视图对象
     * @param list
     * @return
     */
    List<EnergyConsumeUnitVo> toVo(List<EnergyConsumeUnit> list);

    /**
     * 视图对象转实体对象
     * @param vo
     * @return
     */
    EnergyConsumeUnit toDo(EnergyConsumeUnitVo vo);
}
