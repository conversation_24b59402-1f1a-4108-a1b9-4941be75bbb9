package com.senox.realty.convert;


import com.senox.realty.domain.EnergyMeteringPoint;
import com.senox.realty.vo.RealtyToEnergyMeteringPointVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-5-4
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyToEnergyPointConvert {


    RealtyToEnergyMeteringPointVo toRealtyPoint(EnergyMeteringPoint point);

    List<RealtyToEnergyMeteringPointVo> toRealtyPoint(List<EnergyMeteringPoint> voList);
}
