package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingFormTemplate;
import com.senox.realty.domain.FirefightingTemplate;
import com.senox.realty.domain.FirefightingTemplateVariables;
import com.senox.realty.vo.FirefightingFormTemplateVo;
import com.senox.realty.vo.FirefightingTemplateVariablesVo;
import com.senox.realty.vo.FirefightingTemplateVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:33
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingTemplateConvertor {

    /**
     * 消防表单模板视图转实体
     * @param vo
     * @return
     */
    FirefightingTemplate toDo(FirefightingTemplateVo vo);

    /**
     * 消防表单模板实体转视图
     * @param domain
     * @return
     */
    FirefightingTemplateVo toVo(FirefightingTemplate domain);

    /**
     * 消防表单模板实体转视图
     * @param list
     * @return
     */
    List<FirefightingTemplateVo> toVo(List<FirefightingTemplate> list);

    /**
     * 消防表单模板变量视图转实体
     * @param list
     * @return
     */
    List<FirefightingTemplateVariables> toAttrsDo(List<FirefightingTemplateVariablesVo> list);

    /**
     * 消防表单模板变量实体转视图
     * @param list
     * @return
     */
    List<FirefightingTemplateVariablesVo> toAttrsVo(List<FirefightingTemplateVariables> list);

    /**
     * 消防表单模板视图转实体
     * @param vo
     * @return
     */
    FirefightingFormTemplate toFormDo(FirefightingFormTemplateVo vo);

    /**
     * 消防表单模板实体转视图
     * @param domain
     * @return
     */
    FirefightingFormTemplateVo toFormVo(FirefightingFormTemplate domain);

    /**
     * 消防表单模板实体列表转视图
     * @param list
     * @return
     */
    List<FirefightingFormTemplateVo> toFormVo(List<FirefightingFormTemplate> list);
}
