package com.senox.realty.convert;


import com.senox.realty.domain.RealtyReceipt;
import com.senox.realty.vo.RealtyReceiptVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023-3-23
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyReceiptConvert {


    /**
     * vo转do
     *
     * @param vo vo
     * @return domain
     */
    RealtyReceipt toDo(RealtyReceiptVo vo);

    /**
     * do转vo
     *
     * @param domain domain
     * @return vo
     */
    RealtyReceiptVo toVo(RealtyReceipt domain);
}
