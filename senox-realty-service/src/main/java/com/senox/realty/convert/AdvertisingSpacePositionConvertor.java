package com.senox.realty.convert;

import com.senox.realty.domain.AdvertisingSpacePosition;
import com.senox.realty.vo.AdvertisingSpacePositionVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/3 14:47
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdvertisingSpacePositionConvertor {


    /**
     * 广告位区域街道视图对象转实体对象
     * @param vo
     * @return
     */
    AdvertisingSpacePosition toDo(AdvertisingSpacePositionVo vo);

    /**
     * 广告位区域街道实体对象转视图对象
     * @param entity
     * @return
     */
    AdvertisingSpacePositionVo toVo(AdvertisingSpacePosition entity);

    /**
     * 广告位区域街道实体对象列表转视图对象列表
     * @param entityList
     * @return
     */
    List<AdvertisingSpacePositionVo> toVo(List<AdvertisingSpacePosition> entityList);
}
