package com.senox.realty.convert;

import com.senox.realty.domain.EnergyProfitItem;
import com.senox.realty.vo.EnergyProfitItemVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/25 17:24
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface EnergyProfitItemConvertor {

    /**
     * 能源损益明细实体转视图
     * @param domain
     * @return
     */
    EnergyProfitItemVo toVo(EnergyProfitItem domain);

    /**
     * 能源损益明细视图转实体
     * @param vo
     * @return
     */
    EnergyProfitItem toDo(EnergyProfitItemVo vo);

    /**
     * 能源损益明细实体列表转视图列表
     * @param list
     * @return
     */
    List<EnergyProfitItemVo> toVo(List<EnergyProfitItem> list);

    /**
     * 能源损益明细视图转实体
     * @param list
     * @return
     */
    List<EnergyProfitItem> toDo(List<EnergyProfitItemVo> list);
}
