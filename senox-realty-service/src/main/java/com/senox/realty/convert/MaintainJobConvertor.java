package com.senox.realty.convert;

import com.senox.realty.domain.MaintainJob;
import com.senox.realty.vo.MaintainJobVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/4/3 10:55
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MaintainJobConvertor{

    /**
     * 视图转 domain 对象
     * @param vo
     * @return
     */
    MaintainJob toDo(MaintainJobVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    MaintainJobVo toVo(MaintainJob domain);
}
