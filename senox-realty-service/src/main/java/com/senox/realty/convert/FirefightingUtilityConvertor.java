package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingUtility;
import com.senox.realty.vo.FirefightingUtilityVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13 10:41
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingUtilityConvertor {

    /**
     * 公共消防设施实体转视图
     * @param domain
     * @return
     */
    FirefightingUtilityVo toVo(FirefightingUtility domain);

    /**
     * 公共消防设施实体转视图
     * @param list
     * @return
     */
    List<FirefightingUtilityVo> toVo(List<FirefightingUtility> list);

    /**
     * 公共消防设施视图转实体
     * @param vo
     * @return
     */
    FirefightingUtility toDo(FirefightingUtilityVo vo);
}
