package com.senox.realty.convert;

import com.senox.realty.domain.Realty;
import com.senox.realty.domain.RealtyExt;
import com.senox.realty.vo.RealtyVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/11 11:58
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyConvertor {

    /**
     * 物业实体转视图
     * @param domain
     * @return
     */
    RealtyVo toVo(Realty domain);

    /**
     * 物业实体转视图
     * @param list
     * @return
     */
    List<RealtyVo> toVo(List<Realty> list);

    /**
     * 物业视图转实体
     * @param vo
     * @return
     */
    Realty toDo(RealtyVo vo);

    /**
     * 物业视图转扩展实体
     * @param vo
     * @return
     */
    @Mapping(source = "id", target = "realtyId")
    RealtyExt toExtDo(RealtyVo vo);
}
