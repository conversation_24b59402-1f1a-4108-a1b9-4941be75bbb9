package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingNotice;
import com.senox.realty.vo.FirefightingNoticeVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:02
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FireFightingNoticeConvertor {

    /**
     * 店铺消防安全责任告知单视图对象转实体对象
     * @param vo
     * @return
     */
    FirefightingNotice toDo(FirefightingNoticeVo vo);

    /**
     * 店铺消防安全责任告知单实体对象转视图对象
     * @param domain
     * @return
     */
    FirefightingNoticeVo toVo(FirefightingNotice domain);

    /**
     * 店铺消防安全责任告知单实体对象转视图对象
     * @param list
     * @return
     */
    List<FirefightingNoticeVo> toVo(List<FirefightingNotice> list);
}
