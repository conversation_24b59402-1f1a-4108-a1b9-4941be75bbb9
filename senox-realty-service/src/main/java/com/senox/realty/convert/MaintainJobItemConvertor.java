package com.senox.realty.convert;

import com.senox.realty.domain.MaintainJobItem;
import com.senox.realty.vo.MaintainJobItemVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 16:20
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MaintainJobItemConvertor {

    /**
     * 视图转 domain 对象
     * @param vo
     * @return
     */
    MaintainJobItem toDo(MaintainJobItemVo vo);

    /**
     * 视图转 domain 对象
     * @param voList
     * @return
     */
    List<MaintainJobItem> toDo(List<MaintainJobItemVo> voList);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    MaintainJobItemVo toVo(MaintainJobItem domain);

    /**
     * domain 对象 转视图
     * @param domainList
     * @return
     */
    List<MaintainJobItemVo> toVo(List<MaintainJobItem> domainList);
}
