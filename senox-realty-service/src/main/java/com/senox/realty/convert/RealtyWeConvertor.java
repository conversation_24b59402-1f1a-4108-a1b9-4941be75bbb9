package com.senox.realty.convert;

import com.senox.common.utils.StringUtils;
import com.senox.realty.domain.RealtyWe;
import com.senox.realty.vo.RealtyWeVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/24 15:58
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyWeConvertor {

    /**
     * 水电视图转实体
     * @param vo
     * @return
     */
    @Mapping(target = "realtySerial", expression = "java(formatRealtySerial(vo.getRealtySerial(), vo.getRealtyAliasSerial()))")
    RealtyWe toDo(RealtyWeVo vo);

    /**
     * 水电视图转实体
     * @param vo
     * @return
     */
    @Mapping(target = "realtySerial", expression = "java(formatRealtySerial(vo.getRealtySerial(), vo.getRealtyAliasSerial()))")
    List<RealtyWe> toDo(List<RealtyWeVo> vo);

    /**
     * 物业编号处理
     * @param realtySerial
     * @param aliasSerial
     * @return
     */
    default String formatRealtySerial(String realtySerial, String aliasSerial) {
        return StringUtils.isBlank(aliasSerial) ? realtySerial : aliasSerial;
    }


}
