package com.senox.realty.convert;

import com.senox.realty.domain.AdvertisingContract;
import com.senox.realty.vo.AdvertisingContractEditVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/7/20 15:41
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdvertisingContractConvertor {


    /**
     * 编辑视图转实体
     * @param vo
     * @return
     */
    AdvertisingContract editVoToDo(AdvertisingContractEditVo vo);

}
