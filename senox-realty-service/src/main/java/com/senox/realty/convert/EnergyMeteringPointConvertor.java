package com.senox.realty.convert;

import com.senox.common.constant.device.DeviceState;
import com.senox.common.constant.device.EnergyType;
import com.senox.common.constant.device.PowerState;
import com.senox.realty.domain.EnergyMeteringPoint;
import com.senox.realty.vo.EnergyMeteringPointVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface EnergyMeteringPointConvertor {

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    @Mapping(target = "type", expression = "java(getTypeNumber(vo.getEnergyType()))")
    @Mapping(target = "status", expression = "java(getStateNumber(vo.getState()))")
    @Mapping(target = "powerStatus", expression = "java(getPowerNumber(vo.getPowerState()))")
    EnergyMeteringPoint toDo(EnergyMeteringPointVo vo);

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    List<EnergyMeteringPoint> toDo(List<EnergyMeteringPointVo> vo);

    /**
     * do to vo
     *
     * @param domain do
     * @return vo
     */
    @Mapping(target = "energyType", expression = "java(getType(domain.getType()))")
    @Mapping(target = "state", expression = "java(getState(domain.getStatus()))")
    @Mapping(target = "powerState", expression = "java(getPowerState(domain.getPowerStatus()))")
    EnergyMeteringPointVo toVo(EnergyMeteringPoint domain);

    /**
     * do to vo
     *
     * @param domain do
     * @return vo
     */
    List<EnergyMeteringPointVo> toVo(List<EnergyMeteringPoint> domain);

    default EnergyType getType(Integer type) {
        return null == type ? null : EnergyType.fromValue(type);
    }

    default Integer getTypeNumber(EnergyType type) {
        return null == type ? null : type.getValue();
    }

    default DeviceState getState(Integer state) {
        return null == state ? null : DeviceState.fromState(state);
    }

    default Integer getStateNumber(DeviceState state) {
        return null == state ? null : state.getState();
    }

    default PowerState getPowerState(Integer state) {
        return null == state ? null : PowerState.fromState(state);
    }

    default Integer getPowerNumber(PowerState state) {
        return null == state ? null : state.getState();
    }
}
