package com.senox.realty.convert;

import com.senox.realty.domain.RealtyBillWe;
import com.senox.realty.vo.RealtyBillWeVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-20
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyBillWeConvertor {

  /**
   * 视图对象转实体对象
   * @param vo
   * @return
   */
  RealtyBillWe toDo(RealtyBillWeVo vo);

  /**
   * 视图对象转实体对象
   * @param voList
   * @return
   */
  List<RealtyBillWe> toDo(List<RealtyBillWeVo> voList);
}
