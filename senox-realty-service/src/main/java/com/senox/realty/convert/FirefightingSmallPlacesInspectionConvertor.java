package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingSmallPlacesInspection;
import com.senox.realty.domain.FirefightingInspectionAttr;
import com.senox.realty.domain.FirefightingSmallPlacesInspectionDetail;
import com.senox.realty.vo.FirefightingInspectionAttrVo;
import com.senox.realty.vo.FirefightingSmallPlacesInspectionVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/6 14:46
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingSmallPlacesInspectionConvertor {

    /**
     * 三小场所、出租屋消防巡检记录视图转实体
     * @param vo
     * @return
     */
    FirefightingSmallPlacesInspection toInspectionDo(FirefightingSmallPlacesInspectionVo vo);

    /**
     * 三小场所、出租屋消防巡检记录实体转视图
     * @param domain
     * @return
     */
    FirefightingSmallPlacesInspectionVo toInspectionVo(FirefightingSmallPlacesInspection domain);

    /**
     * 三小场所、出租屋消防巡检记录视图转明细实体
     * @param vo
     * @return
     */
    @Mapping(source = "id", target = "inspectionId")
    FirefightingSmallPlacesInspectionDetail toInspectionDetailDo(FirefightingSmallPlacesInspectionVo vo);

}
