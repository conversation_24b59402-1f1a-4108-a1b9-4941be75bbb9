package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.WaterElectricPriceType;
import com.senox.realty.vo.WaterElectricPriceTypeVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/14 10:09
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface WaterElectricPriceTypeConvertor extends BaseConvert<WaterElectricPriceType, WaterElectricPriceTypeVo> {
}
