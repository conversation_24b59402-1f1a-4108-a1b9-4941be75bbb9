package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.MaintainCharge;
import com.senox.realty.vo.MaintainChargeVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/6/6 15:11
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MaintainChargeConvertor extends BaseConvert<MaintainCharge, MaintainChargeVo> {

}
