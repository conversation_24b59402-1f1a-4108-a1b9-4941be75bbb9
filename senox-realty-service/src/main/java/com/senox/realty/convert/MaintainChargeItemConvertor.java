package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.MaintainChargeItem;
import com.senox.realty.vo.MaintainChargeItemVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/6/13 9:03
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MaintainChargeItemConvertor extends BaseConvert<MaintainChargeItem, MaintainChargeItemVo> {

}
