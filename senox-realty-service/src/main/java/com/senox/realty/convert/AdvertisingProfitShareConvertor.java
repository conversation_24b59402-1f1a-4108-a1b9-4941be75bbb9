package com.senox.realty.convert;

import com.senox.realty.domain.AdvertisingProfitShare;
import com.senox.realty.vo.AdvertisingProfitShareVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/25 14:55
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdvertisingProfitShareConvertor {

    /**
     * 视图对象转实体对象
     * @param vo
     * @return
     */
    AdvertisingProfitShare toDo(AdvertisingProfitShareVo vo);

    /**
     * 实体对象转视图对象
     * @param entity
     * @return
     */
    AdvertisingProfitShareVo toVo(AdvertisingProfitShare entity);

    /**
     * 实体对象列表转视图对象列表
     * @param entity
     * @return
     */
    List<AdvertisingProfitShareVo> toVo(List<AdvertisingProfitShare> entity);
}
