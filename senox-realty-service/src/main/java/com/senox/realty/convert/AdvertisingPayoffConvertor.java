package com.senox.realty.convert;

import com.senox.realty.domain.AdvertisingPayoff;
import com.senox.realty.vo.AdvertisingPayoffVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/4 14:07
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdvertisingPayoffConvertor {

    /**
     * 实体对象转视图对象
     * @param entity
     * @return
     */
    AdvertisingPayoffVo toVo(AdvertisingPayoff entity);

    /**
     * 实体对象列表转视图对象列表
     * @param entityList
     * @return
     */
    List<AdvertisingPayoffVo> toVo(List<AdvertisingPayoff> entityList);

    /**
     * 视图对象转实体对象
     * @param vo
     * @return
     */
    AdvertisingPayoff toDo(AdvertisingPayoffVo vo);
}
