package com.senox.realty.convert;

import com.senox.realty.domain.SecurityJournal;
import com.senox.realty.vo.SecurityJournalVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025/3/27 9:00
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface SecurityJournalConvertor {

    /**
     * 安保日志视图转实体
     * @param vo
     * @return
     */
    SecurityJournal toDo(SecurityJournalVo vo);


    /**
     * 安保日志实体转视图
     * @param domain
     * @return
     */
    SecurityJournalVo toVo(SecurityJournal domain);
}
