package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.PowerOutageNotice;
import com.senox.realty.vo.PowerOutageNoticeVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025-05-09
 **/
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PowerOutageNoticeConvert extends BaseConvert<PowerOutageNotice, PowerOutageNoticeVo> {
}
