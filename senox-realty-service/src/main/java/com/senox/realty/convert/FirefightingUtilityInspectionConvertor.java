package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingUtilityInspection;
import com.senox.realty.vo.FirefightingUtilityInspectionVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/5/14 9:07
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingUtilityInspectionConvertor {

    /**
     * 公共消防设施巡检视图转实体
     * @param vo
     * @return
     */
    FirefightingUtilityInspection toDo(FirefightingUtilityInspectionVo vo);

    /**
     * 公共消防设施巡检实体转视图
     * @param domain
     * @return
     */
    FirefightingUtilityInspectionVo toVo(FirefightingUtilityInspection domain);
}
