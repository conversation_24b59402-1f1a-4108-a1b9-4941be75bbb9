package com.senox.realty.convert;

import com.senox.realty.domain.MaintainMaterial;
import com.senox.realty.vo.MaintainMaterialVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 16:16
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MaintainMaterialConvertor{

    /**
     * 视图转 domain 对象
     * @param vo
     * @return
     */
    MaintainMaterial toDo(MaintainMaterialVo vo);

    /**
     * domain 转视图
     * @param domain
     * @return
     */
    List<MaintainMaterialVo> toVo(List<MaintainMaterial> domain);
}
