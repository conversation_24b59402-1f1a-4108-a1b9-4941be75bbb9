package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingFile;
import com.senox.realty.domain.FirefightingFileDetail;
import com.senox.realty.vo.FirefightingFileVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/4/19 15:07
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingFileConvertor {

    /**
     * 视图对象转商铺消防安全档案实体对象
     * @param vo
     * @return
     */
    FirefightingFile toDo(FirefightingFileVo vo);

    /**
     * 商铺消防安全档案实体对象转视图对象
     * @param domain
     * @return
     */
    FirefightingFileVo toVo(FirefightingFile domain);

    /**
     * 视图对象转商铺消防安全档案明细实体对象
     * @param vo
     * @return
     */
    @Mapping(source = "id", target = "fileId")
    FirefightingFileDetail toDetailDo(FirefightingFileVo vo);


}
