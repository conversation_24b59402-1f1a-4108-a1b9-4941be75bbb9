package com.senox.realty.convert;

import com.senox.realty.domain.FirefightingInspectionAttr;
import com.senox.realty.vo.FirefightingInspectionAttrVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/9 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FirefightingInspectionAttrConvertor {

    /**
     * 巡检记录参数视图转实体
     * @param vo
     * @return
     */
    FirefightingInspectionAttr toAttrDo(FirefightingInspectionAttrVo vo);

    /**
     * 巡检记录参数视图转实体
     * @param list
     * @return
     */
    List<FirefightingInspectionAttr> toAttrDo(List<FirefightingInspectionAttrVo> list);

    /**
     * 巡检记录参数实体转视图
     * @param domain
     * @return
     */
    FirefightingInspectionAttrVo toAttrVo(FirefightingInspectionAttr domain);

    /**
     * 巡检记录参数实体转视图
     * @param list
     * @return
     */
    List<FirefightingInspectionAttrVo> toAttrVo(List<FirefightingInspectionAttr> list);
}
