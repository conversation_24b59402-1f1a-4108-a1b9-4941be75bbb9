package com.senox.realty.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.realty.domain.RealtyAlias;
import com.senox.realty.vo.RealtyAliasVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022-8-12
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface RealtyAliasConvertor extends BaseConvert<RealtyAlias, RealtyAliasVo> {


}
