package com.senox.realty.convert;

import com.senox.realty.domain.AdvertisingBill;
import com.senox.realty.vo.AdvertisingBillVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/7/26 17:12
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdvertisingBillConvertor {

    /**
     * 应收账单实体对象转视图对象
     * @param domain
     * @return
     */
    AdvertisingBillVo toVo(AdvertisingBill domain);
}
