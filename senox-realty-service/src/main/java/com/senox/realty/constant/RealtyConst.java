package com.senox.realty.constant;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/1/18 14:37
 */
public class RealtyConst {

    private RealtyConst() {
    }

    /**
     * 批量处理数量
     */
    public static final int BATCH_SIZE_1000 = 1000;
    public static final int BATCH_SIZE_2000 = 2000;
    public static final int BATCH_SIZE_5000 = 5000;
    public static final BigDecimal DF_PENALTY_RATE = BigDecimal.ONE;

    /**
     * 序号日期格式
     */
    public static final String SERIAL_DATE_FORMAT = "yyyyMMdd";
    /**
     * 支付账单标题
     */
    public static final String TITLE_REALTY_BILL = "%s-%s账单";

    public static final String ADVERTISING_SERIAL_PREFIX = "AD";

    public static final String REMARK_ONE_TIME_FEE_PAY_CANCEL = "一次性收费取消";
    public static final String REMARK_ONE_TIME_FEE_REFUND_CANCEL = "一次性收费退费取消";
    public static final String REMARK_DEPOSIT_PAY_CANCEL = "保证金取消";
    public static final String REMARK_DEPOSIT_REFUND_CANCEL = "保证金取消";
    public static final String KEY_CONTRACT_SUSPEND_TIP = "%s%s账单已缴费，无法停用合同至%s";

    /**
     * 缓存常量
     */
    public static class Cache {

        private Cache() {
        }

        /**
         * 费项
         */
        public static final String KEY_FEE = "senox:fee";
        /**
         * 经营区域
         */
        public static final String KEY_BUSINESS_REGION = "senox:businessRegion";
        /**
         * 街道
         */
        public static final String KEY_STREET = "senox:street";
        /**
         * 区域街道
         */
        public static final String KEY_REGION_STREET = "senox:region:street:%s";
        /**
         * 水电价格类别
         */
        public static final String KEY_WE_PRICE_TYPE = "senox:waterElectric:priceType";
        /**
         * 能源消费单元
         */
        public static final String KEY_ENERGY_CONSUME_UNIT = "senox:energy:consume";
        /**
         * 合同编号
         */
        public static final String KEY_CONTRACT_NO = "senox:contractNo:%s";
        /**
         * 维修单号
         */
        public static final String KEY_MAINTAIN_ORDER_NO = "senox:maintain:orderNo:%s";
        /**
         * 维修工单号
         */
        public static final String KEY_MAINTAIN_JOB_NO = "senox:maintain:jobNo:%s";
        /**
         * 滞纳金配置
         */
        public static final String KEY_PENALTY_SETTING = "senox:penaltySetting:%s";
        /**
         * 一次性收费账单号
         */
        public static final String KEY_ONE_TIME_FEE_BILL_NO = "senox:oneTimeFee:billNo:%s";
        /**
         * 支付缓存
         */
        public static final String KEY_REALTY_BILL_PAY = "senox:realty:pay:%s:%s";
        /**
         * 银行报盘
         */
        public static final String KEY_BANK_WITHHOLD = "senox:withhold:%s:%s";
        /**
         * 应收账单生成
         */
        public static final String KEY_REALTY_BILL_BUILD = "senox:realty:bill:%s-%s";
        /**
         * 应付账单生成
         */
        public static final String KEY_REALTY_PAYOFF_BUILD = "senox:realty:payoff:%s-%s";

        /**
         * 账单发票锁
         */
        public static final String KEY_RECEIPT_LOCK = "senox:receipt-lock-%s";

        /**
         * 账单锁
         */
        public static final String KEY_RECEIPT_BILL_LOCK = "senox:receipt-bill-lock-%s";

        /**
         * 物业发票号码
         */
        public static final String REALTY_RECEIPT_NO = "realty-receipt-no";

        /**
         * 广告位编号
         */
        public static final String KEY_ADVERTISING_SERIAL = "senox:advertising:serial";
        /**
         * 广告位合同号
         */
        public static final String KEY_ADVERTISING_CONTRACT = "senox:advertising:contractNo:%s";
        /**
         * 广告位锁
         */
        public static final String KEY_ADVERTISING_LOCK = "senox:advertising:lock";
        /**
         * 广告模板
         */
        public static final String KEY_FIREFIGHTING_FORM_TEMPLATE = "senox:firefighting:form-template";
        /**
         * 物业统计生成
         */
        public static final String KEY_REALTY_STATISTICS_BUILD = "senox:realty:statistics:%s-%s-%s";
        /**
         * 广告位统计生成
         */
        public static final String KEY_ADVERTISING_STATISTICS_BUILD = "senox:advertising:statistics:%s-%s-%s";

        /**
         * 缓存时长 7天
         */
        public static final long TTL_7D = TimeUnit.DAYS.toSeconds(7L);
        /**
         * 缓存时长 2天
         */
        public static final long TTL_2D = TimeUnit.DAYS.toSeconds(2L);
        /**
         * 缓存时长 1h
         */
        public static final long TTL_1H = TimeUnit.HOURS.toSeconds(1L);
        /**
         * 缓存时长 10m
         */
        public static final long TTL_10M = TimeUnit.MINUTES.toSeconds(10L);
        /**
         * 缓存时长 10s
         */
        public static final long TTL_10S = TimeUnit.SECONDS.toSeconds(10L);
    }

    public static class MQ {
        private MQ() {
        }

        public static final String RECEIPT_WECHAT_MESSAGE_SUCCESS = "receipt.wechat.message.success";
        public static final String RECEIPT_REALTY_EXCHANGE = "receipt.topic";
        public static final String MQ_RECEIPT_APPLY_PASS = "receipt.apply.pass.queue";
        public static final String EX_RECEIPT_APPLY_PASS = "receipt.apply.pass.exchange";
        public static final String MQ_RECEIPT_APPLY_STATUS = "receipt.apply.status.queue";
        public static final String EX_RECEIPT_APPLY_STATUS = "receipt.apply.status.exchange";
        public static final String MQ_RECEIPT_REALTY_STATUS = "receipt.realty.status.queue";
        public static final String EX_RECEIPT_REALTY_STATUS = "receipt.realty.status.exchange";

        /**
         * 物维
         */
        public static final String MQ_REALTY_AC_MAINTAIN = "realty.ac.maintain.queue";

        /**
         * 物评价通知
         */
        public static final String MQ_REALTY_AC_MAINTAIN_EVALUATE = "realty.ac.maintain.evaluate.queue";


        /**
         * 维修单未处理提醒
         */
        public static final String MQ_REALTY_AC_UNTREATED = "realty.ac.untreated.queue";
    }
}
