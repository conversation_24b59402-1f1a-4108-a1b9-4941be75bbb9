package com.senox.realty.constant;

/**
 * <AUTHOR>
 * @date 2024/4/23 11:49
 */
public enum TemplateStatus {

    /**
     * 初始化
     */
    INIT(0),
    /**
     * 失效
     */
    INVALID(-1),
    /**
     * 生效
     */
    VALID(1),
    ;

    private final int value;

    TemplateStatus(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static TemplateStatus fromStatus(Integer value) {
        if (value == null) {
            return null;
        }

        for (TemplateStatus item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }
}
