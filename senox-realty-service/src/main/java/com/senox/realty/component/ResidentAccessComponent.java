package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.user.api.clients.ResidentClient;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/24 14:02
 */
@RequiredArgsConstructor
@Component
public class ResidentAccessComponent {

    private final ResidentClient residentClient;


    /**
     * 根据合同号删除住户权限
     * @param contractNo
     */
    public void deleteAccessByContractNo(String contractNo) {
        try {
            residentClient.deleteAccessByContractNo(contractNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 续签恢复用户门禁权限
     * @param oldContractNo
     * @param newContractNo
     */
    public void renewalAccess(String oldContractNo, String newContractNo) {
        try {
            residentClient.renewalAccess(oldContractNo, newContractNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}
