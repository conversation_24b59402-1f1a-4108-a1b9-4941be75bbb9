package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.api.clients.DepartmentClient;
import com.senox.user.vo.DepartmentVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/18 14:52
 */
@Component
public class DepartmentComponent {

    @Autowired
    private DepartmentClient departmentClient;

    /**
     * 根据id查找部门
     * @param id
     * @return
     */
    public DepartmentVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        try {
            return departmentClient.getDepartment(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 部门列表
     * @return
     */
    public List<DepartmentVo> listDepartments() {
        try {
            return departmentClient.listDepartments();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
