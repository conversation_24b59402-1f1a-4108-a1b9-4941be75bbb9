package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.pm.api.clients.OrderClient;
import com.senox.pm.constant.OrderType;
import com.senox.pm.vo.OrderCancelVo;
import com.senox.pm.vo.OrderProductDetailVo;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/2 14:58
 */
@Component
@RequiredArgsConstructor
public class OrderComponent {

    private final OrderClient orderClient;

    /**
     * 支付下单
     * @param order
     * @return
     */
    public OrderResultVo addOrder(OrderVo order) {
        try {
            return orderClient.addOrder(order);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 撤销订单
     * @param cancel
     */
    public void cancelOrder(OrderCancelVo cancel) {
        try {
            orderClient.cancelOrder(cancel);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 查询订单
     * @param id
     * @return
     */
    public OrderVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        try {
            return orderClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 订单明细
     * @param orderType
     * @param productId
     * @return
     */
    public List<OrderProductDetailVo> listProductByProductId(OrderType orderType, Long productId) {
        if (orderType == null || !WrapperClassUtils.biggerThanLong(productId, 0L)) {
            return Collections.emptyList();
        }

        try {
            return orderClient.listProductOrders(orderType, productId);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
