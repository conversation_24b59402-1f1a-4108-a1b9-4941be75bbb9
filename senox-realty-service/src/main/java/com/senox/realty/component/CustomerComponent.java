package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.user.api.clients.CustomerClient;
import com.senox.user.vo.CustomerVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/9/30 15:31
 */
@RequiredArgsConstructor
@Component
public class CustomerComponent {

    private final CustomerClient customerClient;

    /**
     * 根据id查找客户
     * @param id
     * @return
     */
    public CustomerVo findById(Long id) {
        try {
            return customerClient.getCustomer(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

}
