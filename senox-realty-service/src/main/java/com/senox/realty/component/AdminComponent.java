package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.api.clients.AdminUserClient;
import com.senox.user.vo.AdminUserSearchVo;
import com.senox.user.vo.AdminUserVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/26 8:28
 */
@Component
public class AdminComponent {

    @Autowired
    private AdminUserClient adminUserClient;

    private static final Map<String, AdminUserVo> ADMIN_USER_MAP = new HashMap<>(10);


    /**
     * 获取收费员
     * @param id
     * @return
     */
    public AdminUserVo getAdminUser(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return adminUserClient.getAdminUser(id);
    }

    /**
     * 根据用户姓名查找用户
     * @param realName
     * @return
     */
    public AdminUserVo findByRealName(String realName) {
        if (StringUtils.isBlank(realName)) {
            return null;
        }

        // get from local cache
        AdminUserVo result = ADMIN_USER_MAP.get(realName);
        if (result != null) {
            return result;
        }

        try {
            result = adminUserClient.getByRealName(realName);
            ADMIN_USER_MAP.put(realName, result);
            return result;
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 根据中农网用户名查找用户
     * @param znwName
     * @return
     */
    public AdminUserVo findByZnwName(String znwName) {
        if (StringUtils.isBlank(znwName)) {
            return null;
        }
        znwName = znwName.endsWith("1") ? znwName.substring(0, znwName.length() - 1) : znwName;
        return findByRealName(znwName);
    }

    /**
     * 管理员用户列表
     * @param searchVo
     * @return
     */
    public PageResult<AdminUserVo> listAdminUserPage(AdminUserSearchVo searchVo) {
        try {
            return adminUserClient.listAdminUserPage(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return PageResult.emptyPage();
    }
}
