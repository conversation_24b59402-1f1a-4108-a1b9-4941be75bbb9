package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.dm.api.clients.EnergyClient;
import com.senox.dm.dto.EnergyRtuDto;
import com.senox.realty.domain.EnergyRtu;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-08
 **/
@RequiredArgsConstructor
@Component
public class DeviceEnergyRtuComponent {
    private final EnergyClient energyClient;

    /**
     * 远程集中器列表
     * @return 返回集中器列表
     */
    public List<EnergyRtu> remoteList() {
        try {
            List<EnergyRtuDto> rtuDtoList = energyClient.remoteRtuList();
            if (CollectionUtils.isEmpty(rtuDtoList)) {
                return Collections.emptyList();
            }
            List<EnergyRtu> rtuList = new ArrayList<>(rtuDtoList.size());
            for (EnergyRtuDto rtuDto : rtuDtoList) {
                EnergyRtu rtu = new EnergyRtu();
                rtu.setCode(rtuDto.getCode());
                rtu.setName(rtuDto.getName());
                rtu.setStatus(rtuDto.getStatus().getState());
                rtu.setOnLineTime(rtuDto.getOnLineTime());
                rtuList.add(rtu);
            }
            return rtuList;
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
