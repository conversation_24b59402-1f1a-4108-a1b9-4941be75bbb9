package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.user.api.clients.EnterpriseClient;
import com.senox.user.vo.EnterpriseRealtyVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/18 10:45
 */
@Component
@RequiredArgsConstructor
public class EnterpriseComponent {

    private final EnterpriseClient enterpriseClient;

    /**
     * 经营户列表
     * @param search
     * @return
     */
    public List<EnterpriseViewVo> listEnterprise(EnterpriseSearchVo search) {
        try {
            return enterpriseClient.listEnterprise(search);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 经营户物业列表
     * @param enterpriseIds
     * @return
     */
    public List<EnterpriseRealtyVo> listEnterpriseRealty(List<Long> enterpriseIds) {
        try {
            return enterpriseClient.ListEnterpriseRealities(enterpriseIds);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

}
