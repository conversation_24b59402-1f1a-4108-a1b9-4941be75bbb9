package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.dm.api.clients.EnergyClient;
import com.senox.dm.dto.EnergyMeteringPointDto;
import com.senox.realty.domain.EnergyMeteringPoint;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-08
 **/
@RequiredArgsConstructor
@Component
public class DeviceEnergyMeteringPointComponent {
    private final EnergyClient energyClient;

    /**
     * 远程计量点列表
     * @return 返回计量点列表
     */
    public List<EnergyMeteringPoint> remoteList(String meteringPointCode) {
        try {
            List<EnergyMeteringPointDto> meteringPointDtoList = energyClient.remoteMeteringPointList(meteringPointCode);
            if (CollectionUtils.isEmpty(meteringPointDtoList)) {
                return Collections.emptyList();
            }
            List<EnergyMeteringPoint> meteringPointList = new ArrayList<>(meteringPointDtoList.size());
            for (EnergyMeteringPointDto meteringPointDto : meteringPointDtoList) {
                EnergyMeteringPoint meteringPoint = new EnergyMeteringPoint();
                meteringPoint.setCode(meteringPointDto.getCode());
                meteringPoint.setName(meteringPointDto.getName());
                meteringPoint.setType(meteringPointDto.getEnergyType().getValue());
                meteringPoint.setRtuCode(meteringPointDto.getRtuCode());
                meteringPoint.setRtuName(meteringPointDto.getRtuName());
                meteringPoint.setRate(meteringPointDto.getRate());
                meteringPoint.setStatus(meteringPointDto.getState().getState());
                meteringPoint.setPowerStatus(meteringPointDto.getPowerState().getState());
                meteringPointList.add(meteringPoint);
            }
            return meteringPointList;
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}
