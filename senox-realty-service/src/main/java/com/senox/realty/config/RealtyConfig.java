package com.senox.realty.config;

import com.senox.common.constant.device.EnergyType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/27 16:52
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "senox.realty.config")
public class RealtyConfig {

    @Value("${senox.realty.penaltyDate:10}")
    private int dateOfPenalty;

    /**
     * 水基数
     */
    private int waterBase;
    /**
     * 电基数
     */
    private int electricBase;
    /**
     * 抄表日
     */
    private int recordDay;
    /**
     * 默认水单价
     */
    private BigDecimal waterPrice;
    /**
     * 默认电单价
     */
    private BigDecimal electricPrice;
    /**
     * 能源来源
     */
    private Map<EnergyType, List<String>> energySources;


}
