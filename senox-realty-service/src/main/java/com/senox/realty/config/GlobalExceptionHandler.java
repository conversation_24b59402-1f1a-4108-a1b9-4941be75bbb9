package com.senox.realty.config;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.UnAuthorizedException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;

/**
 * 统一异常处理
 * <AUTHOR>
 * @Date 2020/12/22 8:36
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * json 请求体参数异常
     * @param request
     * @param exception
     * @return
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result handleMethodArgumentNotValidException(HttpServletRequest request, MethodArgumentNotValidException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        logger.debug("请求参数错误，uri：{}，异常：{}", request.getRequestURI(), exception.getMessage());
        return Result.fail(ResultConst.INVALID_PARAMETER, fieldErrors.get(0).getDefaultMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = BindException.class)
    public Result handleBindException(HttpServletRequest request, BindException exception) {
        List<ObjectError> errors = exception.getBindingResult().getAllErrors();
        logger.debug("请求参数错误，uri：{}，异常：{}", request.getRequestURI(), exception.getMessage());
        return Result.fail(ResultConst.INVALID_PARAMETER, errors.get(0).getDefaultMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = ConstraintViolationException.class)
    public Result handleConstraintViolationException(HttpServletRequest request, ConstraintViolationException exception) {
        logger.debug("请求参错误，uri：{}，异常：{}", request.getRequestURI(), exception.getMessage());
        Set<ConstraintViolation<?>> violations = exception.getConstraintViolations();
        StringBuilder builder = new StringBuilder();
        for (ConstraintViolation<?> violation : violations) {
            builder.append(violation.getMessage());
            break;
        }
        return Result.fail(ResultConst.INVALID_PARAMETER, builder.toString());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler(value = UnAuthorizedException.class)
    public Result handleUnAuthorizedException(HttpServletRequest request, UnAuthorizedException exception) {
        logger.warn("请求授权异常，uri：{}，参数：{}，异常：{}", request.getRequestURI(), JsonUtils.object2Json(request.getParameterMap()), exception.getMessage());
        return Result.result(exception.getCode(), exception.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(value = BusinessException.class)
    public Result handleBusinessException(HttpServletRequest request, BusinessException exception) {
        logger.error("请求业务异常，uri：" + request.getRequestURI() + "，参数：" + JsonUtils.object2Json(request.getParameterMap()), exception);
        return Result.result(exception.getCode(), exception.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(value = Exception.class)
    public Result handleException(HttpServletRequest request, Exception e) {
        logger.error("请求系统异常，uri：" + request.getRequestURI() + ", 参数：" + JsonUtils.object2Json(request.getParameterMap()), e);
        return Result.fail(ResultConst.ERROR, e.getMessage());
    }
}
