package com.senox.realty.config;

import com.senox.pm.constant.ReceiptEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 商品数据
 * <AUTHOR>
 * @date 2023-3-21
 */
@Getter
@Setter
public class GoodsData {

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品简称
     */
    private String title;

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 税率
     */
    private BigDecimal tax;

    /**
     * 税收编号
     */
    private String taxCode;

    /**
     *使用优惠政策标志
     */
    private ReceiptEnum.PreferentialTaxMark preferentialTaxMark;

    /**
     * 增值税特殊管理
     */
    private ReceiptEnum.SalesTaxManagement salesTaxManagement;

    /**
     * 零税率标识
     */
    private ReceiptEnum.TaxRateMark taxRateMark;

    /**
     * 特定约束类型
     */
    private ReceiptEnum.ConstraintType constraintType;


}
