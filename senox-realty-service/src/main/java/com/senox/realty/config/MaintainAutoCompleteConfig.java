package com.senox.realty.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/10 9:19
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "senox.maintain.complete")
public class MaintainAutoCompleteConfig {

    /**
     * 部门
     */
    private List<Long> skipDepartmentIds;
}
