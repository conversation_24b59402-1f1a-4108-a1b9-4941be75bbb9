package com.senox.realty.config;

import com.senox.context.AdminContextDecorator;
import io.foldright.cffu.CffuFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2021/3/4 17:30
 */
@EnableAsync
@Configuration
public class AsyncConfigure {

    @Value("${senox.executor.pool.coreSize:20}")
    private int corePoolSize;
    @Value("${senox.executor.pool.maxSize:100}")
    private int maxPoolSize;
    @Value("${senox.executor.pool.capacity:2000}")
    private int queueCapacity;
    @Value("${senox.executor.pool.threadName:senox-realty-thread-}")
    private String executorThreadName;

    @Bean
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix(executorThreadName);
        // 装饰器
        executor.setTaskDecorator(new AdminContextDecorator());

        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean
    public CffuFactory cffuFactory(@Qualifier("taskExecutor") Executor executor) {
        return CffuFactory.builder(executor).build();
    }

}
