package com.senox.realty.config;

import lombok.Getter;
import lombok.Setter;

/**
 * rabbitmq 配置
 */
@Getter
@Setter
public class RabbitMqEntity {

    /**
     * 地址
     */
    private String addresses;

    /**
     * 账号
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 虚拟主机
     */
    private String virtualHost;

    /**
     * 连接超时时间
     */
    private int connectionTimeout;

    /**
     * 线程消费数
     */
    private int consumerThreads;
}
