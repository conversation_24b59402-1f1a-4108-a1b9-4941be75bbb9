package com.senox.realty.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/19 08:59
 */
@Configuration
public class RealtyWebConfigure implements WebMvcConfigurer {

    @Value("#{'${senox.adminFilter.excludeUrls:}'.split(',')}")
    private List<String> excludeUrls;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AccessInterceptor(excludeUrls))
                .addPathPatterns("/**");
    }
}
