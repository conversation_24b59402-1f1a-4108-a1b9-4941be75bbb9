package com.senox.realty.config;

import com.senox.common.exception.BusinessException;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-3-21
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "senox.realty.receipt.config")
public class ReceiptConfig {
     private List<GoodsData> goods;
     private Boolean artificialAudit;


     public GoodsData goodsDataByName(String name) {
          if (CollectionUtils.isEmpty(goods) || null == name) {
               return null;
          }
          for (GoodsData goodsData : goods) {
               if (goodsData.getName().equals(name)) {
                    return goodsData;
               }
          }
          throw new BusinessException("未获取到商品信息");
     }

     public GoodsData goodsDataByTaxCode(String taxCode) {
          if (CollectionUtils.isEmpty(goods) || null == taxCode) {
               return null;
          }
          for (GoodsData goodsData : goods) {
               if (goodsData.getTaxCode().equals(taxCode)) {
                    return goodsData;
               }
          }
          throw new BusinessException("未获取到商品信息");
     }
}
