package com.senox.realty.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.senox.realty.utils.RabbitMqUtils;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
public class RabbitMqConfigure {

    @DependsOn("connectionFactory")
    @Bean
    public RabbitTemplate rabbitTemplate(CachingConnectionFactory connectionFactory, ObjectMapper objectMapper) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(jackson2JsonMessageConverter(objectMapper));
        return rabbitTemplate;
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.rabbitmq")
    public RabbitMqEntity rabbitMqEntity() {
        return new RabbitMqEntity();
    }

    @Bean
    public ConnectionFactory connectionFactory(RabbitMqEntity rabbitMqEntity) {
        return RabbitMqUtils.buildMQConnectionFactory(rabbitMqEntity, rabbitMqEntity.getVirtualHost());
    }

    @Bean
    public Jackson2JsonMessageConverter jackson2JsonMessageConverter(ObjectMapper objectMapper) {
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    @Bean
    public SimpleRabbitListenerContainerFactory containerFactory(SimpleRabbitListenerContainerFactoryConfigurer configurer,
                                                                 MessageConverter messageConverter,
                                                                 ConnectionFactory connectionFactory,
                                                                 RabbitMqEntity rabbitMqEntity) {
        return RabbitMqUtils.buildListenerContainerFactory(configurer, messageConverter, connectionFactory, rabbitMqEntity.getConsumerThreads());
    }

}
