package com.senox.realty;

import com.senox.common.spring.SenoxBaseConfigure;
import com.senox.common.spring.SenoxWebConfigure;
import com.senox.common.utils.JsonUtils;
import com.senox.realty.domain.Fee;
import com.senox.realty.service.FeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.sql.init.dependency.DependsOnDatabaseInitialization;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

/**
 * 物业服务
 * <AUTHOR>
 * @Date 2020/12/14 15:29
 */
@Slf4j
@RequiredArgsConstructor
@SpringBootApplication
@Import({SenoxBaseConfigure.class, SenoxWebConfigure.class})
public class RealtyApplication {

    public static final String FEE_DEPOSIT = "保证金";

    private final FeeService feeService;

    /**
     * 保证金
     * @return
     */
    @Bean("depositFee")
    @DependsOnDatabaseInitialization
    public Fee depositFee() {
        Fee result = feeService.findByName(FEE_DEPOSIT);
        log.info("===> Init realty deposit fee {}", JsonUtils.object2Json(result));

        if (result == null) {
            result = new Fee();
            result.setId(0L);
            result.setName(FEE_DEPOSIT);
            log.warn("===> Init realty deposit fee fail. Reset default deposit.");
        }
        return result;
    }

    public static void main(String[] args) {
        SpringApplication.run(RealtyApplication.class, args);
    }
}
